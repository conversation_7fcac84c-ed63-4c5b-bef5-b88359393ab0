"""
Nástroje pro pr<PERSON><PERSON> s <PERSON> API a další pomocné funkce.
"""

import os
from typing import Dict, Any
import httpx
from dotenv import load_dotenv

class BraveSearchTool:
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.search.brave.com/res/v1/web/search"
        self.headers = {
            "Accept": "application/json",
            "X-Subscription-Token": api_key
        }

    async def search(self, query: str, **kwargs) -> Dict[str, Any]:
        """
        Provede vyhledávání pomocí Brave API.
        
        Args:
            query: Vyhledávací dotaz
            **kwargs: Dalš<PERSON> parametry pro vyhledávání (např. count, offset)
            
        Returns:
            Dict obsahující výsledky vyhledávání
        """
        params = {"q": query, **kwargs}
        
        async with httpx.AsyncClient() as client:
            response = await client.get(
                self.base_url,
                headers=self.headers,
                params=params
            )
            response.raise_for_status()
            return response.json()

def setup_tools() -> BraveSearchTool:
    """
    Inicializuje a vrátí nástroje pro agenta.
    
    Returns:
        Instance BraveSearchTool
    """
    load_dotenv()
    brave_api_key = os.getenv("BRAVE_API_KEY")
    
    if not brave_api_key:
        raise ValueError("BRAVE_API_KEY není nastaven v prostředí")
        
    return BraveSearchTool(brave_api_key) 