import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Send, MessageSquare, AlertTriangle, Settings, XCircle } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';

// Předpokládaná adresa backendu (pro komunikaci s Node.js serverem)
const API_BASE_URL = '/api'; // Upravte podle potřeby

// Typ pro zprávu v chatu
interface ChatMessage {
    id: string;
    text: string;
    sender: 'user' | 'ai';
    timestamp: number;
}

// Komponenta pro zobrazení jedné zprávy v chatu
const ChatMessageComponent: React.FC<{ message: ChatMessage }> = ({ message }) => {
    const isUserMessage = message.sender === 'user';

    // Animace pro zobrazení zprávy
    const messageVariants = {
        hidden: { opacity: 0, x: isUserMessage ? 20 : -20 },
        visible: { opacity: 1, x: 0, transition: { duration: 0.3 } },
        exit: { opacity: 0, x: isUserMessage ? -20 : 20, transition: { duration: 0.2 } }
    };

    return (
        <motion.div
            variants={messageVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            className={cn(
                'flex w-full mb-4',
                isUserMessage ? 'justify-end' : 'justify-start'
            )}
        >
            <div
                className={cn(
                    'rounded-xl px-4 py-3 max-w-[70%] sm:max-w-[50%] md:max-w-[40%] shadow-md',
                    isUserMessage
                        ? 'bg-blue-500 text-white ml-auto'
                        : 'bg-gray-200 text-gray-800 mr-auto',
                    'whitespace-pre-wrap break-words' // Zalamování a zachování formátování
                )}
            >
                {message.text}
            </div>
        </motion.div>
    );
};

// Hlavní komponenta aplikace
const AIFriendApp = () => {
    // Stav pro zprávy v chatu
    const [messages, setMessages] = useState<ChatMessage[]>([]);
    // Stav pro aktuálně psanou zprávu uživatele
    const [input, setInput] = useState('');
    // Stav pro načítání odpovědi od AI
    const [isLoading, setIsLoading] = useState(false);
    // Stav pro zobrazení varování o omezeních AI
    const [showDisclaimer, setShowDisclaimer] = useState(true); // Zobrazení úvodního upozornění
    // Stav pro zobrazení nastavení
    const [showSettings, setShowSettings] = useState(false);
     // Stav pro zobrazení hlášení chyby
    const [error, setError] = useState<string | null>(null);

    // Reference na kontejner chatu pro automatické skrolování
    const chatContainerRef = useRef<HTMLDivElement>(null);
    // Reference na vstupní pole
    const inputRef = useRef<HTMLTextAreaElement>(null);

    // Funkce pro odeslání zprávy (odesílá zprávu uživatele a volá AI)
    const sendMessage = useCallback(async () => {
        if (!input.trim()) return;

        const newUserMessage: ChatMessage = {
            id: crypto.randomUUID(),
            text: input,
            sender: 'user',
            timestamp: Date.now(),
        };

        const updatedMessages = [...messages, newUserMessage];
        setMessages(updatedMessages);
        setInput('');
        setIsLoading(true);

        try {
            // Simulace odpovědi AI (místo volání API)
            setTimeout(() => {
                const aiMessage: ChatMessage = {
                    id: crypto.randomUUID(),
                    text: `Toto je simulovaná odpověď na: "${input}"`,
                    sender: 'ai',
                    timestamp: Date.now(),
                };
                setMessages([...updatedMessages, aiMessage]);
                setIsLoading(false);
            }, 1000);
            
            // Skutečné volání API by vypadalo takto:
            /*
            const response = await fetch(`${API_BASE_URL}/generate-response`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ message: input }),
            });

            if (!response.ok) {
                throw new Error(`Chyba při komunikaci s AI: ${response.statusText}`);
            }
            const aiResponseData = await response.json();

            if (!aiResponseData.response) {
                throw new Error("Očekávaná odpověď AI nebyla nalezena.");
            }

            const aiMessage: ChatMessage = {
                id: crypto.randomUUID(),
                text: aiResponseData.response, // Získání textu odpovědi z objektu
                sender: 'ai',
                timestamp: Date.now(),
            };
            setMessages([...updatedMessages, aiMessage]);
            */
        } catch (error: any) {
            setError(`Chyba: ${error.message}`); // Nastavení chybové zprávy
            console.error("Chyba při získávání odpovědi od AI:", error);
            // Zde byste měli uživateli zobrazit chybovou zprávu (např. pomocí toastu nebo v UI)
            const errorMessage: ChatMessage = {
                id: crypto.randomUUID(),
                text: "Promiňte, vyskytla se chyba při zpracování vaší žádosti. Zkuste to prosím znovu.",
                sender: 'ai',
                timestamp: Date.now(),
            };
            setMessages([...updatedMessages, errorMessage]);

        } finally {
            // setIsLoading(false); // Přesunuto do setTimeout pro simulaci
        }
    }, [input, messages]);

    // Efekt pro automatické skrolování na konec chatu při změně zpráv
    useEffect(() => {
        if (chatContainerRef.current) {
            chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
        }
    }, [messages]);

    // Efekt pro fokus na vstupní pole při načtení komponenty
    useEffect(() => {
        if (inputRef.current) {
            inputRef.current.focus();
        }
    }, []);

    // Obsluha odeslání zprávy stisknutím Enter (bez Shift)
    const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    };

    // Funkce pro smazání všech zpráv
      const clearChat = () => {
        setMessages([]);
    };

    return (
        <div className="flex flex-col h-screen bg-gray-100">
            {/* Záhlaví aplikace */}
            <header className="bg-white shadow-md py-4 px-6 flex items-center justify-between">
                <div className="flex items-center gap-2">
                    <MessageSquare className="w-6 h-6 text-blue-500" />
                    <h1 className="text-xl font-semibold text-gray-800">AI Friend</h1>
                </div>
                <Button variant="outline" onClick={() => setShowSettings(true)}>
                    <Settings className="w-5 h-5" />
                </Button>
            </header>

            {/* Hlavní obsah - kontejner chatu */}
            <main className="flex-1 overflow-y-auto p-4 md:p-6" ref={chatContainerRef}>
                <AnimatePresence>
                    {messages.map((message) => (
                        <ChatMessageComponent key={message.id} message={message} />
                    ))}
                </AnimatePresence>
                {isLoading && (
                    <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        className="flex justify-start"
                    >
                        <div className="bg-gray-200 text-gray-800 rounded-xl px-4 py-3 max-w-[50%] animate-pulse">
                            {/* Zde by mohla být animace "psaní" */}
                            AI přemýšlí...
                        </div>
                    </motion.div>
                )}
                {error && (
                    <motion.div
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="mt-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative"
                        role="alert"
                    >
                        <strong className="font-bold">Chyba! </strong>
                        <span className="block sm:inline">{error}</span>
                        <button onClick={() => setError(null)} className="absolute top-0 bottom-0 right-0 px-4 py-3">
                            <XCircle className="h-6 w-6 fill-current" />
                        </button>
                    </motion.div>
                )}
            </main>

            {/* Vstupní pole pro psaní zpráv */}
            <footer className="p-4 md:p-6 bg-white shadow-inner">
                <div className="flex gap-4">
                    <Textarea
                        ref={inputRef}
                        value={input}
                        onChange={(e) => setInput(e.target.value)}
                        onKeyDown={handleKeyDown}
                        placeholder="Napište svou zprávu..."
                        className="flex-1 resize-none min-h-[2.5rem] max-h-[10rem]"
                        rows={1}
                        onInput={(e) => {
                            const target = e.target as HTMLTextAreaElement;
                            target.style.height = 'auto';
                            target.style.height = `${target.scrollHeight}px`;
                        }}
                    />
                    <Button
                        onClick={sendMessage}
                        disabled={isLoading}
                        className="bg-blue-500 text-white hover:bg-blue-600"
                    >
                        {isLoading ? (
                            <svg
                                className="animate-spin h-5 w-5 text-white"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                            >
                                <circle
                                    className="opacity-25"
                                    cx="12"
                                    cy="12"
                                    r="10"
                                    stroke="currentColor"
                                    strokeWidth="4"
                                ></circle>
                                <path
                                    className="opacity-75"
                                    fill="currentColor"
                                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                ></path>
                            </svg>
                        ) : (
                            <Send className="w-5 h-5" />
                        )}
                    </Button>
                </div>
            </footer>

            {/* Upozornění na omezení AI (Disclaimer) - Modal */}
            <AnimatePresence>
                {showDisclaimer && (
                    <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        transition={{ duration: 0.3 }}
                        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
                    >
                        <motion.div
                            initial={{ scale: 0.8, y: -20 }}
                            animate={{ scale: 1, y: 0 }}
                            exit={{ scale: 0.8, y: -20 }}
                            transition={{ duration: 0.3 }}
                            className="bg-white rounded-xl p-6 w-full max-w-md shadow-2xl border border-gray-200"
                        >
                            <div className="flex items-center gap-2 mb-4">
                                <AlertTriangle className="w-6 h-6 text-yellow-500" />
                                <h2 className="text-xl font-semibold text-gray-800">Důležité upozornění</h2>
                            </div>
                            <p className="text-gray-700 mb-4">
                                AI Friend je nástroj pro uspořádání myšlenek, nikoliv náhrada terapie.
                                <ul className="list-disc list-inside mt-2 space-y-1">
                                    <li>AI nerozumí vašim pocitům.</li>
                                    <li>AI může generovat nesprávné informace.</li>
                                    <li>Vaše konverzace NEBUDOU použity k trénování AI.</li>
                                </ul>
                            </p>
                            <p className="text-gray-700 mb-4">
                                V případě vážných problémů vyhledejte odbornou pomoc.
                            </p>
                            <div className="flex justify-end gap-2">
                                <Button
                                    variant="outline"
                                    onClick={() => {
                                        setShowDisclaimer(false);
                                        // Uložení informace o zobrazení upozornění do localStorage
                                        localStorage.setItem('disclaimerShown', 'true');
                                    }}
                                    className="text-gray-700 hover:bg-gray-100"
                                >
                                    Zrušit
                                </Button>
                                <Button
                                    onClick={() => {
                                        setShowDisclaimer(false);
                                        // Uložení informace o zobrazení upozornění do localStorage
                                        localStorage.setItem('disclaimerShown', 'true');
                                    }}
                                    className="bg-blue-500 text-white hover:bg-blue-600"
                                >
                                    Rozumím
                                </Button>
                            </div>
                        </motion.div>
                    </motion.div>
                )}
            </AnimatePresence>

            {/* Modal pro Nastavení */}
            <AnimatePresence>
                {showSettings && (
                    <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        transition={{ duration: 0.3 }}
                        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
                    >
                        <motion.div
                            initial={{ scale: 0.8, y: -20 }}
                            animate={{ scale: 1, y: 0 }}
                            exit={{ scale: 0.8, y: -20 }}
                            transition={{ duration: 0.3 }}
                            className="bg-white rounded-xl p-6 w-full max-w-md shadow-2xl border border-gray-200"
                        >
                            <div className="flex items-center justify-between mb-4">
                                <h2 className="text-xl font-semibold text-gray-800">Nastavení</h2>
                                <Button variant="ghost" onClick={() => setShowSettings(false)}>
                                    <XCircle className="w-5 h-5" />
                                </Button>
                            </div>
                            <div className="space-y-4">
                                 <div>
                                    <h3 className="font-medium text-gray-700">Vymazat konverzaci</h3>
                                    <p className="text-sm text-gray-500">Smaže všechny zprávy v chatu.</p>
                                    <Button
                                        variant="destructive"
                                        onClick={clearChat}
                                        className="mt-2 text-red-500 hover:bg-red-50"
                                    >
                                        <AlertTriangle className="w-4 h-4 mr-2" />
                                        Vymazat
                                    </Button>
                                </div>
                                {/* Další nastavení by se přidávala zde */}
                            </div>
                        </motion.div>
                    </motion.div>
                )}
            </AnimatePresence>
        </div>
    );
};

export default AIFriendApp;
