import json
import logging
import os

from browser_use.browser.browser import <PERSON><PERSON><PERSON>
from browser_use.browser.context import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, BrowserContextConfig
from playwright.async_api import <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON>rowser
from playwright.async_api import <PERSON><PERSON>er<PERSON>ontext as PlaywrightBrowserContext

logger = logging.getLogger(__name__)


class CustomBrowserContext(BrowserContext):
    def __init__(
        self,
        browser: "Browser",
        config: BrowserContextConfig = BrowserContextConfig()
    ):
        super(CustomBrowserContext, self).__init__(browser=browser, config=config)