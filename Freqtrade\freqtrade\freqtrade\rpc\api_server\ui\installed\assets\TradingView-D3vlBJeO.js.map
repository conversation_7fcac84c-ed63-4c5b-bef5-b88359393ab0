{"version": 3, "file": "TradingView-D3vlBJeO.js", "sources": ["../../src/components/ftbot/PairLockList.vue", "../../src/components/ftbot/BotPerformance.vue", "../../src/components/ftbot/BotProfit.vue", "../../src/components/ftbot/BotStatus.vue", "../../src/components/ftbot/BotControls.vue", "../../src/views/TradingView.vue"], "sourcesContent": ["<script setup lang=\"ts\">\nimport type { Lock } from '@/types';\n\nimport { useBotStore } from '@/stores/ftbotwrapper';\nconst botStore = useBotStore();\n\nfunction removePairLock(item: Lock) {\n  console.log(item);\n  if (item.id !== undefined) {\n    botStore.activeBot.deleteLock(item.id);\n  } else {\n    showAlert('This Freqtrade version does not support deleting locks.');\n  }\n}\n</script>\n\n<template>\n  <div>\n    <div class=\"mb-2\">\n      <label class=\"me-auto text-xl\">Pair Locks</label>\n      <Button class=\"float-end\" severity=\"secondary\" @click=\"botStore.activeBot.getLocks\">\n        <template #icon>\n          <i-mdi-refresh />\n        </template>\n      </Button>\n    </div>\n    <div>\n      <DataTable size=\"small\" :items=\"botStore.activeBot.activeLocks\">\n        <Column field=\"pair\" header=\"Pair\"></Column>\n        <Column field=\"lock_end_timestamp\" header=\"Until\">\n          <template #body=\"{ data, field }\">\n            {{ timestampms(data[field]) }}\n          </template>\n        </Column>\n        <Column field=\"reason\" header=\"Reason\"></Column>\n        <Column field=\"actions\" header=\"Actions\">\n          <template #body=\"{ data }\">\n            <Button\n              class=\"btn-xs ms-1\"\n              size=\"small\"\n              severity=\"secondary\"\n              title=\"Delete Lock\"\n              @click=\"removePairLock(data as Lock)\"\n            >\n              <i-mdi-delete />\n            </Button>\n          </template>\n        </Column>\n      </DataTable>\n    </div>\n  </div>\n</template>\n", "<script setup lang=\"ts\">\nimport { useBotStore } from '@/stores/ftbotwrapper';\n\nconst botStore = useBotStore();\nenum PerformanceOptions {\n  performance = 'performance',\n  entryStats = 'entryStats',\n  exitStats = 'exitStats',\n  mixTagStats = 'mixTagStats',\n}\nconst selectedOption = ref<PerformanceOptions>(PerformanceOptions.performance);\n\nfunction formatTextLen(text: string, len: number) {\n  if (text.length > len) {\n    return text.substring(0, len) + '...';\n  }\n  return text;\n}\n\nconst performanceTable = computed<\n  {\n    key: string;\n    label: string;\n    formatter?: (v: unknown) => string;\n  }[]\n>(() => {\n  const textLength = 17;\n  const initialCol = {\n    [PerformanceOptions.performance]: { key: 'pair', label: 'Pair' },\n    [PerformanceOptions.entryStats]: {\n      key: 'enter_tag',\n      label: 'Enter tag',\n      formatter: (v: unknown) => formatTextLen(v as string, textLength),\n    },\n    [PerformanceOptions.exitStats]: {\n      key: 'exit_reason',\n      label: 'Exit Reason',\n      formatter: (v: unknown) => formatTextLen(v as string, textLength),\n    },\n    [PerformanceOptions.mixTagStats]: {\n      key: 'mix_tag',\n      label: 'Mix Tag',\n      formatter: (v: unknown) => formatTextLen(v as string, textLength),\n    },\n  };\n  return [\n    initialCol[selectedOption.value],\n    { key: 'profit', label: 'Profit %' },\n    {\n      key: 'profit_abs',\n      label: `Profit ${botStore.activeBot.botState?.stake_currency}`,\n      formatter: (v: unknown) => formatPrice(v as number, 5),\n    },\n    { key: 'count', label: 'Count' },\n  ];\n});\n\nconst performanceData = computed(() => {\n  if (selectedOption.value === PerformanceOptions.performance) {\n    return botStore.activeBot.performanceStats;\n  }\n  if (selectedOption.value === PerformanceOptions.entryStats) {\n    return botStore.activeBot.entryStats;\n  }\n  if (selectedOption.value === PerformanceOptions.exitStats) {\n    return botStore.activeBot.exitStats;\n  }\n  if (selectedOption.value === PerformanceOptions.mixTagStats) {\n    return botStore.activeBot.mixTagStats;\n  }\n  return [];\n});\n\nconst hasAdvancedStats = computed(() => botStore.activeBot.botApiVersion >= 2.34);\n\nconst options = [\n  { value: PerformanceOptions.performance, text: 'Performance' },\n  { value: PerformanceOptions.entryStats, text: 'Entries' },\n  { value: PerformanceOptions.exitStats, text: 'Exits' },\n  { value: PerformanceOptions.mixTagStats, text: 'Mix Tag' },\n];\n\nfunction refreshSummary() {\n  if (selectedOption.value === PerformanceOptions.performance) {\n    botStore.activeBot.getPerformance();\n  }\n  if (selectedOption.value === PerformanceOptions.entryStats) {\n    botStore.activeBot.getEntryStats();\n  }\n  if (selectedOption.value === PerformanceOptions.exitStats) {\n    botStore.activeBot.getExitStats();\n  }\n  if (selectedOption.value === PerformanceOptions.mixTagStats) {\n    botStore.activeBot.getMixTagStats();\n  }\n}\n\nonMounted(() => {\n  refreshSummary();\n});\n</script>\n<template>\n  <div>\n    <div class=\"mb-2\">\n      <h3 class=\"me-auto text-2xl inline\">Performance</h3>\n      <Button class=\"float-end\" severity=\"secondary\" @click=\"refreshSummary\">\n        <template #icon>\n          <i-mdi-refresh />\n        </template>\n      </Button>\n    </div>\n    <SelectButton\n      v-if=\"hasAdvancedStats\"\n      id=\"order-direction\"\n      v-model=\"selectedOption\"\n      :options=\"options\"\n      :allow-empty=\"false\"\n      option-label=\"text\"\n      option-value=\"value\"\n      size=\"small\"\n      @change=\"refreshSummary\"\n    ></SelectButton>\n    <DataTable size=\"small\" class=\"text-center\" :value=\"performanceData\">\n      <Column\n        v-for=\"field in performanceTable\"\n        :key=\"field.key\"\n        :field=\"field.key\"\n        :header=\"field.label\"\n      >\n        <template #body=\"slotProps\">\n          {{\n            field.formatter ? field.formatter(slotProps.data[field.key]) : slotProps.data[field.key]\n          }}\n        </template>\n      </Column>\n    </DataTable>\n  </div>\n</template>\n", "<script setup lang=\"ts\">\nimport type { ProfitInterface } from '@/types';\n\nconst props = defineProps({\n  profit: { required: true, type: Object as () => ProfitInterface },\n  stakeCurrency: { required: true, type: String },\n  stakeCurrencyDecimals: { required: true, type: Number },\n});\n\nconst profitItems = computed(() => {\n  if (!props.profit) return [];\n  return [\n    {\n      metric: 'ROI closed trades',\n      value: props.profit.profit_closed_coin\n        ? `${formatPriceCurrency(\n            props.profit.profit_closed_coin,\n            props.stakeCurrency,\n            props.stakeCurrencyDecimals,\n          )} (${formatPercent(props.profit.profit_closed_ratio_mean, 2)})`\n        : 'N/A',\n      // (&sum; ${formatPercent(props.profit.profit_closed_ratio_sum,  2,)})`\n    },\n    {\n      metric: 'ROI all trades',\n      value: props.profit.profit_all_coin\n        ? `${formatPriceCurrency(\n            props.profit.profit_all_coin,\n            props.stakeCurrency,\n            props.stakeCurrencyDecimals,\n          )} (${formatPercent(props.profit.profit_all_ratio_mean, 2)})`\n        : 'N/A',\n      //  (&sum; ${formatPercent(props.profit.profit_all_ratio_sum,2,)})`\n    },\n\n    {\n      metric: 'Total Trade count',\n      value: `${props.profit.trade_count ?? 0}`,\n    },\n    {\n      metric: 'Bot started',\n      value: props.profit.bot_start_timestamp,\n      isTs: true,\n    },\n    {\n      metric: 'First Trade opened',\n      value: props.profit.first_trade_timestamp,\n      isTs: true,\n    },\n    {\n      metric: 'Latest Trade opened',\n      value: props.profit.latest_trade_timestamp,\n      isTs: true,\n    },\n    {\n      metric: 'Win / Loss',\n      value: `${props.profit.winning_trades ?? 0} / ${props.profit.losing_trades ?? 0}`,\n    },\n    {\n      metric: 'Winrate',\n      value: `${props.profit.winrate ? formatPercent(props.profit.winrate) : 'N/A'}`,\n    },\n    {\n      metric: 'Expectancy (ratio)',\n      value: `${props.profit.expectancy ? props.profit.expectancy.toFixed(2) : 'N/A'} (${\n        props.profit.expectancy_ratio ? props.profit.expectancy_ratio.toFixed(2) : 'N/A'\n      })`,\n    },\n    {\n      metric: 'Avg. Duration',\n      value: `${props.profit.avg_duration ?? 'N/A'}`,\n    },\n    {\n      metric: 'Best performing',\n      value: props.profit.best_pair\n        ? `${props.profit.best_pair}: ${formatPercent(props.profit.best_pair_profit_ratio, 2)}`\n        : 'N/A',\n    },\n    {\n      metric: 'Trading volume',\n      value: `${formatPriceCurrency(\n        props.profit.trading_volume ?? 0,\n        props.stakeCurrency,\n        props.stakeCurrencyDecimals,\n      )}`,\n    },\n    {\n      metric: 'Profit factor',\n      value: `${props.profit.profit_factor ? props.profit.profit_factor.toFixed(2) : 'N/A'}`,\n    },\n    {\n      metric: 'Max Drawdown',\n      value: `${props.profit.max_drawdown ? formatPercent(props.profit.max_drawdown, 2) : 'N/A'} (${\n        props.profit.max_drawdown_abs\n          ? formatPriceCurrency(\n              props.profit.max_drawdown_abs,\n              props.stakeCurrency,\n              props.stakeCurrencyDecimals,\n            )\n          : 'N/A'\n      }) ${\n        props.profit.max_drawdown_start_timestamp && props.profit.max_drawdown_end_timestamp\n          ? 'from ' +\n            timestampms(props.profit.max_drawdown_start_timestamp) +\n            ' to ' +\n            timestampms(props.profit.max_drawdown_end_timestamp)\n          : ''\n      }`,\n    },\n  ];\n});\n</script>\n\n<template>\n  <DataTable class=\"text-start\" small borderless :value=\"profitItems\">\n    <Column field=\"metric\" header=\"Metric\"></Column>\n    <Column field=\"value\" header=\"Value\"></Column>\n    <!-- <template #cell(value)=\"row\">\n      <DateTimeTZ v-if=\"row.item.isTs && row.value\" :date=\"row.value as number\"></DateTimeTZ>\n      <template v-else>{{ row.value }}</template>\n    </template> -->\n  </DataTable>\n</template>\n", "<script setup lang=\"ts\">\nimport { useBotStore } from '@/stores/ftbotwrapper';\n\nconst botStore = useBotStore();\n</script>\n\n<template>\n  <div v-if=\"botStore.activeBot.botState\" class=\"p-4\">\n    <p class=\"mb-4\">\n      Running Freqtrade <strong>{{ botStore.activeBot.version }}</strong>\n    </p>\n    <p class=\"mb-4\">\n      Running with\n      <strong>\n        {{ botStore.activeBot.botState.max_open_trades }}x{{\n          botStore.activeBot.botState.stake_amount\n        }}\n        {{ botStore.activeBot.botState.stake_currency }}\n      </strong>\n      on\n      <strong>{{ botStore.activeBot.botState.exchange }}</strong> in\n      <strong>{{ botStore.activeBot.botState.trading_mode || 'spot' }}</strong> markets, with\n      Strategy <strong>{{ botStore.activeBot.botState.strategy }}</strong\n      >.\n    </p>\n    <p v-if=\"'stoploss_on_exchange' in botStore.activeBot.botState\" class=\"mb-4\">\n      Stoploss on exchange is\n      <strong>{{\n        botStore.activeBot.botState.stoploss_on_exchange ? 'enabled' : 'disabled'\n      }}</strong\n      >.\n    </p>\n    <p class=\"mb-4\">\n      Currently <strong>{{ botStore.activeBot.botState.state }}</strong\n      >,\n      <strong>force entry: {{ botStore.activeBot.botState.force_entry_enable }}</strong>\n    </p>\n    <p>\n      <strong>{{ botStore.activeBot.botState.dry_run ? 'Dry-Run' : 'Live' }}</strong>\n    </p>\n    <Divider />\n    <p class=\"mb-4\">\n      Avg Profit {{ formatPercent(botStore.activeBot.profit.profit_all_ratio_mean) }} (&sum;\n      {{ formatPercent(botStore.activeBot.profit.profit_all_ratio_sum) }}) in\n      {{ botStore.activeBot.profit.trade_count }} Trades, with an average duration of\n      {{ botStore.activeBot.profit.avg_duration }}. Best pair:\n      {{ botStore.activeBot.profit.best_pair }}.\n    </p>\n    <p v-if=\"botStore.activeBot.profit.first_trade_timestamp\" class=\"mb-4\">\n      <span v-if=\"botStore.activeBot.profit.bot_start_timestamp\" class=\"block\">\n        Bot start date:\n        <strong>\n          <DateTimeTZ :date=\"botStore.activeBot.profit.bot_start_timestamp\" show-timezone />\n        </strong>\n      </span>\n      <span class=\"block\">\n        First trade opened:\n        <strong>\n          <DateTimeTZ :date=\"botStore.activeBot.profit.first_trade_timestamp\" show-timezone />\n        </strong>\n      </span>\n      <span class=\"block\">\n        Last trade opened:\n        <strong>\n          <DateTimeTZ :date=\"botStore.activeBot.profit.latest_trade_timestamp\" show-timezone />\n        </strong>\n      </span>\n    </p>\n    <p>\n      <span v-if=\"botStore.activeBot.profit.profit_factor\" class=\"block\">\n        Profit factor:\n        {{ botStore.activeBot.profit.profit_factor.toFixed(2) }}\n      </span>\n      <span v-if=\"botStore.activeBot.profit.trading_volume\" class=\"block mb-4\">\n        Trading volume:\n        {{\n          formatPriceCurrency(\n            botStore.activeBot.profit.trading_volume,\n            botStore.activeBot.botState.stake_currency,\n            botStore.activeBot.botState.stake_currency_decimals ?? 3,\n          )\n        }}\n      </span>\n    </p>\n    <BotProfit\n      class=\"mx-1\"\n      :profit=\"botStore.activeBot.profit\"\n      :stake-currency=\"botStore.activeBot.botState.stake_currency ?? 'USDT'\"\n      :stake-currency-decimals=\"botStore.activeBot.botState.stake_currency_decimals ?? 3\"\n    />\n  </div>\n</template>\n", "forceexit\n<script setup lang=\"ts\">\nimport type { MsgBoxObject } from '@/components/general/MessageBox.vue';\nimport type MessageBox from '@/components/general/MessageBox.vue';\nimport { useBotStore } from '@/stores/ftbotwrapper';\nimport type { ForceSellPayload } from '@/types';\n\nimport ForceEntryForm from './ForceEntryForm.vue';\n\nconst botStore = useBotStore();\nconst forceEnter = ref<boolean>(false);\nconst msgBox = ref<typeof MessageBox>();\n\nconst isRunning = computed((): boolean => {\n  return botStore.activeBot.botState?.state === 'running';\n});\n\nconst handleStopBot = () => {\n  const msg: MsgBoxObject = {\n    title: 'Stop Bot',\n    message: 'Stop the bot loop from running?',\n    accept: () => {\n      botStore.activeBot.stopBot();\n    },\n  };\n  msgBox.value?.show(msg);\n};\n\nconst handleStopBuy = () => {\n  const msg: MsgBoxObject = {\n    title: 'Pause - Stop Entering',\n    message:\n      'Freqtrade will continue to handle open trades, but will not enter new trades or increase position sizes.',\n    accept: () => {\n      botStore.activeBot.stopBuy();\n    },\n  };\n  msgBox.value?.show(msg);\n};\n\nconst handleReloadConfig = () => {\n  const msg: MsgBoxObject = {\n    title: 'Reload',\n    message: 'Reload configuration (including strategy)?',\n    accept: () => {\n      console.log('reload...');\n      botStore.activeBot.reloadConfig();\n    },\n  };\n  msgBox.value?.show(msg);\n};\n\nconst handleForceExit = () => {\n  const msg: MsgBoxObject = {\n    title: 'ForceExit all',\n    message: 'Really forceexit ALL trades?',\n    accept: () => {\n      const payload: ForceSellPayload = {\n        tradeid: 'all',\n        // TODO: support ordertype (?)\n      };\n      botStore.activeBot.forceexit(payload);\n    },\n  };\n  msgBox.value?.show(msg);\n};\n</script>\n\n<template>\n  <div class=\"flex flex-row gap-1\">\n    <Button\n      size=\"large\"\n      severity=\"secondary\"\n      :disabled=\"!botStore.activeBot.isTrading || isRunning\"\n      title=\"Start Trading\"\n      @click=\"botStore.activeBot.startBot()\"\n    >\n      <template #icon>\n        <i-mdi-play />\n      </template>\n    </Button>\n    <Button\n      size=\"large\"\n      severity=\"secondary\"\n      :disabled=\"!botStore.activeBot.isTrading || !isRunning\"\n      title=\"Stop Trading - Also stops handling open trades.\"\n      @click=\"handleStopBot()\"\n    >\n      <template #icon>\n        <i-mdi-stop />\n      </template>\n    </Button>\n    <Button\n      size=\"large\"\n      severity=\"secondary\"\n      :disabled=\"!botStore.activeBot.isTrading || !isRunning\"\n      title=\"Pause (StopBuy) - Freqtrade will continue to handle open trades, but will not enter new trades or increase position sizes.\"\n      @click=\"handleStopBuy()\"\n    >\n      <template #icon>\n        <i-mdi-pause />\n      </template>\n    </Button>\n    <Button\n      size=\"large\"\n      severity=\"secondary\"\n      :disabled=\"!botStore.activeBot.isTrading\"\n      title=\"Reload Config - reloads configuration including strategy, resetting all settings changed on the fly.\"\n      @click=\"handleReloadConfig()\"\n    >\n      <template #icon>\n        <i-mdi-reload />\n      </template>\n    </Button>\n    <Button\n      severity=\"secondary\"\n      size=\"large\"\n      :disabled=\"!botStore.activeBot.isTrading\"\n      title=\"Force exit all\"\n      @click=\"handleForceExit()\"\n    >\n      <template #icon>\n        <i-mdi-close-box-multiple />\n      </template>\n    </Button>\n    <Button\n      v-if=\"botStore.activeBot.botState && botStore.activeBot.botState.force_entry_enable\"\n      size=\"large\"\n      severity=\"secondary\"\n      :disabled=\"!botStore.activeBot.isTrading || !isRunning\"\n      title=\"Force enter - Immediately enter a trade at an optional price. Exits are then handled according to strategy rules.\"\n      @click=\"forceEnter = true\"\n    >\n      <template #icon>\n        <i-mdi-plus-box-multiple-outline />\n      </template>\n    </Button>\n    <Button\n      v-if=\"botStore.activeBot.isWebserverMode && false\"\n      size=\"large\"\n      severity=\"secondary\"\n      :disabled=\"botStore.activeBot.isTrading\"\n      title=\"Start Trading mode\"\n      @click=\"botStore.activeBot.startTrade()\"\n    >\n      <template #icon>\n        <i-mdi-play />\n      </template>\n    </Button>\n    <ForceEntryForm v-model=\"forceEnter\" :pair=\"botStore.activeBot.selectedPair\" />\n    <MessageBox ref=\"msgBox\" />\n  </div>\n</template>\n", "<script setup lang=\"ts\">\nimport type { GridItemData } from '@/types';\n\nimport { useLayoutStore, findGridLayout, TradeLayout } from '@/stores/layout';\nimport { useBotStore } from '@/stores/ftbotwrapper';\n\nconst botStore = useBotStore();\nconst layoutStore = useLayoutStore();\nconst settingsStore = useSettingsStore();\nconst currentBreakpoint = ref('');\n\nconst breakpointChanged = (newBreakpoint: string) => {\n  // console.log('breakpoint:', newBreakpoint);\n  currentBreakpoint.value = newBreakpoint;\n};\nconst isResizableLayout = computed(() =>\n  ['', 'sm', 'md', 'lg', 'xl'].includes(currentBreakpoint.value),\n);\nconst isLayoutLocked = computed(() => {\n  return layoutStore.layoutLocked || !isResizableLayout.value;\n});\nconst gridLayoutData = computed((): GridItemData[] => {\n  if (isResizableLayout.value) {\n    return layoutStore.tradingLayout;\n  }\n  return [...layoutStore.getTradingLayoutSm];\n});\n\nconst gridLayoutMultiPane = computed(() => {\n  return findGridLayout(gridLayoutData.value, TradeLayout.multiPane);\n});\n\nconst gridLayoutOpenTrades = computed(() => {\n  return findGridLayout(gridLayoutData.value, TradeLayout.openTrades);\n});\n\nconst gridLayoutTradeHistory = computed(() => {\n  return findGridLayout(gridLayoutData.value, TradeLayout.tradeHistory);\n});\n\nconst gridLayoutTradeDetail = computed(() => {\n  return findGridLayout(gridLayoutData.value, TradeLayout.tradeDetail);\n});\n\nconst gridLayoutChartView = computed(() => {\n  return findGridLayout(gridLayoutData.value, TradeLayout.chartView);\n});\n\nconst responsiveGridLayouts = computed(() => {\n  return {\n    sm: layoutStore.getTradingLayoutSm,\n  };\n});\n\nfunction refreshOHLCV(pair: string, columns: string[]) {\n  botStore.activeBot.getPairCandles({\n    pair: pair,\n    timeframe: botStore.activeBot.timeframe,\n    columns: columns,\n  });\n}\n</script>\n\n<template>\n  <GridLayout\n    class=\"h-full w-full\"\n    style=\"padding: 1px\"\n    :row-height=\"50\"\n    :layout=\"gridLayoutData\"\n    :vertical-compact=\"false\"\n    :margin=\"[1, 1]\"\n    :responsive-layouts=\"responsiveGridLayouts\"\n    :is-resizable=\"!isLayoutLocked\"\n    :is-draggable=\"!isLayoutLocked\"\n    :responsive=\"true\"\n    :cols=\"{ lg: 12, md: 12, sm: 12, xs: 4, xxs: 2 }\"\n    :col-num=\"12\"\n    @update:breakpoint=\"breakpointChanged\"\n  >\n    <template #default=\"{ gridItemProps }\">\n      <GridItem\n        v-if=\"gridLayoutMultiPane.h != 0\"\n        v-bind=\"gridItemProps\"\n        :i=\"gridLayoutMultiPane.i\"\n        :x=\"gridLayoutMultiPane.x\"\n        :y=\"gridLayoutMultiPane.y\"\n        :w=\"gridLayoutMultiPane.w\"\n        :h=\"gridLayoutMultiPane.h\"\n        drag-allow-from=\".drag-header\"\n      >\n        <DraggableContainer header=\"Multi Pane\">\n          <div class=\"mt-1 flex justify-center\">\n            <BotControls class=\"mt-1 mb-2\" />\n          </div>\n          <Tabs value=\"0\" scrollable lazy>\n            <TabList>\n              <Tab value=\"0\" severity=\"secondary\">\n                <div title=\"Pairs combined\">\n                  <span v-if=\"settingsStore.multiPaneButtonsShowText\" class=\"ms-1\"\n                    >Pairs combined</span\n                  >\n                  <i-mdi-view-list v-else />\n                </div>\n              </Tab>\n              <Tab value=\"1\" severity=\"secondary\">\n                <div title=\"General\">\n                  <span v-if=\"settingsStore.multiPaneButtonsShowText\" class=\"ms-1\">General</span>\n                  <i-mdi-information v-else />\n                </div>\n              </Tab>\n              <Tab value=\"2\" severity=\"secondary\">\n                <div title=\"Performance\">\n                  <span v-if=\"settingsStore.multiPaneButtonsShowText\" class=\"ms-1\"\n                    >Performance</span\n                  >\n                  <i-mdi-chart-line v-else />\n                </div>\n              </Tab>\n              <Tab value=\"3\" severity=\"secondary\">\n                <div title=\"Balance\">\n                  <span v-if=\"settingsStore.multiPaneButtonsShowText\" class=\"ms-1\">Balance</span>\n                  <i-mdi-bank v-else />\n                </div>\n              </Tab>\n              <Tab value=\"4\" severity=\"secondary\">\n                <div title=\"Time Breakdown\">\n                  <span v-if=\"settingsStore.multiPaneButtonsShowText\" class=\"ms-1\"\n                    >Time Breakdown</span\n                  >\n                  <i-mdi-folder-clock v-else />\n                </div>\n              </Tab>\n              <Tab value=\"5\" severity=\"secondary\">\n                <div title=\"Pairlist\">\n                  <span v-if=\"settingsStore.multiPaneButtonsShowText\" class=\"ms-1\">Pairlist</span>\n                  <i-mdi-format-list-group v-else />\n                </div>\n              </Tab>\n              <Tab value=\"6\" severity=\"secondary\">\n                <div title=\"Pair Locks\">\n                  <span v-if=\"settingsStore.multiPaneButtonsShowText\" class=\"ms-1\">Pair Locks</span>\n                  <i-mdi-lock-alert v-else />\n                </div>\n              </Tab>\n            </TabList>\n            <TabPanels>\n              <TabPanel value=\"0\">\n                <PairSummary\n                  :pairlist=\"botStore.activeBot.whitelist\"\n                  :current-locks=\"botStore.activeBot.activeLocks\"\n                  :trades=\"botStore.activeBot.openTrades\"\n                />\n              </TabPanel>\n              <TabPanel value=\"1\">\n                <BotStatus />\n              </TabPanel>\n              <TabPanel value=\"2\" lazy>\n                <BotPerformance />\n              </TabPanel>\n              <TabPanel value=\"3\" lazy>\n                <BotBalance />\n              </TabPanel>\n              <TabPanel value=\"4\" lazy>\n                <PeriodBreakdown />\n              </TabPanel>\n\n              <TabPanel value=\"5\" lazy>\n                <PairListLive />\n              </TabPanel>\n              <TabPanel value=\"6\" lazy>\n                <PairLockList />\n              </TabPanel>\n            </TabPanels>\n          </Tabs>\n        </DraggableContainer>\n      </GridItem>\n      <GridItem\n        v-if=\"gridLayoutOpenTrades.h != 0\"\n        v-bind=\"gridItemProps\"\n        :i=\"gridLayoutOpenTrades.i\"\n        :x=\"gridLayoutOpenTrades.x\"\n        :y=\"gridLayoutOpenTrades.y\"\n        :w=\"gridLayoutOpenTrades.w\"\n        :h=\"gridLayoutOpenTrades.h\"\n        drag-allow-from=\".drag-header\"\n      >\n        <DraggableContainer header=\"Open Trades\">\n          <TradeList\n            class=\"open-trades\"\n            :trades=\"botStore.activeBot.openTrades\"\n            title=\"Open trades\"\n            :active-trades=\"true\"\n            empty-text=\"Currently no open trades.\"\n          />\n        </DraggableContainer>\n      </GridItem>\n      <GridItem\n        v-if=\"gridLayoutTradeHistory.h != 0\"\n        v-bind=\"gridItemProps\"\n        :i=\"gridLayoutTradeHistory.i\"\n        :x=\"gridLayoutTradeHistory.x\"\n        :y=\"gridLayoutTradeHistory.y\"\n        :w=\"gridLayoutTradeHistory.w\"\n        :h=\"gridLayoutTradeHistory.h\"\n        drag-allow-from=\".drag-header\"\n      >\n        <DraggableContainer header=\"Closed Trades\">\n          <TradeList\n            class=\"trade-history\"\n            :trades=\"botStore.activeBot.closedTrades\"\n            title=\"Trade history\"\n            :show-filter=\"true\"\n            empty-text=\"No closed trades so far.\"\n          />\n        </DraggableContainer>\n      </GridItem>\n      <GridItem\n        v-if=\"\n          botStore.activeBot.detailTradeId &&\n          botStore.activeBot.tradeDetail &&\n          gridLayoutTradeDetail.h != 0\n        \"\n        v-bind=\"gridItemProps\"\n        :i=\"gridLayoutTradeDetail.i\"\n        :x=\"gridLayoutTradeDetail.x\"\n        :y=\"gridLayoutTradeDetail.y\"\n        :w=\"gridLayoutTradeDetail.w\"\n        :h=\"gridLayoutTradeDetail.h\"\n        :min-h=\"4\"\n        drag-allow-from=\".drag-header\"\n      >\n        <DraggableContainer header=\"Trade Detail\">\n          <TradeDetail\n            :trade=\"botStore.activeBot.tradeDetail\"\n            :stake-currency=\"botStore.activeBot.stakeCurrency\"\n          />\n        </DraggableContainer>\n      </GridItem>\n      <GridItem\n        v-if=\"gridLayoutTradeDetail.h != 0\"\n        v-bind=\"gridItemProps\"\n        :i=\"gridLayoutChartView.i\"\n        :x=\"gridLayoutChartView.x\"\n        :y=\"gridLayoutChartView.y\"\n        :w=\"gridLayoutChartView.w\"\n        :h=\"gridLayoutChartView.h\"\n        :min-h=\"6\"\n        drag-allow-from=\".drag-header\"\n      >\n        <DraggableContainer header=\"Chart\">\n          <CandleChartContainer\n            :available-pairs=\"botStore.activeBot.whitelist\"\n            :historic-view=\"!!false\"\n            :timeframe=\"botStore.activeBot.timeframe\"\n            :trades=\"botStore.activeBot.allTrades\"\n            @refresh-data=\"refreshOHLCV\"\n          >\n          </CandleChartContainer>\n        </DraggableContainer>\n      </GridItem>\n    </template>\n  </GridLayout>\n</template>\n"], "names": ["botStore", "useBotStore", "removePairLock", "item", "show<PERSON><PERSON><PERSON>", "selectedOption", "ref", "formatTextLen", "text", "len", "performanceTable", "computed", "v", "_a", "formatPrice", "performanceData", "hasAdvancedStats", "options", "refreshSummary", "onMounted", "props", "__props", "profitItems", "formatPriceCurrency", "formatPercent", "timestampms", "forceEnter", "msgBox", "isRunning", "handleStopBot", "msg", "handleStopBuy", "handleReloadConfig", "handleForceExit", "payload", "layoutStore", "useLayoutStore", "settingsStore", "useSettingsStore", "currentBreakpoint", "breakpointChanged", "newBreakpoint", "isResizableLayout", "isLayoutLocked", "gridLayoutData", "gridLayoutMultiPane", "findGridLayout", "TradeLayout", "gridLayoutOpenTrades", "gridLayoutTradeHistory", "gridLayoutTradeDetail", "gridLayoutChartView", "responsiveGridLayouts", "refreshOHLCV", "pair", "columns"], "mappings": "29CAIA,MAAMA,EAAWC,EAAY,EAE7B,SAASC,EAAeC,EAAY,CAClC,QAAQ,IAAIA,CAAI,EACZA,EAAK,KAAO,OACLH,EAAA,UAAU,WAAWG,EAAK,EAAE,EAErCC,GAAU,yDAAyD,CACrE,m1BCTF,MAAMJ,EAAWC,EAAY,EAOvBI,EAAiBC,EAAwB,aAA8B,EAEpE,SAAAC,EAAcC,EAAcC,EAAa,CAC5C,OAAAD,EAAK,OAASC,EACTD,EAAK,UAAU,EAAGC,CAAG,EAAI,MAE3BD,CAAA,CAGH,MAAAE,EAAmBC,EAMvB,IAAM,OAoBC,MAAA,CAlBY,CAChB,YAAiC,CAAE,IAAK,OAAQ,MAAO,MAAO,EAC9D,WAAgC,CAC/B,IAAK,YACL,MAAO,YACP,UAAYC,GAAeL,EAAcK,EAAa,EAAU,CAClE,EACC,UAA+B,CAC9B,IAAK,cACL,MAAO,cACP,UAAYA,GAAeL,EAAcK,EAAa,EAAU,CAClE,EACC,YAAiC,CAChC,IAAK,UACL,MAAO,UACP,UAAYA,GAAeL,EAAcK,EAAa,EAAU,CAAA,CAEpE,EAEaP,EAAe,KAAK,EAC/B,CAAE,IAAK,SAAU,MAAO,UAAW,EACnC,CACE,IAAK,aACL,MAAO,WAAUQ,EAAAb,EAAS,UAAU,WAAnB,YAAAa,EAA6B,cAAc,GAC5D,UAAYD,GAAeE,GAAYF,EAAa,CAAC,CACvD,EACA,CAAE,IAAK,QAAS,MAAO,OAAQ,CACjC,CAAA,CACD,EAEKG,EAAkBJ,EAAS,IAC3BN,EAAe,QAAU,cACpBL,EAAS,UAAU,iBAExBK,EAAe,QAAU,aACpBL,EAAS,UAAU,WAExBK,EAAe,QAAU,YACpBL,EAAS,UAAU,UAExBK,EAAe,QAAU,cACpBL,EAAS,UAAU,YAErB,CAAC,CACT,EAEKgB,EAAmBL,EAAS,IAAMX,EAAS,UAAU,eAAiB,IAAI,EAE1EiB,EAAU,CACd,CAAE,MAAO,cAAgC,KAAM,aAAc,EAC7D,CAAE,MAAO,aAA+B,KAAM,SAAU,EACxD,CAAE,MAAO,YAA8B,KAAM,OAAQ,EACrD,CAAE,MAAO,cAAgC,KAAM,SAAU,CAC3D,EAEA,SAASC,GAAiB,CACpBb,EAAe,QAAU,eAC3BL,EAAS,UAAU,eAAe,EAEhCK,EAAe,QAAU,cAC3BL,EAAS,UAAU,cAAc,EAE/BK,EAAe,QAAU,aAC3BL,EAAS,UAAU,aAAa,EAE9BK,EAAe,QAAU,eAC3BL,EAAS,UAAU,eAAe,CACpC,CAGF,OAAAmB,GAAU,IAAM,CACCD,EAAA,CAAA,CAChB,u5BChGD,MAAME,EAAQC,EAMRC,EAAcX,EAAS,IACtBS,EAAM,OACJ,CACL,CACE,OAAQ,oBACR,MAAOA,EAAM,OAAO,mBAChB,GAAGG,EACDH,EAAM,OAAO,mBACbA,EAAM,cACNA,EAAM,qBAAA,CACP,KAAKI,EAAcJ,EAAM,OAAO,yBAA0B,CAAC,CAAC,IAC7D,KAEN,EACA,CACE,OAAQ,iBACR,MAAOA,EAAM,OAAO,gBAChB,GAAGG,EACDH,EAAM,OAAO,gBACbA,EAAM,cACNA,EAAM,qBAAA,CACP,KAAKI,EAAcJ,EAAM,OAAO,sBAAuB,CAAC,CAAC,IAC1D,KAEN,EAEA,CACE,OAAQ,oBACR,MAAO,GAAGA,EAAM,OAAO,aAAe,CAAC,EACzC,EACA,CACE,OAAQ,cACR,MAAOA,EAAM,OAAO,oBACpB,KAAM,EACR,EACA,CACE,OAAQ,qBACR,MAAOA,EAAM,OAAO,sBACpB,KAAM,EACR,EACA,CACE,OAAQ,sBACR,MAAOA,EAAM,OAAO,uBACpB,KAAM,EACR,EACA,CACE,OAAQ,aACR,MAAO,GAAGA,EAAM,OAAO,gBAAkB,CAAC,MAAMA,EAAM,OAAO,eAAiB,CAAC,EACjF,EACA,CACE,OAAQ,UACR,MAAO,GAAGA,EAAM,OAAO,QAAUI,EAAcJ,EAAM,OAAO,OAAO,EAAI,KAAK,EAC9E,EACA,CACE,OAAQ,qBACR,MAAO,GAAGA,EAAM,OAAO,WAAaA,EAAM,OAAO,WAAW,QAAQ,CAAC,EAAI,KAAK,KAC5EA,EAAM,OAAO,iBAAmBA,EAAM,OAAO,iBAAiB,QAAQ,CAAC,EAAI,KAC7E,GACF,EACA,CACE,OAAQ,gBACR,MAAO,GAAGA,EAAM,OAAO,cAAgB,KAAK,EAC9C,EACA,CACE,OAAQ,kBACR,MAAOA,EAAM,OAAO,UAChB,GAAGA,EAAM,OAAO,SAAS,KAAKI,EAAcJ,EAAM,OAAO,uBAAwB,CAAC,CAAC,GACnF,KACN,EACA,CACE,OAAQ,iBACR,MAAO,GAAGG,EACRH,EAAM,OAAO,gBAAkB,EAC/BA,EAAM,cACNA,EAAM,qBAAA,CACP,EACH,EACA,CACE,OAAQ,gBACR,MAAO,GAAGA,EAAM,OAAO,cAAgBA,EAAM,OAAO,cAAc,QAAQ,CAAC,EAAI,KAAK,EACtF,EACA,CACE,OAAQ,eACR,MAAO,GAAGA,EAAM,OAAO,aAAeI,EAAcJ,EAAM,OAAO,aAAc,CAAC,EAAI,KAAK,KACvFA,EAAM,OAAO,iBACTG,EACEH,EAAM,OAAO,iBACbA,EAAM,cACNA,EAAM,qBAAA,EAER,KACN,KACEA,EAAM,OAAO,8BAAgCA,EAAM,OAAO,2BACtD,QACAK,EAAYL,EAAM,OAAO,4BAA4B,EACrD,OACAK,EAAYL,EAAM,OAAO,0BAA0B,EACnD,EACN,EAAA,CAEJ,EAnG0B,CAAC,CAoG5B,6fC3GD,MAAMpB,EAAWC,EAAY,wgLCM7B,MAAMD,EAAWC,EAAY,EACvByB,EAAapB,EAAa,EAAK,EAC/BqB,EAASrB,EAAuB,EAEhCsB,EAAYjB,EAAS,IAAe,OACjC,QAAAE,EAAAb,EAAS,UAAU,WAAnB,YAAAa,EAA6B,SAAU,SAAA,CAC/C,EAEKgB,EAAgB,IAAM,OAC1B,MAAMC,EAAoB,CACxB,MAAO,WACP,QAAS,kCACT,OAAQ,IAAM,CACZ9B,EAAS,UAAU,QAAQ,CAAA,CAE/B,GACOa,EAAAc,EAAA,QAAA,MAAAd,EAAO,KAAKiB,EACrB,EAEMC,EAAgB,IAAM,OAC1B,MAAMD,EAAoB,CACxB,MAAO,wBACP,QACE,2GACF,OAAQ,IAAM,CACZ9B,EAAS,UAAU,QAAQ,CAAA,CAE/B,GACOa,EAAAc,EAAA,QAAA,MAAAd,EAAO,KAAKiB,EACrB,EAEME,EAAqB,IAAM,OAC/B,MAAMF,EAAoB,CACxB,MAAO,SACP,QAAS,6CACT,OAAQ,IAAM,CACZ,QAAQ,IAAI,WAAW,EACvB9B,EAAS,UAAU,aAAa,CAAA,CAEpC,GACOa,EAAAc,EAAA,QAAA,MAAAd,EAAO,KAAKiB,EACrB,EAEMG,EAAkB,IAAM,OAC5B,MAAMH,EAAoB,CACxB,MAAO,gBACP,QAAS,+BACT,OAAQ,IAAM,CACZ,MAAMI,EAA4B,CAChC,QAAS,KAEX,EACSlC,EAAA,UAAU,UAAUkC,CAAO,CAAA,CAExC,GACOrB,EAAAc,EAAA,QAAA,MAAAd,EAAO,KAAKiB,EACrB,spEC3DA,MAAM9B,EAAWC,EAAY,EACvBkC,EAAcC,GAAe,EAC7BC,EAAgBC,GAAiB,EACjCC,EAAoBjC,EAAI,EAAE,EAE1BkC,EAAqBC,GAA0B,CAEnDF,EAAkB,MAAQE,CAC5B,EACMC,EAAoB/B,EAAS,IACjC,CAAC,GAAI,KAAM,KAAM,KAAM,IAAI,EAAE,SAAS4B,EAAkB,KAAK,CAC/D,EACMI,EAAiBhC,EAAS,IACvBwB,EAAY,cAAgB,CAACO,EAAkB,KACvD,EACKE,EAAiBjC,EAAS,IAC1B+B,EAAkB,MACbP,EAAY,cAEd,CAAC,GAAGA,EAAY,kBAAkB,CAC1C,EAEKU,EAAsBlC,EAAS,IAC5BmC,EAAeF,EAAe,MAAOG,EAAY,SAAS,CAClE,EAEKC,EAAuBrC,EAAS,IAC7BmC,EAAeF,EAAe,MAAOG,EAAY,UAAU,CACnE,EAEKE,EAAyBtC,EAAS,IAC/BmC,EAAeF,EAAe,MAAOG,EAAY,YAAY,CACrE,EAEKG,EAAwBvC,EAAS,IAC9BmC,EAAeF,EAAe,MAAOG,EAAY,WAAW,CACpE,EAEKI,EAAsBxC,EAAS,IAC5BmC,EAAeF,EAAe,MAAOG,EAAY,SAAS,CAClE,EAEKK,EAAwBzC,EAAS,KAC9B,CACL,GAAIwB,EAAY,kBAClB,EACD,EAEQ,SAAAkB,EAAaC,EAAcC,EAAmB,CACrDvD,EAAS,UAAU,eAAe,CAChC,KAAAsD,EACA,UAAWtD,EAAS,UAAU,UAC9B,QAAAuD,CAAA,CACD,CAAA"}