# database.py
import sqlite3
import os
from datetime import datetime

DATABASE = 'debates.db'

def get_db():
    """<PERSON><PERSON><PERSON> př<PERSON> k databázi."""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row # Vrac<PERSON>ky jako objekty podobné slovníkům
    conn.execute("PRAGMA foreign_keys = ON")
    return conn

def init_db(force=False):
    """Inicializuje schéma datab<PERSON>ze, pokud neexistuje nebo je vynuceno."""
    db_exists = os.path.exists(DATABASE)
    if db_exists and not force:
        return

    print("Inicializuji databázi..." if not db_exists else "Reinicializuji databázi (force=True)...")
    try:
        conn = get_db()
        cursor = conn.cursor()

        if force:
            print("Mažu existující tabulky...")
            cursor.execute("DROP TABLE IF EXISTS summaries;")
            cursor.execute("DROP TABLE IF EXISTS messages;")
            cursor.execute("DROP TABLE IF EXISTS debates;")

        print("Vytvářím tabulku 'debates'...")
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS debates (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            topic TEXT NOT NULL,
            start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            end_time TIMESTAMP,
            model_a TEXT NOT NULL,
            model_b TEXT NOT NULL,
            temperature_a REAL NOT NULL DEFAULT 0.7,
            temperature_b REAL NOT NULL DEFAULT 0.7,
            max_tokens_a INTEGER,
            max_tokens_b INTEGER,
            status TEXT NOT NULL DEFAULT 'starting' -- starting, running, completed, error, cancelled
        );
        """)

        print("Vytvářím tabulku 'messages'...")
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS messages (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            debate_id INTEGER NOT NULL,
            speaker TEXT NOT NULL,      -- 'ModelA', 'ModelB', 'System', 'User'
            model_used TEXT,
            message_text TEXT NOT NULL,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            token_count INTEGER,
            FOREIGN KEY (debate_id) REFERENCES debates (id) ON DELETE CASCADE
        );
        """)

        print("Vytvářím tabulku 'summaries'...")
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS summaries (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            debate_id INTEGER NOT NULL,
            model_used TEXT NOT NULL,
            summary_text TEXT,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (debate_id) REFERENCES debates (id) ON DELETE CASCADE
        );
        """)

        conn.commit()
        print("Schéma databáze inicializováno/aktualizováno.")
    except sqlite3.Error as e:
        print(f"Chyba při inicializaci DB: {e}")
        if conn:
            conn.rollback()
    finally:
        if conn:
            conn.close()

if __name__ == '__main__':
    init_db(force=False) # Nastavte force=True pro smazání a znovu vytvoření