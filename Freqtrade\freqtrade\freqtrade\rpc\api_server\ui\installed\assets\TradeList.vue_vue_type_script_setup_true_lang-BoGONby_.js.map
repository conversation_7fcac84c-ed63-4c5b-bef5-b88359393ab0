{"version": 3, "file": "TradeList.vue_vue_type_script_setup_true_lang-BoGONby_.js", "sources": ["../../src/components/ftbot/ForceEntryForm.vue", "../../node_modules/.pnpm/@primeuix+styles@1.0.0/node_modules/@primeuix/styles/slider/index.mjs", "../../node_modules/.pnpm/primevue@4.3.3_vue@3.5.13_typescript@5.8.2_/node_modules/primevue/slider/style/index.mjs", "../../node_modules/.pnpm/primevue@4.3.3_vue@3.5.13_typescript@5.8.2_/node_modules/primevue/slider/index.mjs", "../../src/components/ftbot/ForceExitForm.vue", "../../src/components/ftbot/TradeActionsPopover.vue", "../../src/components/ftbot/TradeList.vue"], "sourcesContent": ["<script setup lang=\"ts\">\nimport { useBotStore } from '@/stores/ftbotwrapper';\nimport type { ForceEnterPayload } from '@/types';\nimport { OrderSides } from '@/types';\n\nconst props = defineProps({\n  pair: { type: String, default: '' },\n  positionIncrease: { type: Boolean, default: false },\n});\nconst model = defineModel<boolean>();\nconst botStore = useBotStore();\n\nconst form = ref<HTMLFormElement>();\nconst selectedPair = ref('');\nconst price = ref<number | undefined>(undefined);\nconst stakeAmount = ref<number | undefined>(undefined);\nconst leverage = ref<number | undefined>(undefined);\n\nconst ordertype = ref('');\nconst orderSide = ref<OrderSides>(OrderSides.long);\nconst enterTag = ref('force_entry');\n\nconst orderTypeOptions = [\n  { value: 'market', text: 'Market' },\n  { value: 'limit', text: 'Limit' },\n];\nconst orderSideOptions = [\n  { value: 'long', text: 'Long' },\n  { value: 'short', text: 'Short' },\n];\n\nconst checkFormValidity = () => {\n  const valid = form.value?.checkValidity();\n\n  return valid;\n};\n\nconst handleSubmit = async () => {\n  // Exit when the form isn't valid\n  if (!checkFormValidity()) {\n    return;\n  }\n\n  // call forceentry\n  const payload: ForceEnterPayload = { pair: selectedPair.value };\n  if (price.value) {\n    payload.price = Number(price.value);\n  }\n  if (ordertype.value) {\n    payload.ordertype = ordertype.value;\n  }\n  if (stakeAmount.value) {\n    payload.stakeamount = stakeAmount.value;\n  }\n  if (botStore.activeBot.botApiVersion >= 2.13 && botStore.activeBot.shortAllowed) {\n    payload.side = orderSide.value;\n  }\n  if (botStore.activeBot.botApiVersion >= 2.16 && enterTag.value) {\n    payload.entry_tag = enterTag.value;\n  }\n\n  if (leverage.value) {\n    payload.leverage = leverage.value;\n  }\n  botStore.activeBot.forceentry(payload);\n  await nextTick();\n  model.value = false;\n};\nconst resetForm = () => {\n  console.log('resetForm');\n  selectedPair.value = props.pair;\n  price.value = undefined;\n  stakeAmount.value = undefined;\n  ordertype.value =\n    botStore.activeBot.botState?.order_types?.forcebuy ||\n    botStore.activeBot.botState?.order_types?.force_entry ||\n    botStore.activeBot.botState?.order_types?.buy ||\n    botStore.activeBot.botState?.order_types?.entry ||\n    'limit';\n};\n\nconst handleEntry = () => {\n  // Trigger submit handler\n  handleSubmit();\n};\n</script>\n\n<template>\n  <Dialog\n    v-model:visible=\"model\"\n    :header=\"positionIncrease ? `Increasing position for ${pair}` : 'Force entering a trade'\"\n    modal\n    @show=\"resetForm\"\n    @hide=\"resetForm\"\n  >\n    <form ref=\"form\" class=\"space-y-4 md:min-w-[32rem]\" @submit.prevent=\"handleSubmit\">\n      <div v-if=\"botStore.activeBot.botApiVersion >= 2.13 && botStore.activeBot.shortAllowed\">\n        <label class=\"block font-medium mb-1\">Order direction (Long or Short)</label>\n        <SelectButton\n          v-model=\"orderSide\"\n          :options=\"orderSideOptions\"\n          :allow-empty=\"false\"\n          option-label=\"text\"\n          option-value=\"value\"\n          size=\"small\"\n          class=\"w-full\"\n        />\n      </div>\n\n      <div>\n        <label for=\"pair-input\" class=\"block font-medium mb-1\">Pair</label>\n        <InputText\n          id=\"pair-input\"\n          v-model=\"selectedPair\"\n          :disabled=\"positionIncrease\"\n          required\n          class=\"w-full\"\n          @keydown.enter=\"handleEntry\"\n          @focus=\"($event.target as HTMLInputElement).select()\"\n        />\n      </div>\n\n      <div>\n        <label for=\"price-input\" class=\"block font-medium mb-1\">Price [optional]</label>\n        <InputNumber\n          id=\"price-input\"\n          v-model=\"price\"\n          show-buttons\n          :min=\"0\"\n          :max-fraction-digits=\"8\"\n          :step=\"0.1\"\n          class=\"w-full\"\n          @keydown.enter=\"handleEntry\"\n        />\n      </div>\n\n      <div>\n        <label for=\"stake-input\" class=\"block font-medium mb-1\"\n          >* Stake-amount in {{ botStore.activeBot.stakeCurrency }} [optional]</label\n        >\n        <InputNumber\n          id=\"stake-input\"\n          v-model=\"stakeAmount\"\n          show-buttons\n          :min=\"0\"\n          :step=\"botStore.activeBot.stakeCurrency === 'USDT' ? 10 : 1\"\n          :max-fraction-digits=\"5\"\n          fluid\n        />\n      </div>\n\n      <div v-if=\"botStore.activeBot.botApiVersion > 2.16 && botStore.activeBot.shortAllowed\">\n        <label for=\"leverage-input\" class=\"block font-medium mb-1\"\n          >Leverage to apply [optional]</label\n        >\n        <InputNumber\n          id=\"leverage-input\"\n          v-model=\"leverage\"\n          show-buttons\n          :min=\"0\"\n          :step=\"1\"\n          :max-fraction-digits=\"1\"\n          class=\"w-full\"\n          @keydown.enter=\"handleEntry\"\n        />\n      </div>\n\n      <div>\n        <label class=\"block text-sm font-medium mb-1\">OrderType</label>\n        <SelectButton\n          v-model=\"ordertype\"\n          :options=\"orderTypeOptions\"\n          option-label=\"text\"\n          option-value=\"value\"\n          size=\"small\"\n          class=\"w-full\"\n        />\n      </div>\n\n      <div v-if=\"botStore.activeBot.botApiVersion > 1.16\">\n        <label for=\"enterTag-input\" class=\"block text-sm font-medium mb-1\"\n          >* Custom entry tag [optional]</label\n        >\n        <InputText id=\"enterTag-input\" v-model=\"enterTag\" class=\"w-full\" />\n      </div>\n    </form>\n\n    <template #footer>\n      <div class=\"flex justify-end gap-2\">\n        <Button severity=\"secondary\" size=\"small\" @click=\"model = false\"> Cancel </Button>\n        <Button severity=\"primary\" size=\"small\" @click=\"handleEntry\"> Enter Position </Button>\n      </div>\n    </template>\n  </Dialog>\n</template>\n", "var style=({dt:n})=>`\\n.p-slider {\\n    position: relative;\\n    background: ${n(\"slider.track.background\")};\\n    border-radius: ${n(\"slider.track.border.radius\")};\\n}\\n\\n.p-slider-handle {\\n    cursor: grab;\\n    touch-action: none;\\n    user-select: none;\\n    display: flex;\\n    justify-content: center;\\n    align-items: center;\\n    height: ${n(\"slider.handle.height\")};\\n    width: ${n(\"slider.handle.width\")};\\n    background: ${n(\"slider.handle.background\")};\\n    border-radius: ${n(\"slider.handle.border.radius\")};\\n    transition: background ${n(\"slider.transition.duration\")}, color ${n(\"slider.transition.duration\")}, border-color ${n(\"slider.transition.duration\")}, box-shadow ${n(\"slider.transition.duration\")}, outline-color ${n(\"slider.transition.duration\")};\\n    outline-color: transparent;\\n}\\n\\n.p-slider-handle::before {\\n    content: \"\";\\n    width: ${n(\"slider.handle.content.width\")};\\n    height: ${n(\"slider.handle.content.height\")};\\n    display: block;\\n    background: ${n(\"slider.handle.content.background\")};\\n    border-radius: ${n(\"slider.handle.content.border.radius\")};\\n    box-shadow: ${n(\"slider.handle.content.shadow\")};\\n    transition: background ${n(\"slider.transition.duration\")};\\n}\\n\\n.p-slider:not(.p-disabled) .p-slider-handle:hover {\\n    background: ${n(\"slider.handle.hover.background\")};\\n}\\n\\n.p-slider:not(.p-disabled) .p-slider-handle:hover::before {\\n    background: ${n(\"slider.handle.content.hover.background\")};\\n}\\n\\n.p-slider-handle:focus-visible {\\n    box-shadow: ${n(\"slider.handle.focus.ring.shadow\")};\\n    outline: ${n(\"slider.handle.focus.ring.width\")} ${n(\"slider.handle.focus.ring.style\")} ${n(\"slider.handle.focus.ring.color\")};\\n    outline-offset: ${n(\"slider.handle.focus.ring.offset\")};\\n}\\n\\n.p-slider-range {\\n    display: block;\\n    background: ${n(\"slider.range.background\")};\\n    border-radius: ${n(\"slider.track.border.radius\")};\\n}\\n\\n.p-slider.p-slider-horizontal {\\n    height: ${n(\"slider.track.size\")};\\n}\\n\\n.p-slider-horizontal .p-slider-range {\\n    inset-block-start: 0;\\n    inset-inline-start: 0;\\n    height: 100%;\\n}\\n\\n.p-slider-horizontal .p-slider-handle {\\n    inset-block-start: 50%;\\n    margin-block-start: calc(-1 * calc(${n(\"slider.handle.height\")} / 2));\\n    margin-inline-start: calc(-1 * calc(${n(\"slider.handle.width\")} / 2));\\n}\\n\\n.p-slider-vertical {\\n    min-height: 100px;\\n    width: ${n(\"slider.track.size\")};\\n}\\n\\n.p-slider-vertical .p-slider-handle {\\n    inset-inline-start: 50%;\\n    margin-inline-start: calc(-1 * calc(${n(\"slider.handle.width\")} / 2));\\n    margin-block-end: calc(-1 * calc(${n(\"slider.handle.height\")} / 2));\\n}\\n\\n.p-slider-vertical .p-slider-range {\\n    inset-block-end: 0;\\n    inset-inline-start: 0;\\n    width: 100%;\\n}\\n`;export{style};//# sourceMappingURL=index.mjs.map", "import { style } from '@primeuix/styles/slider';\nimport BaseStyle from '@primevue/core/base/style';\n\nvar inlineStyles = {\n  handle: {\n    position: 'absolute'\n  },\n  range: {\n    position: 'absolute'\n  }\n};\nvar classes = {\n  root: function root(_ref) {\n    var instance = _ref.instance,\n      props = _ref.props;\n    return ['p-slider p-component', {\n      'p-disabled': props.disabled,\n      'p-invalid': instance.$invalid,\n      'p-slider-horizontal': props.orientation === 'horizontal',\n      'p-slider-vertical': props.orientation === 'vertical'\n    }];\n  },\n  range: 'p-slider-range',\n  handle: 'p-slider-handle'\n};\nvar SliderStyle = BaseStyle.extend({\n  name: 'slider',\n  style: style,\n  classes: classes,\n  inlineStyles: inlineStyles\n});\n\nexport { SliderStyle as default };\n//# sourceMappingURL=index.mjs.map\n", "import { cn } from '@primeuix/utils';\nimport { getAttribute, isRTL, getWindowScrollLeft, getWindowScrollTop } from '@primeuix/utils/dom';\nimport BaseEditableHolder from '@primevue/core/baseeditableholder';\nimport SliderStyle from 'primevue/slider/style';\nimport { createElementBlock, openBlock, mergeProps, createElementVNode, createCommentVNode } from 'vue';\n\nvar script$1 = {\n  name: 'BaseSlider',\n  \"extends\": BaseEditableHolder,\n  props: {\n    min: {\n      type: Number,\n      \"default\": 0\n    },\n    max: {\n      type: Number,\n      \"default\": 100\n    },\n    orientation: {\n      type: String,\n      \"default\": 'horizontal'\n    },\n    step: {\n      type: Number,\n      \"default\": null\n    },\n    range: {\n      type: Boolean,\n      \"default\": false\n    },\n    tabindex: {\n      type: Number,\n      \"default\": 0\n    },\n    ariaLabelledby: {\n      type: String,\n      \"default\": null\n    },\n    ariaLabel: {\n      type: String,\n      \"default\": null\n    }\n  },\n  style: SliderStyle,\n  provide: function provide() {\n    return {\n      $pcSlider: this,\n      $parentInstance: this\n    };\n  }\n};\n\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: true, configurable: true, writable: true }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nvar script = {\n  name: 'Slider',\n  \"extends\": script$1,\n  inheritAttrs: false,\n  emits: ['change', 'slideend'],\n  dragging: false,\n  handleIndex: null,\n  initX: null,\n  initY: null,\n  barWidth: null,\n  barHeight: null,\n  dragListener: null,\n  dragEndListener: null,\n  beforeUnmount: function beforeUnmount() {\n    this.unbindDragListeners();\n  },\n  methods: {\n    updateDomData: function updateDomData() {\n      var rect = this.$el.getBoundingClientRect();\n      this.initX = rect.left + getWindowScrollLeft();\n      this.initY = rect.top + getWindowScrollTop();\n      this.barWidth = this.$el.offsetWidth;\n      this.barHeight = this.$el.offsetHeight;\n    },\n    setValue: function setValue(event) {\n      var handleValue;\n      var pageX = event.touches ? event.touches[0].pageX : event.pageX;\n      var pageY = event.touches ? event.touches[0].pageY : event.pageY;\n      if (this.orientation === 'horizontal') {\n        // @todo: Check this\n        if (isRTL(this.$el)) {\n          handleValue = (this.initX + this.barWidth - pageX) * 100 / this.barWidth;\n        } else {\n          handleValue = (pageX - this.initX) * 100 / this.barWidth;\n        }\n      } else {\n        handleValue = (this.initY + this.barHeight - pageY) * 100 / this.barHeight;\n      }\n      var newValue = (this.max - this.min) * (handleValue / 100) + this.min;\n      if (this.step) {\n        var oldValue = this.range ? this.value[this.handleIndex] : this.value;\n        var diff = newValue - oldValue;\n        if (diff < 0) newValue = oldValue + Math.ceil(newValue / this.step - oldValue / this.step) * this.step;else if (diff > 0) newValue = oldValue + Math.floor(newValue / this.step - oldValue / this.step) * this.step;\n      } else {\n        newValue = Math.floor(newValue);\n      }\n      this.updateModel(event, newValue);\n    },\n    updateModel: function updateModel(event, value) {\n      var newValue = Math.round(value * 100) / 100;\n      var modelValue;\n      if (this.range) {\n        modelValue = this.value ? _toConsumableArray(this.value) : [];\n        if (this.handleIndex == 0) {\n          if (newValue < this.min) newValue = this.min;else if (newValue >= this.max) newValue = this.max;\n          modelValue[0] = newValue;\n        } else {\n          if (newValue > this.max) newValue = this.max;else if (newValue <= this.min) newValue = this.min;\n          modelValue[1] = newValue;\n        }\n      } else {\n        if (newValue < this.min) newValue = this.min;else if (newValue > this.max) newValue = this.max;\n        modelValue = newValue;\n      }\n      this.writeValue(modelValue, event);\n      this.$emit('change', modelValue);\n    },\n    onDragStart: function onDragStart(event, index) {\n      if (this.disabled) {\n        return;\n      }\n      this.$el.setAttribute('data-p-sliding', true);\n      this.dragging = true;\n      this.updateDomData();\n      if (this.range && this.value[0] === this.max) {\n        this.handleIndex = 0;\n      } else {\n        this.handleIndex = index;\n      }\n      event.currentTarget.focus();\n    },\n    onDrag: function onDrag(event) {\n      if (this.dragging) {\n        this.setValue(event);\n      }\n    },\n    onDragEnd: function onDragEnd(event) {\n      if (this.dragging) {\n        this.dragging = false;\n        this.$el.setAttribute('data-p-sliding', false);\n        this.$emit('slideend', {\n          originalEvent: event,\n          value: this.value\n        });\n      }\n    },\n    onBarClick: function onBarClick(event) {\n      if (this.disabled) {\n        return;\n      }\n      if (getAttribute(event.target, 'data-pc-section') !== 'handle') {\n        this.updateDomData();\n        this.setValue(event);\n      }\n    },\n    onMouseDown: function onMouseDown(event, index) {\n      this.bindDragListeners();\n      this.onDragStart(event, index);\n    },\n    onKeyDown: function onKeyDown(event, index) {\n      this.handleIndex = index;\n      switch (event.code) {\n        case 'ArrowDown':\n        case 'ArrowLeft':\n          this.decrementValue(event, index);\n          event.preventDefault();\n          break;\n        case 'ArrowUp':\n        case 'ArrowRight':\n          this.incrementValue(event, index);\n          event.preventDefault();\n          break;\n        case 'PageDown':\n          this.decrementValue(event, index, true);\n          event.preventDefault();\n          break;\n        case 'PageUp':\n          this.incrementValue(event, index, true);\n          event.preventDefault();\n          break;\n        case 'Home':\n          this.updateModel(event, this.min);\n          event.preventDefault();\n          break;\n        case 'End':\n          this.updateModel(event, this.max);\n          event.preventDefault();\n          break;\n      }\n    },\n    onBlur: function onBlur(event, index) {\n      var _this$formField$onBlu, _this$formField;\n      (_this$formField$onBlu = (_this$formField = this.formField).onBlur) === null || _this$formField$onBlu === void 0 || _this$formField$onBlu.call(_this$formField, event);\n    },\n    decrementValue: function decrementValue(event, index) {\n      var pageKey = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n      var newValue;\n      if (this.range) {\n        if (this.step) newValue = this.value[index] - this.step;else newValue = this.value[index] - 1;\n      } else {\n        if (this.step) newValue = this.value - this.step;else if (!this.step && pageKey) newValue = this.value - 10;else newValue = this.value - 1;\n      }\n      this.updateModel(event, newValue);\n      event.preventDefault();\n    },\n    incrementValue: function incrementValue(event, index) {\n      var pageKey = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n      var newValue;\n      if (this.range) {\n        if (this.step) newValue = this.value[index] + this.step;else newValue = this.value[index] + 1;\n      } else {\n        if (this.step) newValue = this.value + this.step;else if (!this.step && pageKey) newValue = this.value + 10;else newValue = this.value + 1;\n      }\n      this.updateModel(event, newValue);\n      event.preventDefault();\n    },\n    bindDragListeners: function bindDragListeners() {\n      if (!this.dragListener) {\n        this.dragListener = this.onDrag.bind(this);\n        document.addEventListener('mousemove', this.dragListener);\n      }\n      if (!this.dragEndListener) {\n        this.dragEndListener = this.onDragEnd.bind(this);\n        document.addEventListener('mouseup', this.dragEndListener);\n      }\n    },\n    unbindDragListeners: function unbindDragListeners() {\n      if (this.dragListener) {\n        document.removeEventListener('mousemove', this.dragListener);\n        this.dragListener = null;\n      }\n      if (this.dragEndListener) {\n        document.removeEventListener('mouseup', this.dragEndListener);\n        this.dragEndListener = null;\n      }\n    },\n    rangeStyle: function rangeStyle() {\n      if (this.range) {\n        var rangeSliderWidth = this.rangeEndPosition > this.rangeStartPosition ? this.rangeEndPosition - this.rangeStartPosition : this.rangeStartPosition - this.rangeEndPosition;\n        var rangeSliderPosition = this.rangeEndPosition > this.rangeStartPosition ? this.rangeStartPosition : this.rangeEndPosition;\n        if (this.horizontal) {\n          return {\n            'inset-inline-start': rangeSliderPosition + '%',\n            width: rangeSliderWidth + '%'\n          };\n        } else {\n          return {\n            bottom: rangeSliderPosition + '%',\n            height: rangeSliderWidth + '%'\n          };\n        }\n      } else {\n        if (this.horizontal) {\n          return {\n            width: this.handlePosition + '%'\n          };\n        } else {\n          return {\n            height: this.handlePosition + '%'\n          };\n        }\n      }\n    },\n    handleStyle: function handleStyle() {\n      if (this.horizontal) {\n        return {\n          'inset-inline-start': this.handlePosition + '%'\n        };\n      } else {\n        return {\n          bottom: this.handlePosition + '%'\n        };\n      }\n    },\n    rangeStartHandleStyle: function rangeStartHandleStyle() {\n      if (this.horizontal) {\n        return {\n          'inset-inline-start': this.rangeStartPosition + '%'\n        };\n      } else {\n        return {\n          bottom: this.rangeStartPosition + '%'\n        };\n      }\n    },\n    rangeEndHandleStyle: function rangeEndHandleStyle() {\n      if (this.horizontal) {\n        return {\n          'inset-inline-start': this.rangeEndPosition + '%'\n        };\n      } else {\n        return {\n          bottom: this.rangeEndPosition + '%'\n        };\n      }\n    }\n  },\n  computed: {\n    value: function value() {\n      var _this$d_value3;\n      if (this.range) {\n        var _this$d_value$, _this$d_value, _this$d_value$2, _this$d_value2;\n        return [(_this$d_value$ = (_this$d_value = this.d_value) === null || _this$d_value === void 0 ? void 0 : _this$d_value[0]) !== null && _this$d_value$ !== void 0 ? _this$d_value$ : this.min, (_this$d_value$2 = (_this$d_value2 = this.d_value) === null || _this$d_value2 === void 0 ? void 0 : _this$d_value2[1]) !== null && _this$d_value$2 !== void 0 ? _this$d_value$2 : this.max];\n      }\n      return (_this$d_value3 = this.d_value) !== null && _this$d_value3 !== void 0 ? _this$d_value3 : this.min;\n    },\n    horizontal: function horizontal() {\n      return this.orientation === 'horizontal';\n    },\n    vertical: function vertical() {\n      return this.orientation === 'vertical';\n    },\n    handlePosition: function handlePosition() {\n      if (this.value < this.min) return 0;else if (this.value > this.max) return 100;else return (this.value - this.min) * 100 / (this.max - this.min);\n    },\n    rangeStartPosition: function rangeStartPosition() {\n      if (this.value && this.value[0] !== undefined) {\n        if (this.value[0] < this.min) return 0;else return (this.value[0] - this.min) * 100 / (this.max - this.min);\n      } else return 0;\n    },\n    rangeEndPosition: function rangeEndPosition() {\n      if (this.value && this.value.length === 2 && this.value[1] !== undefined) {\n        if (this.value[1] > this.max) return 100;else return (this.value[1] - this.min) * 100 / (this.max - this.min);\n      } else return 100;\n    },\n    dataP: function dataP() {\n      return cn(_defineProperty({}, this.orientation, this.orientation));\n    }\n  }\n};\n\nvar _hoisted_1 = [\"data-p\"];\nvar _hoisted_2 = [\"data-p\"];\nvar _hoisted_3 = [\"tabindex\", \"aria-valuemin\", \"aria-valuenow\", \"aria-valuemax\", \"aria-labelledby\", \"aria-label\", \"aria-orientation\", \"data-p\"];\nvar _hoisted_4 = [\"tabindex\", \"aria-valuemin\", \"aria-valuenow\", \"aria-valuemax\", \"aria-labelledby\", \"aria-label\", \"aria-orientation\", \"data-p\"];\nvar _hoisted_5 = [\"tabindex\", \"aria-valuemin\", \"aria-valuenow\", \"aria-valuemax\", \"aria-labelledby\", \"aria-label\", \"aria-orientation\", \"data-p\"];\nfunction render(_ctx, _cache, $props, $setup, $data, $options) {\n  return openBlock(), createElementBlock(\"div\", mergeProps({\n    \"class\": _ctx.cx('root'),\n    onClick: _cache[18] || (_cache[18] = function () {\n      return $options.onBarClick && $options.onBarClick.apply($options, arguments);\n    })\n  }, _ctx.ptmi('root'), {\n    \"data-p-sliding\": false,\n    \"data-p\": $options.dataP\n  }), [createElementVNode(\"span\", mergeProps({\n    \"class\": _ctx.cx('range'),\n    style: [_ctx.sx('range'), $options.rangeStyle()]\n  }, _ctx.ptm('range'), {\n    \"data-p\": $options.dataP\n  }), null, 16, _hoisted_2), !_ctx.range ? (openBlock(), createElementBlock(\"span\", mergeProps({\n    key: 0,\n    \"class\": _ctx.cx('handle'),\n    style: [_ctx.sx('handle'), $options.handleStyle()],\n    onTouchstartPassive: _cache[0] || (_cache[0] = function ($event) {\n      return $options.onDragStart($event);\n    }),\n    onTouchmovePassive: _cache[1] || (_cache[1] = function ($event) {\n      return $options.onDrag($event);\n    }),\n    onTouchend: _cache[2] || (_cache[2] = function ($event) {\n      return $options.onDragEnd($event);\n    }),\n    onMousedown: _cache[3] || (_cache[3] = function ($event) {\n      return $options.onMouseDown($event);\n    }),\n    onKeydown: _cache[4] || (_cache[4] = function ($event) {\n      return $options.onKeyDown($event);\n    }),\n    onBlur: _cache[5] || (_cache[5] = function ($event) {\n      return $options.onBlur($event);\n    }),\n    tabindex: _ctx.tabindex,\n    role: \"slider\",\n    \"aria-valuemin\": _ctx.min,\n    \"aria-valuenow\": _ctx.d_value,\n    \"aria-valuemax\": _ctx.max,\n    \"aria-labelledby\": _ctx.ariaLabelledby,\n    \"aria-label\": _ctx.ariaLabel,\n    \"aria-orientation\": _ctx.orientation\n  }, _ctx.ptm('handle'), {\n    \"data-p\": $options.dataP\n  }), null, 16, _hoisted_3)) : createCommentVNode(\"\", true), _ctx.range ? (openBlock(), createElementBlock(\"span\", mergeProps({\n    key: 1,\n    \"class\": _ctx.cx('handle'),\n    style: [_ctx.sx('handle'), $options.rangeStartHandleStyle()],\n    onTouchstartPassive: _cache[6] || (_cache[6] = function ($event) {\n      return $options.onDragStart($event, 0);\n    }),\n    onTouchmovePassive: _cache[7] || (_cache[7] = function ($event) {\n      return $options.onDrag($event);\n    }),\n    onTouchend: _cache[8] || (_cache[8] = function ($event) {\n      return $options.onDragEnd($event);\n    }),\n    onMousedown: _cache[9] || (_cache[9] = function ($event) {\n      return $options.onMouseDown($event, 0);\n    }),\n    onKeydown: _cache[10] || (_cache[10] = function ($event) {\n      return $options.onKeyDown($event, 0);\n    }),\n    onBlur: _cache[11] || (_cache[11] = function ($event) {\n      return $options.onBlur($event, 0);\n    }),\n    tabindex: _ctx.tabindex,\n    role: \"slider\",\n    \"aria-valuemin\": _ctx.min,\n    \"aria-valuenow\": _ctx.d_value ? _ctx.d_value[0] : null,\n    \"aria-valuemax\": _ctx.max,\n    \"aria-labelledby\": _ctx.ariaLabelledby,\n    \"aria-label\": _ctx.ariaLabel,\n    \"aria-orientation\": _ctx.orientation\n  }, _ctx.ptm('startHandler'), {\n    \"data-p\": $options.dataP\n  }), null, 16, _hoisted_4)) : createCommentVNode(\"\", true), _ctx.range ? (openBlock(), createElementBlock(\"span\", mergeProps({\n    key: 2,\n    \"class\": _ctx.cx('handle'),\n    style: [_ctx.sx('handle'), $options.rangeEndHandleStyle()],\n    onTouchstartPassive: _cache[12] || (_cache[12] = function ($event) {\n      return $options.onDragStart($event, 1);\n    }),\n    onTouchmovePassive: _cache[13] || (_cache[13] = function ($event) {\n      return $options.onDrag($event);\n    }),\n    onTouchend: _cache[14] || (_cache[14] = function ($event) {\n      return $options.onDragEnd($event);\n    }),\n    onMousedown: _cache[15] || (_cache[15] = function ($event) {\n      return $options.onMouseDown($event, 1);\n    }),\n    onKeydown: _cache[16] || (_cache[16] = function ($event) {\n      return $options.onKeyDown($event, 1);\n    }),\n    onBlur: _cache[17] || (_cache[17] = function ($event) {\n      return $options.onBlur($event, 1);\n    }),\n    tabindex: _ctx.tabindex,\n    role: \"slider\",\n    \"aria-valuemin\": _ctx.min,\n    \"aria-valuenow\": _ctx.d_value ? _ctx.d_value[1] : null,\n    \"aria-valuemax\": _ctx.max,\n    \"aria-labelledby\": _ctx.ariaLabelledby,\n    \"aria-label\": _ctx.ariaLabel,\n    \"aria-orientation\": _ctx.orientation\n  }, _ctx.ptm('endHandler'), {\n    \"data-p\": $options.dataP\n  }), null, 16, _hoisted_5)) : createCommentVNode(\"\", true)], 16, _hoisted_1);\n}\n\nscript.render = render;\n\nexport { script as default };\n//# sourceMappingURL=index.mjs.map\n", "<script setup lang=\"ts\">\nimport { useBotStore } from '@/stores/ftbotwrapper';\nimport type { ForceSellPayload, Trade } from '@/types';\nimport { ref, computed } from 'vue';\nimport { refDebounced } from '@vueuse/core';\n\nconst props = defineProps({\n  trade: {\n    type: Object as () => Trade,\n    required: true,\n  },\n  stakeCurrencyDecimals: {\n    type: Number,\n    required: true,\n  },\n});\n\nconst model = defineModel<boolean>();\n\nconst botStore = useBotStore();\n\nconst form = ref<HTMLFormElement>();\nconst amount = ref<number | undefined>(undefined);\nconst ordertype = ref('limit');\n\nconst checkFormValidity = () => {\n  const valid = form.value?.checkValidity();\n\n  return valid;\n};\n\nasync function handleSubmit() {\n  // Exit when the form isn't valid\n  if (!checkFormValidity()) {\n    return;\n  }\n  // call forceentry\n  const payload: ForceSellPayload = { tradeid: String(props.trade.trade_id) };\n\n  if (ordertype.value) {\n    payload.ordertype = ordertype.value;\n  }\n  if (amount.value) {\n    payload.amount = amount.value;\n  }\n  await nextTick();\n  botStore.activeBot.forceexit(payload);\n  model.value = false;\n}\n\nfunction resetForm() {\n  amount.value = props.trade.amount;\n  ordertype.value =\n    botStore.activeBot.botState?.order_types?.force_exit ||\n    botStore.activeBot.botState?.order_types?.exit ||\n    'limit';\n}\n\nfunction handleExit() {\n  // Trigger submit handler\n  handleSubmit();\n}\n\nconst amountDebounced = refDebounced(amount, 250, { maxWait: 500 });\n\nconst amountInBase = computed<string>(() => {\n  return amountDebounced.value && props.trade.current_rate\n    ? `~${formatPriceCurrency(amountDebounced.value * props.trade.current_rate, props.trade.quote_currency || '', props.stakeCurrencyDecimals)} (Estimated value) `\n    : '';\n});\nconst orderTypeOptions = [\n  { value: 'market', text: 'Market' },\n  { value: 'limit', text: 'Limit' },\n];\n</script>\n\n<template>\n  <Dialog\n    v-model:visible=\"model\"\n    :header=\"`Force exiting a trade`\"\n    modal\n    @show=\"resetForm\"\n    @hide=\"resetForm\"\n  >\n    <form ref=\"form\" class=\"space-y-4 md:min-w-[32rem]\" @submit.prevent=\"handleSubmit\">\n      <div class=\"mb-4\">\n        <p class=\"mb-2\">\n          <span>Exiting Trade #{{ trade.trade_id }} {{ trade.pair }}.</span>\n          <br />\n          <span>Currently owning {{ trade.amount }} {{ trade.base_currency }}</span>\n        </p>\n      </div>\n\n      <div>\n        <label for=\"stake-input\" class=\"block font-medium mb-1\">\n          Amount in {{ trade.base_currency }} [optional]\n          <span class=\"text-sm italic ml-1\">{{ amountInBase }}</span>\n        </label>\n        <div class=\"space-y-2\">\n          <InputNumber\n            id=\"stake-input\"\n            v-model=\"amount\"\n            :min=\"0\"\n            :max=\"trade.amount\"\n            :use-grouping=\"false\"\n            :step=\"0.000001\"\n            :max-fraction-digits=\"8\"\n            class=\"w-full\"\n            show-buttons\n          />\n          <Slider v-model=\"amount\" :min=\"0\" :max=\"trade.amount\" :step=\"0.000001\" class=\"w-full\" />\n        </div>\n      </div>\n\n      <div>\n        <label class=\"block font-medium mb-1\">*OrderType</label>\n        <SelectButton\n          v-model=\"ordertype\"\n          :options=\"orderTypeOptions\"\n          :allow-empty=\"false\"\n          option-label=\"text\"\n          option-value=\"value\"\n          size=\"small\"\n          class=\"w-full\"\n        />\n      </div>\n    </form>\n\n    <template #footer>\n      <div class=\"flex justify-end gap-2\">\n        <Button severity=\"secondary\" size=\"small\" @click=\"model = false\">Cancel</Button>\n        <Button severity=\"primary\" size=\"small\" @click=\"handleExit\">Exit Position</Button>\n      </div>\n    </template>\n  </Dialog>\n</template>\n", "<script setup lang=\"ts\">\nimport type { Trade } from '@/types';\nimport Popover from 'primevue/popover';\n\ndefineProps({\n  trade: { type: Object as () => Trade, required: true },\n  id: { type: Number, required: true },\n  botApiVersion: { type: Number, required: true },\n  enableForceEntry: { type: Boolean, default: false },\n});\nconst emit = defineEmits<{\n  forceExit: [trade: Trade, type?: string];\n  forceExitPartial: [trade: Trade];\n  cancelOpenOrder: [trade: Trade];\n  reloadTrade: [trade: Trade];\n  deleteTrade: [trade: Trade];\n  forceEntry: [trade: Trade];\n}>();\nconst popoverOpen = ref(false);\n\nfunction forceExitHandler(item: Trade, ordertype: string | undefined = undefined) {\n  popoverOpen.value = false;\n  emit('forceExit', item, ordertype);\n}\nfunction forceExitPartialHandler(item: Trade) {\n  popoverOpen.value = false;\n  emit('forceExitPartial', item);\n}\nfunction cancelOpenOrderHandler(item: Trade) {\n  popoverOpen.value = false;\n  emit('cancelOpenOrder', item);\n}\nfunction handleReloadTrade(item: Trade) {\n  popoverOpen.value = false;\n  emit('reloadTrade', item);\n}\nfunction handleDeleteTrade(item: Trade) {\n  popoverOpen.value = false;\n  emit('deleteTrade', item);\n}\nfunction handleForceEntry(item: Trade) {\n  popoverOpen.value = false;\n  emit('forceEntry', item);\n}\nconst popover = ref<InstanceType<typeof Popover> | null>(null);\n</script>\n\n<template>\n  <div>\n    <Button\n      :id=\"`btn-actions-${id}`\"\n      class=\"btn-xs\"\n      size=\"small\"\n      severity=\"secondary\"\n      title=\"Actions\"\n      @click=\"popover?.toggle\"\n    >\n      <i-mdi-gesture-tap />\n    </Button>\n    <Popover\n      ref=\"popover\"\n      :target=\"`btn-actions-${id}`\"\n      :title=\"`Actions for ${trade.pair}`\"\n      triggers=\"manual\"\n      placement=\"left\"\n    >\n      <TradeActions\n        :trade=\"trade\"\n        :bot-api-version=\"botApiVersion\"\n        :enable-force-entry=\"enableForceEntry\"\n        @force-exit=\"forceExitHandler\"\n        @force-exit-partial=\"forceExitPartialHandler\"\n        @delete-trade=\"handleDeleteTrade(trade)\"\n        @cancel-open-order=\"cancelOpenOrderHandler\"\n        @reload-trade=\"handleReloadTrade\"\n        @force-entry=\"handleForceEntry\"\n      />\n      <Button\n        class=\"mt-1 w-full text-start\"\n        size=\"small\"\n        severity=\"secondary\"\n        label=\"Close Actions menu\"\n        @click=\"popover?.hide\"\n      >\n        <template #icon><i-mdi-cancel class=\"me-1\" /></template>\n      </Button>\n    </Popover>\n  </div>\n</template>\n", "<script setup lang=\"ts\">\nimport type { MultiDeletePayload, MultiForcesellPayload, Trade } from '@/types';\nimport { useBotStore } from '@/stores/ftbotwrapper';\nimport { useRouter } from 'vue-router';\n\nenum ModalReasons {\n  removeTrade,\n  forceExit,\n  forceExitPartial,\n  cancelOpenOrder,\n}\n\nconst props = defineProps({\n  trades: { required: true, type: Array as () => Array<Trade> },\n  title: { default: 'Trades', type: String },\n  stakeCurrency: { required: false, default: '', type: String },\n  activeTrades: { default: false, type: Boolean },\n  showFilter: { default: false, type: Boolean },\n  multiBotView: { default: false, type: Boolean },\n  emptyText: { default: 'No Trades to show.', type: String },\n});\nconst botStore = useBotStore();\nconst router = useRouter();\nconst settingsStore = useSettingsStore();\nconst currentPage = ref(1);\nconst selectedItemIndex = ref();\nconst filterText = ref('');\nconst feTrade = ref<Trade>({} as Trade);\nconst perPage = props.activeTrades ? 200 : 15;\nconst tradesTable = ref<HTMLFormElement>();\nconst forceExitVisible = ref(false);\nconst removeTradeVisible = ref(false);\nconst confirmExitText = ref('');\nconst confirmExitValue = ref<ModalReasons | null>(null);\n\nconst increasePosition = ref({ visible: false, trade: {} as Trade });\nfunction formatPriceWithDecimals(price) {\n  return formatPrice(price, botStore.activeBot.stakeCurrencyDecimals);\n}\n\nconst tableFields = ref([\n  { field: 'trade_id', header: 'ID' },\n  { field: 'pair', header: 'Pair' },\n  { field: 'amount', header: 'Amount' },\n  props.activeTrades\n    ? { field: 'stake_amount', header: 'Stake amount' }\n    : { field: 'max_stake_amount', header: 'Total stake amount' },\n  {\n    field: 'open_rate',\n    header: 'Open rate',\n  },\n  {\n    field: props.activeTrades ? 'current_rate' : 'close_rate',\n    header: props.activeTrades ? 'Current rate' : 'Close rate',\n  },\n  {\n    field: 'profit',\n    header: props.activeTrades ? 'Current profit %' : 'Profit %',\n  },\n  { field: 'open_timestamp', header: 'Open date' },\n  ...(props.activeTrades\n    ? [{ field: 'actions', header: '' }]\n    : [\n        { field: 'close_timestamp', header: 'Close date' },\n        { field: 'exit_reason', header: 'Close Reason' },\n      ]),\n]);\n\nif (props.multiBotView) {\n  tableFields.value.unshift({ field: 'botName', header: 'Bot' });\n}\n\nconst feOrderType = ref<string | undefined>(undefined);\nfunction forceExitHandler(item: Trade, ordertype: string | undefined = undefined) {\n  feTrade.value = item;\n  confirmExitValue.value = ModalReasons.forceExit;\n  confirmExitText.value = `Really exit trade ${item.trade_id} (Pair ${item.pair}) using ${ordertype} Order?`;\n  feOrderType.value = ordertype;\n  if (settingsStore.confirmDialog === true) {\n    removeTradeVisible.value = true;\n  } else {\n    forceExitExecuter();\n  }\n}\n\nfunction forceExitExecuter() {\n  if (confirmExitValue.value === ModalReasons.removeTrade) {\n    const payload: MultiDeletePayload = {\n      tradeid: String(feTrade.value.trade_id),\n      botId: feTrade.value.botId,\n    };\n    botStore.deleteTradeMulti(payload).catch((error) => console.log(error.response));\n  }\n  if (confirmExitValue.value === ModalReasons.forceExit) {\n    const payload: MultiForcesellPayload = {\n      tradeid: String(feTrade.value.trade_id),\n      botId: feTrade.value.botId,\n    };\n    if (feOrderType.value) {\n      payload.ordertype = feOrderType.value;\n    }\n    botStore\n      .forceSellMulti(payload)\n      .then((xxx) => console.log(xxx))\n      .catch((error) => console.log(error.response));\n  }\n  if (confirmExitValue.value === ModalReasons.cancelOpenOrder) {\n    const payload: MultiDeletePayload = {\n      tradeid: String(feTrade.value.trade_id),\n      botId: feTrade.value.botId,\n    };\n    botStore.cancelOpenOrderMulti(payload);\n  }\n\n  feOrderType.value = undefined;\n  removeTradeVisible.value = false;\n}\n\nfunction removeTradeHandler(item: Trade) {\n  confirmExitText.value = `Really delete trade ${item.trade_id} (Pair ${item.pair})?`;\n  confirmExitValue.value = ModalReasons.removeTrade;\n  feTrade.value = item;\n  removeTradeVisible.value = true;\n}\n\nfunction forceExitPartialHandler(item: Trade) {\n  feTrade.value = item;\n  forceExitVisible.value = true;\n}\n\nfunction cancelOpenOrderHandler(item: Trade) {\n  confirmExitText.value = `Cancel open order for trade ${item.trade_id} (Pair ${item.pair})?`;\n  feTrade.value = item;\n  confirmExitValue.value = ModalReasons.cancelOpenOrder;\n  removeTradeVisible.value = true;\n}\n\nfunction reloadTradeHandler(item: Trade) {\n  botStore.reloadTradeMulti({ tradeid: String(item.trade_id), botId: item.botId });\n}\n\nfunction handleForceEntry(item: Trade) {\n  increasePosition.value.trade = item;\n  increasePosition.value.visible = true;\n}\n\nconst onRowClicked = ({ data: item, index }) => {\n  if (props.multiBotView && botStore.selectedBot !== item.botId) {\n    // Multibotview - on click switch to the bot trade view\n    botStore.selectBot(item.botId);\n  }\n  if (item && item.trade_id !== botStore.activeBot.detailTradeId) {\n    botStore.activeBot.setDetailTrade(item);\n    selectedItemIndex.value = index;\n    if (props.multiBotView) {\n      router.push({ name: 'Freqtrade Trading' });\n    }\n  } else {\n    botStore.activeBot.setDetailTrade(null);\n    selectedItemIndex.value = undefined;\n  }\n};\n\nwatch(\n  () => botStore.activeBot.detailTradeId,\n  (val) => {\n    const index = props.trades.findIndex((v) => v.trade_id === val);\n    // Unselect when another tradeTable is selected!\n    if (index < 0) {\n      selectedItemIndex.value = undefined;\n    }\n  },\n);\n</script>\n\n<template>\n  <div class=\"h-full overflow-auto w-full\">\n    <DataTable\n      ref=\"tradesTable\"\n      v-model:selection=\"selectedItemIndex\"\n      :value=\"\n        trades.filter(\n          (t) =>\n            t.pair.toLowerCase().includes(filterText.toLowerCase()) ||\n            t.exit_reason?.toLowerCase().includes(filterText.toLowerCase()) ||\n            t.enter_tag?.toLowerCase().includes(filterText.toLowerCase()),\n        )\n      \"\n      :rows=\"perPage\"\n      :paginator=\"!activeTrades\"\n      :first=\"(currentPage - 1) * perPage\"\n      selection-mode=\"single\"\n      data-key=\"botTradeId\"\n      class=\"text-center\"\n      size=\"small\"\n      :scrollable=\"true\"\n      scroll-height=\"flex\"\n      @row-click=\"onRowClicked\"\n    >\n      <template #empty>\n        {{ emptyText }}\n      </template>\n      <Column\n        v-for=\"column in tableFields\"\n        :key=\"column.field\"\n        :field=\"column.field\"\n        :header=\"column.header\"\n      >\n        <template #body=\"{ data, field, index }\">\n          <template v-if=\"field === 'trade_id'\">\n            {{ data.trade_id }}\n            {{\n              botStore.activeBot.botApiVersion > 2.0 && data.trading_mode !== 'spot'\n                ? (data.trade_id ? '| ' : '') + (data.is_short ? 'Short' : 'Long')\n                : ''\n            }}\n          </template>\n          <template v-else-if=\"field === 'pair'\">\n            {{ `${data.pair}${data.open_order_id || data.has_open_orders ? '*' : ''}` }}\n          </template>\n          <template v-else-if=\"field === 'actions'\">\n            <TradeActionsPopover\n              :id=\"index\"\n              :enable-force-entry=\"botStore.activeBot.botState.force_entry_enable\"\n              :trade=\"data as Trade\"\n              :bot-api-version=\"botStore.activeBot.botApiVersion\"\n              @delete-trade=\"removeTradeHandler(data as Trade)\"\n              @force-exit=\"forceExitHandler\"\n              @force-exit-partial=\"forceExitPartialHandler\"\n              @cancel-open-order=\"cancelOpenOrderHandler\"\n              @reload-trade=\"reloadTradeHandler\"\n              @force-entry=\"handleForceEntry\"\n            />\n          </template>\n          <template v-else-if=\"field === 'stake_amount'\">\n            {{ formatPriceWithDecimals(data.stake_amount) }}\n            {{ data.trading_mode !== 'spot' ? `(${data.leverage}x)` : '' }}\n          </template>\n          <template\n            v-else-if=\"field === 'open_rate' || field === 'current_rate' || field === 'close_rate'\"\n          >\n            {{ formatPrice(data[field]) }}\n          </template>\n          <template v-else-if=\"field === 'profit'\">\n            <TradeProfit :trade=\"data\" />\n          </template>\n          <template v-else-if=\"field === 'open_timestamp'\">\n            <DateTimeTZ :date=\"data.open_timestamp\" />\n          </template>\n          <template v-else-if=\"field === 'close_timestamp'\">\n            <DateTimeTZ :date=\"data.close_timestamp ?? 0\" />\n          </template>\n          <template v-else>\n            {{ data[field] }}\n          </template>\n        </template>\n      </Column>\n\n      <template v-if=\"showFilter\" #header>\n        <div class=\"flex justify-end gap-2 p-2\">\n          <InputText v-model=\"filterText\" placeholder=\"Filter\" class=\"w-64\" size=\"small\" />\n        </div>\n      </template>\n    </DataTable>\n\n    <ForceExitForm\n      v-if=\"activeTrades\"\n      v-model=\"forceExitVisible\"\n      :trade=\"feTrade\"\n      :stake-currency-decimals=\"botStore.activeBot.botState.stake_currency_decimals ?? 3\"\n    />\n    <ForceEntryForm\n      v-model=\"increasePosition.visible\"\n      :pair=\"increasePosition.trade?.pair\"\n      position-increase\n    />\n\n    <Dialog v-model:visible=\"removeTradeVisible\" :modal=\"true\" header=\"Exit trade\">\n      <p>{{ confirmExitText }}</p>\n      <template #footer>\n        <Button label=\"Cancel\" @click=\"removeTradeVisible = false\" />\n        <Button label=\"Confirm\" severity=\"danger\" @click=\"forceExitExecuter\" />\n      </template>\n    </Dialog>\n  </div>\n</template>\n"], "names": ["props", "__props", "model", "_useModel", "botStore", "useBotStore", "form", "ref", "<PERSON><PERSON><PERSON>", "price", "stakeAmount", "leverage", "ordertype", "orderSide", "OrderSides", "enterTag", "orderTypeOptions", "orderSideOptions", "checkFormValidity", "_a", "handleSubmit", "payload", "nextTick", "resetForm", "_b", "_d", "_c", "_f", "_e", "_h", "_g", "handleEntry", "style", "n", "inlineStyles", "classes", "_ref", "instance", "SliderStyle", "BaseStyle", "script$1", "BaseEditableHolder", "_typeof", "o", "_defineProperty", "r", "t", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "i", "_toPrimitive", "e", "_toConsumableArray", "_arrayWithoutHoles", "_iterableToArray", "_unsupportedIterableToArray", "_nonIterableSpread", "a", "_arrayLikeToArray", "script", "rect", "getWindowScrollLeft", "getWindowScrollTop", "event", "handleValue", "pageX", "pageY", "isRTL", "newValue", "oldValue", "diff", "value", "modelValue", "index", "getAttribute", "_this$formField$onBlu", "_this$formField", "page<PERSON><PERSON>", "rangeSliderWidth", "rangeSliderPosition", "_this$d_value3", "_this$d_value$", "_this$d_value", "_this$d_value$2", "_this$d_value2", "cn", "_hoisted_1", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "render", "_ctx", "_cache", "$props", "$setup", "$data", "$options", "openBlock", "createElementBlock", "mergeProps", "createElementVNode", "createCommentVNode", "$event", "amount", "handleExit", "amountDebounced", "refDebounced", "amountInBase", "computed", "formatPriceCurrency", "emit", "__emit", "popoverOpen", "forceExitHandler", "item", "forceExitPartialHandler", "cancelOpenOrderHandler", "handleReloadTrade", "handleDeleteTrade", "handleForceEntry", "popover", "router", "useRouter", "settingsStore", "useSettingsStore", "currentPage", "selectedItemIndex", "filterText", "feTrade", "perPage", "tradesTable", "forceExitVisible", "removeTradeVisible", "confirmExitText", "confirmExitValue", "increasePosition", "formatPriceWithDecimals", "formatPrice", "tableFields", "feOrderType", "forceExitExecuter", "error", "xxx", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reloadTradeHandler", "onRowClicked", "watch", "val", "v"], "mappings": "i8BAKA,MAAMA,EAAQC,EAIRC,EAAQC,GAAoBF,EAAA,YAAC,EAC7BG,EAAWC,EAAY,EAEvBC,EAAOC,EAAqB,EAC5BC,EAAeD,EAAI,EAAE,EACrBE,EAAQF,EAAwB,MAAS,EACzCG,EAAcH,EAAwB,MAAS,EAC/CI,EAAWJ,EAAwB,MAAS,EAE5CK,EAAYL,EAAI,EAAE,EAClBM,EAAYN,EAAgBO,GAAW,IAAI,EAC3CC,EAAWR,EAAI,aAAa,EAE5BS,EAAmB,CACvB,CAAE,MAAO,SAAU,KAAM,QAAS,EAClC,CAAE,MAAO,QAAS,KAAM,OAAQ,CAClC,EACMC,EAAmB,CACvB,CAAE,MAAO,OAAQ,KAAM,MAAO,EAC9B,CAAE,MAAO,QAAS,KAAM,OAAQ,CAClC,EAEMC,EAAoB,IAAM,OAGvB,OAFOC,EAAAb,EAAK,QAAL,YAAAa,EAAY,eAG5B,EAEMC,EAAe,SAAY,CAE3B,GAAA,CAACF,IACH,OAIF,MAAMG,EAA6B,CAAE,KAAMb,EAAa,KAAM,EAC1DC,EAAM,QACAY,EAAA,MAAQ,OAAOZ,EAAM,KAAK,GAEhCG,EAAU,QACZS,EAAQ,UAAYT,EAAU,OAE5BF,EAAY,QACdW,EAAQ,YAAcX,EAAY,OAEhCN,EAAS,UAAU,eAAiB,MAAQA,EAAS,UAAU,eACjEiB,EAAQ,KAAOR,EAAU,OAEvBT,EAAS,UAAU,eAAiB,MAAQW,EAAS,QACvDM,EAAQ,UAAYN,EAAS,OAG3BJ,EAAS,QACXU,EAAQ,SAAWV,EAAS,OAErBP,EAAA,UAAU,WAAWiB,CAAO,EACrC,MAAMC,GAAS,EACfpB,EAAM,MAAQ,EAChB,EACMqB,EAAY,IAAM,qBACtB,QAAQ,IAAI,WAAW,EACvBf,EAAa,MAAQR,EAAM,KAC3BS,EAAM,MAAQ,OACdC,EAAY,MAAQ,OACVE,EAAA,QACRY,GAAAL,EAAAf,EAAS,UAAU,WAAnB,YAAAe,EAA6B,cAA7B,YAAAK,EAA0C,aAC1CC,GAAAC,EAAAtB,EAAS,UAAU,WAAnB,YAAAsB,EAA6B,cAA7B,YAAAD,EAA0C,gBAC1CE,GAAAC,EAAAxB,EAAS,UAAU,WAAnB,YAAAwB,EAA6B,cAA7B,YAAAD,EAA0C,QAC1CE,GAAAC,EAAA1B,EAAS,UAAU,WAAnB,YAAA0B,EAA6B,cAA7B,YAAAD,EAA0C,QAC1C,OACJ,EAEME,EAAc,IAAM,CAEXX,EAAA,CACf,whGCpFA,IAAIY,GAAM,CAAC,CAAC,GAAGC,CAAC,IAAI;AAAA;AAAA;AAAA,kBAA2DA,EAAE,yBAAyB,CAAC;AAAA,qBAAyBA,EAAE,4BAA4B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAA2LA,EAAE,sBAAsB,CAAC;AAAA,aAAiBA,EAAE,qBAAqB,CAAC;AAAA,kBAAsBA,EAAE,0BAA0B,CAAC;AAAA,qBAAyBA,EAAE,6BAA6B,CAAC;AAAA,6BAAiCA,EAAE,4BAA4B,CAAC,WAAWA,EAAE,4BAA4B,CAAC,kBAAkBA,EAAE,4BAA4B,CAAC,gBAAgBA,EAAE,4BAA4B,CAAC,mBAAmBA,EAAE,4BAA4B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAAqGA,EAAE,6BAA6B,CAAC;AAAA,cAAkBA,EAAE,8BAA8B,CAAC;AAAA;AAAA,kBAA2CA,EAAE,kCAAkC,CAAC;AAAA,qBAAyBA,EAAE,qCAAqC,CAAC;AAAA,kBAAsBA,EAAE,8BAA8B,CAAC;AAAA,6BAAiCA,EAAE,4BAA4B,CAAC;AAAA;AAAA;AAAA;AAAA,kBAAgFA,EAAE,gCAAgC,CAAC;AAAA;AAAA;AAAA;AAAA,kBAAwFA,EAAE,wCAAwC,CAAC;AAAA;AAAA;AAAA;AAAA,kBAA6DA,EAAE,iCAAiC,CAAC;AAAA,eAAmBA,EAAE,gCAAgC,CAAC,IAAIA,EAAE,gCAAgC,CAAC,IAAIA,EAAE,gCAAgC,CAAC;AAAA,sBAA0BA,EAAE,iCAAiC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAmEA,EAAE,yBAAyB,CAAC;AAAA,qBAAyBA,EAAE,4BAA4B,CAAC;AAAA;AAAA;AAAA;AAAA,cAAwDA,EAAE,mBAAmB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,yCAA+OA,EAAE,sBAAsB,CAAC;AAAA,0CAAoDA,EAAE,qBAAqB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,aAA0EA,EAAE,mBAAmB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,0CAAwHA,EAAE,qBAAqB,CAAC;AAAA,uCAAiDA,EAAE,sBAAsB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,ECGhlFC,GAAe,CACjB,OAAQ,CACN,SAAU,UACX,EACD,MAAO,CACL,SAAU,UACd,CACA,EACIC,GAAU,CACZ,KAAM,SAAcC,EAAM,CACxB,IAAIC,EAAWD,EAAK,SAClBpC,EAAQoC,EAAK,MACf,MAAO,CAAC,uBAAwB,CAC9B,aAAcpC,EAAM,SACpB,YAAaqC,EAAS,SACtB,sBAAuBrC,EAAM,cAAgB,aAC7C,oBAAqBA,EAAM,cAAgB,UACjD,CAAK,CACF,EACD,MAAO,iBACP,OAAQ,iBACV,EACIsC,GAAcC,GAAU,OAAO,CACjC,KAAM,SACN,MAAOP,GACP,QAASG,GACT,aAAcD,EAChB,CAAC,ECxBGM,GAAW,CACb,KAAM,aACN,QAAWC,GACX,MAAO,CACL,IAAK,CACH,KAAM,OACN,QAAW,CACZ,EACD,IAAK,CACH,KAAM,OACN,QAAW,GACZ,EACD,YAAa,CACX,KAAM,OACN,QAAW,YACZ,EACD,KAAM,CACJ,KAAM,OACN,QAAW,IACZ,EACD,MAAO,CACL,KAAM,QACN,QAAW,EACZ,EACD,SAAU,CACR,KAAM,OACN,QAAW,CACZ,EACD,eAAgB,CACd,KAAM,OACN,QAAW,IACZ,EACD,UAAW,CACT,KAAM,OACN,QAAW,IACjB,CACG,EACD,MAAOH,GACP,QAAS,UAAmB,CAC1B,MAAO,CACL,UAAW,KACX,gBAAiB,IAClB,CACL,CACA,EAEA,SAASI,EAAQC,EAAG,CAAE,0BAA2B,OAAOD,EAAwB,OAAO,QAArB,YAA2C,OAAO,OAAO,UAA1B,SAAqC,SAAUC,EAAG,CAAE,OAAO,OAAOA,GAAO,SAAUA,EAAG,CAAE,OAAOA,GAAmB,OAAO,QAArB,YAA+BA,EAAE,cAAgB,QAAUA,IAAM,OAAO,UAAY,SAAW,OAAOA,CAAE,EAAID,EAAQC,CAAC,CAAE,CAC5T,SAASC,GAAgB,EAAGC,EAAGC,EAAG,CAAE,OAAQD,EAAIE,GAAeF,CAAC,KAAM,EAAI,OAAO,eAAe,EAAGA,EAAG,CAAE,MAAOC,EAAG,WAAY,GAAM,aAAc,GAAM,SAAU,EAAI,CAAE,EAAI,EAAED,CAAC,EAAIC,EAAG,CAAE,CACxL,SAASC,GAAeD,EAAG,CAAE,IAAIE,EAAIC,GAAaH,EAAG,QAAQ,EAAG,OAAmBJ,EAAQM,CAAC,GAArB,SAAyBA,EAAIA,EAAI,EAAG,CAC3G,SAASC,GAAaH,EAAGD,EAAG,CAAE,GAAgBH,EAAQI,CAAC,GAArB,UAA0B,CAACA,EAAG,OAAOA,EAAG,IAAII,EAAIJ,EAAE,OAAO,WAAW,EAAG,GAAeI,IAAX,OAAc,CAAE,IAAIF,EAAIE,EAAE,KAAKJ,EAAGD,CAAC,EAAG,GAAgBH,EAAQM,CAAC,GAArB,SAAwB,OAAOA,EAAG,MAAM,IAAI,UAAU,8CAA8C,CAAE,CAAG,OAAqBH,IAAb,SAAiB,OAAS,QAAQC,CAAC,CAAE,CAC7S,SAASK,GAAmBN,EAAG,CAAE,OAAOO,GAAmBP,CAAC,GAAKQ,GAAiBR,CAAC,GAAKS,GAA4BT,CAAC,GAAKU,GAAkB,CAAG,CAC/I,SAASA,IAAqB,CAAE,MAAM,IAAI,UAAU;AAAA,mFAAsI,CAAE,CAC5L,SAASD,GAA4BT,EAAGW,EAAG,CAAE,GAAIX,EAAG,CAAE,GAAgB,OAAOA,GAAnB,SAAsB,OAAOY,EAAkBZ,EAAGW,CAAC,EAAG,IAAIV,EAAI,GAAG,SAAS,KAAKD,CAAC,EAAE,MAAM,EAAG,EAAE,EAAG,OAAoBC,IAAb,UAAkBD,EAAE,cAAgBC,EAAID,EAAE,YAAY,MAAiBC,IAAV,OAAyBA,IAAV,MAAc,MAAM,KAAKD,CAAC,EAAoBC,IAAhB,aAAqB,2CAA2C,KAAKA,CAAC,EAAIW,EAAkBZ,EAAGW,CAAC,EAAI,MAAS,CAAA,CACxX,SAASH,GAAiBR,EAAG,CAAE,GAAmB,OAAO,OAAtB,KAAwCA,EAAE,OAAO,QAAQ,GAAzB,MAAsCA,EAAE,YAAY,GAAtB,KAAyB,OAAO,MAAM,KAAKA,CAAC,CAAE,CAC/I,SAASO,GAAmBP,EAAG,CAAE,GAAI,MAAM,QAAQA,CAAC,EAAG,OAAOY,EAAkBZ,CAAC,CAAE,CACnF,SAASY,EAAkBZ,EAAGW,EAAG,EAAWA,GAAR,MAAaA,EAAIX,EAAE,UAAYW,EAAIX,EAAE,QAAS,QAASK,EAAI,EAAGjB,EAAI,MAAMuB,CAAC,EAAGN,EAAIM,EAAGN,IAAKjB,EAAEiB,CAAC,EAAIL,EAAEK,CAAC,EAAG,OAAOjB,CAAE,CAClJ,IAAIyB,GAAS,CACX,KAAM,SACN,QAAWlB,GACX,aAAc,GACd,MAAO,CAAC,SAAU,UAAU,EAC5B,SAAU,GACV,YAAa,KACb,MAAO,KACP,MAAO,KACP,SAAU,KACV,UAAW,KACX,aAAc,KACd,gBAAiB,KACjB,cAAe,UAAyB,CACtC,KAAK,oBAAqB,CAC3B,EACD,QAAS,CACP,cAAe,UAAyB,CACtC,IAAImB,EAAO,KAAK,IAAI,sBAAuB,EAC3C,KAAK,MAAQA,EAAK,KAAOC,GAAqB,EAC9C,KAAK,MAAQD,EAAK,IAAME,GAAoB,EAC5C,KAAK,SAAW,KAAK,IAAI,YACzB,KAAK,UAAY,KAAK,IAAI,YAC3B,EACD,SAAU,SAAkBC,EAAO,CACjC,IAAIC,EACAC,EAAQF,EAAM,QAAUA,EAAM,QAAQ,CAAC,EAAE,MAAQA,EAAM,MACvDG,EAAQH,EAAM,QAAUA,EAAM,QAAQ,CAAC,EAAE,MAAQA,EAAM,MACvD,KAAK,cAAgB,aAEnBI,GAAM,KAAK,GAAG,EAChBH,GAAe,KAAK,MAAQ,KAAK,SAAWC,GAAS,IAAM,KAAK,SAEhED,GAAeC,EAAQ,KAAK,OAAS,IAAM,KAAK,SAGlDD,GAAe,KAAK,MAAQ,KAAK,UAAYE,GAAS,IAAM,KAAK,UAEnE,IAAIE,GAAY,KAAK,IAAM,KAAK,MAAQJ,EAAc,KAAO,KAAK,IAClE,GAAI,KAAK,KAAM,CACb,IAAIK,EAAW,KAAK,MAAQ,KAAK,MAAM,KAAK,WAAW,EAAI,KAAK,MAC5DC,EAAOF,EAAWC,EAClBC,EAAO,EAAGF,EAAWC,EAAW,KAAK,KAAKD,EAAW,KAAK,KAAOC,EAAW,KAAK,IAAI,EAAI,KAAK,KAAcC,EAAO,IAAGF,EAAWC,EAAW,KAAK,MAAMD,EAAW,KAAK,KAAOC,EAAW,KAAK,IAAI,EAAI,KAAK,KACvN,MACQD,EAAW,KAAK,MAAMA,CAAQ,EAEhC,KAAK,YAAYL,EAAOK,CAAQ,CACjC,EACD,YAAa,SAAqBL,EAAOQ,EAAO,CAC9C,IAAIH,EAAW,KAAK,MAAMG,EAAQ,GAAG,EAAI,IACrCC,EACA,KAAK,OACPA,EAAa,KAAK,MAAQpB,GAAmB,KAAK,KAAK,EAAI,CAAE,EACzD,KAAK,aAAe,GAClBgB,EAAW,KAAK,IAAKA,EAAW,KAAK,IAAaA,GAAY,KAAK,MAAKA,EAAW,KAAK,KAC5FI,EAAW,CAAC,EAAIJ,IAEZA,EAAW,KAAK,IAAKA,EAAW,KAAK,IAAaA,GAAY,KAAK,MAAKA,EAAW,KAAK,KAC5FI,EAAW,CAAC,EAAIJ,KAGdA,EAAW,KAAK,IAAKA,EAAW,KAAK,IAAaA,EAAW,KAAK,MAAKA,EAAW,KAAK,KAC3FI,EAAaJ,GAEf,KAAK,WAAWI,EAAYT,CAAK,EACjC,KAAK,MAAM,SAAUS,CAAU,CAChC,EACD,YAAa,SAAqBT,EAAOU,EAAO,CAC1C,KAAK,WAGT,KAAK,IAAI,aAAa,iBAAkB,EAAI,EAC5C,KAAK,SAAW,GAChB,KAAK,cAAe,EAChB,KAAK,OAAS,KAAK,MAAM,CAAC,IAAM,KAAK,IACvC,KAAK,YAAc,EAEnB,KAAK,YAAcA,EAErBV,EAAM,cAAc,MAAO,EAC5B,EACD,OAAQ,SAAgBA,EAAO,CACzB,KAAK,UACP,KAAK,SAASA,CAAK,CAEtB,EACD,UAAW,SAAmBA,EAAO,CAC/B,KAAK,WACP,KAAK,SAAW,GAChB,KAAK,IAAI,aAAa,iBAAkB,EAAK,EAC7C,KAAK,MAAM,WAAY,CACrB,cAAeA,EACf,MAAO,KAAK,KACtB,CAAS,EAEJ,EACD,WAAY,SAAoBA,EAAO,CACjC,KAAK,UAGLW,GAAaX,EAAM,OAAQ,iBAAiB,IAAM,WACpD,KAAK,cAAe,EACpB,KAAK,SAASA,CAAK,EAEtB,EACD,YAAa,SAAqBA,EAAOU,EAAO,CAC9C,KAAK,kBAAmB,EACxB,KAAK,YAAYV,EAAOU,CAAK,CAC9B,EACD,UAAW,SAAmBV,EAAOU,EAAO,CAE1C,OADA,KAAK,YAAcA,EACXV,EAAM,KAAI,CAChB,IAAK,YACL,IAAK,YACH,KAAK,eAAeA,EAAOU,CAAK,EAChCV,EAAM,eAAgB,EACtB,MACF,IAAK,UACL,IAAK,aACH,KAAK,eAAeA,EAAOU,CAAK,EAChCV,EAAM,eAAgB,EACtB,MACF,IAAK,WACH,KAAK,eAAeA,EAAOU,EAAO,EAAI,EACtCV,EAAM,eAAgB,EACtB,MACF,IAAK,SACH,KAAK,eAAeA,EAAOU,EAAO,EAAI,EACtCV,EAAM,eAAgB,EACtB,MACF,IAAK,OACH,KAAK,YAAYA,EAAO,KAAK,GAAG,EAChCA,EAAM,eAAgB,EACtB,MACF,IAAK,MACH,KAAK,YAAYA,EAAO,KAAK,GAAG,EAChCA,EAAM,eAAgB,EACtB,KACV,CACK,EACD,OAAQ,SAAgBA,EAAOU,EAAO,CACpC,IAAIE,EAAuBC,GAC1BD,GAAyBC,EAAkB,KAAK,WAAW,UAAY,MAAQD,IAA0B,QAAUA,EAAsB,KAAKC,EAAiBb,CAAK,CACtK,EACD,eAAgB,SAAwBA,EAAOU,EAAO,CACpD,IAAII,EAAU,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,GAC9ET,EACA,KAAK,MACH,KAAK,KAAMA,EAAW,KAAK,MAAMK,CAAK,EAAI,KAAK,KAAUL,EAAW,KAAK,MAAMK,CAAK,EAAI,EAExF,KAAK,KAAML,EAAW,KAAK,MAAQ,KAAK,KAAc,CAAC,KAAK,MAAQS,EAAST,EAAW,KAAK,MAAQ,GAAQA,EAAW,KAAK,MAAQ,EAE3I,KAAK,YAAYL,EAAOK,CAAQ,EAChCL,EAAM,eAAgB,CACvB,EACD,eAAgB,SAAwBA,EAAOU,EAAO,CACpD,IAAII,EAAU,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,GAC9ET,EACA,KAAK,MACH,KAAK,KAAMA,EAAW,KAAK,MAAMK,CAAK,EAAI,KAAK,KAAUL,EAAW,KAAK,MAAMK,CAAK,EAAI,EAExF,KAAK,KAAML,EAAW,KAAK,MAAQ,KAAK,KAAc,CAAC,KAAK,MAAQS,EAAST,EAAW,KAAK,MAAQ,GAAQA,EAAW,KAAK,MAAQ,EAE3I,KAAK,YAAYL,EAAOK,CAAQ,EAChCL,EAAM,eAAgB,CACvB,EACD,kBAAmB,UAA6B,CACzC,KAAK,eACR,KAAK,aAAe,KAAK,OAAO,KAAK,IAAI,EACzC,SAAS,iBAAiB,YAAa,KAAK,YAAY,GAErD,KAAK,kBACR,KAAK,gBAAkB,KAAK,UAAU,KAAK,IAAI,EAC/C,SAAS,iBAAiB,UAAW,KAAK,eAAe,EAE5D,EACD,oBAAqB,UAA+B,CAC9C,KAAK,eACP,SAAS,oBAAoB,YAAa,KAAK,YAAY,EAC3D,KAAK,aAAe,MAElB,KAAK,kBACP,SAAS,oBAAoB,UAAW,KAAK,eAAe,EAC5D,KAAK,gBAAkB,KAE1B,EACD,WAAY,UAAsB,CAChC,GAAI,KAAK,MAAO,CACd,IAAIe,EAAmB,KAAK,iBAAmB,KAAK,mBAAqB,KAAK,iBAAmB,KAAK,mBAAqB,KAAK,mBAAqB,KAAK,iBACtJC,EAAsB,KAAK,iBAAmB,KAAK,mBAAqB,KAAK,mBAAqB,KAAK,iBAC3G,OAAI,KAAK,WACA,CACL,qBAAsBA,EAAsB,IAC5C,MAAOD,EAAmB,GAC3B,EAEM,CACL,OAAQC,EAAsB,IAC9B,OAAQD,EAAmB,GAC5B,CAEX,KACQ,QAAI,KAAK,WACA,CACL,MAAO,KAAK,eAAiB,GAC9B,EAEM,CACL,OAAQ,KAAK,eAAiB,GAC/B,CAGN,EACD,YAAa,UAAuB,CAClC,OAAI,KAAK,WACA,CACL,qBAAsB,KAAK,eAAiB,GAC7C,EAEM,CACL,OAAQ,KAAK,eAAiB,GAC/B,CAEJ,EACD,sBAAuB,UAAiC,CACtD,OAAI,KAAK,WACA,CACL,qBAAsB,KAAK,mBAAqB,GACjD,EAEM,CACL,OAAQ,KAAK,mBAAqB,GACnC,CAEJ,EACD,oBAAqB,UAA+B,CAClD,OAAI,KAAK,WACA,CACL,qBAAsB,KAAK,iBAAmB,GAC/C,EAEM,CACL,OAAQ,KAAK,iBAAmB,GACjC,CAET,CACG,EACD,SAAU,CACR,MAAO,UAAiB,CACtB,IAAIE,EACJ,GAAI,KAAK,MAAO,CACd,IAAIC,EAAgBC,EAAeC,EAAiBC,EACpD,MAAO,EAAEH,GAAkBC,EAAgB,KAAK,WAAa,MAAQA,IAAkB,OAAS,OAASA,EAAc,CAAC,KAAO,MAAQD,IAAmB,OAASA,EAAiB,KAAK,KAAME,GAAmBC,EAAiB,KAAK,WAAa,MAAQA,IAAmB,OAAS,OAASA,EAAe,CAAC,KAAO,MAAQD,IAAoB,OAASA,EAAkB,KAAK,GAAG,CAChY,CACM,OAAQH,EAAiB,KAAK,WAAa,MAAQA,IAAmB,OAASA,EAAiB,KAAK,GACtG,EACD,WAAY,UAAsB,CAChC,OAAO,KAAK,cAAgB,YAC7B,EACD,SAAU,UAAoB,CAC5B,OAAO,KAAK,cAAgB,UAC7B,EACD,eAAgB,UAA0B,CACxC,OAAI,KAAK,MAAQ,KAAK,IAAY,EAAW,KAAK,MAAQ,KAAK,IAAY,KAAiB,KAAK,MAAQ,KAAK,KAAO,KAAO,KAAK,IAAM,KAAK,IAC7I,EACD,mBAAoB,UAA8B,CAChD,OAAI,KAAK,OAAS,KAAK,MAAM,CAAC,IAAM,OAC9B,KAAK,MAAM,CAAC,EAAI,KAAK,IAAY,GAAe,KAAK,MAAM,CAAC,EAAI,KAAK,KAAO,KAAO,KAAK,IAAM,KAAK,KAC3F,CACf,EACD,iBAAkB,UAA4B,CAC5C,OAAI,KAAK,OAAS,KAAK,MAAM,SAAW,GAAK,KAAK,MAAM,CAAC,IAAM,OACzD,KAAK,MAAM,CAAC,EAAI,KAAK,IAAY,KAAiB,KAAK,MAAM,CAAC,EAAI,KAAK,KAAO,KAAO,KAAK,IAAM,KAAK,KAC7F,GACf,EACD,MAAO,UAAiB,CACtB,OAAOK,GAAGxC,GAAgB,CAAE,EAAE,KAAK,YAAa,KAAK,WAAW,CAAC,CACvE,CACA,CACA,EAEIyC,GAAa,CAAC,QAAQ,EACtBC,GAAa,CAAC,QAAQ,EACtBC,GAAa,CAAC,WAAY,gBAAiB,gBAAiB,gBAAiB,kBAAmB,aAAc,mBAAoB,QAAQ,EAC1IC,GAAa,CAAC,WAAY,gBAAiB,gBAAiB,gBAAiB,kBAAmB,aAAc,mBAAoB,QAAQ,EAC1IC,GAAa,CAAC,WAAY,gBAAiB,gBAAiB,gBAAiB,kBAAmB,aAAc,mBAAoB,QAAQ,EAC9I,SAASC,GAAOC,EAAMC,EAAQC,EAAQC,EAAQC,EAAOC,EAAU,CAC7D,OAAOC,EAAW,EAAEC,EAAmB,MAAOC,EAAW,CACvD,MAASR,EAAK,GAAG,MAAM,EACvB,QAASC,EAAO,EAAE,IAAMA,EAAO,EAAE,EAAI,UAAY,CAC/C,OAAOI,EAAS,YAAcA,EAAS,WAAW,MAAMA,EAAU,SAAS,CAC5E,EACL,EAAKL,EAAK,KAAK,MAAM,EAAG,CACpB,iBAAkB,GAClB,SAAUK,EAAS,KACpB,CAAA,EAAG,CAACI,EAAmB,OAAQD,EAAW,CACzC,MAASR,EAAK,GAAG,OAAO,EACxB,MAAO,CAACA,EAAK,GAAG,OAAO,EAAGK,EAAS,WAAY,CAAA,CACnD,EAAKL,EAAK,IAAI,OAAO,EAAG,CACpB,SAAUK,EAAS,KACpB,CAAA,EAAG,KAAM,GAAIV,EAAU,EAAIK,EAAK,MAgCJU,EAAmB,GAAI,EAAI,GAhCdJ,EAAW,EAAEC,EAAmB,OAAQC,EAAW,CAC3F,IAAK,EACL,MAASR,EAAK,GAAG,QAAQ,EACzB,MAAO,CAACA,EAAK,GAAG,QAAQ,EAAGK,EAAS,aAAa,EACjD,oBAAqBJ,EAAO,CAAC,IAAMA,EAAO,CAAC,EAAI,SAAUU,EAAQ,CAC/D,OAAON,EAAS,YAAYM,CAAM,CACxC,GACI,mBAAoBV,EAAO,CAAC,IAAMA,EAAO,CAAC,EAAI,SAAUU,EAAQ,CAC9D,OAAON,EAAS,OAAOM,CAAM,CACnC,GACI,WAAYV,EAAO,CAAC,IAAMA,EAAO,CAAC,EAAI,SAAUU,EAAQ,CACtD,OAAON,EAAS,UAAUM,CAAM,CACtC,GACI,YAAaV,EAAO,CAAC,IAAMA,EAAO,CAAC,EAAI,SAAUU,EAAQ,CACvD,OAAON,EAAS,YAAYM,CAAM,CACxC,GACI,UAAWV,EAAO,CAAC,IAAMA,EAAO,CAAC,EAAI,SAAUU,EAAQ,CACrD,OAAON,EAAS,UAAUM,CAAM,CACtC,GACI,OAAQV,EAAO,CAAC,IAAMA,EAAO,CAAC,EAAI,SAAUU,EAAQ,CAClD,OAAON,EAAS,OAAOM,CAAM,CACnC,GACI,SAAUX,EAAK,SACf,KAAM,SACN,gBAAiBA,EAAK,IACtB,gBAAiBA,EAAK,QACtB,gBAAiBA,EAAK,IACtB,kBAAmBA,EAAK,eACxB,aAAcA,EAAK,UACnB,mBAAoBA,EAAK,WAC7B,EAAKA,EAAK,IAAI,QAAQ,EAAG,CACrB,SAAUK,EAAS,KACvB,CAAG,EAAG,KAAM,GAAIT,EAAU,GAAmCI,EAAK,OAASM,EAAS,EAAIC,EAAmB,OAAQC,EAAW,CAC1H,IAAK,EACL,MAASR,EAAK,GAAG,QAAQ,EACzB,MAAO,CAACA,EAAK,GAAG,QAAQ,EAAGK,EAAS,uBAAuB,EAC3D,oBAAqBJ,EAAO,CAAC,IAAMA,EAAO,CAAC,EAAI,SAAUU,EAAQ,CAC/D,OAAON,EAAS,YAAYM,EAAQ,CAAC,CAC3C,GACI,mBAAoBV,EAAO,CAAC,IAAMA,EAAO,CAAC,EAAI,SAAUU,EAAQ,CAC9D,OAAON,EAAS,OAAOM,CAAM,CACnC,GACI,WAAYV,EAAO,CAAC,IAAMA,EAAO,CAAC,EAAI,SAAUU,EAAQ,CACtD,OAAON,EAAS,UAAUM,CAAM,CACtC,GACI,YAAaV,EAAO,CAAC,IAAMA,EAAO,CAAC,EAAI,SAAUU,EAAQ,CACvD,OAAON,EAAS,YAAYM,EAAQ,CAAC,CAC3C,GACI,UAAWV,EAAO,EAAE,IAAMA,EAAO,EAAE,EAAI,SAAUU,EAAQ,CACvD,OAAON,EAAS,UAAUM,EAAQ,CAAC,CACzC,GACI,OAAQV,EAAO,EAAE,IAAMA,EAAO,EAAE,EAAI,SAAUU,EAAQ,CACpD,OAAON,EAAS,OAAOM,EAAQ,CAAC,CACtC,GACI,SAAUX,EAAK,SACf,KAAM,SACN,gBAAiBA,EAAK,IACtB,gBAAiBA,EAAK,QAAUA,EAAK,QAAQ,CAAC,EAAI,KAClD,gBAAiBA,EAAK,IACtB,kBAAmBA,EAAK,eACxB,aAAcA,EAAK,UACnB,mBAAoBA,EAAK,WAC7B,EAAKA,EAAK,IAAI,cAAc,EAAG,CAC3B,SAAUK,EAAS,KACvB,CAAG,EAAG,KAAM,GAAIR,EAAU,GAAKa,EAAmB,GAAI,EAAI,EAAGV,EAAK,OAASM,EAAS,EAAIC,EAAmB,OAAQC,EAAW,CAC1H,IAAK,EACL,MAASR,EAAK,GAAG,QAAQ,EACzB,MAAO,CAACA,EAAK,GAAG,QAAQ,EAAGK,EAAS,qBAAqB,EACzD,oBAAqBJ,EAAO,EAAE,IAAMA,EAAO,EAAE,EAAI,SAAUU,EAAQ,CACjE,OAAON,EAAS,YAAYM,EAAQ,CAAC,CAC3C,GACI,mBAAoBV,EAAO,EAAE,IAAMA,EAAO,EAAE,EAAI,SAAUU,EAAQ,CAChE,OAAON,EAAS,OAAOM,CAAM,CACnC,GACI,WAAYV,EAAO,EAAE,IAAMA,EAAO,EAAE,EAAI,SAAUU,EAAQ,CACxD,OAAON,EAAS,UAAUM,CAAM,CACtC,GACI,YAAaV,EAAO,EAAE,IAAMA,EAAO,EAAE,EAAI,SAAUU,EAAQ,CACzD,OAAON,EAAS,YAAYM,EAAQ,CAAC,CAC3C,GACI,UAAWV,EAAO,EAAE,IAAMA,EAAO,EAAE,EAAI,SAAUU,EAAQ,CACvD,OAAON,EAAS,UAAUM,EAAQ,CAAC,CACzC,GACI,OAAQV,EAAO,EAAE,IAAMA,EAAO,EAAE,EAAI,SAAUU,EAAQ,CACpD,OAAON,EAAS,OAAOM,EAAQ,CAAC,CACtC,GACI,SAAUX,EAAK,SACf,KAAM,SACN,gBAAiBA,EAAK,IACtB,gBAAiBA,EAAK,QAAUA,EAAK,QAAQ,CAAC,EAAI,KAClD,gBAAiBA,EAAK,IACtB,kBAAmBA,EAAK,eACxB,aAAcA,EAAK,UACnB,mBAAoBA,EAAK,WAC7B,EAAKA,EAAK,IAAI,YAAY,EAAG,CACzB,SAAUK,EAAS,KACpB,CAAA,EAAG,KAAM,GAAIP,EAAU,GAAKY,EAAmB,GAAI,EAAI,CAAC,EAAG,GAAIhB,EAAU,CAC5E,CAEA3B,GAAO,OAASgC,2YCvchB,MAAM1F,EAAQC,EAWRC,EAAQC,GAAoBF,EAAA,YAAC,EAE7BG,EAAWC,EAAY,EAEvBC,EAAOC,EAAqB,EAC5BgG,EAAShG,EAAwB,MAAS,EAC1CK,EAAYL,EAAI,OAAO,EAEvBW,EAAoB,IAAM,OAGvB,OAFOC,EAAAb,EAAK,QAAL,YAAAa,EAAY,eAG5B,EAEA,eAAeC,GAAe,CAExB,GAAA,CAACF,IACH,OAGF,MAAMG,EAA4B,CAAE,QAAS,OAAOrB,EAAM,MAAM,QAAQ,CAAE,EAEtEY,EAAU,QACZS,EAAQ,UAAYT,EAAU,OAE5B2F,EAAO,QACTlF,EAAQ,OAASkF,EAAO,OAE1B,MAAMjF,GAAS,EACNlB,EAAA,UAAU,UAAUiB,CAAO,EACpCnB,EAAM,MAAQ,EAAA,CAGhB,SAASqB,GAAY,aACZgF,EAAA,MAAQvG,EAAM,MAAM,OACjBY,EAAA,QACRY,GAAAL,EAAAf,EAAS,UAAU,WAAnB,YAAAe,EAA6B,cAA7B,YAAAK,EAA0C,eAC1CC,GAAAC,EAAAtB,EAAS,UAAU,WAAnB,YAAAsB,EAA6B,cAA7B,YAAAD,EAA0C,OAC1C,OAAA,CAGJ,SAAS+E,GAAa,CAEPpF,EAAA,CAAA,CAGf,MAAMqF,EAAkBC,GAAaH,EAAQ,IAAK,CAAE,QAAS,IAAK,EAE5DI,EAAeC,GAAiB,IAC7BH,EAAgB,OAASzG,EAAM,MAAM,aACxC,IAAI6G,GAAoBJ,EAAgB,MAAQzG,EAAM,MAAM,aAAcA,EAAM,MAAM,gBAAkB,GAAIA,EAAM,qBAAqB,CAAC,sBACxI,EACL,EACKgB,EAAmB,CACvB,CAAE,MAAO,SAAU,KAAM,QAAS,EAClC,CAAE,MAAO,QAAS,KAAM,OAAQ,CAClC,4lMC/DA,MAAM8F,EAAOC,EAQPC,EAAczG,EAAI,EAAK,EAEpB,SAAA0G,EAAiBC,EAAatG,EAAgC,OAAW,CAChFoG,EAAY,MAAQ,GACfF,EAAA,YAAaI,EAAMtG,CAAS,CAAA,CAEnC,SAASuG,EAAwBD,EAAa,CAC5CF,EAAY,MAAQ,GACpBF,EAAK,mBAAoBI,CAAI,CAAA,CAE/B,SAASE,EAAuBF,EAAa,CAC3CF,EAAY,MAAQ,GACpBF,EAAK,kBAAmBI,CAAI,CAAA,CAE9B,SAASG,EAAkBH,EAAa,CACtCF,EAAY,MAAQ,GACpBF,EAAK,cAAeI,CAAI,CAAA,CAE1B,SAASI,EAAkBJ,EAAa,CACtCF,EAAY,MAAQ,GACpBF,EAAK,cAAeI,CAAI,CAAA,CAE1B,SAASK,EAAiBL,EAAa,CACrCF,EAAY,MAAQ,GACpBF,EAAK,aAAcI,CAAI,CAAA,CAEnB,MAAAM,EAAUjH,EAAyC,IAAI,qzCChC7D,MAAMP,EAAQC,EASRG,EAAWC,EAAY,EACvBoH,EAASC,GAAU,EACnBC,EAAgBC,GAAiB,EACjCC,EAActH,EAAI,CAAC,EACnBuH,EAAoBvH,EAAI,EACxBwH,EAAaxH,EAAI,EAAE,EACnByH,EAAUzH,EAAW,EAAW,EAChC0H,EAAUjI,EAAM,aAAe,IAAM,GACrCkI,EAAc3H,EAAqB,EACnC4H,EAAmB5H,EAAI,EAAK,EAC5B6H,EAAqB7H,EAAI,EAAK,EAC9B8H,EAAkB9H,EAAI,EAAE,EACxB+H,EAAmB/H,EAAyB,IAAI,EAEhDgI,EAAmBhI,EAAI,CAAE,QAAS,GAAO,MAAO,CAAA,EAAa,EACnE,SAASiI,EAAwB/H,EAAO,CACtC,OAAOgI,GAAYhI,EAAOL,EAAS,UAAU,qBAAqB,CAAA,CAGpE,MAAMsI,EAAcnI,EAAI,CACtB,CAAE,MAAO,WAAY,OAAQ,IAAK,EAClC,CAAE,MAAO,OAAQ,OAAQ,MAAO,EAChC,CAAE,MAAO,SAAU,OAAQ,QAAS,EACpCP,EAAM,aACF,CAAE,MAAO,eAAgB,OAAQ,cAAe,EAChD,CAAE,MAAO,mBAAoB,OAAQ,oBAAqB,EAC9D,CACE,MAAO,YACP,OAAQ,WACV,EACA,CACE,MAAOA,EAAM,aAAe,eAAiB,aAC7C,OAAQA,EAAM,aAAe,eAAiB,YAChD,EACA,CACE,MAAO,SACP,OAAQA,EAAM,aAAe,mBAAqB,UACpD,EACA,CAAE,MAAO,iBAAkB,OAAQ,WAAY,EAC/C,GAAIA,EAAM,aACN,CAAC,CAAE,MAAO,UAAW,OAAQ,EAAG,CAAC,EACjC,CACE,CAAE,MAAO,kBAAmB,OAAQ,YAAa,EACjD,CAAE,MAAO,cAAe,OAAQ,cAAe,CAAA,CACjD,CACL,EAEGA,EAAM,cACR0I,EAAY,MAAM,QAAQ,CAAE,MAAO,UAAW,OAAQ,MAAO,EAGzD,MAAAC,EAAcpI,EAAwB,MAAS,EAC5C,SAAA0G,EAAiBC,EAAatG,EAAgC,OAAW,CAChFoH,EAAQ,MAAQd,EAChBoB,EAAiB,MAAQ,EACTD,EAAA,MAAQ,qBAAqBnB,EAAK,QAAQ,UAAUA,EAAK,IAAI,WAAWtG,CAAS,UACjG+H,EAAY,MAAQ/H,EAChB+G,EAAc,gBAAkB,GAClCS,EAAmB,MAAQ,GAETQ,EAAA,CACpB,CAGF,SAASA,GAAoB,CACvB,GAAAN,EAAiB,QAAU,EAA0B,CACvD,MAAMjH,EAA8B,CAClC,QAAS,OAAO2G,EAAQ,MAAM,QAAQ,EACtC,MAAOA,EAAQ,MAAM,KACvB,EACS5H,EAAA,iBAAiBiB,CAAO,EAAE,MAAOwH,GAAU,QAAQ,IAAIA,EAAM,QAAQ,CAAC,CAAA,CAE7E,GAAAP,EAAiB,QAAU,EAAwB,CACrD,MAAMjH,EAAiC,CACrC,QAAS,OAAO2G,EAAQ,MAAM,QAAQ,EACtC,MAAOA,EAAQ,MAAM,KACvB,EACIW,EAAY,QACdtH,EAAQ,UAAYsH,EAAY,OAElCvI,EACG,eAAeiB,CAAO,EACtB,KAAMyH,GAAQ,QAAQ,IAAIA,CAAG,CAAC,EAC9B,MAAOD,GAAU,QAAQ,IAAIA,EAAM,QAAQ,CAAC,CAAA,CAE7C,GAAAP,EAAiB,QAAU,EAA8B,CAC3D,MAAMjH,EAA8B,CAClC,QAAS,OAAO2G,EAAQ,MAAM,QAAQ,EACtC,MAAOA,EAAQ,MAAM,KACvB,EACA5H,EAAS,qBAAqBiB,CAAO,CAAA,CAGvCsH,EAAY,MAAQ,OACpBP,EAAmB,MAAQ,EAAA,CAG7B,SAASW,EAAmB7B,EAAa,CACvCmB,EAAgB,MAAQ,uBAAuBnB,EAAK,QAAQ,UAAUA,EAAK,IAAI,KAC/EoB,EAAiB,MAAQ,EACzBN,EAAQ,MAAQd,EAChBkB,EAAmB,MAAQ,EAAA,CAG7B,SAASjB,EAAwBD,EAAa,CAC5Cc,EAAQ,MAAQd,EAChBiB,EAAiB,MAAQ,EAAA,CAG3B,SAASf,EAAuBF,EAAa,CAC3CmB,EAAgB,MAAQ,+BAA+BnB,EAAK,QAAQ,UAAUA,EAAK,IAAI,KACvFc,EAAQ,MAAQd,EAChBoB,EAAiB,MAAQ,EACzBF,EAAmB,MAAQ,EAAA,CAG7B,SAASY,EAAmB9B,EAAa,CAC9B9G,EAAA,iBAAiB,CAAE,QAAS,OAAO8G,EAAK,QAAQ,EAAG,MAAOA,EAAK,MAAO,CAAA,CAGjF,SAASK,EAAiBL,EAAa,CACrCqB,EAAiB,MAAM,MAAQrB,EAC/BqB,EAAiB,MAAM,QAAU,EAAA,CAGnC,MAAMU,GAAe,CAAC,CAAE,KAAM/B,EAAM,MAAA1C,KAAY,CAC1CxE,EAAM,cAAgBI,EAAS,cAAgB8G,EAAK,OAE7C9G,EAAA,UAAU8G,EAAK,KAAK,EAE3BA,GAAQA,EAAK,WAAa9G,EAAS,UAAU,eACtCA,EAAA,UAAU,eAAe8G,CAAI,EACtCY,EAAkB,MAAQtD,EACtBxE,EAAM,cACRyH,EAAO,KAAK,CAAE,KAAM,mBAAA,CAAqB,IAGlCrH,EAAA,UAAU,eAAe,IAAI,EACtC0H,EAAkB,MAAQ,OAE9B,EAEA,OAAAoB,GACE,IAAM9I,EAAS,UAAU,cACxB+I,GAAQ,CACOnJ,EAAM,OAAO,UAAWoJ,GAAMA,EAAE,WAAaD,CAAG,EAElD,IACVrB,EAAkB,MAAQ,OAC5B,CAEJ", "x_google_ignoreList": [1, 2, 3]}