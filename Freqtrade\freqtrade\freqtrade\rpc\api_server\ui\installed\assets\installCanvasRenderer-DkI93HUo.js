import{d as Pg,bH as Lg,bI as Io,bJ as pa,bK as Ig,j as ri,bL as Rg,U as ga,bM as ac,o as Eg,bN as Og,ba as kg,i as Bg,f as Ng}from"./index-jan7QZNA.js";/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */var Us=function(r,t){return Us=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,i){e.__proto__=i}||function(e,i){for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n])},Us(r,t)};function N(r,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");Us(r,t);function e(){this.constructor=r}r.prototype=t===null?Object.create(t):(e.prototype=t.prototype,new e)}var Fg=function(){function r(){this.firefox=!1,this.ie=!1,this.edge=!1,this.newEdge=!1,this.weChat=!1}return r}(),zg=function(){function r(){this.browser=new Fg,this.node=!1,this.wxa=!1,this.worker=!1,this.svgSupported=!1,this.touchEventsSupported=!1,this.pointerEventsSupported=!1,this.domSupported=!1,this.transformSupported=!1,this.transform3dSupported=!1,this.hasGlobalWindow=typeof window<"u"}return r}(),H=new zg;typeof wx=="object"&&typeof wx.getSystemInfoSync=="function"?(H.wxa=!0,H.touchEventsSupported=!0):typeof document>"u"&&typeof self<"u"?H.worker=!0:!H.hasGlobalWindow||"Deno"in window?(H.node=!0,H.svgSupported=!0):Hg(navigator.userAgent,H);function Hg(r,t){var e=t.browser,i=r.match(/Firefox\/([\d.]+)/),n=r.match(/MSIE\s([\d.]+)/)||r.match(/Trident\/.+?rv:(([\d.]+))/),a=r.match(/Edge?\/([\d.]+)/),o=/micromessenger/i.test(r);i&&(e.firefox=!0,e.version=i[1]),n&&(e.ie=!0,e.version=n[1]),a&&(e.edge=!0,e.version=a[1],e.newEdge=+a[1].split(".")[0]>18),o&&(e.weChat=!0),t.svgSupported=typeof SVGRect<"u",t.touchEventsSupported="ontouchstart"in window&&!e.ie&&!e.edge,t.pointerEventsSupported="onpointerdown"in window&&(e.edge||e.ie&&+e.version>=11),t.domSupported=typeof document<"u";var s=document.documentElement.style;t.transform3dSupported=(e.ie&&"transition"in s||e.edge||"WebKitCSSMatrix"in window&&"m11"in new WebKitCSSMatrix||"MozPerspective"in s)&&!("OTransition"in s),t.transformSupported=t.transform3dSupported||e.ie&&+e.version>=9}var Uu=12,Gg="sans-serif",Nr=Uu+"px "+Gg,Vg=20,Wg=100,Ug="007LLmW'55;N0500LLLLLLLLLL00NNNLzWW\\\\WQb\\0FWLg\\bWb\\WQ\\WrWWQ000CL5LLFLL0LL**F*gLLLL5F0LF\\FFF5.5N";function Yg(r){var t={};if(typeof JSON>"u")return t;for(var e=0;e<r.length;e++){var i=String.fromCharCode(e+32),n=(r.charCodeAt(e)-Vg)/Wg;t[i]=n}return t}var Xg=Yg(Ug),yi={createCanvas:function(){return typeof document<"u"&&document.createElement("canvas")},measureText:function(){var r,t;return function(e,i){if(!r){var n=yi.createCanvas();r=n&&n.getContext("2d")}if(r)return t!==i&&(t=r.font=i||Nr),r.measureText(e);e=e||"",i=i||Nr;var a=/((?:\d+)?\.?\d*)px/.exec(i),o=a&&+a[1]||Uu,s=0;if(i.indexOf("mono")>=0)s=o*e.length;else for(var u=0;u<e.length;u++){var l=Xg[e[u]];s+=l==null?o:l*o}return{width:s}}}(),loadImage:function(r,t,e){var i=new Image;return i.onload=t,i.onerror=e,i.src=r,i}},oc=Ze(["Function","RegExp","Date","Error","CanvasGradient","CanvasPattern","Image","Canvas"],function(r,t){return r["[object "+t+"]"]=!0,r},{}),sc=Ze(["Int8","Uint8","Uint8Clamped","Int16","Uint16","Int32","Uint32","Float32","Float64"],function(r,t){return r["[object "+t+"Array]"]=!0,r},{}),_i=Object.prototype.toString,ro=Array.prototype,$g=ro.forEach,qg=ro.filter,Yu=ro.slice,Zg=ro.map,Zl=(function(){}).constructor,En=Zl?Zl.prototype:null,Xu="__proto__",Kg=2311;function uc(){return Kg++}function $u(){for(var r=[],t=0;t<arguments.length;t++)r[t]=arguments[t];typeof console<"u"&&console.error.apply(console,r)}function Z(r){if(r==null||typeof r!="object")return r;var t=r,e=_i.call(r);if(e==="[object Array]"){if(!Ji(r)){t=[];for(var i=0,n=r.length;i<n;i++)t[i]=Z(r[i])}}else if(sc[e]){if(!Ji(r)){var a=r.constructor;if(a.from)t=a.from(r);else{t=new a(r.length);for(var i=0,n=r.length;i<n;i++)t[i]=r[i]}}}else if(!oc[e]&&!Ji(r)&&!hn(r)){t={};for(var o in r)r.hasOwnProperty(o)&&o!==Xu&&(t[o]=Z(r[o]))}return t}function st(r,t,e){if(!F(t)||!F(r))return e?Z(t):r;for(var i in t)if(t.hasOwnProperty(i)&&i!==Xu){var n=r[i],a=t[i];F(a)&&F(n)&&!k(a)&&!k(n)&&!hn(a)&&!hn(n)&&!Kl(a)&&!Kl(n)&&!Ji(a)&&!Ji(n)?st(n,a,e):(e||!(i in r))&&(r[i]=Z(t[i]))}return r}function qx(r,t){for(var e=r[0],i=1,n=r.length;i<n;i++)e=st(e,r[i],t);return e}function O(r,t){if(Object.assign)Object.assign(r,t);else for(var e in t)t.hasOwnProperty(e)&&e!==Xu&&(r[e]=t[e]);return r}function ot(r,t,e){for(var i=ft(t),n=0,a=i.length;n<a;n++){var o=i[n];(e?t[o]!=null:r[o]==null)&&(r[o]=t[o])}return r}function at(r,t){if(r){if(r.indexOf)return r.indexOf(t);for(var e=0,i=r.length;e<i;e++)if(r[e]===t)return e}return-1}function Qg(r,t){var e=r.prototype;function i(){}i.prototype=t.prototype,r.prototype=new i;for(var n in e)e.hasOwnProperty(n)&&(r.prototype[n]=e[n]);r.prototype.constructor=r,r.superClass=t}function Ee(r,t,e){if(r="prototype"in r?r.prototype:r,t="prototype"in t?t.prototype:t,Object.getOwnPropertyNames)for(var i=Object.getOwnPropertyNames(t),n=0;n<i.length;n++){var a=i[n];a!=="constructor"&&(e?t[a]!=null:r[a]==null)&&(r[a]=t[a])}else ot(r,t,e)}function zt(r){return!r||typeof r=="string"?!1:typeof r.length=="number"}function D(r,t,e){if(r&&t)if(r.forEach&&r.forEach===$g)r.forEach(t,e);else if(r.length===+r.length)for(var i=0,n=r.length;i<n;i++)t.call(e,r[i],i,r);else for(var a in r)r.hasOwnProperty(a)&&t.call(e,r[a],a,r)}function Y(r,t,e){if(!r)return[];if(!t)return qu(r);if(r.map&&r.map===Zg)return r.map(t,e);for(var i=[],n=0,a=r.length;n<a;n++)i.push(t.call(e,r[n],n,r));return i}function Ze(r,t,e,i){if(r&&t){for(var n=0,a=r.length;n<a;n++)e=t.call(i,e,r[n],n,r);return e}}function Et(r,t,e){if(!r)return[];if(!t)return qu(r);if(r.filter&&r.filter===qg)return r.filter(t,e);for(var i=[],n=0,a=r.length;n<a;n++)t.call(e,r[n],n,r)&&i.push(r[n]);return i}function Zx(r,t,e){if(r&&t){for(var i=0,n=r.length;i<n;i++)if(t.call(e,r[i],i,r))return r[i]}}function ft(r){if(!r)return[];if(Object.keys)return Object.keys(r);var t=[];for(var e in r)r.hasOwnProperty(e)&&t.push(e);return t}function Jg(r,t){for(var e=[],i=2;i<arguments.length;i++)e[i-2]=arguments[i];return function(){return r.apply(t,e.concat(Yu.call(arguments)))}}var lt=En&&K(En.bind)?En.call.bind(En.bind):Jg;function yt(r){for(var t=[],e=1;e<arguments.length;e++)t[e-1]=arguments[e];return function(){return r.apply(this,t.concat(Yu.call(arguments)))}}function k(r){return Array.isArray?Array.isArray(r):_i.call(r)==="[object Array]"}function K(r){return typeof r=="function"}function B(r){return typeof r=="string"}function Ys(r){return _i.call(r)==="[object String]"}function ut(r){return typeof r=="number"}function F(r){var t=typeof r;return t==="function"||!!r&&t==="object"}function Kl(r){return!!oc[_i.call(r)]}function Ht(r){return!!sc[_i.call(r)]}function hn(r){return typeof r=="object"&&typeof r.nodeType=="number"&&typeof r.ownerDocument=="object"}function io(r){return r.colorStops!=null}function jg(r){return r.image!=null}function Kx(r){return _i.call(r)==="[object RegExp]"}function Oa(r){return r!==r}function vn(){for(var r=[],t=0;t<arguments.length;t++)r[t]=arguments[t];for(var e=0,i=r.length;e<i;e++)if(r[e]!=null)return r[e]}function W(r,t){return r??t}function ya(r,t,e){return r??t??e}function qu(r){for(var t=[],e=1;e<arguments.length;e++)t[e-1]=arguments[e];return Yu.apply(r,t)}function lc(r){if(typeof r=="number")return[r,r,r,r];var t=r.length;return t===2?[r[0],r[1],r[0],r[1]]:t===3?[r[0],r[1],r[2],r[1]]:r}function Te(r,t){if(!r)throw new Error(t)}function ye(r){return r==null?null:typeof r.trim=="function"?r.trim():r.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}var fc="__ec_primitive__";function ka(r){r[fc]=!0}function Ji(r){return r[fc]}var ty=function(){function r(){this.data={}}return r.prototype.delete=function(t){var e=this.has(t);return e&&delete this.data[t],e},r.prototype.has=function(t){return this.data.hasOwnProperty(t)},r.prototype.get=function(t){return this.data[t]},r.prototype.set=function(t,e){return this.data[t]=e,this},r.prototype.keys=function(){return ft(this.data)},r.prototype.forEach=function(t){var e=this.data;for(var i in e)e.hasOwnProperty(i)&&t(e[i],i)},r}(),hc=typeof Map=="function";function ey(){return hc?new Map:new ty}var ry=function(){function r(t){var e=k(t);this.data=ey();var i=this;t instanceof r?t.each(n):t&&D(t,n);function n(a,o){e?i.set(a,o):i.set(o,a)}}return r.prototype.hasKey=function(t){return this.data.has(t)},r.prototype.get=function(t){return this.data.get(t)},r.prototype.set=function(t,e){return this.data.set(t,e),e},r.prototype.each=function(t,e){this.data.forEach(function(i,n){t.call(e,i,n)})},r.prototype.keys=function(){var t=this.data.keys();return hc?Array.from(t):t},r.prototype.removeKey=function(t){this.data.delete(t)},r}();function X(r){return new ry(r)}function iy(r,t){for(var e=new r.constructor(r.length+t.length),i=0;i<r.length;i++)e[i]=r[i];for(var n=r.length,i=0;i<t.length;i++)e[i+n]=t[i];return e}function no(r,t){var e;if(Object.create)e=Object.create(r);else{var i=function(){};i.prototype=r,e=new i}return t&&O(e,t),e}function vc(r){var t=r.style;t.webkitUserSelect="none",t.userSelect="none",t.webkitTapHighlightColor="rgba(0,0,0,0)",t["-webkit-touch-callout"]="none"}function Ke(r,t){return r.hasOwnProperty(t)}function Ft(){}var _a=180/Math.PI;function mi(r,t){return r==null&&(r=0),t==null&&(t=0),[r,t]}function Qx(r,t){return r[0]=t[0],r[1]=t[1],r}function ny(r){return[r[0],r[1]]}function Jx(r,t,e){return r[0]=t,r[1]=e,r}function Ql(r,t,e){return r[0]=t[0]+e[0],r[1]=t[1]+e[1],r}function jx(r,t,e,i){return r[0]=t[0]+e[0]*i,r[1]=t[1]+e[1]*i,r}function ay(r,t,e){return r[0]=t[0]-e[0],r[1]=t[1]-e[1],r}function oy(r){return Math.sqrt(sy(r))}function sy(r){return r[0]*r[0]+r[1]*r[1]}function Ro(r,t,e){return r[0]=t[0]*e,r[1]=t[1]*e,r}function uy(r,t){var e=oy(t);return e===0?(r[0]=0,r[1]=0):(r[0]=t[0]/e,r[1]=t[1]/e),r}function Xs(r,t){return Math.sqrt((r[0]-t[0])*(r[0]-t[0])+(r[1]-t[1])*(r[1]-t[1]))}var ly=Xs;function fy(r,t){return(r[0]-t[0])*(r[0]-t[0])+(r[1]-t[1])*(r[1]-t[1])}var ui=fy;function tC(r,t,e,i){return r[0]=t[0]+i*(e[0]-t[0]),r[1]=t[1]+i*(e[1]-t[1]),r}function me(r,t,e){var i=t[0],n=t[1];return r[0]=e[0]*i+e[2]*n+e[4],r[1]=e[1]*i+e[3]*n+e[5],r}function ni(r,t,e){return r[0]=Math.min(t[0],e[0]),r[1]=Math.min(t[1],e[1]),r}function ai(r,t,e){return r[0]=Math.max(t[0],e[0]),r[1]=Math.max(t[1],e[1]),r}var Vr=function(){function r(t,e){this.target=t,this.topTarget=e&&e.topTarget}return r}(),hy=function(){function r(t){this.handler=t,t.on("mousedown",this._dragStart,this),t.on("mousemove",this._drag,this),t.on("mouseup",this._dragEnd,this)}return r.prototype._dragStart=function(t){for(var e=t.target;e&&!e.draggable;)e=e.parent||e.__hostTarget;e&&(this._draggingTarget=e,e.dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.handler.dispatchToElement(new Vr(e,t),"dragstart",t.event))},r.prototype._drag=function(t){var e=this._draggingTarget;if(e){var i=t.offsetX,n=t.offsetY,a=i-this._x,o=n-this._y;this._x=i,this._y=n,e.drift(a,o,t),this.handler.dispatchToElement(new Vr(e,t),"drag",t.event);var s=this.handler.findHover(i,n,e).target,u=this._dropTarget;this._dropTarget=s,e!==s&&(u&&s!==u&&this.handler.dispatchToElement(new Vr(u,t),"dragleave",t.event),s&&s!==u&&this.handler.dispatchToElement(new Vr(s,t),"dragenter",t.event))}},r.prototype._dragEnd=function(t){var e=this._draggingTarget;e&&(e.dragging=!1),this.handler.dispatchToElement(new Vr(e,t),"dragend",t.event),this._dropTarget&&this.handler.dispatchToElement(new Vr(this._dropTarget,t),"drop",t.event),this._draggingTarget=null,this._dropTarget=null},r}(),be=function(){function r(t){t&&(this._$eventProcessor=t)}return r.prototype.on=function(t,e,i,n){this._$handlers||(this._$handlers={});var a=this._$handlers;if(typeof e=="function"&&(n=i,i=e,e=null),!i||!t)return this;var o=this._$eventProcessor;e!=null&&o&&o.normalizeQuery&&(e=o.normalizeQuery(e)),a[t]||(a[t]=[]);for(var s=0;s<a[t].length;s++)if(a[t][s].h===i)return this;var u={h:i,query:e,ctx:n||this,callAtLast:i.zrEventfulCallAtLast},l=a[t].length-1,f=a[t][l];return f&&f.callAtLast?a[t].splice(l,0,u):a[t].push(u),this},r.prototype.isSilent=function(t){var e=this._$handlers;return!e||!e[t]||!e[t].length},r.prototype.off=function(t,e){var i=this._$handlers;if(!i)return this;if(!t)return this._$handlers={},this;if(e){if(i[t]){for(var n=[],a=0,o=i[t].length;a<o;a++)i[t][a].h!==e&&n.push(i[t][a]);i[t]=n}i[t]&&i[t].length===0&&delete i[t]}else delete i[t];return this},r.prototype.trigger=function(t){for(var e=[],i=1;i<arguments.length;i++)e[i-1]=arguments[i];if(!this._$handlers)return this;var n=this._$handlers[t],a=this._$eventProcessor;if(n)for(var o=e.length,s=n.length,u=0;u<s;u++){var l=n[u];if(!(a&&a.filter&&l.query!=null&&!a.filter(t,l.query)))switch(o){case 0:l.h.call(l.ctx);break;case 1:l.h.call(l.ctx,e[0]);break;case 2:l.h.call(l.ctx,e[0],e[1]);break;default:l.h.apply(l.ctx,e);break}}return a&&a.afterTrigger&&a.afterTrigger(t),this},r.prototype.triggerWithContext=function(t){for(var e=[],i=1;i<arguments.length;i++)e[i-1]=arguments[i];if(!this._$handlers)return this;var n=this._$handlers[t],a=this._$eventProcessor;if(n)for(var o=e.length,s=e[o-1],u=n.length,l=0;l<u;l++){var f=n[l];if(!(a&&a.filter&&f.query!=null&&!a.filter(t,f.query)))switch(o){case 0:f.h.call(s);break;case 1:f.h.call(s,e[0]);break;case 2:f.h.call(s,e[0],e[1]);break;default:f.h.apply(s,e.slice(1,o-1));break}}return a&&a.afterTrigger&&a.afterTrigger(t),this},r}(),vy=Math.log(2);function $s(r,t,e,i,n,a){var o=i+"-"+n,s=r.length;if(a.hasOwnProperty(o))return a[o];if(t===1){var u=Math.round(Math.log((1<<s)-1&~n)/vy);return r[e][u]}for(var l=i|1<<e,f=e+1;i&1<<f;)f++;for(var h=0,c=0,v=0;c<s;c++){var d=1<<c;d&n||(h+=(v%2?-1:1)*r[e][c]*$s(r,t-1,f,l,n|d,a),v++)}return a[o]=h,h}function Jl(r,t){var e=[[r[0],r[1],1,0,0,0,-t[0]*r[0],-t[0]*r[1]],[0,0,0,r[0],r[1],1,-t[1]*r[0],-t[1]*r[1]],[r[2],r[3],1,0,0,0,-t[2]*r[2],-t[2]*r[3]],[0,0,0,r[2],r[3],1,-t[3]*r[2],-t[3]*r[3]],[r[4],r[5],1,0,0,0,-t[4]*r[4],-t[4]*r[5]],[0,0,0,r[4],r[5],1,-t[5]*r[4],-t[5]*r[5]],[r[6],r[7],1,0,0,0,-t[6]*r[6],-t[6]*r[7]],[0,0,0,r[6],r[7],1,-t[7]*r[6],-t[7]*r[7]]],i={},n=$s(e,8,0,0,0,i);if(n!==0){for(var a=[],o=0;o<8;o++)for(var s=0;s<8;s++)a[s]==null&&(a[s]=0),a[s]+=((o+s)%2?-1:1)*$s(e,7,o===0?1:0,1<<o,1<<s,i)/n*t[o];return function(u,l,f){var h=l*a[6]+f*a[7]+1;u[0]=(l*a[0]+f*a[1]+a[2])/h,u[1]=(l*a[3]+f*a[4]+a[5])/h}}}var jl="___zrEVENTSAVED",Eo=[];function cy(r,t,e,i,n){return qs(Eo,t,i,n,!0)&&qs(r,e,Eo[0],Eo[1])}function qs(r,t,e,i,n){if(t.getBoundingClientRect&&H.domSupported&&!cc(t)){var a=t[jl]||(t[jl]={}),o=dy(t,a),s=py(o,a,n);if(s)return s(r,e,i),!0}return!1}function dy(r,t){var e=t.markers;if(e)return e;e=t.markers=[];for(var i=["left","right"],n=["top","bottom"],a=0;a<4;a++){var o=document.createElement("div"),s=o.style,u=a%2,l=(a>>1)%2;s.cssText=["position: absolute","visibility: hidden","padding: 0","margin: 0","border-width: 0","user-select: none","width:0","height:0",i[u]+":0",n[l]+":0",i[1-u]+":auto",n[1-l]+":auto",""].join("!important;"),r.appendChild(o),e.push(o)}return e}function py(r,t,e){for(var i=e?"invTrans":"trans",n=t[i],a=t.srcCoords,o=[],s=[],u=!0,l=0;l<4;l++){var f=r[l].getBoundingClientRect(),h=2*l,c=f.left,v=f.top;o.push(c,v),u=u&&a&&c===a[h]&&v===a[h+1],s.push(r[l].offsetLeft,r[l].offsetTop)}return u&&n?n:(t.srcCoords=o,t[i]=e?Jl(s,o):Jl(o,s))}function cc(r){return r.nodeName.toUpperCase()==="CANVAS"}var gy=/([&<>"'])/g,yy={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};function Ot(r){return r==null?"":(r+"").replace(gy,function(t,e){return yy[e]})}var _y=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,Oo=[],my=H.browser.firefox&&+H.browser.version.split(".")[0]<39;function Zs(r,t,e,i){return e=e||{},i?tf(r,t,e):my&&t.layerX!=null&&t.layerX!==t.offsetX?(e.zrX=t.layerX,e.zrY=t.layerY):t.offsetX!=null?(e.zrX=t.offsetX,e.zrY=t.offsetY):tf(r,t,e),e}function tf(r,t,e){if(H.domSupported&&r.getBoundingClientRect){var i=t.clientX,n=t.clientY;if(cc(r)){var a=r.getBoundingClientRect();e.zrX=i-a.left,e.zrY=n-a.top;return}else if(qs(Oo,r,i,n)){e.zrX=Oo[0],e.zrY=Oo[1];return}}e.zrX=e.zrY=0}function Zu(r){return r||window.event}function qt(r,t,e){if(t=Zu(t),t.zrX!=null)return t;var i=t.type,n=i&&i.indexOf("touch")>=0;if(n){var o=i!=="touchend"?t.targetTouches[0]:t.changedTouches[0];o&&Zs(r,o,t,e)}else{Zs(r,t,t,e);var a=wy(t);t.zrDelta=a?a/120:-(t.detail||0)/3}var s=t.button;return t.which==null&&s!==void 0&&_y.test(t.type)&&(t.which=s&1?1:s&2?3:s&4?2:0),t}function wy(r){var t=r.wheelDelta;if(t)return t;var e=r.deltaX,i=r.deltaY;if(e==null||i==null)return t;var n=Math.abs(i!==0?i:e),a=i>0?-1:i<0?1:e>0?-1:1;return 3*n*a}function Sy(r,t,e,i){r.addEventListener(t,e,i)}function Ty(r,t,e,i){r.removeEventListener(t,e,i)}var dc=function(r){r.preventDefault(),r.stopPropagation(),r.cancelBubble=!0};function eC(r){return r.which===2||r.which===3}var by=function(){function r(){this._track=[]}return r.prototype.recognize=function(t,e,i){return this._doTrack(t,e,i),this._recognize(t)},r.prototype.clear=function(){return this._track.length=0,this},r.prototype._doTrack=function(t,e,i){var n=t.touches;if(n){for(var a={points:[],touches:[],target:e,event:t},o=0,s=n.length;o<s;o++){var u=n[o],l=Zs(i,u,{});a.points.push([l.zrX,l.zrY]),a.touches.push(u)}this._track.push(a)}},r.prototype._recognize=function(t){for(var e in ko)if(ko.hasOwnProperty(e)){var i=ko[e](this._track,t);if(i)return i}},r}();function ef(r){var t=r[1][0]-r[0][0],e=r[1][1]-r[0][1];return Math.sqrt(t*t+e*e)}function xy(r){return[(r[0][0]+r[1][0])/2,(r[0][1]+r[1][1])/2]}var ko={pinch:function(r,t){var e=r.length;if(e){var i=(r[e-1]||{}).points,n=(r[e-2]||{}).points||i;if(n&&n.length>1&&i&&i.length>1){var a=ef(i)/ef(n);!isFinite(a)&&(a=1),t.pinchScale=a;var o=xy(i);return t.pinchX=o[0],t.pinchY=o[1],{type:"pinch",target:r[0].target,event:t}}}}};function Ir(){return[1,0,0,1,0,0]}function Ku(r){return r[0]=1,r[1]=0,r[2]=0,r[3]=1,r[4]=0,r[5]=0,r}function pc(r,t){return r[0]=t[0],r[1]=t[1],r[2]=t[2],r[3]=t[3],r[4]=t[4],r[5]=t[5],r}function li(r,t,e){var i=t[0]*e[0]+t[2]*e[1],n=t[1]*e[0]+t[3]*e[1],a=t[0]*e[2]+t[2]*e[3],o=t[1]*e[2]+t[3]*e[3],s=t[0]*e[4]+t[2]*e[5]+t[4],u=t[1]*e[4]+t[3]*e[5]+t[5];return r[0]=i,r[1]=n,r[2]=a,r[3]=o,r[4]=s,r[5]=u,r}function Ks(r,t,e){return r[0]=t[0],r[1]=t[1],r[2]=t[2],r[3]=t[3],r[4]=t[4]+e[0],r[5]=t[5]+e[1],r}function Qu(r,t,e,i){i===void 0&&(i=[0,0]);var n=t[0],a=t[2],o=t[4],s=t[1],u=t[3],l=t[5],f=Math.sin(e),h=Math.cos(e);return r[0]=n*h+s*f,r[1]=-n*f+s*h,r[2]=a*h+u*f,r[3]=-a*f+h*u,r[4]=h*(o-i[0])+f*(l-i[1])+i[0],r[5]=h*(l-i[1])-f*(o-i[0])+i[1],r}function Cy(r,t,e){var i=e[0],n=e[1];return r[0]=t[0]*i,r[1]=t[1]*n,r[2]=t[2]*i,r[3]=t[3]*n,r[4]=t[4]*i,r[5]=t[5]*n,r}function gc(r,t){var e=t[0],i=t[2],n=t[4],a=t[1],o=t[3],s=t[5],u=e*o-a*i;return u?(u=1/u,r[0]=o*u,r[1]=-a*u,r[2]=-i*u,r[3]=e*u,r[4]=(i*s-o*n)*u,r[5]=(a*n-e*s)*u,r):null}function rC(r){var t=Ir();return pc(t,r),t}var et=function(){function r(t,e){this.x=t||0,this.y=e||0}return r.prototype.copy=function(t){return this.x=t.x,this.y=t.y,this},r.prototype.clone=function(){return new r(this.x,this.y)},r.prototype.set=function(t,e){return this.x=t,this.y=e,this},r.prototype.equal=function(t){return t.x===this.x&&t.y===this.y},r.prototype.add=function(t){return this.x+=t.x,this.y+=t.y,this},r.prototype.scale=function(t){this.x*=t,this.y*=t},r.prototype.scaleAndAdd=function(t,e){this.x+=t.x*e,this.y+=t.y*e},r.prototype.sub=function(t){return this.x-=t.x,this.y-=t.y,this},r.prototype.dot=function(t){return this.x*t.x+this.y*t.y},r.prototype.len=function(){return Math.sqrt(this.x*this.x+this.y*this.y)},r.prototype.lenSquare=function(){return this.x*this.x+this.y*this.y},r.prototype.normalize=function(){var t=this.len();return this.x/=t,this.y/=t,this},r.prototype.distance=function(t){var e=this.x-t.x,i=this.y-t.y;return Math.sqrt(e*e+i*i)},r.prototype.distanceSquare=function(t){var e=this.x-t.x,i=this.y-t.y;return e*e+i*i},r.prototype.negate=function(){return this.x=-this.x,this.y=-this.y,this},r.prototype.transform=function(t){if(t){var e=this.x,i=this.y;return this.x=t[0]*e+t[2]*i+t[4],this.y=t[1]*e+t[3]*i+t[5],this}},r.prototype.toArray=function(t){return t[0]=this.x,t[1]=this.y,t},r.prototype.fromArray=function(t){this.x=t[0],this.y=t[1]},r.set=function(t,e,i){t.x=e,t.y=i},r.copy=function(t,e){t.x=e.x,t.y=e.y},r.len=function(t){return Math.sqrt(t.x*t.x+t.y*t.y)},r.lenSquare=function(t){return t.x*t.x+t.y*t.y},r.dot=function(t,e){return t.x*e.x+t.y*e.y},r.add=function(t,e,i){t.x=e.x+i.x,t.y=e.y+i.y},r.sub=function(t,e,i){t.x=e.x-i.x,t.y=e.y-i.y},r.scale=function(t,e,i){t.x=e.x*i,t.y=e.y*i},r.scaleAndAdd=function(t,e,i,n){t.x=e.x+i.x*n,t.y=e.y+i.y*n},r.lerp=function(t,e,i,n){var a=1-n;t.x=a*e.x+n*i.x,t.y=a*e.y+n*i.y},r}(),On=Math.min,kn=Math.max,nr=new et,ar=new et,or=new et,sr=new et,xi=new et,Ci=new et,J=function(){function r(t,e,i,n){i<0&&(t=t+i,i=-i),n<0&&(e=e+n,n=-n),this.x=t,this.y=e,this.width=i,this.height=n}return r.prototype.union=function(t){var e=On(t.x,this.x),i=On(t.y,this.y);isFinite(this.x)&&isFinite(this.width)?this.width=kn(t.x+t.width,this.x+this.width)-e:this.width=t.width,isFinite(this.y)&&isFinite(this.height)?this.height=kn(t.y+t.height,this.y+this.height)-i:this.height=t.height,this.x=e,this.y=i},r.prototype.applyTransform=function(t){r.applyTransform(this,this,t)},r.prototype.calculateTransform=function(t){var e=this,i=t.width/e.width,n=t.height/e.height,a=Ir();return Ks(a,a,[-e.x,-e.y]),Cy(a,a,[i,n]),Ks(a,a,[t.x,t.y]),a},r.prototype.intersect=function(t,e){if(!t)return!1;t instanceof r||(t=r.create(t));var i=this,n=i.x,a=i.x+i.width,o=i.y,s=i.y+i.height,u=t.x,l=t.x+t.width,f=t.y,h=t.y+t.height,c=!(a<u||l<n||s<f||h<o);if(e){var v=1/0,d=0,y=Math.abs(a-u),p=Math.abs(l-n),g=Math.abs(s-f),_=Math.abs(h-o),m=Math.min(y,p),w=Math.min(g,_);a<u||l<n?m>d&&(d=m,y<p?et.set(Ci,-y,0):et.set(Ci,p,0)):m<v&&(v=m,y<p?et.set(xi,y,0):et.set(xi,-p,0)),s<f||h<o?w>d&&(d=w,g<_?et.set(Ci,0,-g):et.set(Ci,0,_)):m<v&&(v=m,g<_?et.set(xi,0,g):et.set(xi,0,-_))}return e&&et.copy(e,c?xi:Ci),c},r.prototype.contain=function(t,e){var i=this;return t>=i.x&&t<=i.x+i.width&&e>=i.y&&e<=i.y+i.height},r.prototype.clone=function(){return new r(this.x,this.y,this.width,this.height)},r.prototype.copy=function(t){r.copy(this,t)},r.prototype.plain=function(){return{x:this.x,y:this.y,width:this.width,height:this.height}},r.prototype.isFinite=function(){return isFinite(this.x)&&isFinite(this.y)&&isFinite(this.width)&&isFinite(this.height)},r.prototype.isZero=function(){return this.width===0||this.height===0},r.create=function(t){return new r(t.x,t.y,t.width,t.height)},r.copy=function(t,e){t.x=e.x,t.y=e.y,t.width=e.width,t.height=e.height},r.applyTransform=function(t,e,i){if(!i){t!==e&&r.copy(t,e);return}if(i[1]<1e-5&&i[1]>-1e-5&&i[2]<1e-5&&i[2]>-1e-5){var n=i[0],a=i[3],o=i[4],s=i[5];t.x=e.x*n+o,t.y=e.y*a+s,t.width=e.width*n,t.height=e.height*a,t.width<0&&(t.x+=t.width,t.width=-t.width),t.height<0&&(t.y+=t.height,t.height=-t.height);return}nr.x=or.x=e.x,nr.y=sr.y=e.y,ar.x=sr.x=e.x+e.width,ar.y=or.y=e.y+e.height,nr.transform(i),sr.transform(i),ar.transform(i),or.transform(i),t.x=On(nr.x,ar.x,or.x,sr.x),t.y=On(nr.y,ar.y,or.y,sr.y);var u=kn(nr.x,ar.x,or.x,sr.x),l=kn(nr.y,ar.y,or.y,sr.y);t.width=u-t.x,t.height=l-t.y},r}(),yc="silent";function Dy(r,t,e){return{type:r,event:e,target:t.target,topTarget:t.topTarget,cancelBubble:!1,offsetX:e.zrX,offsetY:e.zrY,gestureEvent:e.gestureEvent,pinchX:e.pinchX,pinchY:e.pinchY,pinchScale:e.pinchScale,wheelDelta:e.zrDelta,zrByTouch:e.zrByTouch,which:e.which,stop:My}}function My(){dc(this.event)}var Ay=function(r){N(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.handler=null,e}return t.prototype.dispose=function(){},t.prototype.setCursor=function(){},t}(be),Di=function(){function r(t,e){this.x=t,this.y=e}return r}(),Py=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],Bo=new J(0,0,0,0),_c=function(r){N(t,r);function t(e,i,n,a,o){var s=r.call(this)||this;return s._hovered=new Di(0,0),s.storage=e,s.painter=i,s.painterRoot=a,s._pointerSize=o,n=n||new Ay,s.proxy=null,s.setHandlerProxy(n),s._draggingMgr=new hy(s),s}return t.prototype.setHandlerProxy=function(e){this.proxy&&this.proxy.dispose(),e&&(D(Py,function(i){e.on&&e.on(i,this[i],this)},this),e.handler=this),this.proxy=e},t.prototype.mousemove=function(e){var i=e.zrX,n=e.zrY,a=mc(this,i,n),o=this._hovered,s=o.target;s&&!s.__zr&&(o=this.findHover(o.x,o.y),s=o.target);var u=this._hovered=a?new Di(i,n):this.findHover(i,n),l=u.target,f=this.proxy;f.setCursor&&f.setCursor(l?l.cursor:"default"),s&&l!==s&&this.dispatchToElement(o,"mouseout",e),this.dispatchToElement(u,"mousemove",e),l&&l!==s&&this.dispatchToElement(u,"mouseover",e)},t.prototype.mouseout=function(e){var i=e.zrEventControl;i!=="only_globalout"&&this.dispatchToElement(this._hovered,"mouseout",e),i!=="no_globalout"&&this.trigger("globalout",{type:"globalout",event:e})},t.prototype.resize=function(){this._hovered=new Di(0,0)},t.prototype.dispatch=function(e,i){var n=this[e];n&&n.call(this,i)},t.prototype.dispose=function(){this.proxy.dispose(),this.storage=null,this.proxy=null,this.painter=null},t.prototype.setCursorStyle=function(e){var i=this.proxy;i.setCursor&&i.setCursor(e)},t.prototype.dispatchToElement=function(e,i,n){e=e||{};var a=e.target;if(!(a&&a.silent)){for(var o="on"+i,s=Dy(i,e,n);a&&(a[o]&&(s.cancelBubble=!!a[o].call(a,s)),a.trigger(i,s),a=a.__hostTarget?a.__hostTarget:a.parent,!s.cancelBubble););s.cancelBubble||(this.trigger(i,s),this.painter&&this.painter.eachOtherLayer&&this.painter.eachOtherLayer(function(u){typeof u[o]=="function"&&u[o].call(u,s),u.trigger&&u.trigger(i,s)}))}},t.prototype.findHover=function(e,i,n){var a=this.storage.getDisplayList(),o=new Di(e,i);if(rf(a,o,e,i,n),this._pointerSize&&!o.target){for(var s=[],u=this._pointerSize,l=u/2,f=new J(e-l,i-l,u,u),h=a.length-1;h>=0;h--){var c=a[h];c!==n&&!c.ignore&&!c.ignoreCoarsePointer&&(!c.parent||!c.parent.ignoreCoarsePointer)&&(Bo.copy(c.getBoundingRect()),c.transform&&Bo.applyTransform(c.transform),Bo.intersect(f)&&s.push(c))}if(s.length)for(var v=4,d=Math.PI/12,y=Math.PI*2,p=0;p<l;p+=v)for(var g=0;g<y;g+=d){var _=e+p*Math.cos(g),m=i+p*Math.sin(g);if(rf(s,o,_,m,n),o.target)return o}}return o},t.prototype.processGesture=function(e,i){this._gestureMgr||(this._gestureMgr=new by);var n=this._gestureMgr;i==="start"&&n.clear();var a=n.recognize(e,this.findHover(e.zrX,e.zrY,null).target,this.proxy.dom);if(i==="end"&&n.clear(),a){var o=a.type;e.gestureEvent=o;var s=new Di;s.target=a.target,this.dispatchToElement(s,o,a.event)}},t}(be);D(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],function(r){_c.prototype[r]=function(t){var e=t.zrX,i=t.zrY,n=mc(this,e,i),a,o;if((r!=="mouseup"||!n)&&(a=this.findHover(e,i),o=a.target),r==="mousedown")this._downEl=o,this._downPoint=[t.zrX,t.zrY],this._upEl=o;else if(r==="mouseup")this._upEl=o;else if(r==="click"){if(this._downEl!==this._upEl||!this._downPoint||ly(this._downPoint,[t.zrX,t.zrY])>4)return;this._downPoint=null}this.dispatchToElement(a,r,t)}});function Ly(r,t,e){if(r[r.rectHover?"rectContain":"contain"](t,e)){for(var i=r,n=void 0,a=!1;i;){if(i.ignoreClip&&(a=!0),!a){var o=i.getClipPath();if(o&&!o.contain(t,e))return!1}i.silent&&(n=!0);var s=i.__hostTarget;i=s||i.parent}return n?yc:!0}return!1}function rf(r,t,e,i,n){for(var a=r.length-1;a>=0;a--){var o=r[a],s=void 0;if(o!==n&&!o.ignore&&(s=Ly(o,e,i))&&(!t.topTarget&&(t.topTarget=o),s!==yc)){t.target=o;break}}}function mc(r,t,e){var i=r.painter;return t<0||t>i.getWidth()||e<0||e>i.getHeight()}var wc=32,Mi=7;function Iy(r){for(var t=0;r>=wc;)t|=r&1,r>>=1;return r+t}function nf(r,t,e,i){var n=t+1;if(n===e)return 1;if(i(r[n++],r[t])<0){for(;n<e&&i(r[n],r[n-1])<0;)n++;Ry(r,t,n)}else for(;n<e&&i(r[n],r[n-1])>=0;)n++;return n-t}function Ry(r,t,e){for(e--;t<e;){var i=r[t];r[t++]=r[e],r[e--]=i}}function af(r,t,e,i,n){for(i===t&&i++;i<e;i++){for(var a=r[i],o=t,s=i,u;o<s;)u=o+s>>>1,n(a,r[u])<0?s=u:o=u+1;var l=i-o;switch(l){case 3:r[o+3]=r[o+2];case 2:r[o+2]=r[o+1];case 1:r[o+1]=r[o];break;default:for(;l>0;)r[o+l]=r[o+l-1],l--}r[o]=a}}function No(r,t,e,i,n,a){var o=0,s=0,u=1;if(a(r,t[e+n])>0){for(s=i-n;u<s&&a(r,t[e+n+u])>0;)o=u,u=(u<<1)+1,u<=0&&(u=s);u>s&&(u=s),o+=n,u+=n}else{for(s=n+1;u<s&&a(r,t[e+n-u])<=0;)o=u,u=(u<<1)+1,u<=0&&(u=s);u>s&&(u=s);var l=o;o=n-u,u=n-l}for(o++;o<u;){var f=o+(u-o>>>1);a(r,t[e+f])>0?o=f+1:u=f}return u}function Fo(r,t,e,i,n,a){var o=0,s=0,u=1;if(a(r,t[e+n])<0){for(s=n+1;u<s&&a(r,t[e+n-u])<0;)o=u,u=(u<<1)+1,u<=0&&(u=s);u>s&&(u=s);var l=o;o=n-u,u=n-l}else{for(s=i-n;u<s&&a(r,t[e+n+u])>=0;)o=u,u=(u<<1)+1,u<=0&&(u=s);u>s&&(u=s),o+=n,u+=n}for(o++;o<u;){var f=o+(u-o>>>1);a(r,t[e+f])<0?u=f:o=f+1}return u}function Ey(r,t){var e=Mi,i,n,a=0,o=[];i=[],n=[];function s(v,d){i[a]=v,n[a]=d,a+=1}function u(){for(;a>1;){var v=a-2;if(v>=1&&n[v-1]<=n[v]+n[v+1]||v>=2&&n[v-2]<=n[v]+n[v-1])n[v-1]<n[v+1]&&v--;else if(n[v]>n[v+1])break;f(v)}}function l(){for(;a>1;){var v=a-2;v>0&&n[v-1]<n[v+1]&&v--,f(v)}}function f(v){var d=i[v],y=n[v],p=i[v+1],g=n[v+1];n[v]=y+g,v===a-3&&(i[v+1]=i[v+2],n[v+1]=n[v+2]),a--;var _=Fo(r[p],r,d,y,0,t);d+=_,y-=_,y!==0&&(g=No(r[d+y-1],r,p,g,g-1,t),g!==0&&(y<=g?h(d,y,p,g):c(d,y,p,g)))}function h(v,d,y,p){var g=0;for(g=0;g<d;g++)o[g]=r[v+g];var _=0,m=y,w=v;if(r[w++]=r[m++],--p===0){for(g=0;g<d;g++)r[w+g]=o[_+g];return}if(d===1){for(g=0;g<p;g++)r[w+g]=r[m+g];r[w+p]=o[_];return}for(var T=e,S,b,M;;){S=0,b=0,M=!1;do if(t(r[m],o[_])<0){if(r[w++]=r[m++],b++,S=0,--p===0){M=!0;break}}else if(r[w++]=o[_++],S++,b=0,--d===1){M=!0;break}while((S|b)<T);if(M)break;do{if(S=Fo(r[m],o,_,d,0,t),S!==0){for(g=0;g<S;g++)r[w+g]=o[_+g];if(w+=S,_+=S,d-=S,d<=1){M=!0;break}}if(r[w++]=r[m++],--p===0){M=!0;break}if(b=No(o[_],r,m,p,0,t),b!==0){for(g=0;g<b;g++)r[w+g]=r[m+g];if(w+=b,m+=b,p-=b,p===0){M=!0;break}}if(r[w++]=o[_++],--d===1){M=!0;break}T--}while(S>=Mi||b>=Mi);if(M)break;T<0&&(T=0),T+=2}if(e=T,e<1&&(e=1),d===1){for(g=0;g<p;g++)r[w+g]=r[m+g];r[w+p]=o[_]}else{if(d===0)throw new Error;for(g=0;g<d;g++)r[w+g]=o[_+g]}}function c(v,d,y,p){var g=0;for(g=0;g<p;g++)o[g]=r[y+g];var _=v+d-1,m=p-1,w=y+p-1,T=0,S=0;if(r[w--]=r[_--],--d===0){for(T=w-(p-1),g=0;g<p;g++)r[T+g]=o[g];return}if(p===1){for(w-=d,_-=d,S=w+1,T=_+1,g=d-1;g>=0;g--)r[S+g]=r[T+g];r[w]=o[m];return}for(var b=e;;){var M=0,x=0,C=!1;do if(t(o[m],r[_])<0){if(r[w--]=r[_--],M++,x=0,--d===0){C=!0;break}}else if(r[w--]=o[m--],x++,M=0,--p===1){C=!0;break}while((M|x)<b);if(C)break;do{if(M=d-Fo(o[m],r,v,d,d-1,t),M!==0){for(w-=M,_-=M,d-=M,S=w+1,T=_+1,g=M-1;g>=0;g--)r[S+g]=r[T+g];if(d===0){C=!0;break}}if(r[w--]=o[m--],--p===1){C=!0;break}if(x=p-No(r[_],o,0,p,p-1,t),x!==0){for(w-=x,m-=x,p-=x,S=w+1,T=m+1,g=0;g<x;g++)r[S+g]=o[T+g];if(p<=1){C=!0;break}}if(r[w--]=r[_--],--d===0){C=!0;break}b--}while(M>=Mi||x>=Mi);if(C)break;b<0&&(b=0),b+=2}if(e=b,e<1&&(e=1),p===1){for(w-=d,_-=d,S=w+1,T=_+1,g=d-1;g>=0;g--)r[S+g]=r[T+g];r[w]=o[m]}else{if(p===0)throw new Error;for(T=w-(p-1),g=0;g<p;g++)r[T+g]=o[g]}}return{mergeRuns:u,forceMergeRuns:l,pushRun:s}}function ma(r,t,e,i){e||(e=0),i||(i=r.length);var n=i-e;if(!(n<2)){var a=0;if(n<wc){a=nf(r,e,i,t),af(r,e,i,e+a,t);return}var o=Ey(r,t),s=Iy(n);do{if(a=nf(r,e,i,t),a<s){var u=n;u>s&&(u=s),af(r,e,e+u,e+a,t),a=u}o.pushRun(e,a),o.mergeRuns(),n-=a,e+=a}while(n!==0);o.forceMergeRuns()}}var we=1,wa=2,Yi=4,of=!1;function zo(){of||(of=!0,console.warn("z / z2 / zlevel of displayable is invalid, which may cause unexpected errors"))}function sf(r,t){return r.zlevel===t.zlevel?r.z===t.z?r.z2-t.z2:r.z-t.z:r.zlevel-t.zlevel}var Oy=function(){function r(){this._roots=[],this._displayList=[],this._displayListLen=0,this.displayableSortFunc=sf}return r.prototype.traverse=function(t,e){for(var i=0;i<this._roots.length;i++)this._roots[i].traverse(t,e)},r.prototype.getDisplayList=function(t,e){e=e||!1;var i=this._displayList;return(t||!i.length)&&this.updateDisplayList(e),i},r.prototype.updateDisplayList=function(t){this._displayListLen=0;for(var e=this._roots,i=this._displayList,n=0,a=e.length;n<a;n++)this._updateAndAddDisplayable(e[n],null,t);i.length=this._displayListLen,ma(i,sf)},r.prototype._updateAndAddDisplayable=function(t,e,i){if(!(t.ignore&&!i)){t.beforeUpdate(),t.update(),t.afterUpdate();var n=t.getClipPath();if(t.ignoreClip)e=null;else if(n){e?e=e.slice():e=[];for(var a=n,o=t;a;)a.parent=o,a.updateTransform(),e.push(a),o=a,a=a.getClipPath()}if(t.childrenRef){for(var s=t.childrenRef(),u=0;u<s.length;u++){var l=s[u];t.__dirty&&(l.__dirty|=we),this._updateAndAddDisplayable(l,e,i)}t.__dirty=0}else{var f=t;e&&e.length?f.__clipPaths=e:f.__clipPaths&&f.__clipPaths.length>0&&(f.__clipPaths=[]),isNaN(f.z)&&(zo(),f.z=0),isNaN(f.z2)&&(zo(),f.z2=0),isNaN(f.zlevel)&&(zo(),f.zlevel=0),this._displayList[this._displayListLen++]=f}var h=t.getDecalElement&&t.getDecalElement();h&&this._updateAndAddDisplayable(h,e,i);var c=t.getTextGuideLine();c&&this._updateAndAddDisplayable(c,e,i);var v=t.getTextContent();v&&this._updateAndAddDisplayable(v,e,i)}},r.prototype.addRoot=function(t){t.__zr&&t.__zr.storage===this||this._roots.push(t)},r.prototype.delRoot=function(t){if(t instanceof Array){for(var e=0,i=t.length;e<i;e++)this.delRoot(t[e]);return}var n=at(this._roots,t);n>=0&&this._roots.splice(n,1)},r.prototype.delAllRoots=function(){this._roots=[],this._displayList=[],this._displayListLen=0},r.prototype.getRoots=function(){return this._roots},r.prototype.dispose=function(){this._displayList=null,this._roots=null},r}(),Ba;Ba=H.hasGlobalWindow&&(window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.msRequestAnimationFrame&&window.msRequestAnimationFrame.bind(window)||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(r){return setTimeout(r,16)};var ji={linear:function(r){return r},quadraticIn:function(r){return r*r},quadraticOut:function(r){return r*(2-r)},quadraticInOut:function(r){return(r*=2)<1?.5*r*r:-.5*(--r*(r-2)-1)},cubicIn:function(r){return r*r*r},cubicOut:function(r){return--r*r*r+1},cubicInOut:function(r){return(r*=2)<1?.5*r*r*r:.5*((r-=2)*r*r+2)},quarticIn:function(r){return r*r*r*r},quarticOut:function(r){return 1- --r*r*r*r},quarticInOut:function(r){return(r*=2)<1?.5*r*r*r*r:-.5*((r-=2)*r*r*r-2)},quinticIn:function(r){return r*r*r*r*r},quinticOut:function(r){return--r*r*r*r*r+1},quinticInOut:function(r){return(r*=2)<1?.5*r*r*r*r*r:.5*((r-=2)*r*r*r*r+2)},sinusoidalIn:function(r){return 1-Math.cos(r*Math.PI/2)},sinusoidalOut:function(r){return Math.sin(r*Math.PI/2)},sinusoidalInOut:function(r){return .5*(1-Math.cos(Math.PI*r))},exponentialIn:function(r){return r===0?0:Math.pow(1024,r-1)},exponentialOut:function(r){return r===1?1:1-Math.pow(2,-10*r)},exponentialInOut:function(r){return r===0?0:r===1?1:(r*=2)<1?.5*Math.pow(1024,r-1):.5*(-Math.pow(2,-10*(r-1))+2)},circularIn:function(r){return 1-Math.sqrt(1-r*r)},circularOut:function(r){return Math.sqrt(1- --r*r)},circularInOut:function(r){return(r*=2)<1?-.5*(Math.sqrt(1-r*r)-1):.5*(Math.sqrt(1-(r-=2)*r)+1)},elasticIn:function(r){var t,e=.1,i=.4;return r===0?0:r===1?1:(!e||e<1?(e=1,t=i/4):t=i*Math.asin(1/e)/(2*Math.PI),-(e*Math.pow(2,10*(r-=1))*Math.sin((r-t)*(2*Math.PI)/i)))},elasticOut:function(r){var t,e=.1,i=.4;return r===0?0:r===1?1:(!e||e<1?(e=1,t=i/4):t=i*Math.asin(1/e)/(2*Math.PI),e*Math.pow(2,-10*r)*Math.sin((r-t)*(2*Math.PI)/i)+1)},elasticInOut:function(r){var t,e=.1,i=.4;return r===0?0:r===1?1:(!e||e<1?(e=1,t=i/4):t=i*Math.asin(1/e)/(2*Math.PI),(r*=2)<1?-.5*(e*Math.pow(2,10*(r-=1))*Math.sin((r-t)*(2*Math.PI)/i)):e*Math.pow(2,-10*(r-=1))*Math.sin((r-t)*(2*Math.PI)/i)*.5+1)},backIn:function(r){var t=1.70158;return r*r*((t+1)*r-t)},backOut:function(r){var t=1.70158;return--r*r*((t+1)*r+t)+1},backInOut:function(r){var t=2.5949095;return(r*=2)<1?.5*(r*r*((t+1)*r-t)):.5*((r-=2)*r*((t+1)*r+t)+2)},bounceIn:function(r){return 1-ji.bounceOut(1-r)},bounceOut:function(r){return r<1/2.75?7.5625*r*r:r<2/2.75?7.5625*(r-=1.5/2.75)*r+.75:r<2.5/2.75?7.5625*(r-=2.25/2.75)*r+.9375:7.5625*(r-=2.625/2.75)*r+.984375},bounceInOut:function(r){return r<.5?ji.bounceIn(r*2)*.5:ji.bounceOut(r*2-1)*.5+.5}},Bn=Math.pow,$e=Math.sqrt,Sc=1e-8,Tc=1e-4,uf=$e(3),Nn=1/3,ge=mi(),Qt=mi(),fi=mi();function Ue(r){return r>-1e-8&&r<Sc}function bc(r){return r>Sc||r<-1e-8}function gt(r,t,e,i,n){var a=1-n;return a*a*(a*r+3*n*t)+n*n*(n*i+3*a*e)}function lf(r,t,e,i,n){var a=1-n;return 3*(((t-r)*a+2*(e-t)*n)*a+(i-e)*n*n)}function xc(r,t,e,i,n,a){var o=i+3*(t-e)-r,s=3*(e-t*2+r),u=3*(t-r),l=r-n,f=s*s-3*o*u,h=s*u-9*o*l,c=u*u-3*s*l,v=0;if(Ue(f)&&Ue(h))if(Ue(s))a[0]=0;else{var d=-u/s;d>=0&&d<=1&&(a[v++]=d)}else{var y=h*h-4*f*c;if(Ue(y)){var p=h/f,d=-s/o+p,g=-p/2;d>=0&&d<=1&&(a[v++]=d),g>=0&&g<=1&&(a[v++]=g)}else if(y>0){var _=$e(y),m=f*s+1.5*o*(-h+_),w=f*s+1.5*o*(-h-_);m<0?m=-Bn(-m,Nn):m=Bn(m,Nn),w<0?w=-Bn(-w,Nn):w=Bn(w,Nn);var d=(-s-(m+w))/(3*o);d>=0&&d<=1&&(a[v++]=d)}else{var T=(2*f*s-3*o*h)/(2*$e(f*f*f)),S=Math.acos(T)/3,b=$e(f),M=Math.cos(S),d=(-s-2*b*M)/(3*o),g=(-s+b*(M+uf*Math.sin(S)))/(3*o),x=(-s+b*(M-uf*Math.sin(S)))/(3*o);d>=0&&d<=1&&(a[v++]=d),g>=0&&g<=1&&(a[v++]=g),x>=0&&x<=1&&(a[v++]=x)}}return v}function Cc(r,t,e,i,n){var a=6*e-12*t+6*r,o=9*t+3*i-3*r-9*e,s=3*t-3*r,u=0;if(Ue(o)){if(bc(a)){var l=-s/a;l>=0&&l<=1&&(n[u++]=l)}}else{var f=a*a-4*o*s;if(Ue(f))n[0]=-a/(2*o);else if(f>0){var h=$e(f),l=(-a+h)/(2*o),c=(-a-h)/(2*o);l>=0&&l<=1&&(n[u++]=l),c>=0&&c<=1&&(n[u++]=c)}}return u}function Na(r,t,e,i,n,a){var o=(t-r)*n+r,s=(e-t)*n+t,u=(i-e)*n+e,l=(s-o)*n+o,f=(u-s)*n+s,h=(f-l)*n+l;a[0]=r,a[1]=o,a[2]=l,a[3]=h,a[4]=h,a[5]=f,a[6]=u,a[7]=i}function ky(r,t,e,i,n,a,o,s,u,l,f){var h,c=.005,v=1/0,d,y,p,g;ge[0]=u,ge[1]=l;for(var _=0;_<1;_+=.05)Qt[0]=gt(r,e,n,o,_),Qt[1]=gt(t,i,a,s,_),p=ui(ge,Qt),p<v&&(h=_,v=p);v=1/0;for(var m=0;m<32&&!(c<Tc);m++)d=h-c,y=h+c,Qt[0]=gt(r,e,n,o,d),Qt[1]=gt(t,i,a,s,d),p=ui(Qt,ge),d>=0&&p<v?(h=d,v=p):(fi[0]=gt(r,e,n,o,y),fi[1]=gt(t,i,a,s,y),g=ui(fi,ge),y<=1&&g<v?(h=y,v=g):c*=.5);return f&&(f[0]=gt(r,e,n,o,h),f[1]=gt(t,i,a,s,h)),$e(v)}function By(r,t,e,i,n,a,o,s,u){for(var l=r,f=t,h=0,c=1/u,v=1;v<=u;v++){var d=v*c,y=gt(r,e,n,o,d),p=gt(t,i,a,s,d),g=y-l,_=p-f;h+=Math.sqrt(g*g+_*_),l=y,f=p}return h}function xt(r,t,e,i){var n=1-i;return n*(n*r+2*i*t)+i*i*e}function ff(r,t,e,i){return 2*((1-i)*(t-r)+i*(e-t))}function Ny(r,t,e,i,n){var a=r-2*t+e,o=2*(t-r),s=r-i,u=0;if(Ue(a)){if(bc(o)){var l=-s/o;l>=0&&l<=1&&(n[u++]=l)}}else{var f=o*o-4*a*s;if(Ue(f)){var l=-o/(2*a);l>=0&&l<=1&&(n[u++]=l)}else if(f>0){var h=$e(f),l=(-o+h)/(2*a),c=(-o-h)/(2*a);l>=0&&l<=1&&(n[u++]=l),c>=0&&c<=1&&(n[u++]=c)}}return u}function Dc(r,t,e){var i=r+e-2*t;return i===0?.5:(r-t)/i}function Fa(r,t,e,i,n){var a=(t-r)*i+r,o=(e-t)*i+t,s=(o-a)*i+a;n[0]=r,n[1]=a,n[2]=s,n[3]=s,n[4]=o,n[5]=e}function Fy(r,t,e,i,n,a,o,s,u){var l,f=.005,h=1/0;ge[0]=o,ge[1]=s;for(var c=0;c<1;c+=.05){Qt[0]=xt(r,e,n,c),Qt[1]=xt(t,i,a,c);var v=ui(ge,Qt);v<h&&(l=c,h=v)}h=1/0;for(var d=0;d<32&&!(f<Tc);d++){var y=l-f,p=l+f;Qt[0]=xt(r,e,n,y),Qt[1]=xt(t,i,a,y);var v=ui(Qt,ge);if(y>=0&&v<h)l=y,h=v;else{fi[0]=xt(r,e,n,p),fi[1]=xt(t,i,a,p);var g=ui(fi,ge);p<=1&&g<h?(l=p,h=g):f*=.5}}return u&&(u[0]=xt(r,e,n,l),u[1]=xt(t,i,a,l)),$e(h)}function zy(r,t,e,i,n,a,o){for(var s=r,u=t,l=0,f=1/o,h=1;h<=o;h++){var c=h*f,v=xt(r,e,n,c),d=xt(t,i,a,c),y=v-s,p=d-u;l+=Math.sqrt(y*y+p*p),s=v,u=d}return l}var Hy=/cubic-bezier\(([0-9,\.e ]+)\)/;function Mc(r){var t=r&&Hy.exec(r);if(t){var e=t[1].split(","),i=+ye(e[0]),n=+ye(e[1]),a=+ye(e[2]),o=+ye(e[3]);if(isNaN(i+n+a+o))return;var s=[];return function(u){return u<=0?0:u>=1?1:xc(0,i,a,1,u,s)&&gt(0,n,o,1,s[0])}}}var Gy=function(){function r(t){this._inited=!1,this._startTime=0,this._pausedTime=0,this._paused=!1,this._life=t.life||1e3,this._delay=t.delay||0,this.loop=t.loop||!1,this.onframe=t.onframe||Ft,this.ondestroy=t.ondestroy||Ft,this.onrestart=t.onrestart||Ft,t.easing&&this.setEasing(t.easing)}return r.prototype.step=function(t,e){if(this._inited||(this._startTime=t+this._delay,this._inited=!0),this._paused){this._pausedTime+=e;return}var i=this._life,n=t-this._startTime-this._pausedTime,a=n/i;a<0&&(a=0),a=Math.min(a,1);var o=this.easingFunc,s=o?o(a):a;if(this.onframe(s),a===1)if(this.loop){var u=n%i;this._startTime=t-u,this._pausedTime=0,this.onrestart()}else return!0;return!1},r.prototype.pause=function(){this._paused=!0},r.prototype.resume=function(){this._paused=!1},r.prototype.setEasing=function(t){this.easing=t,this.easingFunc=K(t)?t:ji[t]||Mc(t)},r}(),Ac=function(){function r(t){this.value=t}return r}(),Vy=function(){function r(){this._len=0}return r.prototype.insert=function(t){var e=new Ac(t);return this.insertEntry(e),e},r.prototype.insertEntry=function(t){this.head?(this.tail.next=t,t.prev=this.tail,t.next=null,this.tail=t):this.head=this.tail=t,this._len++},r.prototype.remove=function(t){var e=t.prev,i=t.next;e?e.next=i:this.head=i,i?i.prev=e:this.tail=e,t.next=t.prev=null,this._len--},r.prototype.len=function(){return this._len},r.prototype.clear=function(){this.head=this.tail=null,this._len=0},r}(),Dn=function(){function r(t){this._list=new Vy,this._maxSize=10,this._map={},this._maxSize=t}return r.prototype.put=function(t,e){var i=this._list,n=this._map,a=null;if(n[t]==null){var o=i.len(),s=this._lastRemovedEntry;if(o>=this._maxSize&&o>0){var u=i.head;i.remove(u),delete n[u.key],a=u.value,this._lastRemovedEntry=u}s?s.value=e:s=new Ac(e),s.key=t,i.insertEntry(s),n[t]=s}return a},r.prototype.get=function(t){var e=this._map[t],i=this._list;if(e!=null)return e!==i.tail&&(i.remove(e),i.insertEntry(e)),e.value},r.prototype.clear=function(){this._list.clear(),this._map={}},r.prototype.len=function(){return this._list.len()},r}(),hf={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]};function ae(r){return r=Math.round(r),r<0?0:r>255?255:r}function Wy(r){return r=Math.round(r),r<0?0:r>360?360:r}function cn(r){return r<0?0:r>1?1:r}function Ho(r){var t=r;return t.length&&t.charAt(t.length-1)==="%"?ae(parseFloat(t)/100*255):ae(parseInt(t,10))}function Rr(r){var t=r;return t.length&&t.charAt(t.length-1)==="%"?cn(parseFloat(t)/100):cn(parseFloat(t))}function Go(r,t,e){return e<0?e+=1:e>1&&(e-=1),e*6<1?r+(t-r)*e*6:e*2<1?t:e*3<2?r+(t-r)*(2/3-e)*6:r}function Ye(r,t,e){return r+(t-r)*e}function $t(r,t,e,i,n){return r[0]=t,r[1]=e,r[2]=i,r[3]=n,r}function Qs(r,t){return r[0]=t[0],r[1]=t[1],r[2]=t[2],r[3]=t[3],r}var Pc=new Dn(20),Fn=null;function Wr(r,t){Fn&&Qs(Fn,t),Fn=Pc.put(r,Fn||t.slice())}function jt(r,t){if(r){t=t||[];var e=Pc.get(r);if(e)return Qs(t,e);r=r+"";var i=r.replace(/ /g,"").toLowerCase();if(i in hf)return Qs(t,hf[i]),Wr(r,t),t;var n=i.length;if(i.charAt(0)==="#"){if(n===4||n===5){var a=parseInt(i.slice(1,4),16);if(!(a>=0&&a<=4095)){$t(t,0,0,0,1);return}return $t(t,(a&3840)>>4|(a&3840)>>8,a&240|(a&240)>>4,a&15|(a&15)<<4,n===5?parseInt(i.slice(4),16)/15:1),Wr(r,t),t}else if(n===7||n===9){var a=parseInt(i.slice(1,7),16);if(!(a>=0&&a<=16777215)){$t(t,0,0,0,1);return}return $t(t,(a&16711680)>>16,(a&65280)>>8,a&255,n===9?parseInt(i.slice(7),16)/255:1),Wr(r,t),t}return}var o=i.indexOf("("),s=i.indexOf(")");if(o!==-1&&s+1===n){var u=i.substr(0,o),l=i.substr(o+1,s-(o+1)).split(","),f=1;switch(u){case"rgba":if(l.length!==4)return l.length===3?$t(t,+l[0],+l[1],+l[2],1):$t(t,0,0,0,1);f=Rr(l.pop());case"rgb":if(l.length>=3)return $t(t,Ho(l[0]),Ho(l[1]),Ho(l[2]),l.length===3?f:Rr(l[3])),Wr(r,t),t;$t(t,0,0,0,1);return;case"hsla":if(l.length!==4){$t(t,0,0,0,1);return}return l[3]=Rr(l[3]),Js(l,t),Wr(r,t),t;case"hsl":if(l.length!==3){$t(t,0,0,0,1);return}return Js(l,t),Wr(r,t),t;default:return}}$t(t,0,0,0,1)}}function Js(r,t){var e=(parseFloat(r[0])%360+360)%360/360,i=Rr(r[1]),n=Rr(r[2]),a=n<=.5?n*(i+1):n+i-n*i,o=n*2-a;return t=t||[],$t(t,ae(Go(o,a,e+1/3)*255),ae(Go(o,a,e)*255),ae(Go(o,a,e-1/3)*255),1),r.length===4&&(t[3]=r[3]),t}function Uy(r){if(r){var t=r[0]/255,e=r[1]/255,i=r[2]/255,n=Math.min(t,e,i),a=Math.max(t,e,i),o=a-n,s=(a+n)/2,u,l;if(o===0)u=0,l=0;else{s<.5?l=o/(a+n):l=o/(2-a-n);var f=((a-t)/6+o/2)/o,h=((a-e)/6+o/2)/o,c=((a-i)/6+o/2)/o;t===a?u=c-h:e===a?u=1/3+f-c:i===a&&(u=2/3+h-f),u<0&&(u+=1),u>1&&(u-=1)}var v=[u*360,l,s];return r[3]!=null&&v.push(r[3]),v}}function vf(r,t){var e=jt(r);if(e){for(var i=0;i<3;i++)t<0?e[i]=e[i]*(1-t)|0:e[i]=(255-e[i])*t+e[i]|0,e[i]>255?e[i]=255:e[i]<0&&(e[i]=0);return wi(e,e.length===4?"rgba":"rgb")}}function iC(r,t,e){if(!(!(t&&t.length)||!(r>=0&&r<=1))){e=e||[];var i=r*(t.length-1),n=Math.floor(i),a=Math.ceil(i),o=t[n],s=t[a],u=i-n;return e[0]=ae(Ye(o[0],s[0],u)),e[1]=ae(Ye(o[1],s[1],u)),e[2]=ae(Ye(o[2],s[2],u)),e[3]=cn(Ye(o[3],s[3],u)),e}}function nC(r,t,e){if(!(!(t&&t.length)||!(r>=0&&r<=1))){var i=r*(t.length-1),n=Math.floor(i),a=Math.ceil(i),o=jt(t[n]),s=jt(t[a]),u=i-n,l=wi([ae(Ye(o[0],s[0],u)),ae(Ye(o[1],s[1],u)),ae(Ye(o[2],s[2],u)),cn(Ye(o[3],s[3],u))],"rgba");return e?{color:l,leftIndex:n,rightIndex:a,value:i}:l}}function aC(r,t,e,i){var n=jt(r);if(r)return n=Uy(n),t!=null&&(n[0]=Wy(t)),e!=null&&(n[1]=Rr(e)),i!=null&&(n[2]=Rr(i)),wi(Js(n),"rgba")}function oC(r,t){var e=jt(r);if(e&&t!=null)return e[3]=cn(t),wi(e,"rgba")}function wi(r,t){if(!(!r||!r.length)){var e=r[0]+","+r[1]+","+r[2];return(t==="rgba"||t==="hsva"||t==="hsla")&&(e+=","+r[3]),t+"("+e+")"}}function za(r,t){var e=jt(r);return e?(.299*e[0]+.587*e[1]+.114*e[2])*e[3]/255+(1-e[3])*t:0}var cf=new Dn(100);function df(r){if(B(r)){var t=cf.get(r);return t||(t=vf(r,-.1),cf.put(r,t)),t}else if(io(r)){var e=O({},r);return e.colorStops=Y(r.colorStops,function(i){return{offset:i.offset,color:vf(i.color,-.1)}}),e}return r}var Ha=Math.round;function sC(r){var t;if(!r||r==="transparent")r="none";else if(typeof r=="string"&&r.indexOf("rgba")>-1){var e=jt(r);e&&(r="rgb("+e[0]+","+e[1]+","+e[2]+")",t=e[3])}return{color:r,opacity:t??1}}var Yy=1e-4;function uC(r){return r<Yy&&r>-1e-4}function zn(r){return Ha(r*1e3)/1e3}function pf(r){return Ha(r*1e4)/1e4}function lC(r){return"matrix("+zn(r[0])+","+zn(r[1])+","+zn(r[2])+","+zn(r[3])+","+pf(r[4])+","+pf(r[5])+")"}var fC={left:"start",right:"end",center:"middle",middle:"middle"};function hC(r,t,e){return e==="top"?r+=t/2:e==="bottom"&&(r-=t/2),r}function vC(r){return r&&(r.shadowBlur||r.shadowOffsetX||r.shadowOffsetY)}function cC(r){var t=r.style,e=r.getGlobalScale();return[t.shadowColor,(t.shadowBlur||0).toFixed(2),(t.shadowOffsetX||0).toFixed(2),(t.shadowOffsetY||0).toFixed(2),e[0],e[1]].join(",")}function Xy(r){return r&&!!r.image}function $y(r){return r&&!!r.svgElement}function dC(r){return Xy(r)||$y(r)}function qy(r){return r.type==="linear"}function Zy(r){return r.type==="radial"}function pC(r){return r&&(r.type==="linear"||r.type==="radial")}function gC(r){return"url(#"+r+")"}function yC(r){var t=r.getGlobalScale(),e=Math.max(t[0],t[1]);return Math.max(Math.ceil(Math.log(e)/Math.log(10)),1)}function _C(r){var t=r.x||0,e=r.y||0,i=(r.rotation||0)*_a,n=W(r.scaleX,1),a=W(r.scaleY,1),o=r.skewX||0,s=r.skewY||0,u=[];return(t||e)&&u.push("translate("+t+"px,"+e+"px)"),i&&u.push("rotate("+i+")"),(n!==1||a!==1)&&u.push("scale("+n+","+a+")"),(o||s)&&u.push("skew("+Ha(o*_a)+"deg, "+Ha(s*_a)+"deg)"),u.join(" ")}var mC=function(){return H.hasGlobalWindow&&K(window.btoa)?function(r){return window.btoa(unescape(encodeURIComponent(r)))}:typeof Buffer<"u"?function(r){return Buffer.from(r).toString("base64")}:function(r){return null}}(),js=Array.prototype.slice;function Pe(r,t,e){return(t-r)*e+r}function Vo(r,t,e,i){for(var n=t.length,a=0;a<n;a++)r[a]=Pe(t[a],e[a],i);return r}function Ky(r,t,e,i){for(var n=t.length,a=n&&t[0].length,o=0;o<n;o++){r[o]||(r[o]=[]);for(var s=0;s<a;s++)r[o][s]=Pe(t[o][s],e[o][s],i)}return r}function Hn(r,t,e,i){for(var n=t.length,a=0;a<n;a++)r[a]=t[a]+e[a]*i;return r}function gf(r,t,e,i){for(var n=t.length,a=n&&t[0].length,o=0;o<n;o++){r[o]||(r[o]=[]);for(var s=0;s<a;s++)r[o][s]=t[o][s]+e[o][s]*i}return r}function Qy(r,t){for(var e=r.length,i=t.length,n=e>i?t:r,a=Math.min(e,i),o=n[a-1]||{color:[0,0,0,0],offset:0},s=a;s<Math.max(e,i);s++)n.push({offset:o.offset,color:o.color.slice()})}function Jy(r,t,e){var i=r,n=t;if(!(!i.push||!n.push)){var a=i.length,o=n.length;if(a!==o){var s=a>o;if(s)i.length=o;else for(var u=a;u<o;u++)i.push(e===1?n[u]:js.call(n[u]))}for(var l=i[0]&&i[0].length,u=0;u<i.length;u++)if(e===1)isNaN(i[u])&&(i[u]=n[u]);else for(var f=0;f<l;f++)isNaN(i[u][f])&&(i[u][f]=n[u][f])}}function Sa(r){if(zt(r)){var t=r.length;if(zt(r[0])){for(var e=[],i=0;i<t;i++)e.push(js.call(r[i]));return e}return js.call(r)}return r}function Ta(r){return r[0]=Math.floor(r[0])||0,r[1]=Math.floor(r[1])||0,r[2]=Math.floor(r[2])||0,r[3]=r[3]==null?1:r[3],"rgba("+r.join(",")+")"}function jy(r){return zt(r&&r[0])?2:1}var Gn=0,ba=1,Lc=2,Xi=3,tu=4,eu=5,yf=6;function _f(r){return r===tu||r===eu}function Vn(r){return r===ba||r===Lc}var Ai=[0,0,0,0],t_=function(){function r(t){this.keyframes=[],this.discrete=!1,this._invalid=!1,this._needsSort=!1,this._lastFr=0,this._lastFrP=0,this.propName=t}return r.prototype.isFinished=function(){return this._finished},r.prototype.setFinished=function(){this._finished=!0,this._additiveTrack&&this._additiveTrack.setFinished()},r.prototype.needsAnimate=function(){return this.keyframes.length>=1},r.prototype.getAdditiveTrack=function(){return this._additiveTrack},r.prototype.addKeyframe=function(t,e,i){this._needsSort=!0;var n=this.keyframes,a=n.length,o=!1,s=yf,u=e;if(zt(e)){var l=jy(e);s=l,(l===1&&!ut(e[0])||l===2&&!ut(e[0][0]))&&(o=!0)}else if(ut(e)&&!Oa(e))s=Gn;else if(B(e))if(!isNaN(+e))s=Gn;else{var f=jt(e);f&&(u=f,s=Xi)}else if(io(e)){var h=O({},u);h.colorStops=Y(e.colorStops,function(v){return{offset:v.offset,color:jt(v.color)}}),qy(e)?s=tu:Zy(e)&&(s=eu),u=h}a===0?this.valType=s:(s!==this.valType||s===yf)&&(o=!0),this.discrete=this.discrete||o;var c={time:t,value:u,rawValue:e,percent:0};return i&&(c.easing=i,c.easingFunc=K(i)?i:ji[i]||Mc(i)),n.push(c),c},r.prototype.prepare=function(t,e){var i=this.keyframes;this._needsSort&&i.sort(function(y,p){return y.time-p.time});for(var n=this.valType,a=i.length,o=i[a-1],s=this.discrete,u=Vn(n),l=_f(n),f=0;f<a;f++){var h=i[f],c=h.value,v=o.value;h.percent=h.time/t,s||(u&&f!==a-1?Jy(c,v,n):l&&Qy(c.colorStops,v.colorStops))}if(!s&&n!==eu&&e&&this.needsAnimate()&&e.needsAnimate()&&n===e.valType&&!e._finished){this._additiveTrack=e;for(var d=i[0].value,f=0;f<a;f++)n===Gn?i[f].additiveValue=i[f].value-d:n===Xi?i[f].additiveValue=Hn([],i[f].value,d,-1):Vn(n)&&(i[f].additiveValue=n===ba?Hn([],i[f].value,d,-1):gf([],i[f].value,d,-1))}},r.prototype.step=function(t,e){if(!this._finished){this._additiveTrack&&this._additiveTrack._finished&&(this._additiveTrack=null);var i=this._additiveTrack!=null,n=i?"additiveValue":"value",a=this.valType,o=this.keyframes,s=o.length,u=this.propName,l=a===Xi,f,h=this._lastFr,c=Math.min,v,d;if(s===1)v=d=o[0];else{if(e<0)f=0;else if(e<this._lastFrP){var y=c(h+1,s-1);for(f=y;f>=0&&!(o[f].percent<=e);f--);f=c(f,s-2)}else{for(f=h;f<s&&!(o[f].percent>e);f++);f=c(f-1,s-2)}d=o[f+1],v=o[f]}if(v&&d){this._lastFr=f,this._lastFrP=e;var p=d.percent-v.percent,g=p===0?1:c((e-v.percent)/p,1);d.easingFunc&&(g=d.easingFunc(g));var _=i?this._additiveValue:l?Ai:t[u];if((Vn(a)||l)&&!_&&(_=this._additiveValue=[]),this.discrete)t[u]=g<1?v.rawValue:d.rawValue;else if(Vn(a))a===ba?Vo(_,v[n],d[n],g):Ky(_,v[n],d[n],g);else if(_f(a)){var m=v[n],w=d[n],T=a===tu;t[u]={type:T?"linear":"radial",x:Pe(m.x,w.x,g),y:Pe(m.y,w.y,g),colorStops:Y(m.colorStops,function(b,M){var x=w.colorStops[M];return{offset:Pe(b.offset,x.offset,g),color:Ta(Vo([],b.color,x.color,g))}}),global:w.global},T?(t[u].x2=Pe(m.x2,w.x2,g),t[u].y2=Pe(m.y2,w.y2,g)):t[u].r=Pe(m.r,w.r,g)}else if(l)Vo(_,v[n],d[n],g),i||(t[u]=Ta(_));else{var S=Pe(v[n],d[n],g);i?this._additiveValue=S:t[u]=S}i&&this._addToTarget(t)}}},r.prototype._addToTarget=function(t){var e=this.valType,i=this.propName,n=this._additiveValue;e===Gn?t[i]=t[i]+n:e===Xi?(jt(t[i],Ai),Hn(Ai,Ai,n,1),t[i]=Ta(Ai)):e===ba?Hn(t[i],t[i],n,1):e===Lc&&gf(t[i],t[i],n,1)},r}(),Ju=function(){function r(t,e,i,n){if(this._tracks={},this._trackKeys=[],this._maxTime=0,this._started=0,this._clip=null,this._target=t,this._loop=e,e&&n){$u("Can' use additive animation on looped animation.");return}this._additiveAnimators=n,this._allowDiscrete=i}return r.prototype.getMaxTime=function(){return this._maxTime},r.prototype.getDelay=function(){return this._delay},r.prototype.getLoop=function(){return this._loop},r.prototype.getTarget=function(){return this._target},r.prototype.changeTarget=function(t){this._target=t},r.prototype.when=function(t,e,i){return this.whenWithKeys(t,e,ft(e),i)},r.prototype.whenWithKeys=function(t,e,i,n){for(var a=this._tracks,o=0;o<i.length;o++){var s=i[o],u=a[s];if(!u){u=a[s]=new t_(s);var l=void 0,f=this._getAdditiveTrack(s);if(f){var h=f.keyframes,c=h[h.length-1];l=c&&c.value,f.valType===Xi&&l&&(l=Ta(l))}else l=this._target[s];if(l==null)continue;t>0&&u.addKeyframe(0,Sa(l),n),this._trackKeys.push(s)}u.addKeyframe(t,Sa(e[s]),n)}return this._maxTime=Math.max(this._maxTime,t),this},r.prototype.pause=function(){this._clip.pause(),this._paused=!0},r.prototype.resume=function(){this._clip.resume(),this._paused=!1},r.prototype.isPaused=function(){return!!this._paused},r.prototype.duration=function(t){return this._maxTime=t,this._force=!0,this},r.prototype._doneCallback=function(){this._setTracksFinished(),this._clip=null;var t=this._doneCbs;if(t)for(var e=t.length,i=0;i<e;i++)t[i].call(this)},r.prototype._abortedCallback=function(){this._setTracksFinished();var t=this.animation,e=this._abortedCbs;if(t&&t.removeClip(this._clip),this._clip=null,e)for(var i=0;i<e.length;i++)e[i].call(this)},r.prototype._setTracksFinished=function(){for(var t=this._tracks,e=this._trackKeys,i=0;i<e.length;i++)t[e[i]].setFinished()},r.prototype._getAdditiveTrack=function(t){var e,i=this._additiveAnimators;if(i)for(var n=0;n<i.length;n++){var a=i[n].getTrack(t);a&&(e=a)}return e},r.prototype.start=function(t){if(!(this._started>0)){this._started=1;for(var e=this,i=[],n=this._maxTime||0,a=0;a<this._trackKeys.length;a++){var o=this._trackKeys[a],s=this._tracks[o],u=this._getAdditiveTrack(o),l=s.keyframes,f=l.length;if(s.prepare(n,u),s.needsAnimate())if(!this._allowDiscrete&&s.discrete){var h=l[f-1];h&&(e._target[s.propName]=h.rawValue),s.setFinished()}else i.push(s)}if(i.length||this._force){var c=new Gy({life:n,loop:this._loop,delay:this._delay||0,onframe:function(v){e._started=2;var d=e._additiveAnimators;if(d){for(var y=!1,p=0;p<d.length;p++)if(d[p]._clip){y=!0;break}y||(e._additiveAnimators=null)}for(var p=0;p<i.length;p++)i[p].step(e._target,v);var g=e._onframeCbs;if(g)for(var p=0;p<g.length;p++)g[p](e._target,v)},ondestroy:function(){e._doneCallback()}});this._clip=c,this.animation&&this.animation.addClip(c),t&&c.setEasing(t)}else this._doneCallback();return this}},r.prototype.stop=function(t){if(this._clip){var e=this._clip;t&&e.onframe(1),this._abortedCallback()}},r.prototype.delay=function(t){return this._delay=t,this},r.prototype.during=function(t){return t&&(this._onframeCbs||(this._onframeCbs=[]),this._onframeCbs.push(t)),this},r.prototype.done=function(t){return t&&(this._doneCbs||(this._doneCbs=[]),this._doneCbs.push(t)),this},r.prototype.aborted=function(t){return t&&(this._abortedCbs||(this._abortedCbs=[]),this._abortedCbs.push(t)),this},r.prototype.getClip=function(){return this._clip},r.prototype.getTrack=function(t){return this._tracks[t]},r.prototype.getTracks=function(){var t=this;return Y(this._trackKeys,function(e){return t._tracks[e]})},r.prototype.stopTracks=function(t,e){if(!t.length||!this._clip)return!0;for(var i=this._tracks,n=this._trackKeys,a=0;a<t.length;a++){var o=i[t[a]];o&&!o.isFinished()&&(e?o.step(this._target,1):this._started===1&&o.step(this._target,0),o.setFinished())}for(var s=!0,a=0;a<n.length;a++)if(!i[n[a]].isFinished()){s=!1;break}return s&&this._abortedCallback(),s},r.prototype.saveTo=function(t,e,i){if(t){e=e||this._trackKeys;for(var n=0;n<e.length;n++){var a=e[n],o=this._tracks[a];if(!(!o||o.isFinished())){var s=o.keyframes,u=s[i?0:s.length-1];u&&(t[a]=Sa(u.rawValue))}}}},r.prototype.__changeFinalValue=function(t,e){e=e||ft(t);for(var i=0;i<e.length;i++){var n=e[i],a=this._tracks[n];if(a){var o=a.keyframes;if(o.length>1){var s=o.pop();a.addKeyframe(s.time,t[n]),a.prepare(this._maxTime,a.getAdditiveTrack())}}}},r}();function oi(){return new Date().getTime()}var e_=function(r){N(t,r);function t(e){var i=r.call(this)||this;return i._running=!1,i._time=0,i._pausedTime=0,i._pauseStart=0,i._paused=!1,e=e||{},i.stage=e.stage||{},i}return t.prototype.addClip=function(e){e.animation&&this.removeClip(e),this._head?(this._tail.next=e,e.prev=this._tail,e.next=null,this._tail=e):this._head=this._tail=e,e.animation=this},t.prototype.addAnimator=function(e){e.animation=this;var i=e.getClip();i&&this.addClip(i)},t.prototype.removeClip=function(e){if(e.animation){var i=e.prev,n=e.next;i?i.next=n:this._head=n,n?n.prev=i:this._tail=i,e.next=e.prev=e.animation=null}},t.prototype.removeAnimator=function(e){var i=e.getClip();i&&this.removeClip(i),e.animation=null},t.prototype.update=function(e){for(var i=oi()-this._pausedTime,n=i-this._time,a=this._head;a;){var o=a.next,s=a.step(i,n);s&&(a.ondestroy(),this.removeClip(a)),a=o}this._time=i,e||(this.trigger("frame",n),this.stage.update&&this.stage.update())},t.prototype._startLoop=function(){var e=this;this._running=!0;function i(){e._running&&(Ba(i),!e._paused&&e.update())}Ba(i)},t.prototype.start=function(){this._running||(this._time=oi(),this._pausedTime=0,this._startLoop())},t.prototype.stop=function(){this._running=!1},t.prototype.pause=function(){this._paused||(this._pauseStart=oi(),this._paused=!0)},t.prototype.resume=function(){this._paused&&(this._pausedTime+=oi()-this._pauseStart,this._paused=!1)},t.prototype.clear=function(){for(var e=this._head;e;){var i=e.next;e.prev=e.next=e.animation=null,e=i}this._head=this._tail=null},t.prototype.isFinished=function(){return this._head==null},t.prototype.animate=function(e,i){i=i||{},this.start();var n=new Ju(e,i.loop);return this.addAnimator(n),n},t}(be),r_=300,Wo=H.domSupported,Uo=function(){var r=["click","dblclick","mousewheel","wheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],t=["touchstart","touchend","touchmove"],e={pointerdown:1,pointerup:1,pointermove:1,pointerout:1},i=Y(r,function(n){var a=n.replace("mouse","pointer");return e.hasOwnProperty(a)?a:n});return{mouse:r,touch:t,pointer:i}}(),mf={mouse:["mousemove","mouseup"],pointer:["pointermove","pointerup"]},wf=!1;function ru(r){var t=r.pointerType;return t==="pen"||t==="touch"}function i_(r){r.touching=!0,r.touchTimer!=null&&(clearTimeout(r.touchTimer),r.touchTimer=null),r.touchTimer=setTimeout(function(){r.touching=!1,r.touchTimer=null},700)}function Yo(r){r&&(r.zrByTouch=!0)}function n_(r,t){return qt(r.dom,new a_(r,t),!0)}function Ic(r,t){for(var e=t,i=!1;e&&e.nodeType!==9&&!(i=e.domBelongToZr||e!==t&&e===r.painterRoot);)e=e.parentNode;return i}var a_=function(){function r(t,e){this.stopPropagation=Ft,this.stopImmediatePropagation=Ft,this.preventDefault=Ft,this.type=e.type,this.target=this.currentTarget=t.dom,this.pointerType=e.pointerType,this.clientX=e.clientX,this.clientY=e.clientY}return r}(),ie={mousedown:function(r){r=qt(this.dom,r),this.__mayPointerCapture=[r.zrX,r.zrY],this.trigger("mousedown",r)},mousemove:function(r){r=qt(this.dom,r);var t=this.__mayPointerCapture;t&&(r.zrX!==t[0]||r.zrY!==t[1])&&this.__togglePointerCapture(!0),this.trigger("mousemove",r)},mouseup:function(r){r=qt(this.dom,r),this.__togglePointerCapture(!1),this.trigger("mouseup",r)},mouseout:function(r){r=qt(this.dom,r);var t=r.toElement||r.relatedTarget;Ic(this,t)||(this.__pointerCapturing&&(r.zrEventControl="no_globalout"),this.trigger("mouseout",r))},wheel:function(r){wf=!0,r=qt(this.dom,r),this.trigger("mousewheel",r)},mousewheel:function(r){wf||(r=qt(this.dom,r),this.trigger("mousewheel",r))},touchstart:function(r){r=qt(this.dom,r),Yo(r),this.__lastTouchMoment=new Date,this.handler.processGesture(r,"start"),ie.mousemove.call(this,r),ie.mousedown.call(this,r)},touchmove:function(r){r=qt(this.dom,r),Yo(r),this.handler.processGesture(r,"change"),ie.mousemove.call(this,r)},touchend:function(r){r=qt(this.dom,r),Yo(r),this.handler.processGesture(r,"end"),ie.mouseup.call(this,r),+new Date-+this.__lastTouchMoment<r_&&ie.click.call(this,r)},pointerdown:function(r){ie.mousedown.call(this,r)},pointermove:function(r){ru(r)||ie.mousemove.call(this,r)},pointerup:function(r){ie.mouseup.call(this,r)},pointerout:function(r){ru(r)||ie.mouseout.call(this,r)}};D(["click","dblclick","contextmenu"],function(r){ie[r]=function(t){t=qt(this.dom,t),this.trigger(r,t)}});var iu={pointermove:function(r){ru(r)||iu.mousemove.call(this,r)},pointerup:function(r){iu.mouseup.call(this,r)},mousemove:function(r){this.trigger("mousemove",r)},mouseup:function(r){var t=this.__pointerCapturing;this.__togglePointerCapture(!1),this.trigger("mouseup",r),t&&(r.zrEventControl="only_globalout",this.trigger("mouseout",r))}};function o_(r,t){var e=t.domHandlers;H.pointerEventsSupported?D(Uo.pointer,function(i){xa(t,i,function(n){e[i].call(r,n)})}):(H.touchEventsSupported&&D(Uo.touch,function(i){xa(t,i,function(n){e[i].call(r,n),i_(t)})}),D(Uo.mouse,function(i){xa(t,i,function(n){n=Zu(n),t.touching||e[i].call(r,n)})}))}function s_(r,t){H.pointerEventsSupported?D(mf.pointer,e):H.touchEventsSupported||D(mf.mouse,e);function e(i){function n(a){a=Zu(a),Ic(r,a.target)||(a=n_(r,a),t.domHandlers[i].call(r,a))}xa(t,i,n,{capture:!0})}}function xa(r,t,e,i){r.mounted[t]=e,r.listenerOpts[t]=i,Sy(r.domTarget,t,e,i)}function Xo(r){var t=r.mounted;for(var e in t)t.hasOwnProperty(e)&&Ty(r.domTarget,e,t[e],r.listenerOpts[e]);r.mounted={}}var Sf=function(){function r(t,e){this.mounted={},this.listenerOpts={},this.touching=!1,this.domTarget=t,this.domHandlers=e}return r}(),u_=function(r){N(t,r);function t(e,i){var n=r.call(this)||this;return n.__pointerCapturing=!1,n.dom=e,n.painterRoot=i,n._localHandlerScope=new Sf(e,ie),Wo&&(n._globalHandlerScope=new Sf(document,iu)),o_(n,n._localHandlerScope),n}return t.prototype.dispose=function(){Xo(this._localHandlerScope),Wo&&Xo(this._globalHandlerScope)},t.prototype.setCursor=function(e){this.dom.style&&(this.dom.style.cursor=e||"default")},t.prototype.__togglePointerCapture=function(e){if(this.__mayPointerCapture=null,Wo&&+this.__pointerCapturing^+e){this.__pointerCapturing=e;var i=this._globalHandlerScope;e?s_(this,i):Xo(i)}},t}(be),Rc=1;H.hasGlobalWindow&&(Rc=Math.max(window.devicePixelRatio||window.screen&&window.screen.deviceXDPI/window.screen.logicalXDPI||1,1));var Ga=Rc,nu=.4,au="#333",ou="#ccc",l_="#eee",Tf=Ku,f_=5e-5;function ur(r){return r>f_||r<-5e-5}var lr=[],Ur=[],$o=Ir(),qo=Math.abs,ju=function(){function r(){}return r.prototype.getLocalTransform=function(t){return r.getLocalTransform(this,t)},r.prototype.setPosition=function(t){this.x=t[0],this.y=t[1]},r.prototype.setScale=function(t){this.scaleX=t[0],this.scaleY=t[1]},r.prototype.setSkew=function(t){this.skewX=t[0],this.skewY=t[1]},r.prototype.setOrigin=function(t){this.originX=t[0],this.originY=t[1]},r.prototype.needLocalTransform=function(){return ur(this.rotation)||ur(this.x)||ur(this.y)||ur(this.scaleX-1)||ur(this.scaleY-1)||ur(this.skewX)||ur(this.skewY)},r.prototype.updateTransform=function(){var t=this.parent&&this.parent.transform,e=this.needLocalTransform(),i=this.transform;if(!(e||t)){i&&(Tf(i),this.invTransform=null);return}i=i||Ir(),e?this.getLocalTransform(i):Tf(i),t&&(e?li(i,t,i):pc(i,t)),this.transform=i,this._resolveGlobalScaleRatio(i)},r.prototype._resolveGlobalScaleRatio=function(t){var e=this.globalScaleRatio;if(e!=null&&e!==1){this.getGlobalScale(lr);var i=lr[0]<0?-1:1,n=lr[1]<0?-1:1,a=((lr[0]-i)*e+i)/lr[0]||0,o=((lr[1]-n)*e+n)/lr[1]||0;t[0]*=a,t[1]*=a,t[2]*=o,t[3]*=o}this.invTransform=this.invTransform||Ir(),gc(this.invTransform,t)},r.prototype.getComputedTransform=function(){for(var t=this,e=[];t;)e.push(t),t=t.parent;for(;t=e.pop();)t.updateTransform();return this.transform},r.prototype.setLocalTransform=function(t){if(t){var e=t[0]*t[0]+t[1]*t[1],i=t[2]*t[2]+t[3]*t[3],n=Math.atan2(t[1],t[0]),a=Math.PI/2+n-Math.atan2(t[3],t[2]);i=Math.sqrt(i)*Math.cos(a),e=Math.sqrt(e),this.skewX=a,this.skewY=0,this.rotation=-n,this.x=+t[4],this.y=+t[5],this.scaleX=e,this.scaleY=i,this.originX=0,this.originY=0}},r.prototype.decomposeTransform=function(){if(this.transform){var t=this.parent,e=this.transform;t&&t.transform&&(t.invTransform=t.invTransform||Ir(),li(Ur,t.invTransform,e),e=Ur);var i=this.originX,n=this.originY;(i||n)&&($o[4]=i,$o[5]=n,li(Ur,e,$o),Ur[4]-=i,Ur[5]-=n,e=Ur),this.setLocalTransform(e)}},r.prototype.getGlobalScale=function(t){var e=this.transform;return t=t||[],e?(t[0]=Math.sqrt(e[0]*e[0]+e[1]*e[1]),t[1]=Math.sqrt(e[2]*e[2]+e[3]*e[3]),e[0]<0&&(t[0]=-t[0]),e[3]<0&&(t[1]=-t[1]),t):(t[0]=1,t[1]=1,t)},r.prototype.transformCoordToLocal=function(t,e){var i=[t,e],n=this.invTransform;return n&&me(i,i,n),i},r.prototype.transformCoordToGlobal=function(t,e){var i=[t,e],n=this.transform;return n&&me(i,i,n),i},r.prototype.getLineScale=function(){var t=this.transform;return t&&qo(t[0]-1)>1e-10&&qo(t[3]-1)>1e-10?Math.sqrt(qo(t[0]*t[3]-t[2]*t[1])):1},r.prototype.copyTransform=function(t){h_(this,t)},r.getLocalTransform=function(t,e){e=e||[];var i=t.originX||0,n=t.originY||0,a=t.scaleX,o=t.scaleY,s=t.anchorX,u=t.anchorY,l=t.rotation||0,f=t.x,h=t.y,c=t.skewX?Math.tan(t.skewX):0,v=t.skewY?Math.tan(-t.skewY):0;if(i||n||s||u){var d=i+s,y=n+u;e[4]=-d*a-c*y*o,e[5]=-y*o-v*d*a}else e[4]=e[5]=0;return e[0]=a,e[3]=o,e[1]=v*a,e[2]=c*o,l&&Qu(e,e,l),e[4]+=i+f,e[5]+=n+h,e},r.initDefaultProps=function(){var t=r.prototype;t.scaleX=t.scaleY=t.globalScaleRatio=1,t.x=t.y=t.originX=t.originY=t.skewX=t.skewY=t.rotation=t.anchorX=t.anchorY=0}(),r}(),dn=["x","y","originX","originY","anchorX","anchorY","rotation","scaleX","scaleY","skewX","skewY"];function h_(r,t){for(var e=0;e<dn.length;e++){var i=dn[e];r[i]=t[i]}}var bf={};function Wt(r,t){t=t||Nr;var e=bf[t];e||(e=bf[t]=new Dn(500));var i=e.get(r);return i==null&&(i=yi.measureText(r,t).width,e.put(r,i)),i}function xf(r,t,e,i){var n=Wt(r,t),a=tl(t),o=$i(0,n,e),s=ii(0,a,i),u=new J(o,s,n,a);return u}function Ec(r,t,e,i){var n=((r||"")+"").split(`
`),a=n.length;if(a===1)return xf(n[0],t,e,i);for(var o=new J(0,0,0,0),s=0;s<n.length;s++){var u=xf(n[s],t,e,i);s===0?o.copy(u):o.union(u)}return o}function $i(r,t,e){return e==="right"?r-=t:e==="center"&&(r-=t/2),r}function ii(r,t,e){return e==="middle"?r-=t/2:e==="bottom"&&(r-=t),r}function tl(r){return Wt("国",r)}function Qe(r,t){return typeof r=="string"?r.lastIndexOf("%")>=0?parseFloat(r)/100*t:parseFloat(r):r}function Oc(r,t,e){var i=t.position||"inside",n=t.distance!=null?t.distance:5,a=e.height,o=e.width,s=a/2,u=e.x,l=e.y,f="left",h="top";if(i instanceof Array)u+=Qe(i[0],e.width),l+=Qe(i[1],e.height),f=null,h=null;else switch(i){case"left":u-=n,l+=s,f="right",h="middle";break;case"right":u+=n+o,l+=s,h="middle";break;case"top":u+=o/2,l-=n,f="center",h="bottom";break;case"bottom":u+=o/2,l+=a+n,f="center";break;case"inside":u+=o/2,l+=s,f="center",h="middle";break;case"insideLeft":u+=n,l+=s,h="middle";break;case"insideRight":u+=o-n,l+=s,f="right",h="middle";break;case"insideTop":u+=o/2,l+=n,f="center";break;case"insideBottom":u+=o/2,l+=a-n,f="center",h="bottom";break;case"insideTopLeft":u+=n,l+=n;break;case"insideTopRight":u+=o-n,l+=n,f="right";break;case"insideBottomLeft":u+=n,l+=a-n,h="bottom";break;case"insideBottomRight":u+=o-n,l+=a-n,f="right",h="bottom";break}return r=r||{},r.x=u,r.y=l,r.align=f,r.verticalAlign=h,r}var Zo="__zr_normal__",Ko=dn.concat(["ignore"]),v_=Ze(dn,function(r,t){return r[t]=!0,r},{ignore:!1}),Yr={},c_=new J(0,0,0,0),ao=function(){function r(t){this.id=uc(),this.animators=[],this.currentStates=[],this.states={},this._init(t)}return r.prototype._init=function(t){this.attr(t)},r.prototype.drift=function(t,e,i){switch(this.draggable){case"horizontal":e=0;break;case"vertical":t=0;break}var n=this.transform;n||(n=this.transform=[1,0,0,1,0,0]),n[4]+=t,n[5]+=e,this.decomposeTransform(),this.markRedraw()},r.prototype.beforeUpdate=function(){},r.prototype.afterUpdate=function(){},r.prototype.update=function(){this.updateTransform(),this.__dirty&&this.updateInnerText()},r.prototype.updateInnerText=function(t){var e=this._textContent;if(e&&(!e.ignore||t)){this.textConfig||(this.textConfig={});var i=this.textConfig,n=i.local,a=e.innerTransformable,o=void 0,s=void 0,u=!1;a.parent=n?this:null;var l=!1;if(a.copyTransform(e),i.position!=null){var f=c_;i.layoutRect?f.copy(i.layoutRect):f.copy(this.getBoundingRect()),n||f.applyTransform(this.transform),this.calculateTextPosition?this.calculateTextPosition(Yr,i,f):Oc(Yr,i,f),a.x=Yr.x,a.y=Yr.y,o=Yr.align,s=Yr.verticalAlign;var h=i.origin;if(h&&i.rotation!=null){var c=void 0,v=void 0;h==="center"?(c=f.width*.5,v=f.height*.5):(c=Qe(h[0],f.width),v=Qe(h[1],f.height)),l=!0,a.originX=-a.x+c+(n?0:f.x),a.originY=-a.y+v+(n?0:f.y)}}i.rotation!=null&&(a.rotation=i.rotation);var d=i.offset;d&&(a.x+=d[0],a.y+=d[1],l||(a.originX=-d[0],a.originY=-d[1]));var y=i.inside==null?typeof i.position=="string"&&i.position.indexOf("inside")>=0:i.inside,p=this._innerTextDefaultStyle||(this._innerTextDefaultStyle={}),g=void 0,_=void 0,m=void 0;y&&this.canBeInsideText()?(g=i.insideFill,_=i.insideStroke,(g==null||g==="auto")&&(g=this.getInsideTextFill()),(_==null||_==="auto")&&(_=this.getInsideTextStroke(g),m=!0)):(g=i.outsideFill,_=i.outsideStroke,(g==null||g==="auto")&&(g=this.getOutsideFill()),(_==null||_==="auto")&&(_=this.getOutsideStroke(g),m=!0)),g=g||"#000",(g!==p.fill||_!==p.stroke||m!==p.autoStroke||o!==p.align||s!==p.verticalAlign)&&(u=!0,p.fill=g,p.stroke=_,p.autoStroke=m,p.align=o,p.verticalAlign=s,e.setDefaultTextStyle(p)),e.__dirty|=we,u&&e.dirtyStyle(!0)}},r.prototype.canBeInsideText=function(){return!0},r.prototype.getInsideTextFill=function(){return"#fff"},r.prototype.getInsideTextStroke=function(t){return"#000"},r.prototype.getOutsideFill=function(){return this.__zr&&this.__zr.isDarkMode()?ou:au},r.prototype.getOutsideStroke=function(t){var e=this.__zr&&this.__zr.getBackgroundColor(),i=typeof e=="string"&&jt(e);i||(i=[255,255,255,1]);for(var n=i[3],a=this.__zr.isDarkMode(),o=0;o<3;o++)i[o]=i[o]*n+(a?0:255)*(1-n);return i[3]=1,wi(i,"rgba")},r.prototype.traverse=function(t,e){},r.prototype.attrKV=function(t,e){t==="textConfig"?this.setTextConfig(e):t==="textContent"?this.setTextContent(e):t==="clipPath"?this.setClipPath(e):t==="extra"?(this.extra=this.extra||{},O(this.extra,e)):this[t]=e},r.prototype.hide=function(){this.ignore=!0,this.markRedraw()},r.prototype.show=function(){this.ignore=!1,this.markRedraw()},r.prototype.attr=function(t,e){if(typeof t=="string")this.attrKV(t,e);else if(F(t))for(var i=t,n=ft(i),a=0;a<n.length;a++){var o=n[a];this.attrKV(o,t[o])}return this.markRedraw(),this},r.prototype.saveCurrentToNormalState=function(t){this._innerSaveToNormal(t);for(var e=this._normalState,i=0;i<this.animators.length;i++){var n=this.animators[i],a=n.__fromStateTransition;if(!(n.getLoop()||a&&a!==Zo)){var o=n.targetName,s=o?e[o]:e;n.saveTo(s)}}},r.prototype._innerSaveToNormal=function(t){var e=this._normalState;e||(e=this._normalState={}),t.textConfig&&!e.textConfig&&(e.textConfig=this.textConfig),this._savePrimaryToNormal(t,e,Ko)},r.prototype._savePrimaryToNormal=function(t,e,i){for(var n=0;n<i.length;n++){var a=i[n];t[a]!=null&&!(a in e)&&(e[a]=this[a])}},r.prototype.hasState=function(){return this.currentStates.length>0},r.prototype.getState=function(t){return this.states[t]},r.prototype.ensureState=function(t){var e=this.states;return e[t]||(e[t]={}),e[t]},r.prototype.clearStates=function(t){this.useState(Zo,!1,t)},r.prototype.useState=function(t,e,i,n){var a=t===Zo,o=this.hasState();if(!(!o&&a)){var s=this.currentStates,u=this.stateTransition;if(!(at(s,t)>=0&&(e||s.length===1))){var l;if(this.stateProxy&&!a&&(l=this.stateProxy(t)),l||(l=this.states&&this.states[t]),!l&&!a){$u("State "+t+" not exists.");return}a||this.saveCurrentToNormalState(l);var f=!!(l&&l.hoverLayer||n);f&&this._toggleHoverLayerFlag(!0),this._applyStateObj(t,l,this._normalState,e,!i&&!this.__inHover&&u&&u.duration>0,u);var h=this._textContent,c=this._textGuide;return h&&h.useState(t,e,i,f),c&&c.useState(t,e,i,f),a?(this.currentStates=[],this._normalState={}):e?this.currentStates.push(t):this.currentStates=[t],this._updateAnimationTargets(),this.markRedraw(),!f&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=-2),l}}},r.prototype.useStates=function(t,e,i){if(!t.length)this.clearStates();else{var n=[],a=this.currentStates,o=t.length,s=o===a.length;if(s){for(var u=0;u<o;u++)if(t[u]!==a[u]){s=!1;break}}if(s)return;for(var u=0;u<o;u++){var l=t[u],f=void 0;this.stateProxy&&(f=this.stateProxy(l,t)),f||(f=this.states[l]),f&&n.push(f)}var h=n[o-1],c=!!(h&&h.hoverLayer||i);c&&this._toggleHoverLayerFlag(!0);var v=this._mergeStates(n),d=this.stateTransition;this.saveCurrentToNormalState(v),this._applyStateObj(t.join(","),v,this._normalState,!1,!e&&!this.__inHover&&d&&d.duration>0,d);var y=this._textContent,p=this._textGuide;y&&y.useStates(t,e,c),p&&p.useStates(t,e,c),this._updateAnimationTargets(),this.currentStates=t.slice(),this.markRedraw(),!c&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=-2)}},r.prototype.isSilent=function(){for(var t=this.silent,e=this.parent;!t&&e;){if(e.silent){t=!0;break}e=e.parent}return t},r.prototype._updateAnimationTargets=function(){for(var t=0;t<this.animators.length;t++){var e=this.animators[t];e.targetName&&e.changeTarget(this[e.targetName])}},r.prototype.removeState=function(t){var e=at(this.currentStates,t);if(e>=0){var i=this.currentStates.slice();i.splice(e,1),this.useStates(i)}},r.prototype.replaceState=function(t,e,i){var n=this.currentStates.slice(),a=at(n,t),o=at(n,e)>=0;a>=0?o?n.splice(a,1):n[a]=e:i&&!o&&n.push(e),this.useStates(n)},r.prototype.toggleState=function(t,e){e?this.useState(t,!0):this.removeState(t)},r.prototype._mergeStates=function(t){for(var e={},i,n=0;n<t.length;n++){var a=t[n];O(e,a),a.textConfig&&(i=i||{},O(i,a.textConfig))}return i&&(e.textConfig=i),e},r.prototype._applyStateObj=function(t,e,i,n,a,o){var s=!(e&&n);e&&e.textConfig?(this.textConfig=O({},n?this.textConfig:i.textConfig),O(this.textConfig,e.textConfig)):s&&i.textConfig&&(this.textConfig=i.textConfig);for(var u={},l=!1,f=0;f<Ko.length;f++){var h=Ko[f],c=a&&v_[h];e&&e[h]!=null?c?(l=!0,u[h]=e[h]):this[h]=e[h]:s&&i[h]!=null&&(c?(l=!0,u[h]=i[h]):this[h]=i[h])}if(!a)for(var f=0;f<this.animators.length;f++){var v=this.animators[f],d=v.targetName;v.getLoop()||v.__changeFinalValue(d?(e||i)[d]:e||i)}l&&this._transitionState(t,u,o)},r.prototype._attachComponent=function(t){if(!(t.__zr&&!t.__hostTarget)&&t!==this){var e=this.__zr;e&&t.addSelfToZr(e),t.__zr=e,t.__hostTarget=this}},r.prototype._detachComponent=function(t){t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__hostTarget=null},r.prototype.getClipPath=function(){return this._clipPath},r.prototype.setClipPath=function(t){this._clipPath&&this._clipPath!==t&&this.removeClipPath(),this._attachComponent(t),this._clipPath=t,this.markRedraw()},r.prototype.removeClipPath=function(){var t=this._clipPath;t&&(this._detachComponent(t),this._clipPath=null,this.markRedraw())},r.prototype.getTextContent=function(){return this._textContent},r.prototype.setTextContent=function(t){var e=this._textContent;e!==t&&(e&&e!==t&&this.removeTextContent(),t.innerTransformable=new ju,this._attachComponent(t),this._textContent=t,this.markRedraw())},r.prototype.setTextConfig=function(t){this.textConfig||(this.textConfig={}),O(this.textConfig,t),this.markRedraw()},r.prototype.removeTextConfig=function(){this.textConfig=null,this.markRedraw()},r.prototype.removeTextContent=function(){var t=this._textContent;t&&(t.innerTransformable=null,this._detachComponent(t),this._textContent=null,this._innerTextDefaultStyle=null,this.markRedraw())},r.prototype.getTextGuideLine=function(){return this._textGuide},r.prototype.setTextGuideLine=function(t){this._textGuide&&this._textGuide!==t&&this.removeTextGuideLine(),this._attachComponent(t),this._textGuide=t,this.markRedraw()},r.prototype.removeTextGuideLine=function(){var t=this._textGuide;t&&(this._detachComponent(t),this._textGuide=null,this.markRedraw())},r.prototype.markRedraw=function(){this.__dirty|=we;var t=this.__zr;t&&(this.__inHover?t.refreshHover():t.refresh()),this.__hostTarget&&this.__hostTarget.markRedraw()},r.prototype.dirty=function(){this.markRedraw()},r.prototype._toggleHoverLayerFlag=function(t){this.__inHover=t;var e=this._textContent,i=this._textGuide;e&&(e.__inHover=t),i&&(i.__inHover=t)},r.prototype.addSelfToZr=function(t){if(this.__zr!==t){this.__zr=t;var e=this.animators;if(e)for(var i=0;i<e.length;i++)t.animation.addAnimator(e[i]);this._clipPath&&this._clipPath.addSelfToZr(t),this._textContent&&this._textContent.addSelfToZr(t),this._textGuide&&this._textGuide.addSelfToZr(t)}},r.prototype.removeSelfFromZr=function(t){if(this.__zr){this.__zr=null;var e=this.animators;if(e)for(var i=0;i<e.length;i++)t.animation.removeAnimator(e[i]);this._clipPath&&this._clipPath.removeSelfFromZr(t),this._textContent&&this._textContent.removeSelfFromZr(t),this._textGuide&&this._textGuide.removeSelfFromZr(t)}},r.prototype.animate=function(t,e,i){var n=t?this[t]:this,a=new Ju(n,e,i);return t&&(a.targetName=t),this.addAnimator(a,t),a},r.prototype.addAnimator=function(t,e){var i=this.__zr,n=this;t.during(function(){n.updateDuringAnimation(e)}).done(function(){var a=n.animators,o=at(a,t);o>=0&&a.splice(o,1)}),this.animators.push(t),i&&i.animation.addAnimator(t),i&&i.wakeUp()},r.prototype.updateDuringAnimation=function(t){this.markRedraw()},r.prototype.stopAnimation=function(t,e){for(var i=this.animators,n=i.length,a=[],o=0;o<n;o++){var s=i[o];!t||t===s.scope?s.stop(e):a.push(s)}return this.animators=a,this},r.prototype.animateTo=function(t,e,i){Qo(this,t,e,i)},r.prototype.animateFrom=function(t,e,i){Qo(this,t,e,i,!0)},r.prototype._transitionState=function(t,e,i,n){for(var a=Qo(this,e,i,n),o=0;o<a.length;o++)a[o].__fromStateTransition=t},r.prototype.getBoundingRect=function(){return null},r.prototype.getPaintRect=function(){return null},r.initDefaultProps=function(){var t=r.prototype;t.type="element",t.name="",t.ignore=t.silent=t.isGroup=t.draggable=t.dragging=t.ignoreClip=t.__inHover=!1,t.__dirty=we;function e(i,n,a,o){Object.defineProperty(t,i,{get:function(){if(!this[n]){var u=this[n]=[];s(this,u)}return this[n]},set:function(u){this[a]=u[0],this[o]=u[1],this[n]=u,s(this,u)}});function s(u,l){Object.defineProperty(l,0,{get:function(){return u[a]},set:function(f){u[a]=f}}),Object.defineProperty(l,1,{get:function(){return u[o]},set:function(f){u[o]=f}})}}Object.defineProperty&&(e("position","_legacyPos","x","y"),e("scale","_legacyScale","scaleX","scaleY"),e("origin","_legacyOrigin","originX","originY"))}(),r}();Ee(ao,be);Ee(ao,ju);function Qo(r,t,e,i,n){e=e||{};var a=[];kc(r,"",r,t,e,i,a,n);var o=a.length,s=!1,u=e.done,l=e.aborted,f=function(){s=!0,o--,o<=0&&(s?u&&u():l&&l())},h=function(){o--,o<=0&&(s?u&&u():l&&l())};o||u&&u(),a.length>0&&e.during&&a[0].during(function(d,y){e.during(y)});for(var c=0;c<a.length;c++){var v=a[c];f&&v.done(f),h&&v.aborted(h),e.force&&v.duration(e.duration),v.start(e.easing)}return a}function Jo(r,t,e){for(var i=0;i<e;i++)r[i]=t[i]}function d_(r){return zt(r[0])}function p_(r,t,e){if(zt(t[e]))if(zt(r[e])||(r[e]=[]),Ht(t[e])){var i=t[e].length;r[e].length!==i&&(r[e]=new t[e].constructor(i),Jo(r[e],t[e],i))}else{var n=t[e],a=r[e],o=n.length;if(d_(n))for(var s=n[0].length,u=0;u<o;u++)a[u]?Jo(a[u],n[u],s):a[u]=Array.prototype.slice.call(n[u]);else Jo(a,n,o);a.length=n.length}else r[e]=t[e]}function g_(r,t){return r===t||zt(r)&&zt(t)&&y_(r,t)}function y_(r,t){var e=r.length;if(e!==t.length)return!1;for(var i=0;i<e;i++)if(r[i]!==t[i])return!1;return!0}function kc(r,t,e,i,n,a,o,s){for(var u=ft(i),l=n.duration,f=n.delay,h=n.additive,c=n.setToFinal,v=!F(a),d=r.animators,y=[],p=0;p<u.length;p++){var g=u[p],_=i[g];if(_!=null&&e[g]!=null&&(v||a[g]))if(F(_)&&!zt(_)&&!io(_)){if(t){s||(e[g]=_,r.updateDuringAnimation(t));continue}kc(r,g,e[g],_,n,a&&a[g],o,s)}else y.push(g);else s||(e[g]=_,r.updateDuringAnimation(t),y.push(g))}var m=y.length;if(!h&&m)for(var w=0;w<d.length;w++){var T=d[w];if(T.targetName===t){var S=T.stopTracks(y);if(S){var b=at(d,T);d.splice(b,1)}}}if(n.force||(y=Et(y,function(A){return!g_(i[A],e[A])}),m=y.length),m>0||n.force&&!o.length){var M=void 0,x=void 0,C=void 0;if(s){x={},c&&(M={});for(var w=0;w<m;w++){var g=y[w];x[g]=e[g],c?M[g]=i[g]:e[g]=i[g]}}else if(c){C={};for(var w=0;w<m;w++){var g=y[w];C[g]=Sa(e[g]),p_(e,i,g)}}var T=new Ju(e,!1,!1,h?Et(d,function(L){return L.targetName===t}):null);T.targetName=t,n.scope&&(T.scope=n.scope),c&&M&&T.whenWithKeys(0,M,y),C&&T.whenWithKeys(0,C,y),T.whenWithKeys(l??500,s?x:i,y).delay(f||0),r.addAnimator(T,t),o.push(T)}}var ue=function(r){N(t,r);function t(e){var i=r.call(this)||this;return i.isGroup=!0,i._children=[],i.attr(e),i}return t.prototype.childrenRef=function(){return this._children},t.prototype.children=function(){return this._children.slice()},t.prototype.childAt=function(e){return this._children[e]},t.prototype.childOfName=function(e){for(var i=this._children,n=0;n<i.length;n++)if(i[n].name===e)return i[n]},t.prototype.childCount=function(){return this._children.length},t.prototype.add=function(e){return e&&e!==this&&e.parent!==this&&(this._children.push(e),this._doAdd(e)),this},t.prototype.addBefore=function(e,i){if(e&&e!==this&&e.parent!==this&&i&&i.parent===this){var n=this._children,a=n.indexOf(i);a>=0&&(n.splice(a,0,e),this._doAdd(e))}return this},t.prototype.replace=function(e,i){var n=at(this._children,e);return n>=0&&this.replaceAt(i,n),this},t.prototype.replaceAt=function(e,i){var n=this._children,a=n[i];if(e&&e!==this&&e.parent!==this&&e!==a){n[i]=e,a.parent=null;var o=this.__zr;o&&a.removeSelfFromZr(o),this._doAdd(e)}return this},t.prototype._doAdd=function(e){e.parent&&e.parent.remove(e),e.parent=this;var i=this.__zr;i&&i!==e.__zr&&e.addSelfToZr(i),i&&i.refresh()},t.prototype.remove=function(e){var i=this.__zr,n=this._children,a=at(n,e);return a<0?this:(n.splice(a,1),e.parent=null,i&&e.removeSelfFromZr(i),i&&i.refresh(),this)},t.prototype.removeAll=function(){for(var e=this._children,i=this.__zr,n=0;n<e.length;n++){var a=e[n];i&&a.removeSelfFromZr(i),a.parent=null}return e.length=0,this},t.prototype.eachChild=function(e,i){for(var n=this._children,a=0;a<n.length;a++){var o=n[a];e.call(i,o,a)}return this},t.prototype.traverse=function(e,i){for(var n=0;n<this._children.length;n++){var a=this._children[n],o=e.call(i,a);a.isGroup&&!o&&a.traverse(e,i)}return this},t.prototype.addSelfToZr=function(e){r.prototype.addSelfToZr.call(this,e);for(var i=0;i<this._children.length;i++){var n=this._children[i];n.addSelfToZr(e)}},t.prototype.removeSelfFromZr=function(e){r.prototype.removeSelfFromZr.call(this,e);for(var i=0;i<this._children.length;i++){var n=this._children[i];n.removeSelfFromZr(e)}},t.prototype.getBoundingRect=function(e){for(var i=new J(0,0,0,0),n=e||this._children,a=[],o=null,s=0;s<n.length;s++){var u=n[s];if(!(u.ignore||u.invisible)){var l=u.getBoundingRect(),f=u.getLocalTransform(a);f?(J.applyTransform(i,l,f),o=o||i.clone(),o.union(i)):(o=o||l.clone(),o.union(l))}}return o||i},t}(ao);ue.prototype.type="group";/*!
* ZRender, a high performance 2d drawing library.
*
* Copyright (c) 2013, Baidu Inc.
* All rights reserved.
*
* LICENSE
* https://github.com/ecomfe/zrender/blob/master/LICENSE.txt
*/var Ca={},Bc={};function __(r){delete Bc[r]}function m_(r){if(!r)return!1;if(typeof r=="string")return za(r,1)<nu;if(r.colorStops){for(var t=r.colorStops,e=0,i=t.length,n=0;n<i;n++)e+=za(t[n].color,1);return e/=i,e<nu}return!1}var w_=function(){function r(t,e,i){var n=this;this._sleepAfterStill=10,this._stillFrameAccum=0,this._needsRefresh=!0,this._needsRefreshHover=!0,this._darkMode=!1,i=i||{},this.dom=e,this.id=t;var a=new Oy,o=i.renderer||"canvas";Ca[o]||(o=ft(Ca)[0]),i.useDirtyRect=i.useDirtyRect==null?!1:i.useDirtyRect;var s=new Ca[o](e,a,i,t),u=i.ssr||s.ssrOnly;this.storage=a,this.painter=s;var l=!H.node&&!H.worker&&!u?new u_(s.getViewportRoot(),s.root):null,f=i.useCoarsePointer,h=f==null||f==="auto"?H.touchEventsSupported:!!f,c=44,v;h&&(v=W(i.pointerSize,c)),this.handler=new _c(a,s,l,s.root,v),this.animation=new e_({stage:{update:u?null:function(){return n._flush(!0)}}}),u||this.animation.start()}return r.prototype.add=function(t){this._disposed||!t||(this.storage.addRoot(t),t.addSelfToZr(this),this.refresh())},r.prototype.remove=function(t){this._disposed||!t||(this.storage.delRoot(t),t.removeSelfFromZr(this),this.refresh())},r.prototype.configLayer=function(t,e){this._disposed||(this.painter.configLayer&&this.painter.configLayer(t,e),this.refresh())},r.prototype.setBackgroundColor=function(t){this._disposed||(this.painter.setBackgroundColor&&this.painter.setBackgroundColor(t),this.refresh(),this._backgroundColor=t,this._darkMode=m_(t))},r.prototype.getBackgroundColor=function(){return this._backgroundColor},r.prototype.setDarkMode=function(t){this._darkMode=t},r.prototype.isDarkMode=function(){return this._darkMode},r.prototype.refreshImmediately=function(t){this._disposed||(t||this.animation.update(!0),this._needsRefresh=!1,this.painter.refresh(),this._needsRefresh=!1)},r.prototype.refresh=function(){this._disposed||(this._needsRefresh=!0,this.animation.start())},r.prototype.flush=function(){this._disposed||this._flush(!1)},r.prototype._flush=function(t){var e,i=oi();this._needsRefresh&&(e=!0,this.refreshImmediately(t)),this._needsRefreshHover&&(e=!0,this.refreshHoverImmediately());var n=oi();e?(this._stillFrameAccum=0,this.trigger("rendered",{elapsedTime:n-i})):this._sleepAfterStill>0&&(this._stillFrameAccum++,this._stillFrameAccum>this._sleepAfterStill&&this.animation.stop())},r.prototype.setSleepAfterStill=function(t){this._sleepAfterStill=t},r.prototype.wakeUp=function(){this._disposed||(this.animation.start(),this._stillFrameAccum=0)},r.prototype.refreshHover=function(){this._needsRefreshHover=!0},r.prototype.refreshHoverImmediately=function(){this._disposed||(this._needsRefreshHover=!1,this.painter.refreshHover&&this.painter.getType()==="canvas"&&this.painter.refreshHover())},r.prototype.resize=function(t){this._disposed||(t=t||{},this.painter.resize(t.width,t.height),this.handler.resize())},r.prototype.clearAnimation=function(){this._disposed||this.animation.clear()},r.prototype.getWidth=function(){if(!this._disposed)return this.painter.getWidth()},r.prototype.getHeight=function(){if(!this._disposed)return this.painter.getHeight()},r.prototype.setCursorStyle=function(t){this._disposed||this.handler.setCursorStyle(t)},r.prototype.findHover=function(t,e){if(!this._disposed)return this.handler.findHover(t,e)},r.prototype.on=function(t,e,i){return this._disposed||this.handler.on(t,e,i),this},r.prototype.off=function(t,e){this._disposed||this.handler.off(t,e)},r.prototype.trigger=function(t,e){this._disposed||this.handler.trigger(t,e)},r.prototype.clear=function(){if(!this._disposed){for(var t=this.storage.getRoots(),e=0;e<t.length;e++)t[e]instanceof ue&&t[e].removeSelfFromZr(this);this.storage.delAllRoots(),this.painter.clear()}},r.prototype.dispose=function(){this._disposed||(this.animation.stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this.animation=this.storage=this.painter=this.handler=null,this._disposed=!0,__(this.id))},r}();function Cf(r,t){var e=new w_(uc(),r,t);return Bc[e.id]=e,e}function S_(r,t){Ca[r]=t}var su;function wC(r){if(typeof su=="function")return su(r)}function T_(r){su=r}var b_=1e-4,Nc=20;function x_(r){return r.replace(/^\s+|\s+$/g,"")}function SC(r,t,e,i){var n=t[0],a=t[1],o=e[0],s=e[1],u=a-n,l=s-o;if(u===0)return l===0?o:(o+s)/2;if(i)if(u>0){if(r<=n)return o;if(r>=a)return s}else{if(r>=n)return o;if(r<=a)return s}else{if(r===n)return o;if(r===a)return s}return(r-n)/u*l+o}function ct(r,t){switch(r){case"center":case"middle":r="50%";break;case"left":case"top":r="0%";break;case"right":case"bottom":r="100%";break}return B(r)?x_(r).match(/%$/)?parseFloat(r)/100*t:parseFloat(r):r==null?NaN:+r}function oe(r,t,e){return t==null&&(t=10),t=Math.min(Math.max(0,t),Nc),r=(+r).toFixed(t),e?r:+r}function TC(r){return r.sort(function(t,e){return t-e}),r}function Le(r){if(r=+r,isNaN(r))return 0;if(r>1e-14){for(var t=1,e=0;e<15;e++,t*=10)if(Math.round(r*t)/t===r)return e}return C_(r)}function C_(r){var t=r.toString().toLowerCase(),e=t.indexOf("e"),i=e>0?+t.slice(e+1):0,n=e>0?e:t.length,a=t.indexOf("."),o=a<0?0:n-1-a;return Math.max(0,o-i)}function bC(r,t){var e=Math.log,i=Math.LN10,n=Math.floor(e(r[1]-r[0])/i),a=Math.round(e(Math.abs(t[1]-t[0]))/i),o=Math.min(Math.max(-n+a,0),20);return isFinite(o)?o:20}function xC(r,t){var e=Ze(r,function(v,d){return v+(isNaN(d)?0:d)},0);if(e===0)return[];for(var i=Math.pow(10,t),n=Y(r,function(v){return(isNaN(v)?0:v)/e*i*100}),a=i*100,o=Y(n,function(v){return Math.floor(v)}),s=Ze(o,function(v,d){return v+d},0),u=Y(n,function(v,d){return v-o[d]});s<a;){for(var l=Number.NEGATIVE_INFINITY,f=null,h=0,c=u.length;h<c;++h)u[h]>l&&(l=u[h],f=h);++o[f],u[f]=0,++s}return Y(o,function(v){return v/i})}function D_(r,t){var e=Math.max(Le(r),Le(t)),i=r+t;return e>Nc?i:oe(i,e)}var CC=9007199254740991;function Fc(r){var t=Math.PI*2;return(r%t+t)%t}function Va(r){return r>-1e-4&&r<b_}var M_=/^(?:(\d{4})(?:[-\/](\d{1,2})(?:[-\/](\d{1,2})(?:[T ](\d{1,2})(?::(\d{1,2})(?::(\d{1,2})(?:[.,](\d+))?)?)?(Z|[\+\-]\d\d:?\d\d)?)?)?)?)?$/;function Re(r){if(r instanceof Date)return r;if(B(r)){var t=M_.exec(r);if(!t)return new Date(NaN);if(t[8]){var e=+t[4]||0;return t[8].toUpperCase()!=="Z"&&(e-=+t[8].slice(0,3)),new Date(Date.UTC(+t[1],+(t[2]||1)-1,+t[3]||1,e,+(t[5]||0),+t[6]||0,t[7]?+t[7].substring(0,3):0))}else return new Date(+t[1],+(t[2]||1)-1,+t[3]||1,+t[4]||0,+(t[5]||0),+t[6]||0,t[7]?+t[7].substring(0,3):0)}else if(r==null)return new Date(NaN);return new Date(Math.round(r))}function A_(r){return Math.pow(10,el(r))}function el(r){if(r===0)return 0;var t=Math.floor(Math.log(r)/Math.LN10);return r/Math.pow(10,t)>=10&&t++,t}function zc(r,t){var e=el(r),i=Math.pow(10,e),n=r/i,a;return n<1.5?a=1:n<2.5?a=2:n<4?a=3:n<7?a=5:a=10,r=a*i,e>=-20?+r.toFixed(e<0?-e:0):r}function DC(r,t){var e=(r.length-1)*t+1,i=Math.floor(e),n=+r[i-1],a=e-i;return a?n+a*(r[i]-n):n}function MC(r){r.sort(function(u,l){return s(u,l,0)?-1:1});for(var t=-1/0,e=1,i=0;i<r.length;){for(var n=r[i].interval,a=r[i].close,o=0;o<2;o++)n[o]<=t&&(n[o]=t,a[o]=o?1:1-e),t=n[o],e=a[o];n[0]===n[1]&&a[0]*a[1]!==1?r.splice(i,1):i++}return r;function s(u,l,f){return u.interval[f]<l.interval[f]||u.interval[f]===l.interval[f]&&(u.close[f]-l.close[f]===(f?-1:1)||!f&&s(u,l,1))}}function Je(r){var t=parseFloat(r);return t==r&&(t!==0||!B(r)||r.indexOf("x")<=0)?t:NaN}function P_(r){return!isNaN(Je(r))}function Hc(){return Math.round(Math.random()*9)}function Gc(r,t){return t===0?r:Gc(t,r%t)}function Df(r,t){return r==null?t:t==null?r:r*t/Gc(r,t)}function At(r){throw new Error(r)}function Mf(r,t,e){return(t-r)*e+r}var Vc="series\0",Wc="\0_ec_\0";function _t(r){return r instanceof Array?r:r==null?[]:[r]}function Af(r,t,e){if(r){r[t]=r[t]||{},r.emphasis=r.emphasis||{},r.emphasis[t]=r.emphasis[t]||{};for(var i=0,n=e.length;i<n;i++){var a=e[i];!r.emphasis[t].hasOwnProperty(a)&&r[t].hasOwnProperty(a)&&(r.emphasis[t][a]=r[t][a])}}}var Pf=["fontStyle","fontWeight","fontSize","fontFamily","rich","tag","color","textBorderColor","textBorderWidth","width","height","lineHeight","align","verticalAlign","baseline","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY","backgroundColor","borderColor","borderWidth","borderRadius","padding"];function oo(r){return F(r)&&!k(r)&&!(r instanceof Date)?r.value:r}function L_(r){return F(r)&&!(r instanceof Array)}function I_(r,t,e){var i=e==="normalMerge",n=e==="replaceMerge",a=e==="replaceAll";r=r||[],t=(t||[]).slice();var o=X();D(t,function(u,l){if(!F(u)){t[l]=null;return}});var s=R_(r,o,e);return(i||n)&&E_(s,r,o,t),i&&O_(s,t),i||n?k_(s,t,n):a&&B_(s,t),N_(s),s}function R_(r,t,e){var i=[];if(e==="replaceAll")return i;for(var n=0;n<r.length;n++){var a=r[n];a&&a.id!=null&&t.set(a.id,n),i.push({existing:e==="replaceMerge"||pn(a)?null:a,newOption:null,keyInfo:null,brandNew:null})}return i}function E_(r,t,e,i){D(i,function(n,a){if(!(!n||n.id==null)){var o=tn(n.id),s=e.get(o);if(s!=null){var u=r[s];Te(!u.newOption,'Duplicated option on id "'+o+'".'),u.newOption=n,u.existing=t[s],i[a]=null}}})}function O_(r,t){D(t,function(e,i){if(!(!e||e.name==null))for(var n=0;n<r.length;n++){var a=r[n].existing;if(!r[n].newOption&&a&&(a.id==null||e.id==null)&&!pn(e)&&!pn(a)&&Uc("name",a,e)){r[n].newOption=e,t[i]=null;return}}})}function k_(r,t,e){D(t,function(i){if(i){for(var n,a=0;(n=r[a])&&(n.newOption||pn(n.existing)||n.existing&&i.id!=null&&!Uc("id",i,n.existing));)a++;n?(n.newOption=i,n.brandNew=e):r.push({newOption:i,brandNew:e,existing:null,keyInfo:null}),a++}})}function B_(r,t){D(t,function(e){r.push({newOption:e,brandNew:!0,existing:null,keyInfo:null})})}function N_(r){var t=X();D(r,function(e){var i=e.existing;i&&t.set(i.id,e)}),D(r,function(e){var i=e.newOption;Te(!i||i.id==null||!t.get(i.id)||t.get(i.id)===e,"id duplicates: "+(i&&i.id)),i&&i.id!=null&&t.set(i.id,e),!e.keyInfo&&(e.keyInfo={})}),D(r,function(e,i){var n=e.existing,a=e.newOption,o=e.keyInfo;if(F(a)){if(o.name=a.name!=null?tn(a.name):n?n.name:Vc+i,n)o.id=tn(n.id);else if(a.id!=null)o.id=tn(a.id);else{var s=0;do o.id="\0"+o.name+"\0"+s++;while(t.get(o.id))}t.set(o.id,e)}})}function Uc(r,t,e){var i=se(t[r],null),n=se(e[r],null);return i!=null&&n!=null&&i===n}function tn(r){return se(r,"")}function se(r,t){return r==null?t:B(r)?r:ut(r)||Ys(r)?r+"":t}function rl(r){var t=r.name;return!!(t&&t.indexOf(Vc))}function pn(r){return r&&r.id!=null&&tn(r.id).indexOf(Wc)===0}function AC(r){return Wc+r}function F_(r,t,e){D(r,function(i){var n=i.newOption;F(n)&&(i.keyInfo.mainType=t,i.keyInfo.subType=z_(t,n,i.existing,e))})}function z_(r,t,e,i){var n=t.type?t.type:e?e.subType:i.determineSubType(r,t);return n}function PC(r,t){var e={},i={};return n(r||[],e),n(t||[],i,e),[a(e),a(i)];function n(o,s,u){for(var l=0,f=o.length;l<f;l++){var h=se(o[l].seriesId,null);if(h==null)return;for(var c=_t(o[l].dataIndex),v=u&&u[h],d=0,y=c.length;d<y;d++){var p=c[d];v&&v[p]?v[p]=null:(s[h]||(s[h]={}))[p]=1}}}function a(o,s){var u=[];for(var l in o)if(o.hasOwnProperty(l)&&o[l]!=null)if(s)u.push(+l);else{var f=a(o[l],!0);f.length&&u.push({seriesId:l,dataIndex:f})}return u}}function Mn(r,t){if(t.dataIndexInside!=null)return t.dataIndexInside;if(t.dataIndex!=null)return k(t.dataIndex)?Y(t.dataIndex,function(e){return r.indexOfRawIndex(e)}):r.indexOfRawIndex(t.dataIndex);if(t.name!=null)return k(t.name)?Y(t.name,function(e){return r.indexOfName(e)}):r.indexOfName(t.name)}function mt(){var r="__ec_inner_"+H_++;return function(t){return t[r]||(t[r]={})}}var H_=Hc();function jo(r,t,e){var i=il(t,e),n=i.mainTypeSpecified,a=i.queryOptionMap,o=i.others,s=o,u=e?e.defaultMainType:null;return!n&&u&&a.set(u,{}),a.each(function(l,f){var h=An(r,f,l,{useDefault:u===f,enableAll:e&&e.enableAll!=null?e.enableAll:!0,enableNone:e&&e.enableNone!=null?e.enableNone:!0});s[f+"Models"]=h.models,s[f+"Model"]=h.models[0]}),s}function il(r,t){var e;if(B(r)){var i={};i[r+"Index"]=0,e=i}else e=r;var n=X(),a={},o=!1;return D(e,function(s,u){if(u==="dataIndex"||u==="dataIndexInside"){a[u]=s;return}var l=u.match(/^(\w+)(Index|Id|Name)$/)||[],f=l[1],h=(l[2]||"").toLowerCase();if(!(!f||!h||t&&t.includeMainTypes&&at(t.includeMainTypes,f)<0)){o=o||!!f;var c=n.get(f)||n.set(f,{});c[h]=s}}),{mainTypeSpecified:o,queryOptionMap:n,others:a}}var so={useDefault:!0,enableAll:!1,enableNone:!1},LC={useDefault:!1,enableAll:!0,enableNone:!0};function An(r,t,e,i){i=i||so;var n=e.index,a=e.id,o=e.name,s={models:null,specified:n!=null||a!=null||o!=null};if(!s.specified){var u=void 0;return s.models=i.useDefault&&(u=r.getComponent(t))?[u]:[],s}return n==="none"||n===!1?(Te(i.enableNone,'`"none"` or `false` is not a valid value on index option.'),s.models=[],s):(n==="all"&&(Te(i.enableAll,'`"all"` is not a valid value on index option.'),n=a=o=null),s.models=r.queryComponents({mainType:t,index:n,id:a,name:o}),s)}function Yc(r,t,e){r.setAttribute?r.setAttribute(t,e):r[t]=e}function G_(r,t){return r.getAttribute?r.getAttribute(t):r[t]}function V_(r){return r==="auto"?H.domSupported?"html":"richText":r||"html"}function IC(r,t){var e=X(),i=[];return D(r,function(n){var a=t(n);(e.get(a)||(i.push(a),e.set(a,[]))).push(n)}),{keys:i,buckets:e}}function W_(r,t,e,i,n){var a=t==null||t==="auto";if(i==null)return i;if(ut(i)){var o=Mf(e||0,i,n);return oe(o,a?Math.max(Le(e||0),Le(i)):t)}else{if(B(i))return n<1?e:i;for(var s=[],u=e,l=i,f=Math.max(u?u.length:0,l.length),h=0;h<f;++h){var c=r.getDimensionInfo(h);if(c&&c.type==="ordinal")s[h]=(n<1&&u?u:l)[h];else{var v=u&&u[h]?u[h]:0,d=l[h],o=Mf(v,d,n);s[h]=oe(o,a?Math.max(Le(v),Le(d)):t)}}return s}}var U_=".",fr="___EC__COMPONENT__CONTAINER___",Xc="___EC__EXTENDED_CLASS___";function _e(r){var t={main:"",sub:""};if(r){var e=r.split(U_);t.main=e[0]||"",t.sub=e[1]||""}return t}function Y_(r){Te(/^[a-zA-Z0-9_]+([.][a-zA-Z0-9_]+)?$/.test(r),'componentType "'+r+'" illegal')}function X_(r){return!!(r&&r[Xc])}function nl(r,t){r.$constructor=r,r.extend=function(e){var i=this,n;return $_(i)?n=function(a){N(o,a);function o(){return a.apply(this,arguments)||this}return o}(i):(n=function(){(e.$constructor||i).apply(this,arguments)},Qg(n,this)),O(n.prototype,e),n[Xc]=!0,n.extend=this.extend,n.superCall=K_,n.superApply=Q_,n.superClass=i,n}}function $_(r){return K(r)&&/^class\s/.test(Function.prototype.toString.call(r))}function $c(r,t){r.extend=t.extend}var q_=Math.round(Math.random()*10);function Z_(r){var t=["__\0is_clz",q_++].join("_");r.prototype[t]=!0,r.isInstance=function(e){return!!(e&&e[t])}}function K_(r,t){for(var e=[],i=2;i<arguments.length;i++)e[i-2]=arguments[i];return this.superClass.prototype[t].apply(r,e)}function Q_(r,t,e){return this.superClass.prototype[t].apply(r,e)}function uo(r){var t={};r.registerClass=function(i){var n=i.type||i.prototype.type;if(n){Y_(n),i.prototype.type=n;var a=_e(n);if(!a.sub)t[a.main]=i;else if(a.sub!==fr){var o=e(a);o[a.sub]=i}}return i},r.getClass=function(i,n,a){var o=t[i];if(o&&o[fr]&&(o=n?o[n]:null),a&&!o)throw new Error(n?"Component "+i+"."+(n||"")+" is used but not imported.":i+".type should be specified.");return o},r.getClassesByMainType=function(i){var n=_e(i),a=[],o=t[n.main];return o&&o[fr]?D(o,function(s,u){u!==fr&&a.push(s)}):a.push(o),a},r.hasClass=function(i){var n=_e(i);return!!t[n.main]},r.getAllClassMainTypes=function(){var i=[];return D(t,function(n,a){i.push(a)}),i},r.hasSubTypes=function(i){var n=_e(i),a=t[n.main];return a&&a[fr]};function e(i){var n=t[i.main];return(!n||!n[fr])&&(n=t[i.main]={},n[fr]=!0),n}}function gn(r,t){for(var e=0;e<r.length;e++)r[e][1]||(r[e][1]=r[e][0]);return t=t||!1,function(i,n,a){for(var o={},s=0;s<r.length;s++){var u=r[s][1];if(!(n&&at(n,u)>=0||a&&at(a,u)<0)){var l=i.getShallow(u,t);l!=null&&(o[r[s][0]]=l)}}return o}}var J_=[["fill","color"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["opacity"],["shadowColor"]],j_=gn(J_),t0=function(){function r(){}return r.prototype.getAreaStyle=function(t,e){return j_(this,t,e)},r}(),uu=new Dn(50);function e0(r){if(typeof r=="string"){var t=uu.get(r);return t&&t.image}else return r}function qc(r,t,e,i,n){if(r)if(typeof r=="string"){if(t&&t.__zrImageSrc===r||!e)return t;var a=uu.get(r),o={hostEl:e,cb:i,cbPayload:n};return a?(t=a.image,!lo(t)&&a.pending.push(o)):(t=yi.loadImage(r,Lf,Lf),t.__zrImageSrc=r,uu.put(r,t.__cachedImgObj={image:t,pending:[o]})),t}else return r;else return t}function Lf(){var r=this.__cachedImgObj;this.onload=this.onerror=this.__cachedImgObj=null;for(var t=0;t<r.pending.length;t++){var e=r.pending[t],i=e.cb;i&&i(this,e.cbPayload),e.hostEl.dirty()}r.pending.length=0}function lo(r){return r&&r.width&&r.height}var ts=/\{([a-zA-Z0-9_]+)\|([^}]*)\}/g;function r0(r,t,e,i,n,a){if(!e){r.text="",r.isTruncated=!1;return}var o=(t+"").split(`
`);a=Zc(e,i,n,a);for(var s=!1,u={},l=0,f=o.length;l<f;l++)Kc(u,o[l],a),o[l]=u.textLine,s=s||u.isTruncated;r.text=o.join(`
`),r.isTruncated=s}function Zc(r,t,e,i){i=i||{};var n=O({},i);n.font=t,e=W(e,"..."),n.maxIterations=W(i.maxIterations,2);var a=n.minChar=W(i.minChar,0);n.cnCharWidth=Wt("国",t);var o=n.ascCharWidth=Wt("a",t);n.placeholder=W(i.placeholder,"");for(var s=r=Math.max(0,r-1),u=0;u<a&&s>=o;u++)s-=o;var l=Wt(e,t);return l>s&&(e="",l=0),s=r-l,n.ellipsis=e,n.ellipsisWidth=l,n.contentWidth=s,n.containerWidth=r,n}function Kc(r,t,e){var i=e.containerWidth,n=e.font,a=e.contentWidth;if(!i){r.textLine="",r.isTruncated=!1;return}var o=Wt(t,n);if(o<=i){r.textLine=t,r.isTruncated=!1;return}for(var s=0;;s++){if(o<=a||s>=e.maxIterations){t+=e.ellipsis;break}var u=s===0?i0(t,a,e.ascCharWidth,e.cnCharWidth):o>0?Math.floor(t.length*a/o):0;t=t.substr(0,u),o=Wt(t,n)}t===""&&(t=e.placeholder),r.textLine=t,r.isTruncated=!0}function i0(r,t,e,i){for(var n=0,a=0,o=r.length;a<o&&n<t;a++){var s=r.charCodeAt(a);n+=0<=s&&s<=127?e:i}return a}function n0(r,t){r!=null&&(r+="");var e=t.overflow,i=t.padding,n=t.font,a=e==="truncate",o=tl(n),s=W(t.lineHeight,o),u=!!t.backgroundColor,l=t.lineOverflow==="truncate",f=!1,h=t.width,c;h!=null&&(e==="break"||e==="breakAll")?c=r?Qc(r,t.font,h,e==="breakAll",0).lines:[]:c=r?r.split(`
`):[];var v=c.length*s,d=W(t.height,v);if(v>d&&l){var y=Math.floor(d/s);f=f||c.length>y,c=c.slice(0,y)}if(r&&a&&h!=null)for(var p=Zc(h,n,t.ellipsis,{minChar:t.truncateMinChar,placeholder:t.placeholder}),g={},_=0;_<c.length;_++)Kc(g,c[_],p),c[_]=g.textLine,f=f||g.isTruncated;for(var m=d,w=0,_=0;_<c.length;_++)w=Math.max(Wt(c[_],n),w);h==null&&(h=w);var T=w;return i&&(m+=i[0]+i[2],T+=i[1]+i[3],h+=i[1]+i[3]),u&&(T=h),{lines:c,height:d,outerWidth:T,outerHeight:m,lineHeight:s,calculatedLineHeight:o,contentWidth:w,contentHeight:v,width:h,isTruncated:f}}var a0=function(){function r(){}return r}(),If=function(){function r(t){this.tokens=[],t&&(this.tokens=t)}return r}(),o0=function(){function r(){this.width=0,this.height=0,this.contentWidth=0,this.contentHeight=0,this.outerWidth=0,this.outerHeight=0,this.lines=[],this.isTruncated=!1}return r}();function s0(r,t){var e=new o0;if(r!=null&&(r+=""),!r)return e;for(var i=t.width,n=t.height,a=t.overflow,o=(a==="break"||a==="breakAll")&&i!=null?{width:i,accumWidth:0,breakAll:a==="breakAll"}:null,s=ts.lastIndex=0,u;(u=ts.exec(r))!=null;){var l=u.index;l>s&&es(e,r.substring(s,l),t,o),es(e,u[2],t,o,u[1]),s=ts.lastIndex}s<r.length&&es(e,r.substring(s,r.length),t,o);var f=[],h=0,c=0,v=t.padding,d=a==="truncate",y=t.lineOverflow==="truncate",p={};function g($,it,j){$.width=it,$.lineHeight=j,h+=j,c=Math.max(c,it)}t:for(var _=0;_<e.lines.length;_++){for(var m=e.lines[_],w=0,T=0,S=0;S<m.tokens.length;S++){var b=m.tokens[S],M=b.styleName&&t.rich[b.styleName]||{},x=b.textPadding=M.padding,C=x?x[1]+x[3]:0,A=b.font=M.font||t.font;b.contentHeight=tl(A);var L=W(M.height,b.contentHeight);if(b.innerHeight=L,x&&(L+=x[0]+x[2]),b.height=L,b.lineHeight=ya(M.lineHeight,t.lineHeight,L),b.align=M&&M.align||t.align,b.verticalAlign=M&&M.verticalAlign||"middle",y&&n!=null&&h+b.lineHeight>n){var I=e.lines.length;S>0?(m.tokens=m.tokens.slice(0,S),g(m,T,w),e.lines=e.lines.slice(0,_+1)):e.lines=e.lines.slice(0,_),e.isTruncated=e.isTruncated||e.lines.length<I;break t}var P=M.width,R=P==null||P==="auto";if(typeof P=="string"&&P.charAt(P.length-1)==="%")b.percentWidth=P,f.push(b),b.contentWidth=Wt(b.text,A);else{if(R){var E=M.backgroundColor,U=E&&E.image;U&&(U=e0(U),lo(U)&&(b.width=Math.max(b.width,U.width*L/U.height)))}var z=d&&i!=null?i-T:null;z!=null&&z<b.width?!R||z<C?(b.text="",b.width=b.contentWidth=0):(r0(p,b.text,z-C,A,t.ellipsis,{minChar:t.truncateMinChar}),b.text=p.text,e.isTruncated=e.isTruncated||p.isTruncated,b.width=b.contentWidth=Wt(b.text,A)):b.contentWidth=Wt(b.text,A)}b.width+=C,T+=b.width,M&&(w=Math.max(w,b.lineHeight))}g(m,T,w)}e.outerWidth=e.width=W(i,c),e.outerHeight=e.height=W(n,h),e.contentHeight=h,e.contentWidth=c,v&&(e.outerWidth+=v[1]+v[3],e.outerHeight+=v[0]+v[2]);for(var _=0;_<f.length;_++){var b=f[_],G=b.percentWidth;b.width=parseInt(G,10)/100*e.width}return e}function es(r,t,e,i,n){var a=t==="",o=n&&e.rich[n]||{},s=r.lines,u=o.font||e.font,l=!1,f,h;if(i){var c=o.padding,v=c?c[1]+c[3]:0;if(o.width!=null&&o.width!=="auto"){var d=Qe(o.width,i.width)+v;s.length>0&&d+i.accumWidth>i.width&&(f=t.split(`
`),l=!0),i.accumWidth=d}else{var y=Qc(t,u,i.width,i.breakAll,i.accumWidth);i.accumWidth=y.accumWidth+v,h=y.linesWidths,f=y.lines}}else f=t.split(`
`);for(var p=0;p<f.length;p++){var g=f[p],_=new a0;if(_.styleName=n,_.text=g,_.isLineHolder=!g&&!a,typeof o.width=="number"?_.width=o.width:_.width=h?h[p]:Wt(g,u),!p&&!l){var m=(s[s.length-1]||(s[0]=new If)).tokens,w=m.length;w===1&&m[0].isLineHolder?m[0]=_:(g||!w||a)&&m.push(_)}else s.push(new If([_]))}}function u0(r){var t=r.charCodeAt(0);return t>=32&&t<=591||t>=880&&t<=4351||t>=4608&&t<=5119||t>=7680&&t<=8303}var l0=Ze(",&?/;] ".split(""),function(r,t){return r[t]=!0,r},{});function f0(r){return u0(r)?!!l0[r]:!0}function Qc(r,t,e,i,n){for(var a=[],o=[],s="",u="",l=0,f=0,h=0;h<r.length;h++){var c=r.charAt(h);if(c===`
`){u&&(s+=u,f+=l),a.push(s),o.push(f),s="",u="",l=0,f=0;continue}var v=Wt(c,t),d=i?!1:!f0(c);if(a.length?f+v>e:n+f+v>e){f?(s||u)&&(d?(s||(s=u,u="",l=0,f=l),a.push(s),o.push(f-l),u+=c,l+=v,s="",f=l):(u&&(s+=u,u="",l=0),a.push(s),o.push(f),s=c,f=v)):d?(a.push(u),o.push(l),u=c,l=v):(a.push(c),o.push(v));continue}f+=v,d?(u+=c,l+=v):(u&&(s+=u,u="",l=0),s+=c)}return!a.length&&!s&&(s=r,u="",l=0),u&&(s+=u),s&&(a.push(s),o.push(f)),a.length===1&&(f+=n),{accumWidth:f,lines:a,linesWidths:o}}var lu="__zr_style_"+Math.round(Math.random()*10),Er={shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,shadowColor:"#000",opacity:1,blend:"source-over"},fo={style:{shadowBlur:!0,shadowOffsetX:!0,shadowOffsetY:!0,shadowColor:!0,opacity:!0}};Er[lu]=!0;var Rf=["z","z2","invisible"],h0=["invisible"],Pn=function(r){N(t,r);function t(e){return r.call(this,e)||this}return t.prototype._init=function(e){for(var i=ft(e),n=0;n<i.length;n++){var a=i[n];a==="style"?this.useStyle(e[a]):r.prototype.attrKV.call(this,a,e[a])}this.style||this.useStyle({})},t.prototype.beforeBrush=function(){},t.prototype.afterBrush=function(){},t.prototype.innerBeforeBrush=function(){},t.prototype.innerAfterBrush=function(){},t.prototype.shouldBePainted=function(e,i,n,a){var o=this.transform;if(this.ignore||this.invisible||this.style.opacity===0||this.culling&&v0(this,e,i)||o&&!o[0]&&!o[3])return!1;if(n&&this.__clipPaths){for(var s=0;s<this.__clipPaths.length;++s)if(this.__clipPaths[s].isZeroArea())return!1}if(a&&this.parent)for(var u=this.parent;u;){if(u.ignore)return!1;u=u.parent}return!0},t.prototype.contain=function(e,i){return this.rectContain(e,i)},t.prototype.traverse=function(e,i){e.call(i,this)},t.prototype.rectContain=function(e,i){var n=this.transformCoordToLocal(e,i),a=this.getBoundingRect();return a.contain(n[0],n[1])},t.prototype.getPaintRect=function(){var e=this._paintRect;if(!this._paintRect||this.__dirty){var i=this.transform,n=this.getBoundingRect(),a=this.style,o=a.shadowBlur||0,s=a.shadowOffsetX||0,u=a.shadowOffsetY||0;e=this._paintRect||(this._paintRect=new J(0,0,0,0)),i?J.applyTransform(e,n,i):e.copy(n),(o||s||u)&&(e.width+=o*2+Math.abs(s),e.height+=o*2+Math.abs(u),e.x=Math.min(e.x,e.x+s-o),e.y=Math.min(e.y,e.y+u-o));var l=this.dirtyRectTolerance;e.isZero()||(e.x=Math.floor(e.x-l),e.y=Math.floor(e.y-l),e.width=Math.ceil(e.width+1+l*2),e.height=Math.ceil(e.height+1+l*2))}return e},t.prototype.setPrevPaintRect=function(e){e?(this._prevPaintRect=this._prevPaintRect||new J(0,0,0,0),this._prevPaintRect.copy(e)):this._prevPaintRect=null},t.prototype.getPrevPaintRect=function(){return this._prevPaintRect},t.prototype.animateStyle=function(e){return this.animate("style",e)},t.prototype.updateDuringAnimation=function(e){e==="style"?this.dirtyStyle():this.markRedraw()},t.prototype.attrKV=function(e,i){e!=="style"?r.prototype.attrKV.call(this,e,i):this.style?this.setStyle(i):this.useStyle(i)},t.prototype.setStyle=function(e,i){return typeof e=="string"?this.style[e]=i:O(this.style,e),this.dirtyStyle(),this},t.prototype.dirtyStyle=function(e){e||this.markRedraw(),this.__dirty|=wa,this._rect&&(this._rect=null)},t.prototype.dirty=function(){this.dirtyStyle()},t.prototype.styleChanged=function(){return!!(this.__dirty&wa)},t.prototype.styleUpdated=function(){this.__dirty&=-3},t.prototype.createStyle=function(e){return no(Er,e)},t.prototype.useStyle=function(e){e[lu]||(e=this.createStyle(e)),this.__inHover?this.__hoverStyle=e:this.style=e,this.dirtyStyle()},t.prototype.isStyleObject=function(e){return e[lu]},t.prototype._innerSaveToNormal=function(e){r.prototype._innerSaveToNormal.call(this,e);var i=this._normalState;e.style&&!i.style&&(i.style=this._mergeStyle(this.createStyle(),this.style)),this._savePrimaryToNormal(e,i,Rf)},t.prototype._applyStateObj=function(e,i,n,a,o,s){r.prototype._applyStateObj.call(this,e,i,n,a,o,s);var u=!(i&&a),l;if(i&&i.style?o?a?l=i.style:(l=this._mergeStyle(this.createStyle(),n.style),this._mergeStyle(l,i.style)):(l=this._mergeStyle(this.createStyle(),a?this.style:n.style),this._mergeStyle(l,i.style)):u&&(l=n.style),l)if(o){var f=this.style;if(this.style=this.createStyle(u?{}:f),u)for(var h=ft(f),c=0;c<h.length;c++){var v=h[c];v in l&&(l[v]=l[v],this.style[v]=f[v])}for(var d=ft(l),c=0;c<d.length;c++){var v=d[c];this.style[v]=this.style[v]}this._transitionState(e,{style:l},s,this.getAnimationStyleProps())}else this.useStyle(l);for(var y=this.__inHover?h0:Rf,c=0;c<y.length;c++){var v=y[c];i&&i[v]!=null?this[v]=i[v]:u&&n[v]!=null&&(this[v]=n[v])}},t.prototype._mergeStates=function(e){for(var i=r.prototype._mergeStates.call(this,e),n,a=0;a<e.length;a++){var o=e[a];o.style&&(n=n||{},this._mergeStyle(n,o.style))}return n&&(i.style=n),i},t.prototype._mergeStyle=function(e,i){return O(e,i),e},t.prototype.getAnimationStyleProps=function(){return fo},t.initDefaultProps=function(){var e=t.prototype;e.type="displayable",e.invisible=!1,e.z=0,e.z2=0,e.zlevel=0,e.culling=!1,e.cursor="pointer",e.rectHover=!1,e.incremental=!1,e._rect=null,e.dirtyRectTolerance=0,e.__dirty=we|wa}(),t}(ao),rs=new J(0,0,0,0),is=new J(0,0,0,0);function v0(r,t,e){return rs.copy(r.getBoundingRect()),r.transform&&rs.applyTransform(r.transform),is.width=t,is.height=e,!rs.intersect(is)}var kt=Math.min,Bt=Math.max,ns=Math.sin,as=Math.cos,hr=Math.PI*2,Wn=mi(),Un=mi(),Yn=mi();function RC(r,t,e){if(r.length!==0){for(var i=r[0],n=i[0],a=i[0],o=i[1],s=i[1],u=1;u<r.length;u++)i=r[u],n=kt(n,i[0]),a=Bt(a,i[0]),o=kt(o,i[1]),s=Bt(s,i[1]);t[0]=n,t[1]=o,e[0]=a,e[1]=s}}function Ef(r,t,e,i,n,a){n[0]=kt(r,e),n[1]=kt(t,i),a[0]=Bt(r,e),a[1]=Bt(t,i)}var Of=[],kf=[];function c0(r,t,e,i,n,a,o,s,u,l){var f=Cc,h=gt,c=f(r,e,n,o,Of);u[0]=1/0,u[1]=1/0,l[0]=-1/0,l[1]=-1/0;for(var v=0;v<c;v++){var d=h(r,e,n,o,Of[v]);u[0]=kt(d,u[0]),l[0]=Bt(d,l[0])}c=f(t,i,a,s,kf);for(var v=0;v<c;v++){var y=h(t,i,a,s,kf[v]);u[1]=kt(y,u[1]),l[1]=Bt(y,l[1])}u[0]=kt(r,u[0]),l[0]=Bt(r,l[0]),u[0]=kt(o,u[0]),l[0]=Bt(o,l[0]),u[1]=kt(t,u[1]),l[1]=Bt(t,l[1]),u[1]=kt(s,u[1]),l[1]=Bt(s,l[1])}function d0(r,t,e,i,n,a,o,s){var u=Dc,l=xt,f=Bt(kt(u(r,e,n),1),0),h=Bt(kt(u(t,i,a),1),0),c=l(r,e,n,f),v=l(t,i,a,h);o[0]=kt(r,n,c),o[1]=kt(t,a,v),s[0]=Bt(r,n,c),s[1]=Bt(t,a,v)}function p0(r,t,e,i,n,a,o,s,u){var l=ni,f=ai,h=Math.abs(n-a);if(h%hr<1e-4&&h>1e-4){s[0]=r-e,s[1]=t-i,u[0]=r+e,u[1]=t+i;return}if(Wn[0]=as(n)*e+r,Wn[1]=ns(n)*i+t,Un[0]=as(a)*e+r,Un[1]=ns(a)*i+t,l(s,Wn,Un),f(u,Wn,Un),n=n%hr,n<0&&(n=n+hr),a=a%hr,a<0&&(a=a+hr),n>a&&!o?a+=hr:n<a&&o&&(n+=hr),o){var c=a;a=n,n=c}for(var v=0;v<a;v+=Math.PI/2)v>n&&(Yn[0]=as(v)*e+r,Yn[1]=ns(v)*i+t,l(s,Yn,s),f(u,Yn,u))}var Q={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},vr=[],cr=[],ve=[],Be=[],ce=[],de=[],os=Math.min,ss=Math.max,dr=Math.cos,pr=Math.sin,Me=Math.abs,fu=Math.PI,Ve=fu*2,us=typeof Float32Array<"u",Pi=[];function ls(r){var t=Math.round(r/fu*1e8)/1e8;return t%2*fu}function g0(r,t){var e=ls(r[0]);e<0&&(e+=Ve);var i=e-r[0],n=r[1];n+=i,!t&&n-e>=Ve?n=e+Ve:t&&e-n>=Ve?n=e-Ve:!t&&e>n?n=e+(Ve-ls(e-n)):t&&e<n&&(n=e-(Ve-ls(n-e))),r[0]=e,r[1]=n}var pi=function(){function r(t){this.dpr=1,this._xi=0,this._yi=0,this._x0=0,this._y0=0,this._len=0,t&&(this._saveData=!1),this._saveData&&(this.data=[])}return r.prototype.increaseVersion=function(){this._version++},r.prototype.getVersion=function(){return this._version},r.prototype.setScale=function(t,e,i){i=i||0,i>0&&(this._ux=Me(i/Ga/t)||0,this._uy=Me(i/Ga/e)||0)},r.prototype.setDPR=function(t){this.dpr=t},r.prototype.setContext=function(t){this._ctx=t},r.prototype.getContext=function(){return this._ctx},r.prototype.beginPath=function(){return this._ctx&&this._ctx.beginPath(),this.reset(),this},r.prototype.reset=function(){this._saveData&&(this._len=0),this._pathSegLen&&(this._pathSegLen=null,this._pathLen=0),this._version++},r.prototype.moveTo=function(t,e){return this._drawPendingPt(),this.addData(Q.M,t,e),this._ctx&&this._ctx.moveTo(t,e),this._x0=t,this._y0=e,this._xi=t,this._yi=e,this},r.prototype.lineTo=function(t,e){var i=Me(t-this._xi),n=Me(e-this._yi),a=i>this._ux||n>this._uy;if(this.addData(Q.L,t,e),this._ctx&&a&&this._ctx.lineTo(t,e),a)this._xi=t,this._yi=e,this._pendingPtDist=0;else{var o=i*i+n*n;o>this._pendingPtDist&&(this._pendingPtX=t,this._pendingPtY=e,this._pendingPtDist=o)}return this},r.prototype.bezierCurveTo=function(t,e,i,n,a,o){return this._drawPendingPt(),this.addData(Q.C,t,e,i,n,a,o),this._ctx&&this._ctx.bezierCurveTo(t,e,i,n,a,o),this._xi=a,this._yi=o,this},r.prototype.quadraticCurveTo=function(t,e,i,n){return this._drawPendingPt(),this.addData(Q.Q,t,e,i,n),this._ctx&&this._ctx.quadraticCurveTo(t,e,i,n),this._xi=i,this._yi=n,this},r.prototype.arc=function(t,e,i,n,a,o){this._drawPendingPt(),Pi[0]=n,Pi[1]=a,g0(Pi,o),n=Pi[0],a=Pi[1];var s=a-n;return this.addData(Q.A,t,e,i,i,n,s,0,o?0:1),this._ctx&&this._ctx.arc(t,e,i,n,a,o),this._xi=dr(a)*i+t,this._yi=pr(a)*i+e,this},r.prototype.arcTo=function(t,e,i,n,a){return this._drawPendingPt(),this._ctx&&this._ctx.arcTo(t,e,i,n,a),this},r.prototype.rect=function(t,e,i,n){return this._drawPendingPt(),this._ctx&&this._ctx.rect(t,e,i,n),this.addData(Q.R,t,e,i,n),this},r.prototype.closePath=function(){this._drawPendingPt(),this.addData(Q.Z);var t=this._ctx,e=this._x0,i=this._y0;return t&&t.closePath(),this._xi=e,this._yi=i,this},r.prototype.fill=function(t){t&&t.fill(),this.toStatic()},r.prototype.stroke=function(t){t&&t.stroke(),this.toStatic()},r.prototype.len=function(){return this._len},r.prototype.setData=function(t){var e=t.length;!(this.data&&this.data.length===e)&&us&&(this.data=new Float32Array(e));for(var i=0;i<e;i++)this.data[i]=t[i];this._len=e},r.prototype.appendPath=function(t){t instanceof Array||(t=[t]);for(var e=t.length,i=0,n=this._len,a=0;a<e;a++)i+=t[a].len();us&&this.data instanceof Float32Array&&(this.data=new Float32Array(n+i));for(var a=0;a<e;a++)for(var o=t[a].data,s=0;s<o.length;s++)this.data[n++]=o[s];this._len=n},r.prototype.addData=function(t,e,i,n,a,o,s,u,l){if(this._saveData){var f=this.data;this._len+arguments.length>f.length&&(this._expandData(),f=this.data);for(var h=0;h<arguments.length;h++)f[this._len++]=arguments[h]}},r.prototype._drawPendingPt=function(){this._pendingPtDist>0&&(this._ctx&&this._ctx.lineTo(this._pendingPtX,this._pendingPtY),this._pendingPtDist=0)},r.prototype._expandData=function(){if(!(this.data instanceof Array)){for(var t=[],e=0;e<this._len;e++)t[e]=this.data[e];this.data=t}},r.prototype.toStatic=function(){if(this._saveData){this._drawPendingPt();var t=this.data;t instanceof Array&&(t.length=this._len,us&&this._len>11&&(this.data=new Float32Array(t)))}},r.prototype.getBoundingRect=function(){ve[0]=ve[1]=ce[0]=ce[1]=Number.MAX_VALUE,Be[0]=Be[1]=de[0]=de[1]=-Number.MAX_VALUE;var t=this.data,e=0,i=0,n=0,a=0,o;for(o=0;o<this._len;){var s=t[o++],u=o===1;switch(u&&(e=t[o],i=t[o+1],n=e,a=i),s){case Q.M:e=n=t[o++],i=a=t[o++],ce[0]=n,ce[1]=a,de[0]=n,de[1]=a;break;case Q.L:Ef(e,i,t[o],t[o+1],ce,de),e=t[o++],i=t[o++];break;case Q.C:c0(e,i,t[o++],t[o++],t[o++],t[o++],t[o],t[o+1],ce,de),e=t[o++],i=t[o++];break;case Q.Q:d0(e,i,t[o++],t[o++],t[o],t[o+1],ce,de),e=t[o++],i=t[o++];break;case Q.A:var l=t[o++],f=t[o++],h=t[o++],c=t[o++],v=t[o++],d=t[o++]+v;o+=1;var y=!t[o++];u&&(n=dr(v)*h+l,a=pr(v)*c+f),p0(l,f,h,c,v,d,y,ce,de),e=dr(d)*h+l,i=pr(d)*c+f;break;case Q.R:n=e=t[o++],a=i=t[o++];var p=t[o++],g=t[o++];Ef(n,a,n+p,a+g,ce,de);break;case Q.Z:e=n,i=a;break}ni(ve,ve,ce),ai(Be,Be,de)}return o===0&&(ve[0]=ve[1]=Be[0]=Be[1]=0),new J(ve[0],ve[1],Be[0]-ve[0],Be[1]-ve[1])},r.prototype._calculateLength=function(){var t=this.data,e=this._len,i=this._ux,n=this._uy,a=0,o=0,s=0,u=0;this._pathSegLen||(this._pathSegLen=[]);for(var l=this._pathSegLen,f=0,h=0,c=0;c<e;){var v=t[c++],d=c===1;d&&(a=t[c],o=t[c+1],s=a,u=o);var y=-1;switch(v){case Q.M:a=s=t[c++],o=u=t[c++];break;case Q.L:{var p=t[c++],g=t[c++],_=p-a,m=g-o;(Me(_)>i||Me(m)>n||c===e-1)&&(y=Math.sqrt(_*_+m*m),a=p,o=g);break}case Q.C:{var w=t[c++],T=t[c++],p=t[c++],g=t[c++],S=t[c++],b=t[c++];y=By(a,o,w,T,p,g,S,b,10),a=S,o=b;break}case Q.Q:{var w=t[c++],T=t[c++],p=t[c++],g=t[c++];y=zy(a,o,w,T,p,g,10),a=p,o=g;break}case Q.A:var M=t[c++],x=t[c++],C=t[c++],A=t[c++],L=t[c++],I=t[c++],P=I+L;c+=1,d&&(s=dr(L)*C+M,u=pr(L)*A+x),y=ss(C,A)*os(Ve,Math.abs(I)),a=dr(P)*C+M,o=pr(P)*A+x;break;case Q.R:{s=a=t[c++],u=o=t[c++];var R=t[c++],E=t[c++];y=R*2+E*2;break}case Q.Z:{var _=s-a,m=u-o;y=Math.sqrt(_*_+m*m),a=s,o=u;break}}y>=0&&(l[h++]=y,f+=y)}return this._pathLen=f,f},r.prototype.rebuildPath=function(t,e){var i=this.data,n=this._ux,a=this._uy,o=this._len,s,u,l,f,h,c,v=e<1,d,y,p=0,g=0,_,m=0,w,T;if(!(v&&(this._pathSegLen||this._calculateLength(),d=this._pathSegLen,y=this._pathLen,_=e*y,!_)))t:for(var S=0;S<o;){var b=i[S++],M=S===1;switch(M&&(l=i[S],f=i[S+1],s=l,u=f),b!==Q.L&&m>0&&(t.lineTo(w,T),m=0),b){case Q.M:s=l=i[S++],u=f=i[S++],t.moveTo(l,f);break;case Q.L:{h=i[S++],c=i[S++];var x=Me(h-l),C=Me(c-f);if(x>n||C>a){if(v){var A=d[g++];if(p+A>_){var L=(_-p)/A;t.lineTo(l*(1-L)+h*L,f*(1-L)+c*L);break t}p+=A}t.lineTo(h,c),l=h,f=c,m=0}else{var I=x*x+C*C;I>m&&(w=h,T=c,m=I)}break}case Q.C:{var P=i[S++],R=i[S++],E=i[S++],U=i[S++],z=i[S++],G=i[S++];if(v){var A=d[g++];if(p+A>_){var L=(_-p)/A;Na(l,P,E,z,L,vr),Na(f,R,U,G,L,cr),t.bezierCurveTo(vr[1],cr[1],vr[2],cr[2],vr[3],cr[3]);break t}p+=A}t.bezierCurveTo(P,R,E,U,z,G),l=z,f=G;break}case Q.Q:{var P=i[S++],R=i[S++],E=i[S++],U=i[S++];if(v){var A=d[g++];if(p+A>_){var L=(_-p)/A;Fa(l,P,E,L,vr),Fa(f,R,U,L,cr),t.quadraticCurveTo(vr[1],cr[1],vr[2],cr[2]);break t}p+=A}t.quadraticCurveTo(P,R,E,U),l=E,f=U;break}case Q.A:var $=i[S++],it=i[S++],j=i[S++],wt=i[S++],Ut=i[S++],ke=i[S++],er=i[S++],rr=!i[S++],Gr=j>wt?j:wt,Vt=Me(j-wt)>.001,dt=Ut+ke,V=!1;if(v){var A=d[g++];p+A>_&&(dt=Ut+ke*(_-p)/A,V=!0),p+=A}if(Vt&&t.ellipse?t.ellipse($,it,j,wt,er,Ut,dt,rr):t.arc($,it,Gr,Ut,dt,rr),V)break t;M&&(s=dr(Ut)*j+$,u=pr(Ut)*wt+it),l=dr(dt)*j+$,f=pr(dt)*wt+it;break;case Q.R:s=l=i[S],u=f=i[S+1],h=i[S++],c=i[S++];var q=i[S++],ir=i[S++];if(v){var A=d[g++];if(p+A>_){var Dt=_-p;t.moveTo(h,c),t.lineTo(h+os(Dt,q),c),Dt-=q,Dt>0&&t.lineTo(h+q,c+os(Dt,ir)),Dt-=ir,Dt>0&&t.lineTo(h+ss(q-Dt,0),c+ir),Dt-=q,Dt>0&&t.lineTo(h,c+ss(ir-Dt,0));break t}p+=A}t.rect(h,c,q,ir);break;case Q.Z:if(v){var A=d[g++];if(p+A>_){var L=(_-p)/A;t.lineTo(l*(1-L)+s*L,f*(1-L)+u*L);break t}p+=A}t.closePath(),l=s,f=u}}},r.prototype.clone=function(){var t=new r,e=this.data;return t.data=e.slice?e.slice():Array.prototype.slice.call(e),t._len=this._len,t},r.CMD=Q,r.initDefaultProps=function(){var t=r.prototype;t._saveData=!0,t._ux=0,t._uy=0,t._pendingPtDist=0,t._version=0}(),r}();function Xr(r,t,e,i,n,a,o){if(n===0)return!1;var s=n,u=0,l=r;if(o>t+s&&o>i+s||o<t-s&&o<i-s||a>r+s&&a>e+s||a<r-s&&a<e-s)return!1;if(r!==e)u=(t-i)/(r-e),l=(r*i-e*t)/(r-e);else return Math.abs(a-r)<=s/2;var f=u*a-o+l,h=f*f/(u*u+1);return h<=s/2*s/2}function y0(r,t,e,i,n,a,o,s,u,l,f){if(u===0)return!1;var h=u;if(f>t+h&&f>i+h&&f>a+h&&f>s+h||f<t-h&&f<i-h&&f<a-h&&f<s-h||l>r+h&&l>e+h&&l>n+h&&l>o+h||l<r-h&&l<e-h&&l<n-h&&l<o-h)return!1;var c=ky(r,t,e,i,n,a,o,s,l,f,null);return c<=h/2}function _0(r,t,e,i,n,a,o,s,u){if(o===0)return!1;var l=o;if(u>t+l&&u>i+l&&u>a+l||u<t-l&&u<i-l&&u<a-l||s>r+l&&s>e+l&&s>n+l||s<r-l&&s<e-l&&s<n-l)return!1;var f=Fy(r,t,e,i,n,a,s,u,null);return f<=l/2}var Bf=Math.PI*2;function Xn(r){return r%=Bf,r<0&&(r+=Bf),r}var Li=Math.PI*2;function m0(r,t,e,i,n,a,o,s,u){if(o===0)return!1;var l=o;s-=r,u-=t;var f=Math.sqrt(s*s+u*u);if(f-l>e||f+l<e)return!1;if(Math.abs(i-n)%Li<1e-4)return!0;if(a){var h=i;i=Xn(n),n=Xn(h)}else i=Xn(i),n=Xn(n);i>n&&(n+=Li);var c=Math.atan2(u,s);return c<0&&(c+=Li),c>=i&&c<=n||c+Li>=i&&c+Li<=n}function gr(r,t,e,i,n,a){if(a>t&&a>i||a<t&&a<i||i===t)return 0;var o=(a-t)/(i-t),s=i<t?1:-1;(o===1||o===0)&&(s=i<t?.5:-.5);var u=o*(e-r)+r;return u===n?1/0:u>n?s:0}var Ne=pi.CMD,yr=Math.PI*2,w0=1e-4;function S0(r,t){return Math.abs(r-t)<w0}var Mt=[-1,-1,-1],Kt=[-1,-1];function T0(){var r=Kt[0];Kt[0]=Kt[1],Kt[1]=r}function b0(r,t,e,i,n,a,o,s,u,l){if(l>t&&l>i&&l>a&&l>s||l<t&&l<i&&l<a&&l<s)return 0;var f=xc(t,i,a,s,l,Mt);if(f===0)return 0;for(var h=0,c=-1,v=void 0,d=void 0,y=0;y<f;y++){var p=Mt[y],g=p===0||p===1?.5:1,_=gt(r,e,n,o,p);_<u||(c<0&&(c=Cc(t,i,a,s,Kt),Kt[1]<Kt[0]&&c>1&&T0(),v=gt(t,i,a,s,Kt[0]),c>1&&(d=gt(t,i,a,s,Kt[1]))),c===2?p<Kt[0]?h+=v<t?g:-g:p<Kt[1]?h+=d<v?g:-g:h+=s<d?g:-g:p<Kt[0]?h+=v<t?g:-g:h+=s<v?g:-g)}return h}function x0(r,t,e,i,n,a,o,s){if(s>t&&s>i&&s>a||s<t&&s<i&&s<a)return 0;var u=Ny(t,i,a,s,Mt);if(u===0)return 0;var l=Dc(t,i,a);if(l>=0&&l<=1){for(var f=0,h=xt(t,i,a,l),c=0;c<u;c++){var v=Mt[c]===0||Mt[c]===1?.5:1,d=xt(r,e,n,Mt[c]);d<o||(Mt[c]<l?f+=h<t?v:-v:f+=a<h?v:-v)}return f}else{var v=Mt[0]===0||Mt[0]===1?.5:1,d=xt(r,e,n,Mt[0]);return d<o?0:a<t?v:-v}}function C0(r,t,e,i,n,a,o,s){if(s-=t,s>e||s<-e)return 0;var u=Math.sqrt(e*e-s*s);Mt[0]=-u,Mt[1]=u;var l=Math.abs(i-n);if(l<1e-4)return 0;if(l>=yr-1e-4){i=0,n=yr;var f=a?1:-1;return o>=Mt[0]+r&&o<=Mt[1]+r?f:0}if(i>n){var h=i;i=n,n=h}i<0&&(i+=yr,n+=yr);for(var c=0,v=0;v<2;v++){var d=Mt[v];if(d+r>o){var y=Math.atan2(s,d),f=a?1:-1;y<0&&(y=yr+y),(y>=i&&y<=n||y+yr>=i&&y+yr<=n)&&(y>Math.PI/2&&y<Math.PI*1.5&&(f=-f),c+=f)}}return c}function Jc(r,t,e,i,n){for(var a=r.data,o=r.len(),s=0,u=0,l=0,f=0,h=0,c,v,d=0;d<o;){var y=a[d++],p=d===1;switch(y===Ne.M&&d>1&&(e||(s+=gr(u,l,f,h,i,n))),p&&(u=a[d],l=a[d+1],f=u,h=l),y){case Ne.M:f=a[d++],h=a[d++],u=f,l=h;break;case Ne.L:if(e){if(Xr(u,l,a[d],a[d+1],t,i,n))return!0}else s+=gr(u,l,a[d],a[d+1],i,n)||0;u=a[d++],l=a[d++];break;case Ne.C:if(e){if(y0(u,l,a[d++],a[d++],a[d++],a[d++],a[d],a[d+1],t,i,n))return!0}else s+=b0(u,l,a[d++],a[d++],a[d++],a[d++],a[d],a[d+1],i,n)||0;u=a[d++],l=a[d++];break;case Ne.Q:if(e){if(_0(u,l,a[d++],a[d++],a[d],a[d+1],t,i,n))return!0}else s+=x0(u,l,a[d++],a[d++],a[d],a[d+1],i,n)||0;u=a[d++],l=a[d++];break;case Ne.A:var g=a[d++],_=a[d++],m=a[d++],w=a[d++],T=a[d++],S=a[d++];d+=1;var b=!!(1-a[d++]);c=Math.cos(T)*m+g,v=Math.sin(T)*w+_,p?(f=c,h=v):s+=gr(u,l,c,v,i,n);var M=(i-g)*w/m+g;if(e){if(m0(g,_,w,T,T+S,b,t,M,n))return!0}else s+=C0(g,_,w,T,T+S,b,M,n);u=Math.cos(T+S)*m+g,l=Math.sin(T+S)*w+_;break;case Ne.R:f=u=a[d++],h=l=a[d++];var x=a[d++],C=a[d++];if(c=f+x,v=h+C,e){if(Xr(f,h,c,h,t,i,n)||Xr(c,h,c,v,t,i,n)||Xr(c,v,f,v,t,i,n)||Xr(f,v,f,h,t,i,n))return!0}else s+=gr(c,h,c,v,i,n),s+=gr(f,v,f,h,i,n);break;case Ne.Z:if(e){if(Xr(u,l,f,h,t,i,n))return!0}else s+=gr(u,l,f,h,i,n);u=f,l=h;break}}return!e&&!S0(l,h)&&(s+=gr(u,l,f,h,i,n)||0),s!==0}function D0(r,t,e){return Jc(r,0,!1,t,e)}function M0(r,t,e,i){return Jc(r,t,!0,e,i)}var jc=ot({fill:"#000",stroke:null,strokePercent:1,fillOpacity:1,strokeOpacity:1,lineDashOffset:0,lineWidth:1,lineCap:"butt",miterLimit:10,strokeNoScale:!1,strokeFirst:!1},Er),A0={style:ot({fill:!0,stroke:!0,strokePercent:!0,fillOpacity:!0,strokeOpacity:!0,lineDashOffset:!0,lineWidth:!0,miterLimit:!0},fo.style)},fs=dn.concat(["invisible","culling","z","z2","zlevel","parent"]),nt=function(r){N(t,r);function t(e){return r.call(this,e)||this}return t.prototype.update=function(){var e=this;r.prototype.update.call(this);var i=this.style;if(i.decal){var n=this._decalEl=this._decalEl||new t;n.buildPath===t.prototype.buildPath&&(n.buildPath=function(u){e.buildPath(u,e.shape)}),n.silent=!0;var a=n.style;for(var o in i)a[o]!==i[o]&&(a[o]=i[o]);a.fill=i.fill?i.decal:null,a.decal=null,a.shadowColor=null,i.strokeFirst&&(a.stroke=null);for(var s=0;s<fs.length;++s)n[fs[s]]=this[fs[s]];n.__dirty|=we}else this._decalEl&&(this._decalEl=null)},t.prototype.getDecalElement=function(){return this._decalEl},t.prototype._init=function(e){var i=ft(e);this.shape=this.getDefaultShape();var n=this.getDefaultStyle();n&&this.useStyle(n);for(var a=0;a<i.length;a++){var o=i[a],s=e[o];o==="style"?this.style?O(this.style,s):this.useStyle(s):o==="shape"?O(this.shape,s):r.prototype.attrKV.call(this,o,s)}this.style||this.useStyle({})},t.prototype.getDefaultStyle=function(){return null},t.prototype.getDefaultShape=function(){return{}},t.prototype.canBeInsideText=function(){return this.hasFill()},t.prototype.getInsideTextFill=function(){var e=this.style.fill;if(e!=="none"){if(B(e)){var i=za(e,0);return i>.5?au:i>.2?l_:ou}else if(e)return ou}return au},t.prototype.getInsideTextStroke=function(e){var i=this.style.fill;if(B(i)){var n=this.__zr,a=!!(n&&n.isDarkMode()),o=za(e,0)<nu;if(a===o)return i}},t.prototype.buildPath=function(e,i,n){},t.prototype.pathUpdated=function(){this.__dirty&=-5},t.prototype.getUpdatedPathProxy=function(e){return!this.path&&this.createPathProxy(),this.path.beginPath(),this.buildPath(this.path,this.shape,e),this.path},t.prototype.createPathProxy=function(){this.path=new pi(!1)},t.prototype.hasStroke=function(){var e=this.style,i=e.stroke;return!(i==null||i==="none"||!(e.lineWidth>0))},t.prototype.hasFill=function(){var e=this.style,i=e.fill;return i!=null&&i!=="none"},t.prototype.getBoundingRect=function(){var e=this._rect,i=this.style,n=!e;if(n){var a=!1;this.path||(a=!0,this.createPathProxy());var o=this.path;(a||this.__dirty&Yi)&&(o.beginPath(),this.buildPath(o,this.shape,!1),this.pathUpdated()),e=o.getBoundingRect()}if(this._rect=e,this.hasStroke()&&this.path&&this.path.len()>0){var s=this._rectStroke||(this._rectStroke=e.clone());if(this.__dirty||n){s.copy(e);var u=i.strokeNoScale?this.getLineScale():1,l=i.lineWidth;if(!this.hasFill()){var f=this.strokeContainThreshold;l=Math.max(l,f??4)}u>1e-10&&(s.width+=l/u,s.height+=l/u,s.x-=l/u/2,s.y-=l/u/2)}return s}return e},t.prototype.contain=function(e,i){var n=this.transformCoordToLocal(e,i),a=this.getBoundingRect(),o=this.style;if(e=n[0],i=n[1],a.contain(e,i)){var s=this.path;if(this.hasStroke()){var u=o.lineWidth,l=o.strokeNoScale?this.getLineScale():1;if(l>1e-10&&(this.hasFill()||(u=Math.max(u,this.strokeContainThreshold)),M0(s,u/l,e,i)))return!0}if(this.hasFill())return D0(s,e,i)}return!1},t.prototype.dirtyShape=function(){this.__dirty|=Yi,this._rect&&(this._rect=null),this._decalEl&&this._decalEl.dirtyShape(),this.markRedraw()},t.prototype.dirty=function(){this.dirtyStyle(),this.dirtyShape()},t.prototype.animateShape=function(e){return this.animate("shape",e)},t.prototype.updateDuringAnimation=function(e){e==="style"?this.dirtyStyle():e==="shape"?this.dirtyShape():this.markRedraw()},t.prototype.attrKV=function(e,i){e==="shape"?this.setShape(i):r.prototype.attrKV.call(this,e,i)},t.prototype.setShape=function(e,i){var n=this.shape;return n||(n=this.shape={}),typeof e=="string"?n[e]=i:O(n,e),this.dirtyShape(),this},t.prototype.shapeChanged=function(){return!!(this.__dirty&Yi)},t.prototype.createStyle=function(e){return no(jc,e)},t.prototype._innerSaveToNormal=function(e){r.prototype._innerSaveToNormal.call(this,e);var i=this._normalState;e.shape&&!i.shape&&(i.shape=O({},this.shape))},t.prototype._applyStateObj=function(e,i,n,a,o,s){r.prototype._applyStateObj.call(this,e,i,n,a,o,s);var u=!(i&&a),l;if(i&&i.shape?o?a?l=i.shape:(l=O({},n.shape),O(l,i.shape)):(l=O({},a?this.shape:n.shape),O(l,i.shape)):u&&(l=n.shape),l)if(o){this.shape=O({},this.shape);for(var f={},h=ft(l),c=0;c<h.length;c++){var v=h[c];typeof l[v]=="object"?this.shape[v]=l[v]:f[v]=l[v]}this._transitionState(e,{shape:f},s)}else this.shape=l,this.dirtyShape()},t.prototype._mergeStates=function(e){for(var i=r.prototype._mergeStates.call(this,e),n,a=0;a<e.length;a++){var o=e[a];o.shape&&(n=n||{},this._mergeStyle(n,o.shape))}return n&&(i.shape=n),i},t.prototype.getAnimationStyleProps=function(){return A0},t.prototype.isZeroArea=function(){return!1},t.extend=function(e){var i=function(a){N(o,a);function o(s){var u=a.call(this,s)||this;return e.init&&e.init.call(u,s),u}return o.prototype.getDefaultStyle=function(){return Z(e.style)},o.prototype.getDefaultShape=function(){return Z(e.shape)},o}(t);for(var n in e)typeof e[n]=="function"&&(i.prototype[n]=e[n]);return i},t.initDefaultProps=function(){var e=t.prototype;e.type="path",e.strokeContainThreshold=5,e.segmentIgnoreThreshold=0,e.subPixelOptimize=!1,e.autoBatch=!1,e.__dirty=we|wa|Yi}(),t}(Pn),P0=ot({strokeFirst:!0,font:Nr,x:0,y:0,textAlign:"left",textBaseline:"top",miterLimit:2},jc),Wa=function(r){N(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.hasStroke=function(){var e=this.style,i=e.stroke;return i!=null&&i!=="none"&&e.lineWidth>0},t.prototype.hasFill=function(){var e=this.style,i=e.fill;return i!=null&&i!=="none"},t.prototype.createStyle=function(e){return no(P0,e)},t.prototype.setBoundingRect=function(e){this._rect=e},t.prototype.getBoundingRect=function(){var e=this.style;if(!this._rect){var i=e.text;i!=null?i+="":i="";var n=Ec(i,e.font,e.textAlign,e.textBaseline);if(n.x+=e.x||0,n.y+=e.y||0,this.hasStroke()){var a=e.lineWidth;n.x-=a/2,n.y-=a/2,n.width+=a,n.height+=a}this._rect=n}return this._rect},t.initDefaultProps=function(){var e=t.prototype;e.dirtyRectTolerance=10}(),t}(Pn);Wa.prototype.type="tspan";var L0=ot({x:0,y:0},Er),I0={style:ot({x:!0,y:!0,width:!0,height:!0,sx:!0,sy:!0,sWidth:!0,sHeight:!0},fo.style)};function R0(r){return!!(r&&typeof r!="string"&&r.width&&r.height)}var zr=function(r){N(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.createStyle=function(e){return no(L0,e)},t.prototype._getSize=function(e){var i=this.style,n=i[e];if(n!=null)return n;var a=R0(i.image)?i.image:this.__image;if(!a)return 0;var o=e==="width"?"height":"width",s=i[o];return s==null?a[e]:a[e]/a[o]*s},t.prototype.getWidth=function(){return this._getSize("width")},t.prototype.getHeight=function(){return this._getSize("height")},t.prototype.getAnimationStyleProps=function(){return I0},t.prototype.getBoundingRect=function(){var e=this.style;return this._rect||(this._rect=new J(e.x||0,e.y||0,this.getWidth(),this.getHeight())),this._rect},t}(Pn);zr.prototype.type="image";function E0(r,t){var e=t.x,i=t.y,n=t.width,a=t.height,o=t.r,s,u,l,f;n<0&&(e=e+n,n=-n),a<0&&(i=i+a,a=-a),typeof o=="number"?s=u=l=f=o:o instanceof Array?o.length===1?s=u=l=f=o[0]:o.length===2?(s=l=o[0],u=f=o[1]):o.length===3?(s=o[0],u=f=o[1],l=o[2]):(s=o[0],u=o[1],l=o[2],f=o[3]):s=u=l=f=0;var h;s+u>n&&(h=s+u,s*=n/h,u*=n/h),l+f>n&&(h=l+f,l*=n/h,f*=n/h),u+l>a&&(h=u+l,u*=a/h,l*=a/h),s+f>a&&(h=s+f,s*=a/h,f*=a/h),r.moveTo(e+s,i),r.lineTo(e+n-u,i),u!==0&&r.arc(e+n-u,i+u,u,-Math.PI/2,0),r.lineTo(e+n,i+a-l),l!==0&&r.arc(e+n-l,i+a-l,l,0,Math.PI/2),r.lineTo(e+f,i+a),f!==0&&r.arc(e+f,i+a-f,f,Math.PI/2,Math.PI),r.lineTo(e,i+s),s!==0&&r.arc(e+s,i+s,s,Math.PI,Math.PI*1.5)}var si=Math.round;function td(r,t,e){if(t){var i=t.x1,n=t.x2,a=t.y1,o=t.y2;r.x1=i,r.x2=n,r.y1=a,r.y2=o;var s=e&&e.lineWidth;return s&&(si(i*2)===si(n*2)&&(r.x1=r.x2=Mr(i,s,!0)),si(a*2)===si(o*2)&&(r.y1=r.y2=Mr(a,s,!0))),r}}function ed(r,t,e){if(t){var i=t.x,n=t.y,a=t.width,o=t.height;r.x=i,r.y=n,r.width=a,r.height=o;var s=e&&e.lineWidth;return s&&(r.x=Mr(i,s,!0),r.y=Mr(n,s,!0),r.width=Math.max(Mr(i+a,s,!1)-r.x,a===0?0:1),r.height=Math.max(Mr(n+o,s,!1)-r.y,o===0?0:1)),r}}function Mr(r,t,e){if(!t)return r;var i=si(r*2);return(i+si(t))%2===0?i/2:(i+(e?1:-1))/2}var O0=function(){function r(){this.x=0,this.y=0,this.width=0,this.height=0}return r}(),k0={},Pt=function(r){N(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultShape=function(){return new O0},t.prototype.buildPath=function(e,i){var n,a,o,s;if(this.subPixelOptimize){var u=ed(k0,i,this.style);n=u.x,a=u.y,o=u.width,s=u.height,u.r=i.r,i=u}else n=i.x,a=i.y,o=i.width,s=i.height;i.r?E0(e,i):e.rect(n,a,o,s)},t.prototype.isZeroArea=function(){return!this.shape.width||!this.shape.height},t}(nt);Pt.prototype.type="rect";var Nf={fill:"#000"},Ff=2,B0={style:ot({fill:!0,stroke:!0,fillOpacity:!0,strokeOpacity:!0,lineWidth:!0,fontSize:!0,lineHeight:!0,width:!0,height:!0,textShadowColor:!0,textShadowBlur:!0,textShadowOffsetX:!0,textShadowOffsetY:!0,backgroundColor:!0,padding:!0,borderColor:!0,borderWidth:!0,borderRadius:!0},fo.style)},Lt=function(r){N(t,r);function t(e){var i=r.call(this)||this;return i.type="text",i._children=[],i._defaultStyle=Nf,i.attr(e),i}return t.prototype.childrenRef=function(){return this._children},t.prototype.update=function(){r.prototype.update.call(this),this.styleChanged()&&this._updateSubTexts();for(var e=0;e<this._children.length;e++){var i=this._children[e];i.zlevel=this.zlevel,i.z=this.z,i.z2=this.z2,i.culling=this.culling,i.cursor=this.cursor,i.invisible=this.invisible}},t.prototype.updateTransform=function(){var e=this.innerTransformable;e?(e.updateTransform(),e.transform&&(this.transform=e.transform)):r.prototype.updateTransform.call(this)},t.prototype.getLocalTransform=function(e){var i=this.innerTransformable;return i?i.getLocalTransform(e):r.prototype.getLocalTransform.call(this,e)},t.prototype.getComputedTransform=function(){return this.__hostTarget&&(this.__hostTarget.getComputedTransform(),this.__hostTarget.updateInnerText(!0)),r.prototype.getComputedTransform.call(this)},t.prototype._updateSubTexts=function(){this._childCursor=0,G0(this.style),this.style.rich?this._updateRichTexts():this._updatePlainTexts(),this._children.length=this._childCursor,this.styleUpdated()},t.prototype.addSelfToZr=function(e){r.prototype.addSelfToZr.call(this,e);for(var i=0;i<this._children.length;i++)this._children[i].__zr=e},t.prototype.removeSelfFromZr=function(e){r.prototype.removeSelfFromZr.call(this,e);for(var i=0;i<this._children.length;i++)this._children[i].__zr=null},t.prototype.getBoundingRect=function(){if(this.styleChanged()&&this._updateSubTexts(),!this._rect){for(var e=new J(0,0,0,0),i=this._children,n=[],a=null,o=0;o<i.length;o++){var s=i[o],u=s.getBoundingRect(),l=s.getLocalTransform(n);l?(e.copy(u),e.applyTransform(l),a=a||e.clone(),a.union(e)):(a=a||u.clone(),a.union(u))}this._rect=a||e}return this._rect},t.prototype.setDefaultTextStyle=function(e){this._defaultStyle=e||Nf},t.prototype.setTextContent=function(e){},t.prototype._mergeStyle=function(e,i){if(!i)return e;var n=i.rich,a=e.rich||n&&{};return O(e,i),n&&a?(this._mergeRich(a,n),e.rich=a):a&&(e.rich=a),e},t.prototype._mergeRich=function(e,i){for(var n=ft(i),a=0;a<n.length;a++){var o=n[a];e[o]=e[o]||{},O(e[o],i[o])}},t.prototype.getAnimationStyleProps=function(){return B0},t.prototype._getOrCreateChild=function(e){var i=this._children[this._childCursor];return(!i||!(i instanceof e))&&(i=new e),this._children[this._childCursor++]=i,i.__zr=this.__zr,i.parent=this,i},t.prototype._updatePlainTexts=function(){var e=this.style,i=e.font||Nr,n=e.padding,a=Yf(e),o=n0(a,e),s=hs(e),u=!!e.backgroundColor,l=o.outerHeight,f=o.outerWidth,h=o.contentWidth,c=o.lines,v=o.lineHeight,d=this._defaultStyle;this.isTruncated=!!o.isTruncated;var y=e.x||0,p=e.y||0,g=e.align||d.align||"left",_=e.verticalAlign||d.verticalAlign||"top",m=y,w=ii(p,o.contentHeight,_);if(s||n){var T=$i(y,f,g),S=ii(p,l,_);s&&this._renderBackground(e,e,T,S,f,l)}w+=v/2,n&&(m=Uf(y,g,n),_==="top"?w+=n[0]:_==="bottom"&&(w-=n[2]));for(var b=0,M=!1,x=Wf("fill"in e?e.fill:(M=!0,d.fill)),C=Vf("stroke"in e?e.stroke:!u&&(!d.autoStroke||M)?(b=Ff,d.stroke):null),A=e.textShadowBlur>0,L=e.width!=null&&(e.overflow==="truncate"||e.overflow==="break"||e.overflow==="breakAll"),I=o.calculatedLineHeight,P=0;P<c.length;P++){var R=this._getOrCreateChild(Wa),E=R.createStyle();R.useStyle(E),E.text=c[P],E.x=m,E.y=w,E.textAlign=g,E.textBaseline="middle",E.opacity=e.opacity,E.strokeFirst=!0,A&&(E.shadowBlur=e.textShadowBlur||0,E.shadowColor=e.textShadowColor||"transparent",E.shadowOffsetX=e.textShadowOffsetX||0,E.shadowOffsetY=e.textShadowOffsetY||0),E.stroke=C,E.fill=x,C&&(E.lineWidth=e.lineWidth||b,E.lineDash=e.lineDash,E.lineDashOffset=e.lineDashOffset||0),E.font=i,Hf(E,e),w+=v,L&&R.setBoundingRect(new J($i(E.x,h,E.textAlign),ii(E.y,I,E.textBaseline),h,I))}},t.prototype._updateRichTexts=function(){var e=this.style,i=Yf(e),n=s0(i,e),a=n.width,o=n.outerWidth,s=n.outerHeight,u=e.padding,l=e.x||0,f=e.y||0,h=this._defaultStyle,c=e.align||h.align,v=e.verticalAlign||h.verticalAlign;this.isTruncated=!!n.isTruncated;var d=$i(l,o,c),y=ii(f,s,v),p=d,g=y;u&&(p+=u[3],g+=u[0]);var _=p+a;hs(e)&&this._renderBackground(e,e,d,y,o,s);for(var m=!!e.backgroundColor,w=0;w<n.lines.length;w++){for(var T=n.lines[w],S=T.tokens,b=S.length,M=T.lineHeight,x=T.width,C=0,A=p,L=_,I=b-1,P=void 0;C<b&&(P=S[C],!P.align||P.align==="left");)this._placeToken(P,e,M,g,A,"left",m),x-=P.width,A+=P.width,C++;for(;I>=0&&(P=S[I],P.align==="right");)this._placeToken(P,e,M,g,L,"right",m),x-=P.width,L-=P.width,I--;for(A+=(a-(A-p)-(_-L)-x)/2;C<=I;)P=S[C],this._placeToken(P,e,M,g,A+P.width/2,"center",m),A+=P.width,C++;g+=M}},t.prototype._placeToken=function(e,i,n,a,o,s,u){var l=i.rich[e.styleName]||{};l.text=e.text;var f=e.verticalAlign,h=a+n/2;f==="top"?h=a+e.height/2:f==="bottom"&&(h=a+n-e.height/2);var c=!e.isLineHolder&&hs(l);c&&this._renderBackground(l,i,s==="right"?o-e.width:s==="center"?o-e.width/2:o,h-e.height/2,e.width,e.height);var v=!!l.backgroundColor,d=e.textPadding;d&&(o=Uf(o,s,d),h-=e.height/2-d[0]-e.innerHeight/2);var y=this._getOrCreateChild(Wa),p=y.createStyle();y.useStyle(p);var g=this._defaultStyle,_=!1,m=0,w=Wf("fill"in l?l.fill:"fill"in i?i.fill:(_=!0,g.fill)),T=Vf("stroke"in l?l.stroke:"stroke"in i?i.stroke:!v&&!u&&(!g.autoStroke||_)?(m=Ff,g.stroke):null),S=l.textShadowBlur>0||i.textShadowBlur>0;p.text=e.text,p.x=o,p.y=h,S&&(p.shadowBlur=l.textShadowBlur||i.textShadowBlur||0,p.shadowColor=l.textShadowColor||i.textShadowColor||"transparent",p.shadowOffsetX=l.textShadowOffsetX||i.textShadowOffsetX||0,p.shadowOffsetY=l.textShadowOffsetY||i.textShadowOffsetY||0),p.textAlign=s,p.textBaseline="middle",p.font=e.font||Nr,p.opacity=ya(l.opacity,i.opacity,1),Hf(p,l),T&&(p.lineWidth=ya(l.lineWidth,i.lineWidth,m),p.lineDash=W(l.lineDash,i.lineDash),p.lineDashOffset=i.lineDashOffset||0,p.stroke=T),w&&(p.fill=w);var b=e.contentWidth,M=e.contentHeight;y.setBoundingRect(new J($i(p.x,b,p.textAlign),ii(p.y,M,p.textBaseline),b,M))},t.prototype._renderBackground=function(e,i,n,a,o,s){var u=e.backgroundColor,l=e.borderWidth,f=e.borderColor,h=u&&u.image,c=u&&!h,v=e.borderRadius,d=this,y,p;if(c||e.lineHeight||l&&f){y=this._getOrCreateChild(Pt),y.useStyle(y.createStyle()),y.style.fill=null;var g=y.shape;g.x=n,g.y=a,g.width=o,g.height=s,g.r=v,y.dirtyShape()}if(c){var _=y.style;_.fill=u||null,_.fillOpacity=W(e.fillOpacity,1)}else if(h){p=this._getOrCreateChild(zr),p.onload=function(){d.dirtyStyle()};var m=p.style;m.image=u.image,m.x=n,m.y=a,m.width=o,m.height=s}if(l&&f){var _=y.style;_.lineWidth=l,_.stroke=f,_.strokeOpacity=W(e.strokeOpacity,1),_.lineDash=e.borderDash,_.lineDashOffset=e.borderDashOffset||0,y.strokeContainThreshold=0,y.hasFill()&&y.hasStroke()&&(_.strokeFirst=!0,_.lineWidth*=2)}var w=(y||p).style;w.shadowBlur=e.shadowBlur||0,w.shadowColor=e.shadowColor||"transparent",w.shadowOffsetX=e.shadowOffsetX||0,w.shadowOffsetY=e.shadowOffsetY||0,w.opacity=ya(e.opacity,i.opacity,1)},t.makeFont=function(e){var i="";return H0(e)&&(i=[e.fontStyle,e.fontWeight,z0(e.fontSize),e.fontFamily||"sans-serif"].join(" ")),i&&ye(i)||e.textFont||e.font},t}(Pn),N0={left:!0,right:1,center:1},F0={top:1,bottom:1,middle:1},zf=["fontStyle","fontWeight","fontSize","fontFamily"];function z0(r){return typeof r=="string"&&(r.indexOf("px")!==-1||r.indexOf("rem")!==-1||r.indexOf("em")!==-1)?r:isNaN(+r)?Uu+"px":r+"px"}function Hf(r,t){for(var e=0;e<zf.length;e++){var i=zf[e],n=t[i];n!=null&&(r[i]=n)}}function H0(r){return r.fontSize!=null||r.fontFamily||r.fontWeight}function G0(r){return Gf(r),D(r.rich,Gf),r}function Gf(r){if(r){r.font=Lt.makeFont(r);var t=r.align;t==="middle"&&(t="center"),r.align=t==null||N0[t]?t:"left";var e=r.verticalAlign;e==="center"&&(e="middle"),r.verticalAlign=e==null||F0[e]?e:"top";var i=r.padding;i&&(r.padding=lc(r.padding))}}function Vf(r,t){return r==null||t<=0||r==="transparent"||r==="none"?null:r.image||r.colorStops?"#000":r}function Wf(r){return r==null||r==="none"?null:r.image||r.colorStops?"#000":r}function Uf(r,t,e){return t==="right"?r-e[1]:t==="center"?r+e[3]/2-e[1]/2:r+e[3]}function Yf(r){var t=r.text;return t!=null&&(t+=""),t}function hs(r){return!!(r.backgroundColor||r.lineHeight||r.borderWidth&&r.borderColor)}var rt=mt(),V0=function(r,t,e,i){if(i){var n=rt(i);n.dataIndex=e,n.dataType=t,n.seriesIndex=r,n.ssrType="chart",i.type==="group"&&i.traverse(function(a){var o=rt(a);o.seriesIndex=r,o.dataIndex=e,o.dataType=t,o.ssrType="chart"})}},Xf=1,$f={},rd=mt(),al=mt(),ol=0,ho=1,vo=2,je=["emphasis","blur","select"],qf=["normal","emphasis","blur","select"],W0=10,U0=9,Or="highlight",Da="downplay",en="select",Ma="unselect",rn="toggleSelect";function $r(r){return r!=null&&r!=="none"}function co(r,t,e){r.onHoverStateChange&&(r.hoverState||0)!==e&&r.onHoverStateChange(t),r.hoverState=e}function id(r){co(r,"emphasis",vo)}function nd(r){r.hoverState===vo&&co(r,"normal",ol)}function sl(r){co(r,"blur",ho)}function ad(r){r.hoverState===ho&&co(r,"normal",ol)}function Y0(r){r.selected=!0}function X0(r){r.selected=!1}function Zf(r,t,e){t(r,e)}function Oe(r,t,e){Zf(r,t,e),r.isGroup&&r.traverse(function(i){Zf(i,t,e)})}function EC(r,t){switch(t){case"emphasis":r.hoverState=vo;break;case"normal":r.hoverState=ol;break;case"blur":r.hoverState=ho;break;case"select":r.selected=!0}}function $0(r,t,e,i){for(var n=r.style,a={},o=0;o<t.length;o++){var s=t[o],u=n[s];a[s]=u??(i&&i[s])}for(var o=0;o<r.animators.length;o++){var l=r.animators[o];l.__fromStateTransition&&l.__fromStateTransition.indexOf(e)<0&&l.targetName==="style"&&l.saveTo(a,t)}return a}function q0(r,t,e,i){var n=e&&at(e,"select")>=0,a=!1;if(r instanceof nt){var o=rd(r),s=n&&o.selectFill||o.normalFill,u=n&&o.selectStroke||o.normalStroke;if($r(s)||$r(u)){i=i||{};var l=i.style||{};l.fill==="inherit"?(a=!0,i=O({},i),l=O({},l),l.fill=s):!$r(l.fill)&&$r(s)?(a=!0,i=O({},i),l=O({},l),l.fill=df(s)):!$r(l.stroke)&&$r(u)&&(a||(i=O({},i),l=O({},l)),l.stroke=df(u)),i.style=l}}if(i&&i.z2==null){a||(i=O({},i));var f=r.z2EmphasisLift;i.z2=r.z2+(f??W0)}return i}function Z0(r,t,e){if(e&&e.z2==null){e=O({},e);var i=r.z2SelectLift;e.z2=r.z2+(i??U0)}return e}function K0(r,t,e){var i=at(r.currentStates,t)>=0,n=r.style.opacity,a=i?null:$0(r,["opacity"],t,{opacity:1});e=e||{};var o=e.style||{};return o.opacity==null&&(e=O({},e),o=O({opacity:i?n:a.opacity*.1},o),e.style=o),e}function vs(r,t){var e=this.states[r];if(this.style){if(r==="emphasis")return q0(this,r,t,e);if(r==="blur")return K0(this,r,e);if(r==="select")return Z0(this,r,e)}return e}function Q0(r){r.stateProxy=vs;var t=r.getTextContent(),e=r.getTextGuideLine();t&&(t.stateProxy=vs),e&&(e.stateProxy=vs)}function Kf(r,t){!ld(r,t)&&!r.__highByOuter&&Oe(r,id)}function Qf(r,t){!ld(r,t)&&!r.__highByOuter&&Oe(r,nd)}function hu(r,t){r.__highByOuter|=1<<(t||0),Oe(r,id)}function vu(r,t){!(r.__highByOuter&=~(1<<(t||0)))&&Oe(r,nd)}function J0(r){Oe(r,sl)}function od(r){Oe(r,ad)}function sd(r){Oe(r,Y0)}function ud(r){Oe(r,X0)}function ld(r,t){return r.__highDownSilentOnTouch&&t.zrByTouch}function fd(r){var t=r.getModel(),e=[],i=[];t.eachComponent(function(n,a){var o=al(a),s=n==="series",u=s?r.getViewOfSeriesModel(a):r.getViewOfComponentModel(a);!s&&i.push(u),o.isBlured&&(u.group.traverse(function(l){ad(l)}),s&&e.push(a)),o.isBlured=!1}),D(i,function(n){n&&n.toggleBlurSeries&&n.toggleBlurSeries(e,!1,t)})}function cu(r,t,e,i){var n=i.getModel();e=e||"coordinateSystem";function a(l,f){for(var h=0;h<f.length;h++){var c=l.getItemGraphicEl(f[h]);c&&od(c)}}if(r!=null&&!(!t||t==="none")){var o=n.getSeriesByIndex(r),s=o.coordinateSystem;s&&s.master&&(s=s.master);var u=[];n.eachSeries(function(l){var f=o===l,h=l.coordinateSystem;h&&h.master&&(h=h.master);var c=h&&s?h===s:f;if(!(e==="series"&&!f||e==="coordinateSystem"&&!c||t==="series"&&f)){var v=i.getViewOfSeriesModel(l);if(v.group.traverse(function(p){p.__highByOuter&&f&&t==="self"||sl(p)}),zt(t))a(l.getData(),t);else if(F(t))for(var d=ft(t),y=0;y<d.length;y++)a(l.getData(d[y]),t[d[y]]);u.push(l),al(l).isBlured=!0}}),n.eachComponent(function(l,f){if(l!=="series"){var h=i.getViewOfComponentModel(f);h&&h.toggleBlurSeries&&h.toggleBlurSeries(u,!0,n)}})}}function du(r,t,e){if(!(r==null||t==null)){var i=e.getModel().getComponent(r,t);if(i){al(i).isBlured=!0;var n=e.getViewOfComponentModel(i);!n||!n.focusBlurEnabled||n.group.traverse(function(a){sl(a)})}}}function j0(r,t,e){var i=r.seriesIndex,n=r.getData(t.dataType);if(n){var a=Mn(n,t);a=(k(a)?a[0]:a)||0;var o=n.getItemGraphicEl(a);if(!o)for(var s=n.count(),u=0;!o&&u<s;)o=n.getItemGraphicEl(u++);if(o){var l=rt(o);cu(i,l.focus,l.blurScope,e)}else{var f=r.get(["emphasis","focus"]),h=r.get(["emphasis","blurScope"]);f!=null&&cu(i,f,h,e)}}}function ul(r,t,e,i){var n={focusSelf:!1,dispatchers:null};if(r==null||r==="series"||t==null||e==null)return n;var a=i.getModel().getComponent(r,t);if(!a)return n;var o=i.getViewOfComponentModel(a);if(!o||!o.findHighDownDispatchers)return n;for(var s=o.findHighDownDispatchers(e),u,l=0;l<s.length;l++)if(rt(s[l]).focus==="self"){u=!0;break}return{focusSelf:u,dispatchers:s}}function tm(r,t,e){var i=rt(r),n=ul(i.componentMainType,i.componentIndex,i.componentHighDownName,e),a=n.dispatchers,o=n.focusSelf;a?(o&&du(i.componentMainType,i.componentIndex,e),D(a,function(s){return Kf(s,t)})):(cu(i.seriesIndex,i.focus,i.blurScope,e),i.focus==="self"&&du(i.componentMainType,i.componentIndex,e),Kf(r,t))}function em(r,t,e){fd(e);var i=rt(r),n=ul(i.componentMainType,i.componentIndex,i.componentHighDownName,e).dispatchers;n?D(n,function(a){return Qf(a,t)}):Qf(r,t)}function rm(r,t,e){if(yu(t)){var i=t.dataType,n=r.getData(i),a=Mn(n,t);k(a)||(a=[a]),r[t.type===rn?"toggleSelect":t.type===en?"select":"unselect"](a,i)}}function Jf(r){var t=r.getAllData();D(t,function(e){var i=e.data,n=e.type;i.eachItemGraphicEl(function(a,o){r.isSelected(o,n)?sd(a):ud(a)})})}function im(r){var t=[];return r.eachSeries(function(e){var i=e.getAllData();D(i,function(n){n.data;var a=n.type,o=e.getSelectedDataIndices();if(o.length>0){var s={dataIndex:o,seriesIndex:e.seriesIndex};a!=null&&(s.dataType=a),t.push(s)}})}),t}function pu(r,t,e){hd(r,!0),Oe(r,Q0),am(r,t,e)}function nm(r){hd(r,!1)}function OC(r,t,e,i){i?nm(r):pu(r,t,e)}function am(r,t,e){var i=rt(r);t!=null?(i.focus=t,i.blurScope=e):i.focus&&(i.focus=null)}var jf=["emphasis","blur","select"],om={itemStyle:"getItemStyle",lineStyle:"getLineStyle",areaStyle:"getAreaStyle"};function kC(r,t,e,i){e=e||"itemStyle";for(var n=0;n<jf.length;n++){var a=jf[n],o=t.getModel([a,e]),s=r.ensureState(a);s.style=i?i(o):o[om[e]]()}}function hd(r,t){var e=t===!1,i=r;r.highDownSilentOnTouch&&(i.__highDownSilentOnTouch=r.highDownSilentOnTouch),(!e||i.__highDownDispatcher)&&(i.__highByOuter=i.__highByOuter||0,i.__highDownDispatcher=!e)}function gu(r){return!!(r&&r.__highDownDispatcher)}function BC(r,t,e){var i=rt(r);i.componentMainType=t.mainType,i.componentIndex=t.componentIndex,i.componentHighDownName=e}function sm(r){var t=$f[r];return t==null&&Xf<=32&&(t=$f[r]=Xf++),t}function yu(r){var t=r.type;return t===en||t===Ma||t===rn}function th(r){var t=r.type;return t===Or||t===Da}function um(r){var t=rd(r);t.normalFill=r.style.fill,t.normalStroke=r.style.stroke;var e=r.states.select||{};t.selectFill=e.style&&e.style.fill||null,t.selectStroke=e.style&&e.style.stroke||null}var qr=pi.CMD,lm=[[],[],[]],eh=Math.sqrt,fm=Math.atan2;function vd(r,t){if(t){var e=r.data,i=r.len(),n,a,o,s,u,l,f=qr.M,h=qr.C,c=qr.L,v=qr.R,d=qr.A,y=qr.Q;for(o=0,s=0;o<i;){switch(n=e[o++],s=o,a=0,n){case f:a=1;break;case c:a=1;break;case h:a=3;break;case y:a=2;break;case d:var p=t[4],g=t[5],_=eh(t[0]*t[0]+t[1]*t[1]),m=eh(t[2]*t[2]+t[3]*t[3]),w=fm(-t[1]/m,t[0]/_);e[o]*=_,e[o++]+=p,e[o]*=m,e[o++]+=g,e[o++]*=_,e[o++]*=m,e[o++]+=w,e[o++]+=w,o+=2,s=o;break;case v:l[0]=e[o++],l[1]=e[o++],me(l,l,t),e[s++]=l[0],e[s++]=l[1],l[0]+=e[o++],l[1]+=e[o++],me(l,l,t),e[s++]=l[0],e[s++]=l[1]}for(u=0;u<a;u++){var T=lm[u];T[0]=e[o++],T[1]=e[o++],me(T,T,t),e[s++]=T[0],e[s++]=T[1]}}r.increaseVersion()}}var cs=Math.sqrt,$n=Math.sin,qn=Math.cos,Ii=Math.PI;function rh(r){return Math.sqrt(r[0]*r[0]+r[1]*r[1])}function _u(r,t){return(r[0]*t[0]+r[1]*t[1])/(rh(r)*rh(t))}function ih(r,t){return(r[0]*t[1]<r[1]*t[0]?-1:1)*Math.acos(_u(r,t))}function nh(r,t,e,i,n,a,o,s,u,l,f){var h=u*(Ii/180),c=qn(h)*(r-e)/2+$n(h)*(t-i)/2,v=-1*$n(h)*(r-e)/2+qn(h)*(t-i)/2,d=c*c/(o*o)+v*v/(s*s);d>1&&(o*=cs(d),s*=cs(d));var y=(n===a?-1:1)*cs((o*o*(s*s)-o*o*(v*v)-s*s*(c*c))/(o*o*(v*v)+s*s*(c*c)))||0,p=y*o*v/s,g=y*-s*c/o,_=(r+e)/2+qn(h)*p-$n(h)*g,m=(t+i)/2+$n(h)*p+qn(h)*g,w=ih([1,0],[(c-p)/o,(v-g)/s]),T=[(c-p)/o,(v-g)/s],S=[(-1*c-p)/o,(-1*v-g)/s],b=ih(T,S);if(_u(T,S)<=-1&&(b=Ii),_u(T,S)>=1&&(b=0),b<0){var M=Math.round(b/Ii*1e6)/1e6;b=Ii*2+M%2*Ii}f.addData(l,_,m,o,s,w,b,h,a)}var hm=/([mlvhzcqtsa])([^mlvhzcqtsa]*)/ig,vm=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;function cm(r){var t=new pi;if(!r)return t;var e=0,i=0,n=e,a=i,o,s=pi.CMD,u=r.match(hm);if(!u)return t;for(var l=0;l<u.length;l++){for(var f=u[l],h=f.charAt(0),c=void 0,v=f.match(vm)||[],d=v.length,y=0;y<d;y++)v[y]=parseFloat(v[y]);for(var p=0;p<d;){var g=void 0,_=void 0,m=void 0,w=void 0,T=void 0,S=void 0,b=void 0,M=e,x=i,C=void 0,A=void 0;switch(h){case"l":e+=v[p++],i+=v[p++],c=s.L,t.addData(c,e,i);break;case"L":e=v[p++],i=v[p++],c=s.L,t.addData(c,e,i);break;case"m":e+=v[p++],i+=v[p++],c=s.M,t.addData(c,e,i),n=e,a=i,h="l";break;case"M":e=v[p++],i=v[p++],c=s.M,t.addData(c,e,i),n=e,a=i,h="L";break;case"h":e+=v[p++],c=s.L,t.addData(c,e,i);break;case"H":e=v[p++],c=s.L,t.addData(c,e,i);break;case"v":i+=v[p++],c=s.L,t.addData(c,e,i);break;case"V":i=v[p++],c=s.L,t.addData(c,e,i);break;case"C":c=s.C,t.addData(c,v[p++],v[p++],v[p++],v[p++],v[p++],v[p++]),e=v[p-2],i=v[p-1];break;case"c":c=s.C,t.addData(c,v[p++]+e,v[p++]+i,v[p++]+e,v[p++]+i,v[p++]+e,v[p++]+i),e+=v[p-2],i+=v[p-1];break;case"S":g=e,_=i,C=t.len(),A=t.data,o===s.C&&(g+=e-A[C-4],_+=i-A[C-3]),c=s.C,M=v[p++],x=v[p++],e=v[p++],i=v[p++],t.addData(c,g,_,M,x,e,i);break;case"s":g=e,_=i,C=t.len(),A=t.data,o===s.C&&(g+=e-A[C-4],_+=i-A[C-3]),c=s.C,M=e+v[p++],x=i+v[p++],e+=v[p++],i+=v[p++],t.addData(c,g,_,M,x,e,i);break;case"Q":M=v[p++],x=v[p++],e=v[p++],i=v[p++],c=s.Q,t.addData(c,M,x,e,i);break;case"q":M=v[p++]+e,x=v[p++]+i,e+=v[p++],i+=v[p++],c=s.Q,t.addData(c,M,x,e,i);break;case"T":g=e,_=i,C=t.len(),A=t.data,o===s.Q&&(g+=e-A[C-4],_+=i-A[C-3]),e=v[p++],i=v[p++],c=s.Q,t.addData(c,g,_,e,i);break;case"t":g=e,_=i,C=t.len(),A=t.data,o===s.Q&&(g+=e-A[C-4],_+=i-A[C-3]),e+=v[p++],i+=v[p++],c=s.Q,t.addData(c,g,_,e,i);break;case"A":m=v[p++],w=v[p++],T=v[p++],S=v[p++],b=v[p++],M=e,x=i,e=v[p++],i=v[p++],c=s.A,nh(M,x,e,i,S,b,m,w,T,c,t);break;case"a":m=v[p++],w=v[p++],T=v[p++],S=v[p++],b=v[p++],M=e,x=i,e+=v[p++],i+=v[p++],c=s.A,nh(M,x,e,i,S,b,m,w,T,c,t);break}}(h==="z"||h==="Z")&&(c=s.Z,t.addData(c),e=n,i=a),o=c}return t.toStatic(),t}var cd=function(r){N(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.applyTransform=function(e){},t}(nt);function dd(r){return r.setData!=null}function pd(r,t){var e=cm(r),i=O({},t);return i.buildPath=function(n){if(dd(n)){n.setData(e.data);var a=n.getContext();a&&n.rebuildPath(a,1)}else{var a=n;e.rebuildPath(a,1)}},i.applyTransform=function(n){vd(e,n),this.dirtyShape()},i}function dm(r,t){return new cd(pd(r,t))}function pm(r,t){var e=pd(r,t),i=function(n){N(a,n);function a(o){var s=n.call(this,o)||this;return s.applyTransform=e.applyTransform,s.buildPath=e.buildPath,s}return a}(cd);return i}function gm(r,t){for(var e=[],i=r.length,n=0;n<i;n++){var a=r[n];e.push(a.getUpdatedPathProxy(!0))}var o=new nt(t);return o.createPathProxy(),o.buildPath=function(s){if(dd(s)){s.appendPath(e);var u=s.getContext();u&&s.rebuildPath(u,1)}},o}function NC(r,t){t=t||{};var e=new nt;return r.shape&&e.setShape(r.shape),e.setStyle(r.style),t.bakeTransform?vd(e.path,r.getComputedTransform()):t.toLocal?e.setLocalTransform(r.getComputedTransform()):e.copyTransform(r),e.buildPath=r.buildPath,e.applyTransform=e.applyTransform,e.z=r.z,e.z2=r.z2,e.zlevel=r.zlevel,e}var ym=function(){function r(){this.cx=0,this.cy=0,this.r=0}return r}(),po=function(r){N(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultShape=function(){return new ym},t.prototype.buildPath=function(e,i){e.moveTo(i.cx+i.r,i.cy),e.arc(i.cx,i.cy,i.r,0,Math.PI*2)},t}(nt);po.prototype.type="circle";var _m=function(){function r(){this.cx=0,this.cy=0,this.rx=0,this.ry=0}return r}(),ll=function(r){N(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultShape=function(){return new _m},t.prototype.buildPath=function(e,i){var n=.5522848,a=i.cx,o=i.cy,s=i.rx,u=i.ry,l=s*n,f=u*n;e.moveTo(a-s,o),e.bezierCurveTo(a-s,o-f,a-l,o-u,a,o-u),e.bezierCurveTo(a+l,o-u,a+s,o-f,a+s,o),e.bezierCurveTo(a+s,o+f,a+l,o+u,a,o+u),e.bezierCurveTo(a-l,o+u,a-s,o+f,a-s,o),e.closePath()},t}(nt);ll.prototype.type="ellipse";var gd=Math.PI,ds=gd*2,_r=Math.sin,Zr=Math.cos,mm=Math.acos,St=Math.atan2,ah=Math.abs,nn=Math.sqrt,qi=Math.max,pe=Math.min,re=1e-4;function wm(r,t,e,i,n,a,o,s){var u=e-r,l=i-t,f=o-n,h=s-a,c=h*u-f*l;if(!(c*c<re))return c=(f*(t-a)-h*(r-n))/c,[r+c*u,t+c*l]}function Zn(r,t,e,i,n,a,o){var s=r-e,u=t-i,l=(o?a:-a)/nn(s*s+u*u),f=l*u,h=-l*s,c=r+f,v=t+h,d=e+f,y=i+h,p=(c+d)/2,g=(v+y)/2,_=d-c,m=y-v,w=_*_+m*m,T=n-a,S=c*y-d*v,b=(m<0?-1:1)*nn(qi(0,T*T*w-S*S)),M=(S*m-_*b)/w,x=(-S*_-m*b)/w,C=(S*m+_*b)/w,A=(-S*_+m*b)/w,L=M-p,I=x-g,P=C-p,R=A-g;return L*L+I*I>P*P+R*R&&(M=C,x=A),{cx:M,cy:x,x0:-f,y0:-h,x1:M*(n/T-1),y1:x*(n/T-1)}}function Sm(r){var t;if(k(r)){var e=r.length;if(!e)return r;e===1?t=[r[0],r[0],0,0]:e===2?t=[r[0],r[0],r[1],r[1]]:e===3?t=r.concat(r[2]):t=r}else t=[r,r,r,r];return t}function Tm(r,t){var e,i=qi(t.r,0),n=qi(t.r0||0,0),a=i>0,o=n>0;if(!(!a&&!o)){if(a||(i=n,n=0),n>i){var s=i;i=n,n=s}var u=t.startAngle,l=t.endAngle;if(!(isNaN(u)||isNaN(l))){var f=t.cx,h=t.cy,c=!!t.clockwise,v=ah(l-u),d=v>ds&&v%ds;if(d>re&&(v=d),!(i>re))r.moveTo(f,h);else if(v>ds-re)r.moveTo(f+i*Zr(u),h+i*_r(u)),r.arc(f,h,i,u,l,!c),n>re&&(r.moveTo(f+n*Zr(l),h+n*_r(l)),r.arc(f,h,n,l,u,c));else{var y=void 0,p=void 0,g=void 0,_=void 0,m=void 0,w=void 0,T=void 0,S=void 0,b=void 0,M=void 0,x=void 0,C=void 0,A=void 0,L=void 0,I=void 0,P=void 0,R=i*Zr(u),E=i*_r(u),U=n*Zr(l),z=n*_r(l),G=v>re;if(G){var $=t.cornerRadius;$&&(e=Sm($),y=e[0],p=e[1],g=e[2],_=e[3]);var it=ah(i-n)/2;if(m=pe(it,g),w=pe(it,_),T=pe(it,y),S=pe(it,p),x=b=qi(m,w),C=M=qi(T,S),(b>re||M>re)&&(A=i*Zr(l),L=i*_r(l),I=n*Zr(u),P=n*_r(u),v<gd)){var j=wm(R,E,I,P,A,L,U,z);if(j){var wt=R-j[0],Ut=E-j[1],ke=A-j[0],er=L-j[1],rr=1/_r(mm((wt*ke+Ut*er)/(nn(wt*wt+Ut*Ut)*nn(ke*ke+er*er)))/2),Gr=nn(j[0]*j[0]+j[1]*j[1]);x=pe(b,(i-Gr)/(rr+1)),C=pe(M,(n-Gr)/(rr-1))}}}if(!G)r.moveTo(f+R,h+E);else if(x>re){var Vt=pe(g,x),dt=pe(_,x),V=Zn(I,P,R,E,i,Vt,c),q=Zn(A,L,U,z,i,dt,c);r.moveTo(f+V.cx+V.x0,h+V.cy+V.y0),x<b&&Vt===dt?r.arc(f+V.cx,h+V.cy,x,St(V.y0,V.x0),St(q.y0,q.x0),!c):(Vt>0&&r.arc(f+V.cx,h+V.cy,Vt,St(V.y0,V.x0),St(V.y1,V.x1),!c),r.arc(f,h,i,St(V.cy+V.y1,V.cx+V.x1),St(q.cy+q.y1,q.cx+q.x1),!c),dt>0&&r.arc(f+q.cx,h+q.cy,dt,St(q.y1,q.x1),St(q.y0,q.x0),!c))}else r.moveTo(f+R,h+E),r.arc(f,h,i,u,l,!c);if(!(n>re)||!G)r.lineTo(f+U,h+z);else if(C>re){var Vt=pe(y,C),dt=pe(p,C),V=Zn(U,z,A,L,n,-dt,c),q=Zn(R,E,I,P,n,-Vt,c);r.lineTo(f+V.cx+V.x0,h+V.cy+V.y0),C<M&&Vt===dt?r.arc(f+V.cx,h+V.cy,C,St(V.y0,V.x0),St(q.y0,q.x0),!c):(dt>0&&r.arc(f+V.cx,h+V.cy,dt,St(V.y0,V.x0),St(V.y1,V.x1),!c),r.arc(f,h,n,St(V.cy+V.y1,V.cx+V.x1),St(q.cy+q.y1,q.cx+q.x1),c),Vt>0&&r.arc(f+q.cx,h+q.cy,Vt,St(q.y1,q.x1),St(q.y0,q.x0),!c))}else r.lineTo(f+U,h+z),r.arc(f,h,n,l,u,c)}r.closePath()}}}var bm=function(){function r(){this.cx=0,this.cy=0,this.r0=0,this.r=0,this.startAngle=0,this.endAngle=Math.PI*2,this.clockwise=!0,this.cornerRadius=0}return r}(),fl=function(r){N(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultShape=function(){return new bm},t.prototype.buildPath=function(e,i){Tm(e,i)},t.prototype.isZeroArea=function(){return this.shape.startAngle===this.shape.endAngle||this.shape.r===this.shape.r0},t}(nt);fl.prototype.type="sector";var xm=function(){function r(){this.cx=0,this.cy=0,this.r=0,this.r0=0}return r}(),hl=function(r){N(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultShape=function(){return new xm},t.prototype.buildPath=function(e,i){var n=i.cx,a=i.cy,o=Math.PI*2;e.moveTo(n+i.r,a),e.arc(n,a,i.r,0,o,!1),e.moveTo(n+i.r0,a),e.arc(n,a,i.r0,0,o,!0)},t}(nt);hl.prototype.type="ring";function Cm(r,t,e,i){var n=[],a=[],o=[],s=[],u,l,f,h;if(i){f=[1/0,1/0],h=[-1/0,-1/0];for(var c=0,v=r.length;c<v;c++)ni(f,f,r[c]),ai(h,h,r[c]);ni(f,f,i[0]),ai(h,h,i[1])}for(var c=0,v=r.length;c<v;c++){var d=r[c];if(e)u=r[c?c-1:v-1],l=r[(c+1)%v];else if(c===0||c===v-1){n.push(ny(r[c]));continue}else u=r[c-1],l=r[c+1];ay(a,l,u),Ro(a,a,t);var y=Xs(d,u),p=Xs(d,l),g=y+p;g!==0&&(y/=g,p/=g),Ro(o,a,-y),Ro(s,a,p);var _=Ql([],d,o),m=Ql([],d,s);i&&(ai(_,_,f),ni(_,_,h),ai(m,m,f),ni(m,m,h)),n.push(_),n.push(m)}return e&&n.push(n.shift()),n}function yd(r,t,e){var i=t.smooth,n=t.points;if(n&&n.length>=2){if(i){var a=Cm(n,i,e,t.smoothConstraint);r.moveTo(n[0][0],n[0][1]);for(var o=n.length,s=0;s<(e?o:o-1);s++){var u=a[s*2],l=a[s*2+1],f=n[(s+1)%o];r.bezierCurveTo(u[0],u[1],l[0],l[1],f[0],f[1])}}else{r.moveTo(n[0][0],n[0][1]);for(var s=1,h=n.length;s<h;s++)r.lineTo(n[s][0],n[s][1])}e&&r.closePath()}}var Dm=function(){function r(){this.points=null,this.smooth=0,this.smoothConstraint=null}return r}(),vl=function(r){N(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultShape=function(){return new Dm},t.prototype.buildPath=function(e,i){yd(e,i,!0)},t}(nt);vl.prototype.type="polygon";var Mm=function(){function r(){this.points=null,this.percent=1,this.smooth=0,this.smoothConstraint=null}return r}(),cl=function(r){N(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new Mm},t.prototype.buildPath=function(e,i){yd(e,i,!1)},t}(nt);cl.prototype.type="polyline";var Am={},Pm=function(){function r(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.percent=1}return r}(),Si=function(r){N(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new Pm},t.prototype.buildPath=function(e,i){var n,a,o,s;if(this.subPixelOptimize){var u=td(Am,i,this.style);n=u.x1,a=u.y1,o=u.x2,s=u.y2}else n=i.x1,a=i.y1,o=i.x2,s=i.y2;var l=i.percent;l!==0&&(e.moveTo(n,a),l<1&&(o=n*(1-l)+o*l,s=a*(1-l)+s*l),e.lineTo(o,s))},t.prototype.pointAt=function(e){var i=this.shape;return[i.x1*(1-e)+i.x2*e,i.y1*(1-e)+i.y2*e]},t}(nt);Si.prototype.type="line";var It=[],Lm=function(){function r(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.cpx1=0,this.cpy1=0,this.percent=1}return r}();function oh(r,t,e){var i=r.cpx2,n=r.cpy2;return i!=null||n!=null?[(e?lf:gt)(r.x1,r.cpx1,r.cpx2,r.x2,t),(e?lf:gt)(r.y1,r.cpy1,r.cpy2,r.y2,t)]:[(e?ff:xt)(r.x1,r.cpx1,r.x2,t),(e?ff:xt)(r.y1,r.cpy1,r.y2,t)]}var dl=function(r){N(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new Lm},t.prototype.buildPath=function(e,i){var n=i.x1,a=i.y1,o=i.x2,s=i.y2,u=i.cpx1,l=i.cpy1,f=i.cpx2,h=i.cpy2,c=i.percent;c!==0&&(e.moveTo(n,a),f==null||h==null?(c<1&&(Fa(n,u,o,c,It),u=It[1],o=It[2],Fa(a,l,s,c,It),l=It[1],s=It[2]),e.quadraticCurveTo(u,l,o,s)):(c<1&&(Na(n,u,f,o,c,It),u=It[1],f=It[2],o=It[3],Na(a,l,h,s,c,It),l=It[1],h=It[2],s=It[3]),e.bezierCurveTo(u,l,f,h,o,s)))},t.prototype.pointAt=function(e){return oh(this.shape,e,!1)},t.prototype.tangentAt=function(e){var i=oh(this.shape,e,!0);return uy(i,i)},t}(nt);dl.prototype.type="bezier-curve";var Im=function(){function r(){this.cx=0,this.cy=0,this.r=0,this.startAngle=0,this.endAngle=Math.PI*2,this.clockwise=!0}return r}(),go=function(r){N(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new Im},t.prototype.buildPath=function(e,i){var n=i.cx,a=i.cy,o=Math.max(i.r,0),s=i.startAngle,u=i.endAngle,l=i.clockwise,f=Math.cos(s),h=Math.sin(s);e.moveTo(f*o+n,h*o+a),e.arc(n,a,o,s,u,!l)},t}(nt);go.prototype.type="arc";var Rm=function(r){N(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type="compound",e}return t.prototype._updatePathDirty=function(){for(var e=this.shape.paths,i=this.shapeChanged(),n=0;n<e.length;n++)i=i||e[n].shapeChanged();i&&this.dirtyShape()},t.prototype.beforeBrush=function(){this._updatePathDirty();for(var e=this.shape.paths||[],i=this.getGlobalScale(),n=0;n<e.length;n++)e[n].path||e[n].createPathProxy(),e[n].path.setScale(i[0],i[1],e[n].segmentIgnoreThreshold)},t.prototype.buildPath=function(e,i){for(var n=i.paths||[],a=0;a<n.length;a++)n[a].buildPath(e,n[a].shape,!0)},t.prototype.afterBrush=function(){for(var e=this.shape.paths||[],i=0;i<e.length;i++)e[i].pathUpdated()},t.prototype.getBoundingRect=function(){return this._updatePathDirty.call(this),nt.prototype.getBoundingRect.call(this)},t}(nt),_d=function(){function r(t){this.colorStops=t||[]}return r.prototype.addColorStop=function(t,e){this.colorStops.push({offset:t,color:e})},r}(),Em=function(r){N(t,r);function t(e,i,n,a,o,s){var u=r.call(this,o)||this;return u.x=e??0,u.y=i??0,u.x2=n??1,u.y2=a??0,u.type="linear",u.global=s||!1,u}return t}(_d),Om=function(r){N(t,r);function t(e,i,n,a,o){var s=r.call(this,a)||this;return s.x=e??.5,s.y=i??.5,s.r=n??.5,s.type="radial",s.global=o||!1,s}return t}(_d),mr=[0,0],wr=[0,0],Kn=new et,Qn=new et,Ua=function(){function r(t,e){this._corners=[],this._axes=[],this._origin=[0,0];for(var i=0;i<4;i++)this._corners[i]=new et;for(var i=0;i<2;i++)this._axes[i]=new et;t&&this.fromBoundingRect(t,e)}return r.prototype.fromBoundingRect=function(t,e){var i=this._corners,n=this._axes,a=t.x,o=t.y,s=a+t.width,u=o+t.height;if(i[0].set(a,o),i[1].set(s,o),i[2].set(s,u),i[3].set(a,u),e)for(var l=0;l<4;l++)i[l].transform(e);et.sub(n[0],i[1],i[0]),et.sub(n[1],i[3],i[0]),n[0].normalize(),n[1].normalize();for(var l=0;l<2;l++)this._origin[l]=n[l].dot(i[0])},r.prototype.intersect=function(t,e){var i=!0,n=!e;return Kn.set(1/0,1/0),Qn.set(0,0),!this._intersectCheckOneSide(this,t,Kn,Qn,n,1)&&(i=!1,n)||!this._intersectCheckOneSide(t,this,Kn,Qn,n,-1)&&(i=!1,n)||n||et.copy(e,i?Kn:Qn),i},r.prototype._intersectCheckOneSide=function(t,e,i,n,a,o){for(var s=!0,u=0;u<2;u++){var l=this._axes[u];if(this._getProjMinMaxOnAxis(u,t._corners,mr),this._getProjMinMaxOnAxis(u,e._corners,wr),mr[1]<wr[0]||mr[0]>wr[1]){if(s=!1,a)return s;var f=Math.abs(wr[0]-mr[1]),h=Math.abs(mr[0]-wr[1]);Math.min(f,h)>n.len()&&(f<h?et.scale(n,l,-f*o):et.scale(n,l,h*o))}else if(i){var f=Math.abs(wr[0]-mr[1]),h=Math.abs(mr[0]-wr[1]);Math.min(f,h)<i.len()&&(f<h?et.scale(i,l,f*o):et.scale(i,l,-h*o))}}return s},r.prototype._getProjMinMaxOnAxis=function(t,e,i){for(var n=this._axes[t],a=this._origin,o=e[0].dot(n)+a[t],s=o,u=o,l=1;l<e.length;l++){var f=e[l].dot(n)+a[t];s=Math.min(f,s),u=Math.max(f,u)}i[0]=s,i[1]=u},r}(),km=[],Bm=function(r){N(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.notClear=!0,e.incremental=!0,e._displayables=[],e._temporaryDisplayables=[],e._cursor=0,e}return t.prototype.traverse=function(e,i){e.call(i,this)},t.prototype.useStyle=function(){this.style={}},t.prototype.getCursor=function(){return this._cursor},t.prototype.innerAfterBrush=function(){this._cursor=this._displayables.length},t.prototype.clearDisplaybles=function(){this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.markRedraw(),this.notClear=!1},t.prototype.clearTemporalDisplayables=function(){this._temporaryDisplayables=[]},t.prototype.addDisplayable=function(e,i){i?this._temporaryDisplayables.push(e):this._displayables.push(e),this.markRedraw()},t.prototype.addDisplayables=function(e,i){i=i||!1;for(var n=0;n<e.length;n++)this.addDisplayable(e[n],i)},t.prototype.getDisplayables=function(){return this._displayables},t.prototype.getTemporalDisplayables=function(){return this._temporaryDisplayables},t.prototype.eachPendingDisplayable=function(e){for(var i=this._cursor;i<this._displayables.length;i++)e&&e(this._displayables[i]);for(var i=0;i<this._temporaryDisplayables.length;i++)e&&e(this._temporaryDisplayables[i])},t.prototype.update=function(){this.updateTransform();for(var e=this._cursor;e<this._displayables.length;e++){var i=this._displayables[e];i.parent=this,i.update(),i.parent=null}for(var e=0;e<this._temporaryDisplayables.length;e++){var i=this._temporaryDisplayables[e];i.parent=this,i.update(),i.parent=null}},t.prototype.getBoundingRect=function(){if(!this._rect){for(var e=new J(1/0,1/0,-1/0,-1/0),i=0;i<this._displayables.length;i++){var n=this._displayables[i],a=n.getBoundingRect().clone();n.needLocalTransform()&&a.applyTransform(n.getLocalTransform(km)),e.union(a)}this._rect=e}return this._rect},t.prototype.contain=function(e,i){var n=this.transformCoordToLocal(e,i),a=this.getBoundingRect();if(a.contain(n[0],n[1]))for(var o=0;o<this._displayables.length;o++){var s=this._displayables[o];if(s.contain(e,i))return!0}return!1},t}(Pn),md=mt();function Nm(r,t,e,i,n){var a;if(t&&t.ecModel){var o=t.ecModel.getUpdatePayload();a=o&&o.animation}var s=t&&t.isAnimationEnabled(),u=r==="update";if(s){var l=void 0,f=void 0,h=void 0;i?(l=W(i.duration,200),f=W(i.easing,"cubicOut"),h=0):(l=t.getShallow(u?"animationDurationUpdate":"animationDuration"),f=t.getShallow(u?"animationEasingUpdate":"animationEasing"),h=t.getShallow(u?"animationDelayUpdate":"animationDelay")),a&&(a.duration!=null&&(l=a.duration),a.easing!=null&&(f=a.easing),a.delay!=null&&(h=a.delay)),K(h)&&(h=h(e,n)),K(l)&&(l=l(e));var c={duration:l||0,delay:h,easing:f};return c}else return null}function pl(r,t,e,i,n,a,o){var s=!1,u;K(n)?(o=a,a=n,n=null):F(n)&&(a=n.cb,o=n.during,s=n.isFrom,u=n.removeOpt,n=n.dataIndex);var l=r==="leave";l||t.stopAnimation("leave");var f=Nm(r,i,n,l?u||{}:null,i&&i.getAnimationDelayParams?i.getAnimationDelayParams(t,n):null);if(f&&f.duration>0){var h=f.duration,c=f.delay,v=f.easing,d={duration:h,delay:c||0,easing:v,done:a,force:!!a||!!o,setToFinal:!l,scope:r,during:o};s?t.animateFrom(e,d):t.animateTo(e,d)}else t.stopAnimation(),!s&&t.attr(e),o&&o(1),a&&a()}function Ln(r,t,e,i,n,a){pl("update",r,t,e,i,n,a)}function wd(r,t,e,i,n,a){pl("enter",r,t,e,i,n,a)}function an(r){if(!r.__zr)return!0;for(var t=0;t<r.animators.length;t++){var e=r.animators[t];if(e.scope==="leave")return!0}return!1}function Sd(r,t,e,i,n,a){an(r)||pl("leave",r,t,e,i,n,a)}function sh(r,t,e,i){r.removeTextContent(),r.removeTextGuideLine(),Sd(r,{style:{opacity:0}},t,e,i)}function Fm(r,t,e){function i(){r.parent&&r.parent.remove(r)}r.isGroup?r.traverse(function(n){n.isGroup||sh(n,t,e,i)}):sh(r,t,e,i)}function FC(r){md(r).oldStyle=r.style}function zC(r){return md(r).oldStyle}var Ya=Math.max,Xa=Math.min,mu={};function zm(r){return nt.extend(r)}var Hm=pm;function Gm(r,t){return Hm(r,t)}function fe(r,t){mu[r]=t}function Vm(r){if(mu.hasOwnProperty(r))return mu[r]}function gl(r,t,e,i){var n=dm(r,t);return e&&(i==="center"&&(e=bd(e,n.getBoundingRect())),xd(n,e)),n}function Td(r,t,e){var i=new zr({style:{image:r,x:t.x,y:t.y,width:t.width,height:t.height},onload:function(n){if(e==="center"){var a={width:n.width,height:n.height};i.setStyle(bd(t,a))}}});return i}function bd(r,t){var e=t.width/t.height,i=r.height*e,n;i<=r.width?n=r.height:(i=r.width,n=i/e);var a=r.x+r.width/2,o=r.y+r.height/2;return{x:a-i/2,y:o-n/2,width:i,height:n}}var Wm=gm;function xd(r,t){if(r.applyTransform){var e=r.getBoundingRect(),i=e.calculateTransform(t);r.applyTransform(i)}}function yl(r,t){return td(r,r,{lineWidth:t}),r}function Um(r){return ed(r.shape,r.shape,r.style),r}var Ym=Mr;function Xm(r,t){for(var e=Ku([]);r&&r!==t;)li(e,r.getLocalTransform(),e),r=r.parent;return e}function _l(r,t,e){return t&&!zt(t)&&(t=ju.getLocalTransform(t)),e&&(t=gc([],t)),me([],r,t)}function $m(r,t,e){var i=t[4]===0||t[5]===0||t[0]===0?1:Math.abs(2*t[4]/t[0]),n=t[4]===0||t[5]===0||t[2]===0?1:Math.abs(2*t[4]/t[2]),a=[r==="left"?-i:r==="right"?i:0,r==="top"?-n:r==="bottom"?n:0];return a=_l(a,t,e),Math.abs(a[0])>Math.abs(a[1])?a[0]>0?"right":"left":a[1]>0?"bottom":"top"}function uh(r){return!r.isGroup}function qm(r){return r.shape!=null}function Zm(r,t,e){if(!r||!t)return;function i(o){var s={};return o.traverse(function(u){uh(u)&&u.anid&&(s[u.anid]=u)}),s}function n(o){var s={x:o.x,y:o.y,rotation:o.rotation};return qm(o)&&(s.shape=O({},o.shape)),s}var a=i(r);t.traverse(function(o){if(uh(o)&&o.anid){var s=a[o.anid];if(s){var u=n(o);o.attr(n(s)),Ln(o,u,e,rt(o).dataIndex)}}})}function Km(r,t){return Y(r,function(e){var i=e[0];i=Ya(i,t.x),i=Xa(i,t.x+t.width);var n=e[1];return n=Ya(n,t.y),n=Xa(n,t.y+t.height),[i,n]})}function Qm(r,t){var e=Ya(r.x,t.x),i=Xa(r.x+r.width,t.x+t.width),n=Ya(r.y,t.y),a=Xa(r.y+r.height,t.y+t.height);if(i>=e&&a>=n)return{x:e,y:n,width:i-e,height:a-n}}function ml(r,t,e){var i=O({rectHover:!0},t),n=i.style={strokeNoScale:!0};if(e=e||{x:-1,y:-1,width:2,height:2},r)return r.indexOf("image://")===0?(n.image=r.slice(8),ot(n,e),new zr(i)):gl(r.replace("path://",""),i,e,"center")}function Jm(r,t,e,i,n){for(var a=0,o=n[n.length-1];a<n.length;a++){var s=n[a];if(Cd(r,t,e,i,s[0],s[1],o[0],o[1]))return!0;o=s}}function Cd(r,t,e,i,n,a,o,s){var u=e-r,l=i-t,f=o-n,h=s-a,c=ps(f,h,u,l);if(jm(c))return!1;var v=r-n,d=t-a,y=ps(v,d,u,l)/c;if(y<0||y>1)return!1;var p=ps(v,d,f,h)/c;return!(p<0||p>1)}function ps(r,t,e,i){return r*i-e*t}function jm(r){return r<=1e-6&&r>=-1e-6}function yo(r){var t=r.itemTooltipOption,e=r.componentModel,i=r.itemName,n=B(t)?{formatter:t}:t,a=e.mainType,o=e.componentIndex,s={componentType:a,name:i,$vars:["name"]};s[a+"Index"]=o;var u=r.formatterParamsExtra;u&&D(ft(u),function(f){Ke(s,f)||(s[f]=u[f],s.$vars.push(f))});var l=rt(r.el);l.componentMainType=a,l.componentIndex=o,l.tooltipConfig={name:i,option:ot({content:i,encodeHTMLContent:!0,formatterParams:s},n)}}function lh(r,t){var e;r.isGroup&&(e=t(r)),e||r.traverse(t)}function Dd(r,t){if(r)if(k(r))for(var e=0;e<r.length;e++)lh(r[e],t);else lh(r,t)}fe("circle",po);fe("ellipse",ll);fe("sector",fl);fe("ring",hl);fe("polygon",vl);fe("polyline",cl);fe("rect",Pt);fe("line",Si);fe("bezierCurve",dl);fe("arc",go);const t1=Object.freeze(Object.defineProperty({__proto__:null,Arc:go,BezierCurve:dl,BoundingRect:J,Circle:po,CompoundPath:Rm,Ellipse:ll,Group:ue,Image:zr,IncrementalDisplayable:Bm,Line:Si,LinearGradient:Em,OrientedBoundingRect:Ua,Path:nt,Point:et,Polygon:vl,Polyline:cl,RadialGradient:Om,Rect:Pt,Ring:hl,Sector:fl,Text:Lt,applyTransform:_l,clipPointsByRect:Km,clipRectByRect:Qm,createIcon:ml,extendPath:Gm,extendShape:zm,getShapeClass:Vm,getTransform:Xm,groupTransition:Zm,initProps:wd,isElementRemoved:an,lineLineIntersect:Cd,linePolygonIntersect:Jm,makeImage:Td,makePath:gl,mergePath:Wm,registerShape:fe,removeElement:Sd,removeElementWithFadeOut:Fm,resizePath:xd,setTooltipConfig:yo,subPixelOptimize:Ym,subPixelOptimizeLine:yl,subPixelOptimizeRect:Um,transformDirection:$m,traverseElements:Dd,updateProps:Ln},Symbol.toStringTag,{value:"Module"}));var _o={};function Md(r,t){for(var e=0;e<je.length;e++){var i=je[e],n=t[i],a=r.ensureState(i);a.style=a.style||{},a.style.text=n}var o=r.currentStates.slice();r.clearStates(!0),r.setStyle({text:t.normal}),r.useStates(o,!0)}function wu(r,t,e){var i=r.labelFetcher,n=r.labelDataIndex,a=r.labelDimIndex,o=t.normal,s;i&&(s=i.getFormattedLabel(n,"normal",null,a,o&&o.get("formatter"),e!=null?{interpolatedValue:e}:null)),s==null&&(s=K(r.defaultText)?r.defaultText(n,r,e):r.defaultText);for(var u={normal:s},l=0;l<je.length;l++){var f=je[l],h=t[f];u[f]=W(i?i.getFormattedLabel(n,f,null,a,h&&h.get("formatter")):null,s)}return u}function e1(r,t,e,i){e=e||_o;for(var n=r instanceof Lt,a=!1,o=0;o<qf.length;o++){var s=t[qf[o]];if(s&&s.getShallow("show")){a=!0;break}}var u=n?r:r.getTextContent();if(a){n||(u||(u=new Lt,r.setTextContent(u)),r.stateProxy&&(u.stateProxy=r.stateProxy));var l=wu(e,t),f=t.normal,h=!!f.getShallow("show"),c=tr(f,i&&i.normal,e,!1,!n);c.text=l.normal,n||r.setTextConfig(fh(f,e,!1));for(var o=0;o<je.length;o++){var v=je[o],s=t[v];if(s){var d=u.ensureState(v),y=!!W(s.getShallow("show"),h);if(y!==h&&(d.ignore=!y),d.style=tr(s,i&&i[v],e,!0,!n),d.style.text=l[v],!n){var p=r.ensureState(v);p.textConfig=fh(s,e,!0)}}}u.silent=!!f.getShallow("silent"),u.style.x!=null&&(c.x=u.style.x),u.style.y!=null&&(c.y=u.style.y),u.ignore=!h,u.useStyle(c),u.dirty(),e.enableTextSetter&&(wl(u).setLabelText=function(g){var _=wu(e,t,g);Md(u,_)})}else u&&(u.ignore=!0);r.dirty()}function HC(r,t){t=t||"label";for(var e={normal:r.getModel(t)},i=0;i<je.length;i++){var n=je[i];e[n]=r.getModel([n,t])}return e}function tr(r,t,e,i,n){var a={};return r1(a,r,e,i,n),t&&O(a,t),a}function fh(r,t,e){t=t||{};var i={},n,a=r.getShallow("rotate"),o=W(r.getShallow("distance"),e?null:5),s=r.getShallow("offset");return n=r.getShallow("position")||(e?null:"inside"),n==="outside"&&(n=t.defaultOutsidePosition||"top"),n!=null&&(i.position=n),s!=null&&(i.offset=s),a!=null&&(a*=Math.PI/180,i.rotation=a),o!=null&&(i.distance=o),i.outsideFill=r.get("color")==="inherit"?t.inheritColor||null:"auto",i}function r1(r,t,e,i,n){e=e||_o;var a=t.ecModel,o=a&&a.option.textStyle,s=i1(t),u;if(s){u={};for(var l in s)if(s.hasOwnProperty(l)){var f=t.getModel(["rich",l]);dh(u[l]={},f,o,e,i,n,!1,!0)}}u&&(r.rich=u);var h=t.get("overflow");h&&(r.overflow=h);var c=t.get("minMargin");c!=null&&(r.margin=c),dh(r,t,o,e,i,n,!0,!1)}function i1(r){for(var t;r&&r!==r.ecModel;){var e=(r.option||_o).rich;if(e){t=t||{};for(var i=ft(e),n=0;n<i.length;n++){var a=i[n];t[a]=1}}r=r.parentModel}return t}var hh=["fontStyle","fontWeight","fontSize","fontFamily","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY"],vh=["align","lineHeight","width","height","tag","verticalAlign","ellipsis"],ch=["padding","borderWidth","borderRadius","borderDashOffset","backgroundColor","borderColor","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"];function dh(r,t,e,i,n,a,o,s){e=!n&&e||_o;var u=i&&i.inheritColor,l=t.getShallow("color"),f=t.getShallow("textBorderColor"),h=W(t.getShallow("opacity"),e.opacity);(l==="inherit"||l==="auto")&&(u?l=u:l=null),(f==="inherit"||f==="auto")&&(u?f=u:f=null),a||(l=l||e.color,f=f||e.textBorderColor),l!=null&&(r.fill=l),f!=null&&(r.stroke=f);var c=W(t.getShallow("textBorderWidth"),e.textBorderWidth);c!=null&&(r.lineWidth=c);var v=W(t.getShallow("textBorderType"),e.textBorderType);v!=null&&(r.lineDash=v);var d=W(t.getShallow("textBorderDashOffset"),e.textBorderDashOffset);d!=null&&(r.lineDashOffset=d),!n&&h==null&&!s&&(h=i&&i.defaultOpacity),h!=null&&(r.opacity=h),!n&&!a&&r.fill==null&&i.inheritColor&&(r.fill=i.inheritColor);for(var y=0;y<hh.length;y++){var p=hh[y],g=W(t.getShallow(p),e[p]);g!=null&&(r[p]=g)}for(var y=0;y<vh.length;y++){var p=vh[y],g=t.getShallow(p);g!=null&&(r[p]=g)}if(r.verticalAlign==null){var _=t.getShallow("baseline");_!=null&&(r.verticalAlign=_)}if(!o||!i.disableBox){for(var y=0;y<ch.length;y++){var p=ch[y],g=t.getShallow(p);g!=null&&(r[p]=g)}var m=t.getShallow("borderType");m!=null&&(r.borderDash=m),(r.backgroundColor==="auto"||r.backgroundColor==="inherit")&&u&&(r.backgroundColor=u),(r.borderColor==="auto"||r.borderColor==="inherit")&&u&&(r.borderColor=u)}}function n1(r,t){var e=t&&t.getModel("textStyle");return ye([r.fontStyle||e&&e.getShallow("fontStyle")||"",r.fontWeight||e&&e.getShallow("fontWeight")||"",(r.fontSize||e&&e.getShallow("fontSize")||12)+"px",r.fontFamily||e&&e.getShallow("fontFamily")||"sans-serif"].join(" "))}var wl=mt();function GC(r,t,e,i){if(r){var n=wl(r);n.prevValue=n.value,n.value=e;var a=t.normal;n.valueAnimation=a.get("valueAnimation"),n.valueAnimation&&(n.precision=a.get("precision"),n.defaultInterpolatedText=i,n.statesModels=t)}}function VC(r,t,e,i,n){var a=wl(r);if(!a.valueAnimation||a.prevValue===a.value)return;var o=a.defaultInterpolatedText,s=W(a.interpolatedValue,a.prevValue),u=a.value;function l(f){var h=W_(e,a.precision,s,u,f);a.interpolatedValue=f===1?null:h;var c=wu({labelDataIndex:t,labelFetcher:n,defaultText:o?o(h):h+""},a.statesModels,h);Md(r,c)}r.percent=0,(a.prevValue==null?wd:Ln)(r,{percent:1},i,t,null,l)}var a1=["textStyle","color"],gs=["fontStyle","fontWeight","fontSize","fontFamily","padding","lineHeight","rich","width","height","overflow"],ys=new Lt,o1=function(){function r(){}return r.prototype.getTextColor=function(t){var e=this.ecModel;return this.getShallow("color")||(!t&&e?e.get(a1):null)},r.prototype.getFont=function(){return n1({fontStyle:this.getShallow("fontStyle"),fontWeight:this.getShallow("fontWeight"),fontSize:this.getShallow("fontSize"),fontFamily:this.getShallow("fontFamily")},this.ecModel)},r.prototype.getTextRect=function(t){for(var e={text:t,verticalAlign:this.getShallow("verticalAlign")||this.getShallow("baseline")},i=0;i<gs.length;i++)e[gs[i]]=this.getShallow(gs[i]);return ys.useStyle(e),ys.update(),ys.getBoundingRect()},r}(),Ad=[["lineWidth","width"],["stroke","color"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["lineDash","type"],["lineDashOffset","dashOffset"],["lineCap","cap"],["lineJoin","join"],["miterLimit"]],s1=gn(Ad),u1=function(){function r(){}return r.prototype.getLineStyle=function(t){return s1(this,t)},r}(),Pd=[["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["lineDash","borderType"],["lineDashOffset","borderDashOffset"],["lineCap","borderCap"],["lineJoin","borderJoin"],["miterLimit","borderMiterLimit"]],l1=gn(Pd),f1=function(){function r(){}return r.prototype.getItemStyle=function(t,e){return l1(this,t,e)},r}(),ht=function(){function r(t,e,i){this.parentModel=e,this.ecModel=i,this.option=t}return r.prototype.init=function(t,e,i){},r.prototype.mergeOption=function(t,e){st(this.option,t,!0)},r.prototype.get=function(t,e){return t==null?this.option:this._doGet(this.parsePath(t),!e&&this.parentModel)},r.prototype.getShallow=function(t,e){var i=this.option,n=i==null?i:i[t];if(n==null&&!e){var a=this.parentModel;a&&(n=a.getShallow(t))}return n},r.prototype.getModel=function(t,e){var i=t!=null,n=i?this.parsePath(t):null,a=i?this._doGet(n):this.option;return e=e||this.parentModel&&this.parentModel.getModel(this.resolveParentPath(n)),new r(a,e,this.ecModel)},r.prototype.isEmpty=function(){return this.option==null},r.prototype.restoreData=function(){},r.prototype.clone=function(){var t=this.constructor;return new t(Z(this.option))},r.prototype.parsePath=function(t){return typeof t=="string"?t.split("."):t},r.prototype.resolveParentPath=function(t){return t},r.prototype.isAnimationEnabled=function(){if(!H.node&&this.option){if(this.option.animation!=null)return!!this.option.animation;if(this.parentModel)return this.parentModel.isAnimationEnabled()}},r.prototype._doGet=function(t,e){var i=this.option;if(!t)return i;for(var n=0;n<t.length&&!(t[n]&&(i=i&&typeof i=="object"?i[t[n]]:null,i==null));n++);return i==null&&e&&(i=e._doGet(this.resolveParentPath(t),e.parentModel)),i},r}();nl(ht);Z_(ht);Ee(ht,u1);Ee(ht,f1);Ee(ht,t0);Ee(ht,o1);var h1=Math.round(Math.random()*10);function mo(r){return[r||"",h1++].join("_")}function v1(r){var t={};r.registerSubTypeDefaulter=function(e,i){var n=_e(e);t[n.main]=i},r.determineSubType=function(e,i){var n=i.type;if(!n){var a=_e(e).main;r.hasSubTypes(e)&&t[a]&&(n=t[a](i))}return n}}function c1(r,t){r.topologicalTravel=function(a,o,s,u){if(!a.length)return;var l=e(o),f=l.graph,h=l.noEntryList,c={};for(D(a,function(_){c[_]=!0});h.length;){var v=h.pop(),d=f[v],y=!!c[v];y&&(s.call(u,v,d.originalDeps.slice()),delete c[v]),D(d.successor,y?g:p)}D(c,function(){var _="";throw new Error(_)});function p(_){f[_].entryCount--,f[_].entryCount===0&&h.push(_)}function g(_){c[_]=!0,p(_)}};function e(a){var o={},s=[];return D(a,function(u){var l=i(o,u),f=l.originalDeps=t(u),h=n(f,a);l.entryCount=h.length,l.entryCount===0&&s.push(u),D(h,function(c){at(l.predecessor,c)<0&&l.predecessor.push(c);var v=i(o,c);at(v.successor,c)<0&&v.successor.push(u)})}),{graph:o,noEntryList:s}}function i(a,o){return a[o]||(a[o]={predecessor:[],successor:[]}),a[o]}function n(a,o){var s=[];return D(a,function(u){at(o,u)>=0&&s.push(u)}),s}}function d1(r,t){return st(st({},r,!0),t,!0)}const p1={time:{month:["January","February","March","April","May","June","July","August","September","October","November","December"],monthAbbr:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],dayOfWeek:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],dayOfWeekAbbr:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"]},legend:{selector:{all:"All",inverse:"Inv"}},toolbox:{brush:{title:{rect:"Box Select",polygon:"Lasso Select",lineX:"Horizontally Select",lineY:"Vertically Select",keep:"Keep Selections",clear:"Clear Selections"}},dataView:{title:"Data View",lang:["Data View","Close","Refresh"]},dataZoom:{title:{zoom:"Zoom",back:"Zoom Reset"}},magicType:{title:{line:"Switch to Line Chart",bar:"Switch to Bar Chart",stack:"Stack",tiled:"Tile"}},restore:{title:"Restore"},saveAsImage:{title:"Save as Image",lang:["Right Click to Save Image"]}},series:{typeNames:{pie:"Pie chart",bar:"Bar chart",line:"Line chart",scatter:"Scatter plot",effectScatter:"Ripple scatter plot",radar:"Radar chart",tree:"Tree",treemap:"Treemap",boxplot:"Boxplot",candlestick:"Candlestick",k:"K line chart",heatmap:"Heat map",map:"Map",parallel:"Parallel coordinate map",lines:"Line graph",graph:"Relationship graph",sankey:"Sankey diagram",funnel:"Funnel chart",gauge:"Gauge",pictorialBar:"Pictorial bar",themeRiver:"Theme River Map",sunburst:"Sunburst",custom:"Custom chart",chart:"Chart"}},aria:{general:{withTitle:'This is a chart about "{title}"',withoutTitle:"This is a chart"},series:{single:{prefix:"",withName:" with type {seriesType} named {seriesName}.",withoutName:" with type {seriesType}."},multiple:{prefix:". It consists of {seriesCount} series count.",withName:" The {seriesId} series is a {seriesType} representing {seriesName}.",withoutName:" The {seriesId} series is a {seriesType}.",separator:{middle:"",end:""}}},data:{allData:"The data is as follows: ",partialData:"The first {displayCnt} items are: ",withName:"the data for {name} is {value}",withoutName:"{value}",separator:{middle:", ",end:". "}}}},g1={time:{month:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"],monthAbbr:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],dayOfWeek:["星期日","星期一","星期二","星期三","星期四","星期五","星期六"],dayOfWeekAbbr:["日","一","二","三","四","五","六"]},legend:{selector:{all:"全选",inverse:"反选"}},toolbox:{brush:{title:{rect:"矩形选择",polygon:"圈选",lineX:"横向选择",lineY:"纵向选择",keep:"保持选择",clear:"清除选择"}},dataView:{title:"数据视图",lang:["数据视图","关闭","刷新"]},dataZoom:{title:{zoom:"区域缩放",back:"区域缩放还原"}},magicType:{title:{line:"切换为折线图",bar:"切换为柱状图",stack:"切换为堆叠",tiled:"切换为平铺"}},restore:{title:"还原"},saveAsImage:{title:"保存为图片",lang:["右键另存为图片"]}},series:{typeNames:{pie:"饼图",bar:"柱状图",line:"折线图",scatter:"散点图",effectScatter:"涟漪散点图",radar:"雷达图",tree:"树图",treemap:"矩形树图",boxplot:"箱型图",candlestick:"K线图",k:"K线图",heatmap:"热力图",map:"地图",parallel:"平行坐标图",lines:"线图",graph:"关系图",sankey:"桑基图",funnel:"漏斗图",gauge:"仪表盘图",pictorialBar:"象形柱图",themeRiver:"主题河流图",sunburst:"旭日图",custom:"自定义图表",chart:"图表"}},aria:{general:{withTitle:"这是一个关于“{title}”的图表。",withoutTitle:"这是一个图表，"},series:{single:{prefix:"",withName:"图表类型是{seriesType}，表示{seriesName}。",withoutName:"图表类型是{seriesType}。"},multiple:{prefix:"它由{seriesCount}个图表系列组成。",withName:"第{seriesId}个系列是一个表示{seriesName}的{seriesType}，",withoutName:"第{seriesId}个系列是一个{seriesType}，",separator:{middle:"；",end:"。"}}},data:{allData:"其数据是——",partialData:"其中，前{displayCnt}项是——",withName:"{name}的数据是{value}",withoutName:"{value}",separator:{middle:"，",end:""}}}};var $a="ZH",Sl="EN",hi=Sl,Aa={},Tl={},Ld=H.domSupported?function(){var r=(document.documentElement.lang||navigator.language||navigator.browserLanguage||hi).toUpperCase();return r.indexOf($a)>-1?$a:hi}():hi;function Id(r,t){r=r.toUpperCase(),Tl[r]=new ht(t),Aa[r]=t}function y1(r){if(B(r)){var t=Aa[r.toUpperCase()]||{};return r===$a||r===Sl?Z(t):st(Z(t),Z(Aa[hi]),!1)}else return st(Z(r),Z(Aa[hi]),!1)}function _1(r){return Tl[r]}function m1(){return Tl[hi]}Id(Sl,p1);Id($a,g1);var bl=1e3,xl=bl*60,on=xl*60,Jt=on*24,ph=Jt*365,Zi={year:"{yyyy}",month:"{MMM}",day:"{d}",hour:"{HH}:{mm}",minute:"{HH}:{mm}",second:"{HH}:{mm}:{ss}",millisecond:"{HH}:{mm}:{ss} {SSS}",none:"{yyyy}-{MM}-{dd} {HH}:{mm}:{ss} {SSS}"},Jn="{yyyy}-{MM}-{dd}",gh={year:"{yyyy}",month:"{yyyy}-{MM}",day:Jn,hour:Jn+" "+Zi.hour,minute:Jn+" "+Zi.minute,second:Jn+" "+Zi.second,millisecond:Zi.none},_s=["year","month","day","hour","minute","second","millisecond"],Rd=["year","half-year","quarter","month","week","half-week","day","half-day","quarter-day","hour","minute","second","millisecond"];function Fe(r,t){return r+="","0000".substr(0,t-r.length)+r}function vi(r){switch(r){case"half-year":case"quarter":return"month";case"week":case"half-week":return"day";case"half-day":case"quarter-day":return"hour";default:return r}}function w1(r){return r===vi(r)}function S1(r){switch(r){case"year":case"month":return"day";case"millisecond":return"millisecond";default:return"second"}}function wo(r,t,e,i){var n=Re(r),a=n[Cl(e)](),o=n[ci(e)]()+1,s=Math.floor((o-1)/3)+1,u=n[So(e)](),l=n["get"+(e?"UTC":"")+"Day"](),f=n[yn(e)](),h=(f-1)%12+1,c=n[To(e)](),v=n[bo(e)](),d=n[xo(e)](),y=f>=12?"pm":"am",p=y.toUpperCase(),g=i instanceof ht?i:_1(i||Ld)||m1(),_=g.getModel("time"),m=_.get("month"),w=_.get("monthAbbr"),T=_.get("dayOfWeek"),S=_.get("dayOfWeekAbbr");return(t||"").replace(/{a}/g,y+"").replace(/{A}/g,p+"").replace(/{yyyy}/g,a+"").replace(/{yy}/g,Fe(a%100+"",2)).replace(/{Q}/g,s+"").replace(/{MMMM}/g,m[o-1]).replace(/{MMM}/g,w[o-1]).replace(/{MM}/g,Fe(o,2)).replace(/{M}/g,o+"").replace(/{dd}/g,Fe(u,2)).replace(/{d}/g,u+"").replace(/{eeee}/g,T[l]).replace(/{ee}/g,S[l]).replace(/{e}/g,l+"").replace(/{HH}/g,Fe(f,2)).replace(/{H}/g,f+"").replace(/{hh}/g,Fe(h+"",2)).replace(/{h}/g,h+"").replace(/{mm}/g,Fe(c,2)).replace(/{m}/g,c+"").replace(/{ss}/g,Fe(v,2)).replace(/{s}/g,v+"").replace(/{SSS}/g,Fe(d,3)).replace(/{S}/g,d+"")}function T1(r,t,e,i,n){var a=null;if(B(e))a=e;else if(K(e))a=e(r.value,t,{level:r.level});else{var o=O({},Zi);if(r.level>0)for(var s=0;s<_s.length;++s)o[_s[s]]="{primary|"+o[_s[s]]+"}";var u=e?e.inherit===!1?e:ot(e,o):o,l=Ed(r.value,n);if(u[l])a=u[l];else if(u.inherit){for(var f=Rd.indexOf(l),s=f-1;s>=0;--s)if(u[l]){a=u[l];break}a=a||o.none}if(k(a)){var h=r.level==null?0:r.level>=0?r.level:a.length+r.level;h=Math.min(h,a.length-1),a=a[h]}}return wo(new Date(r.value),a,n,i)}function Ed(r,t){var e=Re(r),i=e[ci(t)]()+1,n=e[So(t)](),a=e[yn(t)](),o=e[To(t)](),s=e[bo(t)](),u=e[xo(t)](),l=u===0,f=l&&s===0,h=f&&o===0,c=h&&a===0,v=c&&n===1,d=v&&i===1;return d?"year":v?"month":c?"day":h?"hour":f?"minute":l?"second":"millisecond"}function yh(r,t,e){var i=ut(r)?Re(r):r;switch(t=t||Ed(r,e),t){case"year":return i[Cl(e)]();case"half-year":return i[ci(e)]()>=6?1:0;case"quarter":return Math.floor((i[ci(e)]()+1)/4);case"month":return i[ci(e)]();case"day":return i[So(e)]();case"half-day":return i[yn(e)]()/24;case"hour":return i[yn(e)]();case"minute":return i[To(e)]();case"second":return i[bo(e)]();case"millisecond":return i[xo(e)]()}}function Cl(r){return r?"getUTCFullYear":"getFullYear"}function ci(r){return r?"getUTCMonth":"getMonth"}function So(r){return r?"getUTCDate":"getDate"}function yn(r){return r?"getUTCHours":"getHours"}function To(r){return r?"getUTCMinutes":"getMinutes"}function bo(r){return r?"getUTCSeconds":"getSeconds"}function xo(r){return r?"getUTCMilliseconds":"getMilliseconds"}function b1(r){return r?"setUTCFullYear":"setFullYear"}function Od(r){return r?"setUTCMonth":"setMonth"}function kd(r){return r?"setUTCDate":"setDate"}function Bd(r){return r?"setUTCHours":"setHours"}function Nd(r){return r?"setUTCMinutes":"setMinutes"}function Fd(r){return r?"setUTCSeconds":"setSeconds"}function zd(r){return r?"setUTCMilliseconds":"setMilliseconds"}function Hd(r){if(!P_(r))return B(r)?r:"-";var t=(r+"").split(".");return t[0].replace(/(\d{1,3})(?=(?:\d{3})+(?!\d))/g,"$1,")+(t.length>1?"."+t[1]:"")}function Gd(r,t){return r=(r||"").toLowerCase().replace(/-(.)/g,function(e,i){return i.toUpperCase()}),t&&r&&(r=r.charAt(0).toUpperCase()+r.slice(1)),r}var In=lc;function Su(r,t,e){var i="{yyyy}-{MM}-{dd} {HH}:{mm}:{ss}";function n(f){return f&&ye(f)?f:"-"}function a(f){return!!(f!=null&&!isNaN(f)&&isFinite(f))}var o=t==="time",s=r instanceof Date;if(o||s){var u=o?Re(r):r;if(isNaN(+u)){if(s)return"-"}else return wo(u,i,e)}if(t==="ordinal")return Ys(r)?n(r):ut(r)&&a(r)?r+"":"-";var l=Je(r);return a(l)?Hd(l):Ys(r)?n(r):typeof r=="boolean"?r+"":"-"}var _h=["a","b","c","d","e","f","g"],ms=function(r,t){return"{"+r+(t??"")+"}"};function Vd(r,t,e){k(t)||(t=[t]);var i=t.length;if(!i)return"";for(var n=t[0].$vars||[],a=0;a<n.length;a++){var o=_h[a];r=r.replace(ms(o),ms(o,0))}for(var s=0;s<i;s++)for(var u=0;u<n.length;u++){var l=t[s][n[u]];r=r.replace(ms(_h[u],s),e?Ot(l):l)}return r}function WC(r,t,e){return D(t,function(i,n){r=r.replace("{"+n+"}",i)}),r}function x1(r,t){var e=B(r)?{color:r,extraCssText:t}:r||{},i=e.color,n=e.type;t=e.extraCssText;var a=e.renderMode||"html";if(!i)return"";if(a==="html")return n==="subItem"?'<span style="display:inline-block;vertical-align:middle;margin-right:8px;margin-left:3px;border-radius:4px;width:4px;height:4px;background-color:'+Ot(i)+";"+(t||"")+'"></span>':'<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:'+Ot(i)+";"+(t||"")+'"></span>';var o=e.markerId||"markerX";return{renderMode:a,content:"{"+o+"|}  ",style:n==="subItem"?{width:4,height:4,borderRadius:2,backgroundColor:i}:{width:10,height:10,borderRadius:5,backgroundColor:i}}}function _n(r,t){return t=t||"transparent",B(r)?r:F(r)&&r.colorStops&&(r.colorStops[0]||{}).color||t}function mh(r,t){if(t==="_blank"||t==="blank"){var e=window.open();e.opener=null,e.location.href=r}else window.open(r,t)}var Pa=D,C1=["left","right","top","bottom","width","height"],Ar=[["width","left","right"],["height","top","bottom"]];function Dl(r,t,e,i,n){var a=0,o=0;i==null&&(i=1/0),n==null&&(n=1/0);var s=0;t.eachChild(function(u,l){var f=u.getBoundingRect(),h=t.childAt(l+1),c=h&&h.getBoundingRect(),v,d;if(r==="horizontal"){var y=f.width+(c?-c.x+f.x:0);v=a+y,v>i||u.newline?(a=0,v=y,o+=s+e,s=f.height):s=Math.max(s,f.height)}else{var p=f.height+(c?-c.y+f.y:0);d=o+p,d>n||u.newline?(a+=s+e,o=0,d=p,s=f.width):s=Math.max(s,f.width)}u.newline||(u.x=a,u.y=o,u.markRedraw(),r==="horizontal"?a=v+e:o=d+e)})}var di=Dl;yt(Dl,"vertical");yt(Dl,"horizontal");function UC(r,t,e){var i=t.width,n=t.height,a=ct(r.left,i),o=ct(r.top,n),s=ct(r.right,i),u=ct(r.bottom,n);return(isNaN(a)||isNaN(parseFloat(r.left)))&&(a=0),(isNaN(s)||isNaN(parseFloat(r.right)))&&(s=i),(isNaN(o)||isNaN(parseFloat(r.top)))&&(o=0),(isNaN(u)||isNaN(parseFloat(r.bottom)))&&(u=n),e=In(e||0),{width:Math.max(s-a-e[1]-e[3],0),height:Math.max(u-o-e[0]-e[2],0)}}function gi(r,t,e){e=In(e||0);var i=t.width,n=t.height,a=ct(r.left,i),o=ct(r.top,n),s=ct(r.right,i),u=ct(r.bottom,n),l=ct(r.width,i),f=ct(r.height,n),h=e[2]+e[0],c=e[1]+e[3],v=r.aspect;switch(isNaN(l)&&(l=i-s-c-a),isNaN(f)&&(f=n-u-h-o),v!=null&&(isNaN(l)&&isNaN(f)&&(v>i/n?l=i*.8:f=n*.8),isNaN(l)&&(l=v*f),isNaN(f)&&(f=l/v)),isNaN(a)&&(a=i-s-l-c),isNaN(o)&&(o=n-u-f-h),r.left||r.right){case"center":a=i/2-l/2-e[3];break;case"right":a=i-l-c;break}switch(r.top||r.bottom){case"middle":case"center":o=n/2-f/2-e[0];break;case"bottom":o=n-f-h;break}a=a||0,o=o||0,isNaN(l)&&(l=i-c-a-(s||0)),isNaN(f)&&(f=n-h-o-(u||0));var d=new J(a+e[3],o+e[0],l,f);return d.margin=e,d}function D1(r,t,e,i,n,a){var o=!n||!n.hv||n.hv[0],s=!n||!n.hv||n.hv[1],u=n&&n.boundingMode||"all";if(a=a||r,a.x=r.x,a.y=r.y,!o&&!s)return!1;var l;if(u==="raw")l=r.type==="group"?new J(0,0,+t.width||0,+t.height||0):r.getBoundingRect();else if(l=r.getBoundingRect(),r.needLocalTransform()){var f=r.getLocalTransform();l=l.clone(),l.applyTransform(f)}var h=gi(ot({width:l.width,height:l.height},t),e,i),c=o?h.x-l.x:0,v=s?h.y-l.y:0;return u==="raw"?(a.x=c,a.y=v):(a.x+=c,a.y+=v),a===r&&r.markRedraw(),!0}function YC(r,t){return r[Ar[t][0]]!=null||r[Ar[t][1]]!=null&&r[Ar[t][2]]!=null}function qa(r){var t=r.layoutMode||r.constructor.layoutMode;return F(t)?t:t?{type:t}:null}function mn(r,t,e){var i=e&&e.ignoreSize;!k(i)&&(i=[i,i]);var n=o(Ar[0],0),a=o(Ar[1],1);l(Ar[0],r,n),l(Ar[1],r,a);function o(f,h){var c={},v=0,d={},y=0,p=2;if(Pa(f,function(m){d[m]=r[m]}),Pa(f,function(m){s(t,m)&&(c[m]=d[m]=t[m]),u(c,m)&&v++,u(d,m)&&y++}),i[h])return u(t,f[1])?d[f[2]]=null:u(t,f[2])&&(d[f[1]]=null),d;if(y===p||!v)return d;if(v>=p)return c;for(var g=0;g<f.length;g++){var _=f[g];if(!s(c,_)&&s(r,_)){c[_]=r[_];break}}return c}function s(f,h){return f.hasOwnProperty(h)}function u(f,h){return f[h]!=null&&f[h]!=="auto"}function l(f,h,c){Pa(f,function(v){h[v]=c[v]})}}function Ml(r){return M1({},r)}function M1(r,t){return t&&r&&Pa(C1,function(e){t.hasOwnProperty(e)&&(r[e]=t[e])}),r}var A1=mt(),tt=function(r){N(t,r);function t(e,i,n){var a=r.call(this,e,i,n)||this;return a.uid=mo("ec_cpt_model"),a}return t.prototype.init=function(e,i,n){this.mergeDefaultAndTheme(e,n)},t.prototype.mergeDefaultAndTheme=function(e,i){var n=qa(this),a=n?Ml(e):{},o=i.getTheme();st(e,o.get(this.mainType)),st(e,this.getDefaultOption()),n&&mn(e,a,n)},t.prototype.mergeOption=function(e,i){st(this.option,e,!0);var n=qa(this);n&&mn(this.option,e,n)},t.prototype.optionUpdated=function(e,i){},t.prototype.getDefaultOption=function(){var e=this.constructor;if(!X_(e))return e.defaultOption;var i=A1(this);if(!i.defaultOption){for(var n=[],a=e;a;){var o=a.prototype.defaultOption;o&&n.push(o),a=a.superClass}for(var s={},u=n.length-1;u>=0;u--)s=st(s,n[u],!0);i.defaultOption=s}return i.defaultOption},t.prototype.getReferringComponents=function(e,i){var n=e+"Index",a=e+"Id";return An(this.ecModel,e,{index:this.get(n,!0),id:this.get(a,!0)},i)},t.prototype.getBoxLayoutParams=function(){var e=this;return{left:e.get("left"),top:e.get("top"),right:e.get("right"),bottom:e.get("bottom"),width:e.get("width"),height:e.get("height")}},t.prototype.getZLevelKey=function(){return""},t.prototype.setZLevel=function(e){this.option.zlevel=e},t.protoInitialize=function(){var e=t.prototype;e.type="component",e.id="",e.name="",e.mainType="",e.subType="",e.componentIndex=0}(),t}(ht);$c(tt,ht);uo(tt);v1(tt);c1(tt,P1);function P1(r){var t=[];return D(tt.getClassesByMainType(r),function(e){t=t.concat(e.dependencies||e.prototype.dependencies||[])}),t=Y(t,function(e){return _e(e).main}),r!=="dataset"&&at(t,"dataset")<=0&&t.unshift("dataset"),t}var Wd="";typeof navigator<"u"&&(Wd=navigator.platform||"");var Kr="rgba(0, 0, 0, 0.2)";const L1={darkMode:"auto",colorBy:"series",color:["#5470c6","#91cc75","#fac858","#ee6666","#73c0de","#3ba272","#fc8452","#9a60b4","#ea7ccc"],gradientColor:["#f6efa6","#d88273","#bf444c"],aria:{decal:{decals:[{color:Kr,dashArrayX:[1,0],dashArrayY:[2,5],symbolSize:1,rotation:Math.PI/6},{color:Kr,symbol:"circle",dashArrayX:[[8,8],[0,8,8,0]],dashArrayY:[6,0],symbolSize:.8},{color:Kr,dashArrayX:[1,0],dashArrayY:[4,3],rotation:-Math.PI/4},{color:Kr,dashArrayX:[[6,6],[0,6,6,0]],dashArrayY:[6,0]},{color:Kr,dashArrayX:[[1,0],[1,6]],dashArrayY:[1,0,6,0],rotation:Math.PI/4},{color:Kr,symbol:"triangle",dashArrayX:[[9,9],[0,9,9,0]],dashArrayY:[7,2],symbolSize:.75}]}},textStyle:{fontFamily:Wd.match(/^Win/)?"Microsoft YaHei":"sans-serif",fontSize:12,fontStyle:"normal",fontWeight:"normal"},blendMode:null,stateAnimation:{duration:300,easing:"cubicOut"},animation:"auto",animationDuration:1e3,animationDurationUpdate:500,animationEasing:"cubicInOut",animationEasingUpdate:"cubicInOut",animationThreshold:2e3,progressiveThreshold:3e3,progressive:400,hoverLayerThreshold:3e3,useUTC:!1};var Ud=X(["tooltip","label","itemName","itemId","itemGroupId","itemChildGroupId","seriesName"]),xe="original",Gt="arrayRows",he="objectRows",Ce="keyedColumns",qe="typedArray",Yd="unknown",Se="column",Ti="row",pt={Must:1,Might:2,Not:3},Xd=mt();function I1(r){Xd(r).datasetMap=X()}function XC(r,t,e){var i={},n=Al(t);if(!n||!r)return i;var a=[],o=[],s=t.ecModel,u=Xd(s).datasetMap,l=n.uid+"_"+e.seriesLayoutBy,f,h;r=r.slice(),D(r,function(y,p){var g=F(y)?y:r[p]={name:y};g.type==="ordinal"&&f==null&&(f=p,h=d(g)),i[g.name]=[]});var c=u.get(l)||u.set(l,{categoryWayDim:h,valueWayDim:0});D(r,function(y,p){var g=y.name,_=d(y);if(f==null){var m=c.valueWayDim;v(i[g],m,_),v(o,m,_),c.valueWayDim+=_}else if(f===p)v(i[g],0,_),v(a,0,_);else{var m=c.categoryWayDim;v(i[g],m,_),v(o,m,_),c.categoryWayDim+=_}});function v(y,p,g){for(var _=0;_<g;_++)y.push(p+_)}function d(y){var p=y.dimsDef;return p?p.length:1}return a.length&&(i.itemName=a),o.length&&(i.seriesName=o),i}function $C(r,t,e){var i={},n=Al(r);if(!n)return i;var a=t.sourceFormat,o=t.dimensionsDefine,s;(a===he||a===Ce)&&D(o,function(f,h){(F(f)?f.name:f)==="name"&&(s=h)});var u=function(){for(var f={},h={},c=[],v=0,d=Math.min(5,e);v<d;v++){var y=qd(t.data,a,t.seriesLayoutBy,o,t.startIndex,v);c.push(y);var p=y===pt.Not;if(p&&f.v==null&&v!==s&&(f.v=v),(f.n==null||f.n===f.v||!p&&c[f.n]===pt.Not)&&(f.n=v),g(f)&&c[f.n]!==pt.Not)return f;p||(y===pt.Might&&h.v==null&&v!==s&&(h.v=v),(h.n==null||h.n===h.v)&&(h.n=v))}function g(_){return _.v!=null&&_.n!=null}return g(f)?f:g(h)?h:null}();if(u){i.value=[u.v];var l=s??u.n;i.itemName=[l],i.seriesName=[l]}return i}function Al(r){var t=r.get("data",!0);if(!t)return An(r.ecModel,"dataset",{index:r.get("datasetIndex",!0),id:r.get("datasetId",!0)},so).models[0]}function R1(r){return!r.get("transform",!0)&&!r.get("fromTransformResult",!0)?[]:An(r.ecModel,"dataset",{index:r.get("fromDatasetIndex",!0),id:r.get("fromDatasetId",!0)},so).models}function $d(r,t){return qd(r.data,r.sourceFormat,r.seriesLayoutBy,r.dimensionsDefine,r.startIndex,t)}function qd(r,t,e,i,n,a){var o,s=5;if(Ht(r))return pt.Not;var u,l;if(i){var f=i[a];F(f)?(u=f.name,l=f.type):B(f)&&(u=f)}if(l!=null)return l==="ordinal"?pt.Must:pt.Not;if(t===Gt){var h=r;if(e===Ti){for(var c=h[a],v=0;v<(c||[]).length&&v<s;v++)if((o=w(c[n+v]))!=null)return o}else for(var v=0;v<h.length&&v<s;v++){var d=h[n+v];if(d&&(o=w(d[a]))!=null)return o}}else if(t===he){var y=r;if(!u)return pt.Not;for(var v=0;v<y.length&&v<s;v++){var p=y[v];if(p&&(o=w(p[u]))!=null)return o}}else if(t===Ce){var g=r;if(!u)return pt.Not;var c=g[u];if(!c||Ht(c))return pt.Not;for(var v=0;v<c.length&&v<s;v++)if((o=w(c[v]))!=null)return o}else if(t===xe)for(var _=r,v=0;v<_.length&&v<s;v++){var p=_[v],m=oo(p);if(!k(m))return pt.Not;if((o=w(m[a]))!=null)return o}function w(T){var S=B(T);if(T!=null&&Number.isFinite(Number(T))&&T!=="")return S?pt.Might:pt.Not;if(S&&T!=="-")return pt.Must}return pt.Not}var Tu=X();function qC(r,t){Te(Tu.get(r)==null&&t),Tu.set(r,t)}function E1(r,t,e){var i=Tu.get(t);if(!i)return e;var n=i(r);return n?e.concat(n):e}var wh=mt(),O1=mt(),Pl=function(){function r(){}return r.prototype.getColorFromPalette=function(t,e,i){var n=_t(this.get("color",!0)),a=this.get("colorLayer",!0);return Zd(this,wh,n,a,t,e,i)},r.prototype.clearColorPalette=function(){B1(this,wh)},r}();function ZC(r,t,e,i){var n=_t(r.get(["aria","decal","decals"]));return Zd(r,O1,n,null,t,e,i)}function k1(r,t){for(var e=r.length,i=0;i<e;i++)if(r[i].length>t)return r[i];return r[e-1]}function Zd(r,t,e,i,n,a,o){a=a||r;var s=t(a),u=s.paletteIdx||0,l=s.paletteNameMap=s.paletteNameMap||{};if(l.hasOwnProperty(n))return l[n];var f=o==null||!i?e:k1(i,o);if(f=f||e,!(!f||!f.length)){var h=f[u];return n&&(l[n]=h),s.paletteIdx=(u+1)%f.length,h}}function B1(r,t){t(r).paletteIdx=0,t(r).paletteNameMap={}}var jn,Ri,Sh,Th="\0_ec_inner",N1=1,Ll=function(r){N(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.init=function(e,i,n,a,o,s){a=a||{},this.option=null,this._theme=new ht(a),this._locale=new ht(o),this._optionManager=s},t.prototype.setOption=function(e,i,n){var a=Ch(i);this._optionManager.setOption(e,n,a),this._resetOption(null,a)},t.prototype.resetOption=function(e,i){return this._resetOption(e,Ch(i))},t.prototype._resetOption=function(e,i){var n=!1,a=this._optionManager;if(!e||e==="recreate"){var o=a.mountOption(e==="recreate");!this.option||e==="recreate"?Sh(this,o):(this.restoreData(),this._mergeOption(o,i)),n=!0}if((e==="timeline"||e==="media")&&this.restoreData(),!e||e==="recreate"||e==="timeline"){var s=a.getTimelineOption(this);s&&(n=!0,this._mergeOption(s,i))}if(!e||e==="recreate"||e==="media"){var u=a.getMediaOption(this);u.length&&D(u,function(l){n=!0,this._mergeOption(l,i)},this)}return n},t.prototype.mergeOption=function(e){this._mergeOption(e,null)},t.prototype._mergeOption=function(e,i){var n=this.option,a=this._componentsMap,o=this._componentsCount,s=[],u=X(),l=i&&i.replaceMergeMainTypeMap;I1(this),D(e,function(h,c){h!=null&&(tt.hasClass(c)?c&&(s.push(c),u.set(c,!0)):n[c]=n[c]==null?Z(h):st(n[c],h,!0))}),l&&l.each(function(h,c){tt.hasClass(c)&&!u.get(c)&&(s.push(c),u.set(c,!0))}),tt.topologicalTravel(s,tt.getAllClassMainTypes(),f,this);function f(h){var c=E1(this,h,_t(e[h])),v=a.get(h),d=v?l&&l.get(h)?"replaceMerge":"normalMerge":"replaceAll",y=I_(v,c,d);F_(y,h,tt),n[h]=null,a.set(h,null),o.set(h,0);var p=[],g=[],_=0,m;D(y,function(w,T){var S=w.existing,b=w.newOption;if(!b)S&&(S.mergeOption({},this),S.optionUpdated({},!1));else{var M=h==="series",x=tt.getClass(h,w.keyInfo.subType,!M);if(!x)return;if(h==="tooltip"){if(m)return;m=!0}if(S&&S.constructor===x)S.name=w.keyInfo.name,S.mergeOption(b,this),S.optionUpdated(b,!1);else{var C=O({componentIndex:T},w.keyInfo);S=new x(b,this,this,C),O(S,C),w.brandNew&&(S.__requireNewView=!0),S.init(b,this,this),S.optionUpdated(null,!0)}}S?(p.push(S.option),g.push(S),_++):(p.push(void 0),g.push(void 0))},this),n[h]=p,a.set(h,g),o.set(h,_),h==="series"&&jn(this)}this._seriesIndices||jn(this)},t.prototype.getOption=function(){var e=Z(this.option);return D(e,function(i,n){if(tt.hasClass(n)){for(var a=_t(i),o=a.length,s=!1,u=o-1;u>=0;u--)a[u]&&!pn(a[u])?s=!0:(a[u]=null,!s&&o--);a.length=o,e[n]=a}}),delete e[Th],e},t.prototype.getTheme=function(){return this._theme},t.prototype.getLocaleModel=function(){return this._locale},t.prototype.setUpdatePayload=function(e){this._payload=e},t.prototype.getUpdatePayload=function(){return this._payload},t.prototype.getComponent=function(e,i){var n=this._componentsMap.get(e);if(n){var a=n[i||0];if(a)return a;if(i==null){for(var o=0;o<n.length;o++)if(n[o])return n[o]}}},t.prototype.queryComponents=function(e){var i=e.mainType;if(!i)return[];var n=e.index,a=e.id,o=e.name,s=this._componentsMap.get(i);if(!s||!s.length)return[];var u;return n!=null?(u=[],D(_t(n),function(l){s[l]&&u.push(s[l])})):a!=null?u=bh("id",a,s):o!=null?u=bh("name",o,s):u=Et(s,function(l){return!!l}),xh(u,e)},t.prototype.findComponents=function(e){var i=e.query,n=e.mainType,a=s(i),o=a?this.queryComponents(a):Et(this._componentsMap.get(n),function(l){return!!l});return u(xh(o,e));function s(l){var f=n+"Index",h=n+"Id",c=n+"Name";return l&&(l[f]!=null||l[h]!=null||l[c]!=null)?{mainType:n,index:l[f],id:l[h],name:l[c]}:null}function u(l){return e.filter?Et(l,e.filter):l}},t.prototype.eachComponent=function(e,i,n){var a=this._componentsMap;if(K(e)){var o=i,s=e;a.each(function(h,c){for(var v=0;h&&v<h.length;v++){var d=h[v];d&&s.call(o,c,d,d.componentIndex)}})}else for(var u=B(e)?a.get(e):F(e)?this.findComponents(e):null,l=0;u&&l<u.length;l++){var f=u[l];f&&i.call(n,f,f.componentIndex)}},t.prototype.getSeriesByName=function(e){var i=se(e,null);return Et(this._componentsMap.get("series"),function(n){return!!n&&i!=null&&n.name===i})},t.prototype.getSeriesByIndex=function(e){return this._componentsMap.get("series")[e]},t.prototype.getSeriesByType=function(e){return Et(this._componentsMap.get("series"),function(i){return!!i&&i.subType===e})},t.prototype.getSeries=function(){return Et(this._componentsMap.get("series"),function(e){return!!e})},t.prototype.getSeriesCount=function(){return this._componentsCount.get("series")},t.prototype.eachSeries=function(e,i){Ri(this),D(this._seriesIndices,function(n){var a=this._componentsMap.get("series")[n];e.call(i,a,n)},this)},t.prototype.eachRawSeries=function(e,i){D(this._componentsMap.get("series"),function(n){n&&e.call(i,n,n.componentIndex)})},t.prototype.eachSeriesByType=function(e,i,n){Ri(this),D(this._seriesIndices,function(a){var o=this._componentsMap.get("series")[a];o.subType===e&&i.call(n,o,a)},this)},t.prototype.eachRawSeriesByType=function(e,i,n){return D(this.getSeriesByType(e),i,n)},t.prototype.isSeriesFiltered=function(e){return Ri(this),this._seriesIndicesMap.get(e.componentIndex)==null},t.prototype.getCurrentSeriesIndices=function(){return(this._seriesIndices||[]).slice()},t.prototype.filterSeries=function(e,i){Ri(this);var n=[];D(this._seriesIndices,function(a){var o=this._componentsMap.get("series")[a];e.call(i,o,a)&&n.push(a)},this),this._seriesIndices=n,this._seriesIndicesMap=X(n)},t.prototype.restoreData=function(e){jn(this);var i=this._componentsMap,n=[];i.each(function(a,o){tt.hasClass(o)&&n.push(o)}),tt.topologicalTravel(n,tt.getAllClassMainTypes(),function(a){D(i.get(a),function(o){o&&(a!=="series"||!F1(o,e))&&o.restoreData()})})},t.internalField=function(){jn=function(e){var i=e._seriesIndices=[];D(e._componentsMap.get("series"),function(n){n&&i.push(n.componentIndex)}),e._seriesIndicesMap=X(i)},Ri=function(e){},Sh=function(e,i){e.option={},e.option[Th]=N1,e._componentsMap=X({series:[]}),e._componentsCount=X();var n=i.aria;F(n)&&n.enabled==null&&(n.enabled=!0),z1(i,e._theme.option),st(i,L1,!1),e._mergeOption(i,null)}}(),t}(ht);function F1(r,t){if(t){var e=t.seriesIndex,i=t.seriesId,n=t.seriesName;return e!=null&&r.componentIndex!==e||i!=null&&r.id!==i||n!=null&&r.name!==n}}function z1(r,t){var e=r.color&&!r.colorLayer;D(t,function(i,n){n==="colorLayer"&&e||tt.hasClass(n)||(typeof i=="object"?r[n]=r[n]?st(r[n],i,!1):Z(i):r[n]==null&&(r[n]=i))})}function bh(r,t,e){if(k(t)){var i=X();return D(t,function(a){if(a!=null){var o=se(a,null);o!=null&&i.set(a,!0)}}),Et(e,function(a){return a&&i.get(a[r])})}else{var n=se(t,null);return Et(e,function(a){return a&&n!=null&&a[r]===n})}}function xh(r,t){return t.hasOwnProperty("subType")?Et(r,function(e){return e&&e.subType===t.subType}):r}function Ch(r){var t=X();return r&&D(_t(r.replaceMerge),function(e){t.set(e,!0)}),{replaceMergeMainTypeMap:t}}Ee(Ll,Pl);var H1=["getDom","getZr","getWidth","getHeight","getDevicePixelRatio","dispatchAction","isSSR","isDisposed","on","off","getDataURL","getConnectedDataURL","getOption","getId","updateLabelLayout"],Kd=function(){function r(t){D(H1,function(e){this[e]=lt(t[e],t)},this)}return r}(),ws={},Qd=function(){function r(){this._coordinateSystems=[]}return r.prototype.create=function(t,e){var i=[];D(ws,function(n,a){var o=n.create(t,e);i=i.concat(o||[])}),this._coordinateSystems=i},r.prototype.update=function(t,e){D(this._coordinateSystems,function(i){i.update&&i.update(t,e)})},r.prototype.getCoordinateSystems=function(){return this._coordinateSystems.slice()},r.register=function(t,e){ws[t]=e},r.get=function(t){return ws[t]},r}(),G1=/^(min|max)?(.+)$/,V1=function(){function r(t){this._timelineOptions=[],this._mediaList=[],this._currentMediaIndices=[],this._api=t}return r.prototype.setOption=function(t,e,i){t&&(D(_t(t.series),function(o){o&&o.data&&Ht(o.data)&&ka(o.data)}),D(_t(t.dataset),function(o){o&&o.source&&Ht(o.source)&&ka(o.source)})),t=Z(t);var n=this._optionBackup,a=W1(t,e,!n);this._newBaseOption=a.baseOption,n?(a.timelineOptions.length&&(n.timelineOptions=a.timelineOptions),a.mediaList.length&&(n.mediaList=a.mediaList),a.mediaDefault&&(n.mediaDefault=a.mediaDefault)):this._optionBackup=a},r.prototype.mountOption=function(t){var e=this._optionBackup;return this._timelineOptions=e.timelineOptions,this._mediaList=e.mediaList,this._mediaDefault=e.mediaDefault,this._currentMediaIndices=[],Z(t?e.baseOption:this._newBaseOption)},r.prototype.getTimelineOption=function(t){var e,i=this._timelineOptions;if(i.length){var n=t.getComponent("timeline");n&&(e=Z(i[n.getCurrentIndex()]))}return e},r.prototype.getMediaOption=function(t){var e=this._api.getWidth(),i=this._api.getHeight(),n=this._mediaList,a=this._mediaDefault,o=[],s=[];if(!n.length&&!a)return s;for(var u=0,l=n.length;u<l;u++)U1(n[u].query,e,i)&&o.push(u);return!o.length&&a&&(o=[-1]),o.length&&!X1(o,this._currentMediaIndices)&&(s=Y(o,function(f){return Z(f===-1?a.option:n[f].option)})),this._currentMediaIndices=o,s},r}();function W1(r,t,e){var i=[],n,a,o=r.baseOption,s=r.timeline,u=r.options,l=r.media,f=!!r.media,h=!!(u||s||o&&o.timeline);o?(a=o,a.timeline||(a.timeline=s)):((h||f)&&(r.options=r.media=null),a=r),f&&k(l)&&D(l,function(v){v&&v.option&&(v.query?i.push(v):n||(n=v))}),c(a),D(u,function(v){return c(v)}),D(i,function(v){return c(v.option)});function c(v){D(t,function(d){d(v,e)})}return{baseOption:a,timelineOptions:u||[],mediaDefault:n,mediaList:i}}function U1(r,t,e){var i={width:t,height:e,aspectratio:t/e},n=!0;return D(r,function(a,o){var s=o.match(G1);if(!(!s||!s[1]||!s[2])){var u=s[1],l=s[2].toLowerCase();Y1(i[l],a,u)||(n=!1)}}),n}function Y1(r,t,e){return e==="min"?r>=t:e==="max"?r<=t:r===t}function X1(r,t){return r.join(",")===t.join(",")}var te=D,wn=F,Dh=["areaStyle","lineStyle","nodeStyle","linkStyle","chordStyle","label","labelLine"];function Ss(r){var t=r&&r.itemStyle;if(t)for(var e=0,i=Dh.length;e<i;e++){var n=Dh[e],a=t.normal,o=t.emphasis;a&&a[n]&&(r[n]=r[n]||{},r[n].normal?st(r[n].normal,a[n]):r[n].normal=a[n],a[n]=null),o&&o[n]&&(r[n]=r[n]||{},r[n].emphasis?st(r[n].emphasis,o[n]):r[n].emphasis=o[n],o[n]=null)}}function Ct(r,t,e){if(r&&r[t]&&(r[t].normal||r[t].emphasis)){var i=r[t].normal,n=r[t].emphasis;i&&(e?(r[t].normal=r[t].emphasis=null,ot(r[t],i)):r[t]=i),n&&(r.emphasis=r.emphasis||{},r.emphasis[t]=n,n.focus&&(r.emphasis.focus=n.focus),n.blurScope&&(r.emphasis.blurScope=n.blurScope))}}function Ki(r){Ct(r,"itemStyle"),Ct(r,"lineStyle"),Ct(r,"areaStyle"),Ct(r,"label"),Ct(r,"labelLine"),Ct(r,"upperLabel"),Ct(r,"edgeLabel")}function vt(r,t){var e=wn(r)&&r[t],i=wn(e)&&e.textStyle;if(i)for(var n=0,a=Pf.length;n<a;n++){var o=Pf[n];i.hasOwnProperty(o)&&(e[o]=i[o])}}function Zt(r){r&&(Ki(r),vt(r,"label"),r.emphasis&&vt(r.emphasis,"label"))}function $1(r){if(wn(r)){Ss(r),Ki(r),vt(r,"label"),vt(r,"upperLabel"),vt(r,"edgeLabel"),r.emphasis&&(vt(r.emphasis,"label"),vt(r.emphasis,"upperLabel"),vt(r.emphasis,"edgeLabel"));var t=r.markPoint;t&&(Ss(t),Zt(t));var e=r.markLine;e&&(Ss(e),Zt(e));var i=r.markArea;i&&Zt(i);var n=r.data;if(r.type==="graph"){n=n||r.nodes;var a=r.links||r.edges;if(a&&!Ht(a))for(var o=0;o<a.length;o++)Zt(a[o]);D(r.categories,function(l){Ki(l)})}if(n&&!Ht(n))for(var o=0;o<n.length;o++)Zt(n[o]);if(t=r.markPoint,t&&t.data)for(var s=t.data,o=0;o<s.length;o++)Zt(s[o]);if(e=r.markLine,e&&e.data)for(var u=e.data,o=0;o<u.length;o++)k(u[o])?(Zt(u[o][0]),Zt(u[o][1])):Zt(u[o]);r.type==="gauge"?(vt(r,"axisLabel"),vt(r,"title"),vt(r,"detail")):r.type==="treemap"?(Ct(r.breadcrumb,"itemStyle"),D(r.levels,function(l){Ki(l)})):r.type==="tree"&&Ki(r.leaves)}}function Ae(r){return k(r)?r:r?[r]:[]}function Mh(r){return(k(r)?r[0]:r)||{}}function q1(r,t){te(Ae(r.series),function(i){wn(i)&&$1(i)});var e=["xAxis","yAxis","radiusAxis","angleAxis","singleAxis","parallelAxis","radar"];t&&e.push("valueAxis","categoryAxis","logAxis","timeAxis"),te(e,function(i){te(Ae(r[i]),function(n){n&&(vt(n,"axisLabel"),vt(n.axisPointer,"label"))})}),te(Ae(r.parallel),function(i){var n=i&&i.parallelAxisDefault;vt(n,"axisLabel"),vt(n&&n.axisPointer,"label")}),te(Ae(r.calendar),function(i){Ct(i,"itemStyle"),vt(i,"dayLabel"),vt(i,"monthLabel"),vt(i,"yearLabel")}),te(Ae(r.radar),function(i){vt(i,"name"),i.name&&i.axisName==null&&(i.axisName=i.name,delete i.name),i.nameGap!=null&&i.axisNameGap==null&&(i.axisNameGap=i.nameGap,delete i.nameGap)}),te(Ae(r.geo),function(i){wn(i)&&(Zt(i),te(Ae(i.regions),function(n){Zt(n)}))}),te(Ae(r.timeline),function(i){Zt(i),Ct(i,"label"),Ct(i,"itemStyle"),Ct(i,"controlStyle",!0);var n=i.data;k(n)&&D(n,function(a){F(a)&&(Ct(a,"label"),Ct(a,"itemStyle"))})}),te(Ae(r.toolbox),function(i){Ct(i,"iconStyle"),te(i.feature,function(n){Ct(n,"iconStyle")})}),vt(Mh(r.axisPointer),"label"),vt(Mh(r.tooltip).axisPointer,"label")}function Z1(r,t){for(var e=t.split(","),i=r,n=0;n<e.length&&(i=i&&i[e[n]],i!=null);n++);return i}function K1(r,t,e,i){for(var n=t.split(","),a=r,o,s=0;s<n.length-1;s++)o=n[s],a[o]==null&&(a[o]={}),a=a[o];a[n[s]]==null&&(a[n[s]]=e)}function Ah(r){r&&D(Q1,function(t){t[0]in r&&!(t[1]in r)&&(r[t[1]]=r[t[0]])})}var Q1=[["x","left"],["y","top"],["x2","right"],["y2","bottom"]],J1=["grid","geo","parallel","legend","toolbox","title","visualMap","dataZoom","timeline"],Ts=[["borderRadius","barBorderRadius"],["borderColor","barBorderColor"],["borderWidth","barBorderWidth"]];function Ei(r){var t=r&&r.itemStyle;if(t)for(var e=0;e<Ts.length;e++){var i=Ts[e][1],n=Ts[e][0];t[i]!=null&&(t[n]=t[i])}}function Ph(r){r&&r.alignTo==="edge"&&r.margin!=null&&r.edgeDistance==null&&(r.edgeDistance=r.margin)}function Lh(r){r&&r.downplay&&!r.blur&&(r.blur=r.downplay)}function j1(r){r&&r.focusNodeAdjacency!=null&&(r.emphasis=r.emphasis||{},r.emphasis.focus==null&&(r.emphasis.focus="adjacency"))}function Jd(r,t){if(r)for(var e=0;e<r.length;e++)t(r[e]),r[e]&&Jd(r[e].children,t)}function jd(r,t){q1(r,t),r.series=_t(r.series),D(r.series,function(e){if(F(e)){var i=e.type;if(i==="line")e.clipOverflow!=null&&(e.clip=e.clipOverflow);else if(i==="pie"||i==="gauge"){e.clockWise!=null&&(e.clockwise=e.clockWise),Ph(e.label);var n=e.data;if(n&&!Ht(n))for(var a=0;a<n.length;a++)Ph(n[a]);e.hoverOffset!=null&&(e.emphasis=e.emphasis||{},(e.emphasis.scaleSize=null)&&(e.emphasis.scaleSize=e.hoverOffset))}else if(i==="gauge"){var o=Z1(e,"pointer.color");o!=null&&K1(e,"itemStyle.color",o)}else if(i==="bar"){Ei(e),Ei(e.backgroundStyle),Ei(e.emphasis);var n=e.data;if(n&&!Ht(n))for(var a=0;a<n.length;a++)typeof n[a]=="object"&&(Ei(n[a]),Ei(n[a]&&n[a].emphasis))}else if(i==="sunburst"){var s=e.highlightPolicy;s&&(e.emphasis=e.emphasis||{},e.emphasis.focus||(e.emphasis.focus=s)),Lh(e),Jd(e.data,Lh)}else i==="graph"||i==="sankey"?j1(e):i==="map"&&(e.mapType&&!e.map&&(e.map=e.mapType),e.mapLocation&&ot(e,e.mapLocation));e.hoverAnimation!=null&&(e.emphasis=e.emphasis||{},e.emphasis&&e.emphasis.scale==null&&(e.emphasis.scale=e.hoverAnimation)),Ah(e)}}),r.dataRange&&(r.visualMap=r.dataRange),D(J1,function(e){var i=r[e];i&&(k(i)||(i=[i]),D(i,function(n){Ah(n)}))})}function tw(r){var t=X();r.eachSeries(function(e){var i=e.get("stack");if(i){var n=t.get(i)||t.set(i,[]),a=e.getData(),o={stackResultDimension:a.getCalculationInfo("stackResultDimension"),stackedOverDimension:a.getCalculationInfo("stackedOverDimension"),stackedDimension:a.getCalculationInfo("stackedDimension"),stackedByDimension:a.getCalculationInfo("stackedByDimension"),isStackedByIndex:a.getCalculationInfo("isStackedByIndex"),data:a,seriesModel:e};if(!o.stackedDimension||!(o.isStackedByIndex||o.stackedByDimension))return;n.length&&a.setCalculationInfo("stackedOnSeries",n[n.length-1].seriesModel),n.push(o)}}),t.each(ew)}function ew(r){D(r,function(t,e){var i=[],n=[NaN,NaN],a=[t.stackResultDimension,t.stackedOverDimension],o=t.data,s=t.isStackedByIndex,u=t.seriesModel.get("stackStrategy")||"samesign";o.modify(a,function(l,f,h){var c=o.get(t.stackedDimension,h);if(isNaN(c))return n;var v,d;s?d=o.getRawIndex(h):v=o.get(t.stackedByDimension,h);for(var y=NaN,p=e-1;p>=0;p--){var g=r[p];if(s||(d=g.data.rawIndexOf(g.stackedByDimension,v)),d>=0){var _=g.data.getByRawIndex(g.stackResultDimension,d);if(u==="all"||u==="positive"&&_>0||u==="negative"&&_<0||u==="samesign"&&c>=0&&_>0||u==="samesign"&&c<=0&&_<0){c=D_(c,_),y=_;break}}}return i[0]=c,i[1]=y,i})})}var Co=function(){function r(t){this.data=t.data||(t.sourceFormat===Ce?{}:[]),this.sourceFormat=t.sourceFormat||Yd,this.seriesLayoutBy=t.seriesLayoutBy||Se,this.startIndex=t.startIndex||0,this.dimensionsDetectedCount=t.dimensionsDetectedCount,this.metaRawOption=t.metaRawOption;var e=this.dimensionsDefine=t.dimensionsDefine;if(e)for(var i=0;i<e.length;i++){var n=e[i];n.type==null&&$d(this,i)===pt.Must&&(n.type="ordinal")}}return r}();function Il(r){return r instanceof Co}function bu(r,t,e){e=e||ep(r);var i=t.seriesLayoutBy,n=iw(r,e,i,t.sourceHeader,t.dimensions),a=new Co({data:r,sourceFormat:e,seriesLayoutBy:i,dimensionsDefine:n.dimensionsDefine,startIndex:n.startIndex,dimensionsDetectedCount:n.dimensionsDetectedCount,metaRawOption:Z(t)});return a}function tp(r){return new Co({data:r,sourceFormat:Ht(r)?qe:xe})}function rw(r){return new Co({data:r.data,sourceFormat:r.sourceFormat,seriesLayoutBy:r.seriesLayoutBy,dimensionsDefine:Z(r.dimensionsDefine),startIndex:r.startIndex,dimensionsDetectedCount:r.dimensionsDetectedCount})}function ep(r){var t=Yd;if(Ht(r))t=qe;else if(k(r)){r.length===0&&(t=Gt);for(var e=0,i=r.length;e<i;e++){var n=r[e];if(n!=null){if(k(n)||Ht(n)){t=Gt;break}else if(F(n)){t=he;break}}}}else if(F(r)){for(var a in r)if(Ke(r,a)&&zt(r[a])){t=Ce;break}}return t}function iw(r,t,e,i,n){var a,o;if(!r)return{dimensionsDefine:Ih(n),startIndex:o,dimensionsDetectedCount:a};if(t===Gt){var s=r;i==="auto"||i==null?Rh(function(l){l!=null&&l!=="-"&&(B(l)?o==null&&(o=1):o=0)},e,s,10):o=ut(i)?i:i?1:0,!n&&o===1&&(n=[],Rh(function(l,f){n[f]=l!=null?l+"":""},e,s,1/0)),a=n?n.length:e===Ti?s.length:s[0]?s[0].length:null}else if(t===he)n||(n=nw(r));else if(t===Ce)n||(n=[],D(r,function(l,f){n.push(f)}));else if(t===xe){var u=oo(r[0]);a=k(u)&&u.length||1}return{startIndex:o,dimensionsDefine:Ih(n),dimensionsDetectedCount:a}}function nw(r){for(var t=0,e;t<r.length&&!(e=r[t++]););if(e)return ft(e)}function Ih(r){if(r){var t=X();return Y(r,function(e,i){e=F(e)?e:{name:e};var n={name:e.name,displayName:e.displayName,type:e.type};if(n.name==null)return n;n.name+="",n.displayName==null&&(n.displayName=n.name);var a=t.get(n.name);return a?n.name+="-"+a.count++:t.set(n.name,{count:1}),n})}}function Rh(r,t,e,i){if(t===Ti)for(var n=0;n<e.length&&n<i;n++)r(e[n]?e[n][0]:null,n);else for(var a=e[0]||[],n=0;n<a.length&&n<i;n++)r(a[n],n)}function rp(r){var t=r.sourceFormat;return t===he||t===Ce}var Sr,Tr,br,Eh,Oh,ip=function(){function r(t,e){var i=Il(t)?t:tp(t);this._source=i;var n=this._data=i.data;i.sourceFormat===qe&&(this._offset=0,this._dimSize=e,this._data=n),Oh(this,n,i)}return r.prototype.getSource=function(){return this._source},r.prototype.count=function(){return 0},r.prototype.getItem=function(t,e){},r.prototype.appendData=function(t){},r.prototype.clean=function(){},r.protoInitialize=function(){var t=r.prototype;t.pure=!1,t.persistent=!0}(),r.internalField=function(){var t;Oh=function(o,s,u){var l=u.sourceFormat,f=u.seriesLayoutBy,h=u.startIndex,c=u.dimensionsDefine,v=Eh[Rl(l,f)];if(O(o,v),l===qe)o.getItem=e,o.count=n,o.fillStorage=i;else{var d=np(l,f);o.getItem=lt(d,null,s,h,c);var y=ap(l,f);o.count=lt(y,null,s,h,c)}};var e=function(o,s){o=o-this._offset,s=s||[];for(var u=this._data,l=this._dimSize,f=l*o,h=0;h<l;h++)s[h]=u[f+h];return s},i=function(o,s,u,l){for(var f=this._data,h=this._dimSize,c=0;c<h;c++){for(var v=l[c],d=v[0]==null?1/0:v[0],y=v[1]==null?-1/0:v[1],p=s-o,g=u[c],_=0;_<p;_++){var m=f[_*h+c];g[o+_]=m,m<d&&(d=m),m>y&&(y=m)}v[0]=d,v[1]=y}},n=function(){return this._data?this._data.length/this._dimSize:0};Eh=(t={},t[Gt+"_"+Se]={pure:!0,appendData:a},t[Gt+"_"+Ti]={pure:!0,appendData:function(){throw new Error('Do not support appendData when set seriesLayoutBy: "row".')}},t[he]={pure:!0,appendData:a},t[Ce]={pure:!0,appendData:function(o){var s=this._data;D(o,function(u,l){for(var f=s[l]||(s[l]=[]),h=0;h<(u||[]).length;h++)f.push(u[h])})}},t[xe]={appendData:a},t[qe]={persistent:!1,pure:!0,appendData:function(o){this._data=o},clean:function(){this._offset+=this.count(),this._data=null}},t);function a(o){for(var s=0;s<o.length;s++)this._data.push(o[s])}}(),r}(),kh=function(r,t,e,i){return r[i]},aw=(Sr={},Sr[Gt+"_"+Se]=function(r,t,e,i){return r[i+t]},Sr[Gt+"_"+Ti]=function(r,t,e,i,n){i+=t;for(var a=n||[],o=r,s=0;s<o.length;s++){var u=o[s];a[s]=u?u[i]:null}return a},Sr[he]=kh,Sr[Ce]=function(r,t,e,i,n){for(var a=n||[],o=0;o<e.length;o++){var s=e[o].name,u=r[s];a[o]=u?u[i]:null}return a},Sr[xe]=kh,Sr);function np(r,t){var e=aw[Rl(r,t)];return e}var Bh=function(r,t,e){return r.length},ow=(Tr={},Tr[Gt+"_"+Se]=function(r,t,e){return Math.max(0,r.length-t)},Tr[Gt+"_"+Ti]=function(r,t,e){var i=r[0];return i?Math.max(0,i.length-t):0},Tr[he]=Bh,Tr[Ce]=function(r,t,e){var i=e[0].name,n=r[i];return n?n.length:0},Tr[xe]=Bh,Tr);function ap(r,t){var e=ow[Rl(r,t)];return e}var bs=function(r,t,e){return r[t]},sw=(br={},br[Gt]=bs,br[he]=function(r,t,e){return r[e]},br[Ce]=bs,br[xe]=function(r,t,e){var i=oo(r);return i instanceof Array?i[t]:i},br[qe]=bs,br);function op(r){var t=sw[r];return t}function Rl(r,t){return r===Gt?r+"_"+t:r}function Za(r,t,e){if(r){var i=r.getRawDataItem(t);if(i!=null){var n=r.getStore(),a=n.getSource().sourceFormat;if(e!=null){var o=r.getDimensionIndex(e),s=n.getDimensionProperty(o);return op(a)(i,o,s)}else{var u=i;return a===xe&&(u=oo(i)),u}}}}var uw=/\{@(.+?)\}/g,lw=function(){function r(){}return r.prototype.getDataParams=function(t,e){var i=this.getData(e),n=this.getRawValue(t,e),a=i.getRawIndex(t),o=i.getName(t),s=i.getRawDataItem(t),u=i.getItemVisual(t,"style"),l=u&&u[i.getItemVisual(t,"drawType")||"fill"],f=u&&u.stroke,h=this.mainType,c=h==="series",v=i.userOutput&&i.userOutput.get();return{componentType:h,componentSubType:this.subType,componentIndex:this.componentIndex,seriesType:c?this.subType:null,seriesIndex:this.seriesIndex,seriesId:c?this.id:null,seriesName:c?this.name:null,name:o,dataIndex:a,data:s,dataType:e,value:n,color:l,borderColor:f,dimensionNames:v?v.fullDimensions:null,encode:v?v.encode:null,$vars:["seriesName","name","value"]}},r.prototype.getFormattedLabel=function(t,e,i,n,a,o){e=e||"normal";var s=this.getData(i),u=this.getDataParams(t,i);if(o&&(u.value=o.interpolatedValue),n!=null&&k(u.value)&&(u.value=u.value[n]),!a){var l=s.getItemModel(t);a=l.get(e==="normal"?["label","formatter"]:[e,"label","formatter"])}if(K(a))return u.status=e,u.dimensionIndex=n,a(u);if(B(a)){var f=Vd(a,u);return f.replace(uw,function(h,c){var v=c.length,d=c;d.charAt(0)==="["&&d.charAt(v-1)==="]"&&(d=+d.slice(1,v-1));var y=Za(s,t,d);if(o&&k(o.interpolatedValue)){var p=s.getDimensionIndex(d);p>=0&&(y=o.interpolatedValue[p])}return y!=null?y+"":""})}},r.prototype.getRawValue=function(t,e){return Za(this.getData(e),t)},r.prototype.formatTooltip=function(t,e,i){},r}();function Nh(r){var t,e;return F(r)?r.type&&(e=r):t=r,{text:t,frag:e}}function sn(r){return new fw(r)}var fw=function(){function r(t){t=t||{},this._reset=t.reset,this._plan=t.plan,this._count=t.count,this._onDirty=t.onDirty,this._dirty=!0}return r.prototype.perform=function(t){var e=this._upstream,i=t&&t.skip;if(this._dirty&&e){var n=this.context;n.data=n.outputData=e.context.outputData}this.__pipeline&&(this.__pipeline.currentTask=this);var a;this._plan&&!i&&(a=this._plan(this.context));var o=f(this._modBy),s=this._modDataCount||0,u=f(t&&t.modBy),l=t&&t.modDataCount||0;(o!==u||s!==l)&&(a="reset");function f(_){return!(_>=1)&&(_=1),_}var h;(this._dirty||a==="reset")&&(this._dirty=!1,h=this._doReset(i)),this._modBy=u,this._modDataCount=l;var c=t&&t.step;if(e?this._dueEnd=e._outputDueEnd:this._dueEnd=this._count?this._count(this.context):1/0,this._progress){var v=this._dueIndex,d=Math.min(c!=null?this._dueIndex+c:1/0,this._dueEnd);if(!i&&(h||v<d)){var y=this._progress;if(k(y))for(var p=0;p<y.length;p++)this._doProgress(y[p],v,d,u,l);else this._doProgress(y,v,d,u,l)}this._dueIndex=d;var g=this._settedOutputEnd!=null?this._settedOutputEnd:d;this._outputDueEnd=g}else this._dueIndex=this._outputDueEnd=this._settedOutputEnd!=null?this._settedOutputEnd:this._dueEnd;return this.unfinished()},r.prototype.dirty=function(){this._dirty=!0,this._onDirty&&this._onDirty(this.context)},r.prototype._doProgress=function(t,e,i,n,a){Fh.reset(e,i,n,a),this._callingProgress=t,this._callingProgress({start:e,end:i,count:i-e,next:Fh.next},this.context)},r.prototype._doReset=function(t){this._dueIndex=this._outputDueEnd=this._dueEnd=0,this._settedOutputEnd=null;var e,i;!t&&this._reset&&(e=this._reset(this.context),e&&e.progress&&(i=e.forceFirstProgress,e=e.progress),k(e)&&!e.length&&(e=null)),this._progress=e,this._modBy=this._modDataCount=null;var n=this._downstream;return n&&n.dirty(),i},r.prototype.unfinished=function(){return this._progress&&this._dueIndex<this._dueEnd},r.prototype.pipe=function(t){(this._downstream!==t||this._dirty)&&(this._downstream=t,t._upstream=this,t.dirty())},r.prototype.dispose=function(){this._disposed||(this._upstream&&(this._upstream._downstream=null),this._downstream&&(this._downstream._upstream=null),this._dirty=!1,this._disposed=!0)},r.prototype.getUpstream=function(){return this._upstream},r.prototype.getDownstream=function(){return this._downstream},r.prototype.setOutputEnd=function(t){this._outputDueEnd=this._settedOutputEnd=t},r}(),Fh=function(){var r,t,e,i,n,a={reset:function(u,l,f,h){t=u,r=l,e=f,i=h,n=Math.ceil(i/e),a.next=e>1&&i>0?s:o}};return a;function o(){return t<r?t++:null}function s(){var u=t%n*e+Math.ceil(t/n),l=t>=r?null:u<i?u:t;return t++,l}}();function La(r,t){var e=t&&t.type;return e==="ordinal"?r:(e==="time"&&!ut(r)&&r!=null&&r!=="-"&&(r=+Re(r)),r==null||r===""?NaN:Number(r))}var hw=X({number:function(r){return parseFloat(r)},time:function(r){return+Re(r)},trim:function(r){return B(r)?ye(r):r}});function KC(r){return hw.get(r)}var sp={lt:function(r,t){return r<t},lte:function(r,t){return r<=t},gt:function(r,t){return r>t},gte:function(r,t){return r>=t}},vw=function(){function r(t,e){if(!ut(e)){var i="";At(i)}this._opFn=sp[t],this._rvalFloat=Je(e)}return r.prototype.evaluate=function(t){return ut(t)?this._opFn(t,this._rvalFloat):this._opFn(Je(t),this._rvalFloat)},r}(),cw=function(){function r(t,e){var i=t==="desc";this._resultLT=i?1:-1,e==null&&(e=i?"min":"max"),this._incomparable=e==="min"?-1/0:1/0}return r.prototype.evaluate=function(t,e){var i=ut(t)?t:Je(t),n=ut(e)?e:Je(e),a=isNaN(i),o=isNaN(n);if(a&&(i=this._incomparable),o&&(n=this._incomparable),a&&o){var s=B(t),u=B(e);s&&(i=u?t:0),u&&(n=s?e:0)}return i<n?this._resultLT:i>n?-this._resultLT:0},r}(),dw=function(){function r(t,e){this._rval=e,this._isEQ=t,this._rvalTypeof=typeof e,this._rvalFloat=Je(e)}return r.prototype.evaluate=function(t){var e=t===this._rval;if(!e){var i=typeof t;i!==this._rvalTypeof&&(i==="number"||this._rvalTypeof==="number")&&(e=Je(t)===this._rvalFloat)}return this._isEQ?e:!e},r}();function QC(r,t){return r==="eq"||r==="ne"?new dw(r==="eq",t):Ke(sp,r)?new vw(r,t):null}var pw=function(){function r(){}return r.prototype.getRawData=function(){throw new Error("not supported")},r.prototype.getRawDataItem=function(t){throw new Error("not supported")},r.prototype.cloneRawData=function(){},r.prototype.getDimensionInfo=function(t){},r.prototype.cloneAllDimensionInfo=function(){},r.prototype.count=function(){},r.prototype.retrieveValue=function(t,e){},r.prototype.retrieveValueFromItem=function(t,e){},r.prototype.convertValue=function(t,e){return La(t,e)},r}();function gw(r,t){var e=new pw,i=r.data,n=e.sourceFormat=r.sourceFormat,a=r.startIndex,o="";r.seriesLayoutBy!==Se&&At(o);var s=[],u={},l=r.dimensionsDefine;if(l)D(l,function(y,p){var g=y.name,_={index:p,name:g,displayName:y.displayName};if(s.push(_),g!=null){var m="";Ke(u,g)&&At(m),u[g]=_}});else for(var f=0;f<r.dimensionsDetectedCount;f++)s.push({index:f});var h=np(n,Se);t.__isBuiltIn&&(e.getRawDataItem=function(y){return h(i,a,s,y)},e.getRawData=lt(yw,null,r)),e.cloneRawData=lt(_w,null,r);var c=ap(n,Se);e.count=lt(c,null,i,a,s);var v=op(n);e.retrieveValue=function(y,p){var g=h(i,a,s,y);return d(g,p)};var d=e.retrieveValueFromItem=function(y,p){if(y!=null){var g=s[p];if(g)return v(y,p,g.name)}};return e.getDimensionInfo=lt(mw,null,s,u),e.cloneAllDimensionInfo=lt(ww,null,s),e}function yw(r){var t=r.sourceFormat;if(!El(t)){var e="";At(e)}return r.data}function _w(r){var t=r.sourceFormat,e=r.data;if(!El(t)){var i="";At(i)}if(t===Gt){for(var n=[],a=0,o=e.length;a<o;a++)n.push(e[a].slice());return n}else if(t===he){for(var n=[],a=0,o=e.length;a<o;a++)n.push(O({},e[a]));return n}}function mw(r,t,e){if(e!=null){if(ut(e)||!isNaN(e)&&!Ke(t,e))return r[e];if(Ke(t,e))return t[e]}}function ww(r){return Z(r)}var up=X();function Sw(r){r=Z(r);var t=r.type,e="";t||At(e);var i=t.split(":");i.length!==2&&At(e);var n=!1;i[0]==="echarts"&&(t=i[1],n=!0),r.__isBuiltIn=n,up.set(t,r)}function Tw(r,t,e){var i=_t(r),n=i.length,a="";n||At(a);for(var o=0,s=n;o<s;o++){var u=i[o];t=bw(u,t),o!==s-1&&(t.length=Math.max(t.length,1))}return t}function bw(r,t,e,i){var n="";t.length||At(n),F(r)||At(n);var a=r.type,o=up.get(a);o||At(n);var s=Y(t,function(l){return gw(l,o)}),u=_t(o.transform({upstream:s[0],upstreamList:s,config:Z(r.config)}));return Y(u,function(l,f){var h="";F(l)||At(h),l.data||At(h);var c=ep(l.data);El(c)||At(h);var v,d=t[0];if(d&&f===0&&!l.dimensions){var y=d.startIndex;y&&(l.data=d.data.slice(0,y).concat(l.data)),v={seriesLayoutBy:Se,sourceHeader:y,dimensions:d.metaRawOption.dimensions}}else v={seriesLayoutBy:Se,sourceHeader:0,dimensions:l.dimensions};return bu(l.data,v,null)})}function El(r){return r===Gt||r===he}var Do="undefined",xw=typeof Uint32Array===Do?Array:Uint32Array,Cw=typeof Uint16Array===Do?Array:Uint16Array,lp=typeof Int32Array===Do?Array:Int32Array,zh=typeof Float64Array===Do?Array:Float64Array,fp={float:zh,int:lp,ordinal:Array,number:Array,time:zh},xs;function Qr(r){return r>65535?xw:Cw}function Jr(){return[1/0,-1/0]}function Dw(r){var t=r.constructor;return t===Array?r.slice():new t(r)}function Hh(r,t,e,i,n){var a=fp[e||"float"];if(n){var o=r[t],s=o&&o.length;if(s!==i){for(var u=new a(i),l=0;l<s;l++)u[l]=o[l];r[t]=u}}else r[t]=new a(i)}var xu=function(){function r(){this._chunks=[],this._rawExtent=[],this._extent=[],this._count=0,this._rawCount=0,this._calcDimNameToIdx=X()}return r.prototype.initData=function(t,e,i){this._provider=t,this._chunks=[],this._indices=null,this.getRawIndex=this._getRawIdxIdentity;var n=t.getSource(),a=this.defaultDimValueGetter=xs[n.sourceFormat];this._dimValueGetter=i||a,this._rawExtent=[],rp(n),this._dimensions=Y(e,function(o){return{type:o.type,property:o.property}}),this._initDataFromProvider(0,t.count())},r.prototype.getProvider=function(){return this._provider},r.prototype.getSource=function(){return this._provider.getSource()},r.prototype.ensureCalculationDimension=function(t,e){var i=this._calcDimNameToIdx,n=this._dimensions,a=i.get(t);if(a!=null){if(n[a].type===e)return a}else a=n.length;return n[a]={type:e},i.set(t,a),this._chunks[a]=new fp[e||"float"](this._rawCount),this._rawExtent[a]=Jr(),a},r.prototype.collectOrdinalMeta=function(t,e){var i=this._chunks[t],n=this._dimensions[t],a=this._rawExtent,o=n.ordinalOffset||0,s=i.length;o===0&&(a[t]=Jr());for(var u=a[t],l=o;l<s;l++){var f=i[l]=e.parseAndCollect(i[l]);isNaN(f)||(u[0]=Math.min(f,u[0]),u[1]=Math.max(f,u[1]))}n.ordinalMeta=e,n.ordinalOffset=s,n.type="ordinal"},r.prototype.getOrdinalMeta=function(t){var e=this._dimensions[t],i=e.ordinalMeta;return i},r.prototype.getDimensionProperty=function(t){var e=this._dimensions[t];return e&&e.property},r.prototype.appendData=function(t){var e=this._provider,i=this.count();e.appendData(t);var n=e.count();return e.persistent||(n+=i),i<n&&this._initDataFromProvider(i,n,!0),[i,n]},r.prototype.appendValues=function(t,e){for(var i=this._chunks,n=this._dimensions,a=n.length,o=this._rawExtent,s=this.count(),u=s+Math.max(t.length,e||0),l=0;l<a;l++){var f=n[l];Hh(i,l,f.type,u,!0)}for(var h=[],c=s;c<u;c++)for(var v=c-s,d=0;d<a;d++){var f=n[d],y=xs.arrayRows.call(this,t[v]||h,f.property,v,d);i[d][c]=y;var p=o[d];y<p[0]&&(p[0]=y),y>p[1]&&(p[1]=y)}return this._rawCount=this._count=u,{start:s,end:u}},r.prototype._initDataFromProvider=function(t,e,i){for(var n=this._provider,a=this._chunks,o=this._dimensions,s=o.length,u=this._rawExtent,l=Y(o,function(_){return _.property}),f=0;f<s;f++){var h=o[f];u[f]||(u[f]=Jr()),Hh(a,f,h.type,e,i)}if(n.fillStorage)n.fillStorage(t,e,a,u);else for(var c=[],v=t;v<e;v++){c=n.getItem(v,c);for(var d=0;d<s;d++){var y=a[d],p=this._dimValueGetter(c,l[d],v,d);y[v]=p;var g=u[d];p<g[0]&&(g[0]=p),p>g[1]&&(g[1]=p)}}!n.persistent&&n.clean&&n.clean(),this._rawCount=this._count=e,this._extent=[]},r.prototype.count=function(){return this._count},r.prototype.get=function(t,e){if(!(e>=0&&e<this._count))return NaN;var i=this._chunks[t];return i?i[this.getRawIndex(e)]:NaN},r.prototype.getValues=function(t,e){var i=[],n=[];if(e==null){e=t,t=[];for(var a=0;a<this._dimensions.length;a++)n.push(a)}else n=t;for(var a=0,o=n.length;a<o;a++)i.push(this.get(n[a],e));return i},r.prototype.getByRawIndex=function(t,e){if(!(e>=0&&e<this._rawCount))return NaN;var i=this._chunks[t];return i?i[e]:NaN},r.prototype.getSum=function(t){var e=this._chunks[t],i=0;if(e)for(var n=0,a=this.count();n<a;n++){var o=this.get(t,n);isNaN(o)||(i+=o)}return i},r.prototype.getMedian=function(t){var e=[];this.each([t],function(a){isNaN(a)||e.push(a)});var i=e.sort(function(a,o){return a-o}),n=this.count();return n===0?0:n%2===1?i[(n-1)/2]:(i[n/2]+i[n/2-1])/2},r.prototype.indexOfRawIndex=function(t){if(t>=this._rawCount||t<0)return-1;if(!this._indices)return t;var e=this._indices,i=e[t];if(i!=null&&i<this._count&&i===t)return t;for(var n=0,a=this._count-1;n<=a;){var o=(n+a)/2|0;if(e[o]<t)n=o+1;else if(e[o]>t)a=o-1;else return o}return-1},r.prototype.indicesOfNearest=function(t,e,i){var n=this._chunks,a=n[t],o=[];if(!a)return o;i==null&&(i=1/0);for(var s=1/0,u=-1,l=0,f=0,h=this.count();f<h;f++){var c=this.getRawIndex(f),v=e-a[c],d=Math.abs(v);d<=i&&((d<s||d===s&&v>=0&&u<0)&&(s=d,u=v,l=0),v===u&&(o[l++]=f))}return o.length=l,o},r.prototype.getIndices=function(){var t,e=this._indices;if(e){var i=e.constructor,n=this._count;if(i===Array){t=new i(n);for(var a=0;a<n;a++)t[a]=e[a]}else t=new i(e.buffer,0,n)}else{var i=Qr(this._rawCount);t=new i(this.count());for(var a=0;a<t.length;a++)t[a]=a}return t},r.prototype.filter=function(t,e){if(!this._count)return this;for(var i=this.clone(),n=i.count(),a=Qr(i._rawCount),o=new a(n),s=[],u=t.length,l=0,f=t[0],h=i._chunks,c=0;c<n;c++){var v=void 0,d=i.getRawIndex(c);if(u===0)v=e(c);else if(u===1){var y=h[f][d];v=e(y,c)}else{for(var p=0;p<u;p++)s[p]=h[t[p]][d];s[p]=c,v=e.apply(null,s)}v&&(o[l++]=d)}return l<n&&(i._indices=o),i._count=l,i._extent=[],i._updateGetRawIdx(),i},r.prototype.selectRange=function(t){var e=this.clone(),i=e._count;if(!i)return this;var n=ft(t),a=n.length;if(!a)return this;var o=e.count(),s=Qr(e._rawCount),u=new s(o),l=0,f=n[0],h=t[f][0],c=t[f][1],v=e._chunks,d=!1;if(!e._indices){var y=0;if(a===1){for(var p=v[n[0]],g=0;g<i;g++){var _=p[g];(_>=h&&_<=c||isNaN(_))&&(u[l++]=y),y++}d=!0}else if(a===2){for(var p=v[n[0]],m=v[n[1]],w=t[n[1]][0],T=t[n[1]][1],g=0;g<i;g++){var _=p[g],S=m[g];(_>=h&&_<=c||isNaN(_))&&(S>=w&&S<=T||isNaN(S))&&(u[l++]=y),y++}d=!0}}if(!d)if(a===1)for(var g=0;g<o;g++){var b=e.getRawIndex(g),_=v[n[0]][b];(_>=h&&_<=c||isNaN(_))&&(u[l++]=b)}else for(var g=0;g<o;g++){for(var M=!0,b=e.getRawIndex(g),x=0;x<a;x++){var C=n[x],_=v[C][b];(_<t[C][0]||_>t[C][1])&&(M=!1)}M&&(u[l++]=e.getRawIndex(g))}return l<o&&(e._indices=u),e._count=l,e._extent=[],e._updateGetRawIdx(),e},r.prototype.map=function(t,e){var i=this.clone(t);return this._updateDims(i,t,e),i},r.prototype.modify=function(t,e){this._updateDims(this,t,e)},r.prototype._updateDims=function(t,e,i){for(var n=t._chunks,a=[],o=e.length,s=t.count(),u=[],l=t._rawExtent,f=0;f<e.length;f++)l[e[f]]=Jr();for(var h=0;h<s;h++){for(var c=t.getRawIndex(h),v=0;v<o;v++)u[v]=n[e[v]][c];u[o]=h;var d=i&&i.apply(null,u);if(d!=null){typeof d!="object"&&(a[0]=d,d=a);for(var f=0;f<d.length;f++){var y=e[f],p=d[f],g=l[y],_=n[y];_&&(_[c]=p),p<g[0]&&(g[0]=p),p>g[1]&&(g[1]=p)}}}},r.prototype.lttbDownSample=function(t,e){var i=this.clone([t],!0),n=i._chunks,a=n[t],o=this.count(),s=0,u=Math.floor(1/e),l=this.getRawIndex(0),f,h,c,v=new(Qr(this._rawCount))(Math.min((Math.ceil(o/u)+2)*2,o));v[s++]=l;for(var d=1;d<o-1;d+=u){for(var y=Math.min(d+u,o-1),p=Math.min(d+u*2,o),g=(p+y)/2,_=0,m=y;m<p;m++){var w=this.getRawIndex(m),T=a[w];isNaN(T)||(_+=T)}_/=p-y;var S=d,b=Math.min(d+u,o),M=d-1,x=a[l];f=-1,c=S;for(var C=-1,A=0,m=S;m<b;m++){var w=this.getRawIndex(m),T=a[w];if(isNaN(T)){A++,C<0&&(C=w);continue}h=Math.abs((M-g)*(T-x)-(M-m)*(_-x)),h>f&&(f=h,c=w)}A>0&&A<b-S&&(v[s++]=Math.min(C,c),c=Math.max(C,c)),v[s++]=c,l=c}return v[s++]=this.getRawIndex(o-1),i._count=s,i._indices=v,i.getRawIndex=this._getRawIdx,i},r.prototype.minmaxDownSample=function(t,e){for(var i=this.clone([t],!0),n=i._chunks,a=Math.floor(1/e),o=n[t],s=this.count(),u=new(Qr(this._rawCount))(Math.ceil(s/a)*2),l=0,f=0;f<s;f+=a){var h=f,c=o[this.getRawIndex(h)],v=f,d=o[this.getRawIndex(v)],y=a;f+a>s&&(y=s-f);for(var p=0;p<y;p++){var g=this.getRawIndex(f+p),_=o[g];_<c&&(c=_,h=f+p),_>d&&(d=_,v=f+p)}var m=this.getRawIndex(h),w=this.getRawIndex(v);h<v?(u[l++]=m,u[l++]=w):(u[l++]=w,u[l++]=m)}return i._count=l,i._indices=u,i._updateGetRawIdx(),i},r.prototype.downSample=function(t,e,i,n){for(var a=this.clone([t],!0),o=a._chunks,s=[],u=Math.floor(1/e),l=o[t],f=this.count(),h=a._rawExtent[t]=Jr(),c=new(Qr(this._rawCount))(Math.ceil(f/u)),v=0,d=0;d<f;d+=u){u>f-d&&(u=f-d,s.length=u);for(var y=0;y<u;y++){var p=this.getRawIndex(d+y);s[y]=l[p]}var g=i(s),_=this.getRawIndex(Math.min(d+n(s,g)||0,f-1));l[_]=g,g<h[0]&&(h[0]=g),g>h[1]&&(h[1]=g),c[v++]=_}return a._count=v,a._indices=c,a._updateGetRawIdx(),a},r.prototype.each=function(t,e){if(this._count)for(var i=t.length,n=this._chunks,a=0,o=this.count();a<o;a++){var s=this.getRawIndex(a);switch(i){case 0:e(a);break;case 1:e(n[t[0]][s],a);break;case 2:e(n[t[0]][s],n[t[1]][s],a);break;default:for(var u=0,l=[];u<i;u++)l[u]=n[t[u]][s];l[u]=a,e.apply(null,l)}}},r.prototype.getDataExtent=function(t){var e=this._chunks[t],i=Jr();if(!e)return i;var n=this.count(),a=!this._indices,o;if(a)return this._rawExtent[t].slice();if(o=this._extent[t],o)return o.slice();o=i;for(var s=o[0],u=o[1],l=0;l<n;l++){var f=this.getRawIndex(l),h=e[f];h<s&&(s=h),h>u&&(u=h)}return o=[s,u],this._extent[t]=o,o},r.prototype.getRawDataItem=function(t){var e=this.getRawIndex(t);if(this._provider.persistent)return this._provider.getItem(e);for(var i=[],n=this._chunks,a=0;a<n.length;a++)i.push(n[a][e]);return i},r.prototype.clone=function(t,e){var i=new r,n=this._chunks,a=t&&Ze(t,function(s,u){return s[u]=!0,s},{});if(a)for(var o=0;o<n.length;o++)i._chunks[o]=a[o]?Dw(n[o]):n[o];else i._chunks=n;return this._copyCommonProps(i),e||(i._indices=this._cloneIndices()),i._updateGetRawIdx(),i},r.prototype._copyCommonProps=function(t){t._count=this._count,t._rawCount=this._rawCount,t._provider=this._provider,t._dimensions=this._dimensions,t._extent=Z(this._extent),t._rawExtent=Z(this._rawExtent)},r.prototype._cloneIndices=function(){if(this._indices){var t=this._indices.constructor,e=void 0;if(t===Array){var i=this._indices.length;e=new t(i);for(var n=0;n<i;n++)e[n]=this._indices[n]}else e=new t(this._indices);return e}return null},r.prototype._getRawIdxIdentity=function(t){return t},r.prototype._getRawIdx=function(t){return t<this._count&&t>=0?this._indices[t]:-1},r.prototype._updateGetRawIdx=function(){this.getRawIndex=this._indices?this._getRawIdx:this._getRawIdxIdentity},r.internalField=function(){function t(e,i,n,a){return La(e[a],this._dimensions[a])}xs={arrayRows:t,objectRows:function(e,i,n,a){return La(e[i],this._dimensions[a])},keyedColumns:t,original:function(e,i,n,a){var o=e&&(e.value==null?e:e.value);return La(o instanceof Array?o[a]:o,this._dimensions[a])},typedArray:function(e,i,n,a){return e[a]}}}(),r}(),hp=function(){function r(t){this._sourceList=[],this._storeList=[],this._upstreamSignList=[],this._versionSignBase=0,this._dirty=!0,this._sourceHost=t}return r.prototype.dirty=function(){this._setLocalSource([],[]),this._storeList=[],this._dirty=!0},r.prototype._setLocalSource=function(t,e){this._sourceList=t,this._upstreamSignList=e,this._versionSignBase++,this._versionSignBase>9e10&&(this._versionSignBase=0)},r.prototype._getVersionSign=function(){return this._sourceHost.uid+"_"+this._versionSignBase},r.prototype.prepareSource=function(){this._isDirty()&&(this._createSource(),this._dirty=!1)},r.prototype._createSource=function(){this._setLocalSource([],[]);var t=this._sourceHost,e=this._getUpstreamSourceManagers(),i=!!e.length,n,a;if(ta(t)){var o=t,s=void 0,u=void 0,l=void 0;if(i){var f=e[0];f.prepareSource(),l=f.getSource(),s=l.data,u=l.sourceFormat,a=[f._getVersionSign()]}else s=o.get("data",!0),u=Ht(s)?qe:xe,a=[];var h=this._getSourceMetaRawOption()||{},c=l&&l.metaRawOption||{},v=W(h.seriesLayoutBy,c.seriesLayoutBy)||null,d=W(h.sourceHeader,c.sourceHeader),y=W(h.dimensions,c.dimensions),p=v!==c.seriesLayoutBy||!!d!=!!c.sourceHeader||y;n=p?[bu(s,{seriesLayoutBy:v,sourceHeader:d,dimensions:y},u)]:[]}else{var g=t;if(i){var _=this._applyTransform(e);n=_.sourceList,a=_.upstreamSignList}else{var m=g.get("source",!0);n=[bu(m,this._getSourceMetaRawOption(),null)],a=[]}}this._setLocalSource(n,a)},r.prototype._applyTransform=function(t){var e=this._sourceHost,i=e.get("transform",!0),n=e.get("fromTransformResult",!0);if(n!=null){var a="";t.length!==1&&Vh(a)}var o,s=[],u=[];return D(t,function(l){l.prepareSource();var f=l.getSource(n||0),h="";n!=null&&!f&&Vh(h),s.push(f),u.push(l._getVersionSign())}),i?o=Tw(i,s,{datasetIndex:e.componentIndex}):n!=null&&(o=[rw(s[0])]),{sourceList:o,upstreamSignList:u}},r.prototype._isDirty=function(){if(this._dirty)return!0;for(var t=this._getUpstreamSourceManagers(),e=0;e<t.length;e++){var i=t[e];if(i._isDirty()||this._upstreamSignList[e]!==i._getVersionSign())return!0}},r.prototype.getSource=function(t){t=t||0;var e=this._sourceList[t];if(!e){var i=this._getUpstreamSourceManagers();return i[0]&&i[0].getSource(t)}return e},r.prototype.getSharedDataStore=function(t){var e=t.makeStoreSchema();return this._innerGetDataStore(e.dimensions,t.source,e.hash)},r.prototype._innerGetDataStore=function(t,e,i){var n=0,a=this._storeList,o=a[n];o||(o=a[n]={});var s=o[i];if(!s){var u=this._getUpstreamSourceManagers()[0];ta(this._sourceHost)&&u?s=u._innerGetDataStore(t,e,i):(s=new xu,s.initData(new ip(e,t.length),t)),o[i]=s}return s},r.prototype._getUpstreamSourceManagers=function(){var t=this._sourceHost;if(ta(t)){var e=Al(t);return e?[e.getSourceManager()]:[]}else return Y(R1(t),function(i){return i.getSourceManager()})},r.prototype._getSourceMetaRawOption=function(){var t=this._sourceHost,e,i,n;if(ta(t))e=t.get("seriesLayoutBy",!0),i=t.get("sourceHeader",!0),n=t.get("dimensions",!0);else if(!this._getUpstreamSourceManagers().length){var a=t;e=a.get("seriesLayoutBy",!0),i=a.get("sourceHeader",!0),n=a.get("dimensions",!0)}return{seriesLayoutBy:e,sourceHeader:i,dimensions:n}},r}();function Gh(r){var t=r.option.transform;t&&ka(r.option.transform)}function ta(r){return r.mainType==="series"}function Vh(r){throw new Error(r)}var Mw="line-height:1";function vp(r){var t=r.lineHeight;return t==null?Mw:"line-height:"+Ot(t+"")+"px"}function cp(r,t){var e=r.color||"#6e7079",i=r.fontSize||12,n=r.fontWeight||"400",a=r.color||"#464646",o=r.fontSize||14,s=r.fontWeight||"900";return t==="html"?{nameStyle:"font-size:"+Ot(i+"")+"px;color:"+Ot(e)+";font-weight:"+Ot(n+""),valueStyle:"font-size:"+Ot(o+"")+"px;color:"+Ot(a)+";font-weight:"+Ot(s+"")}:{nameStyle:{fontSize:i,fill:e,fontWeight:n},valueStyle:{fontSize:o,fill:a,fontWeight:s}}}var Aw=[0,10,20,30],Pw=["",`
`,`

`,`


`];function Sn(r,t){return t.type=r,t}function Cu(r){return r.type==="section"}function dp(r){return Cu(r)?Lw:Iw}function pp(r){if(Cu(r)){var t=0,e=r.blocks.length,i=e>1||e>0&&!r.noHeader;return D(r.blocks,function(n){var a=pp(n);a>=t&&(t=a+ +(i&&(!a||Cu(n)&&!n.noHeader)))}),t}return 0}function Lw(r,t,e,i){var n=t.noHeader,a=Rw(pp(t)),o=[],s=t.blocks||[];Te(!s||k(s)),s=s||[];var u=r.orderMode;if(t.sortBlocks&&u){s=s.slice();var l={valueAsc:"asc",valueDesc:"desc"};if(Ke(l,u)){var f=new cw(l[u],null);s.sort(function(y,p){return f.evaluate(y.sortParam,p.sortParam)})}else u==="seriesDesc"&&s.reverse()}D(s,function(y,p){var g=t.valueFormatter,_=dp(y)(g?O(O({},r),{valueFormatter:g}):r,y,p>0?a.html:0,i);_!=null&&o.push(_)});var h=r.renderMode==="richText"?o.join(a.richText):Du(i,o.join(""),n?e:a.html);if(n)return h;var c=Su(t.header,"ordinal",r.useUTC),v=cp(i,r.renderMode).nameStyle,d=vp(i);return r.renderMode==="richText"?gp(r,c,v)+a.richText+h:Du(i,'<div style="'+v+";"+d+';">'+Ot(c)+"</div>"+h,e)}function Iw(r,t,e,i){var n=r.renderMode,a=t.noName,o=t.noValue,s=!t.markerType,u=t.name,l=r.useUTC,f=t.valueFormatter||r.valueFormatter||function(w){return w=k(w)?w:[w],Y(w,function(T,S){return Su(T,k(v)?v[S]:v,l)})};if(!(a&&o)){var h=s?"":r.markupStyleCreator.makeTooltipMarker(t.markerType,t.markerColor||"#333",n),c=a?"":Su(u,"ordinal",l),v=t.valueType,d=o?[]:f(t.value,t.dataIndex),y=!s||!a,p=!s&&a,g=cp(i,n),_=g.nameStyle,m=g.valueStyle;return n==="richText"?(s?"":h)+(a?"":gp(r,c,_))+(o?"":kw(r,d,y,p,m)):Du(i,(s?"":h)+(a?"":Ew(c,!s,_))+(o?"":Ow(d,y,p,m)),e)}}function Wh(r,t,e,i,n,a){if(r){var o=dp(r),s={useUTC:n,renderMode:e,orderMode:i,markupStyleCreator:t,valueFormatter:r.valueFormatter};return o(s,r,0,a)}}function Rw(r){return{html:Aw[r],richText:Pw[r]}}function Du(r,t,e){var i='<div style="clear:both"></div>',n="margin: "+e+"px 0 0",a=vp(r);return'<div style="'+n+";"+a+';">'+t+i+"</div>"}function Ew(r,t,e){var i=t?"margin-left:2px":"";return'<span style="'+e+";"+i+'">'+Ot(r)+"</span>"}function Ow(r,t,e,i){var n=e?"10px":"20px",a=t?"float:right;margin-left:"+n:"";return r=k(r)?r:[r],'<span style="'+a+";"+i+'">'+Y(r,function(o){return Ot(o)}).join("&nbsp;&nbsp;")+"</span>"}function gp(r,t,e){return r.markupStyleCreator.wrapRichTextStyle(t,e)}function kw(r,t,e,i,n){var a=[n],o=i?10:20;return e&&a.push({padding:[0,0,0,o],align:"right"}),r.markupStyleCreator.wrapRichTextStyle(k(t)?t.join("  "):t,a)}function Bw(r,t){var e=r.getData().getItemVisual(t,"style"),i=e[r.visualDrawType];return _n(i)}function yp(r,t){var e=r.get("padding");return e??(t==="richText"?[8,10]:10)}var Cs=function(){function r(){this.richTextStyles={},this._nextStyleNameId=Hc()}return r.prototype._generateStyleName=function(){return"__EC_aUTo_"+this._nextStyleNameId++},r.prototype.makeTooltipMarker=function(t,e,i){var n=i==="richText"?this._generateStyleName():null,a=x1({color:e,type:t,renderMode:i,markerId:n});return B(a)?a:(this.richTextStyles[n]=a.style,a.content)},r.prototype.wrapRichTextStyle=function(t,e){var i={};k(e)?D(e,function(a){return O(i,a)}):O(i,e);var n=this._generateStyleName();return this.richTextStyles[n]=i,"{"+n+"|"+t+"}"},r}();function Nw(r){var t=r.series,e=r.dataIndex,i=r.multipleSeries,n=t.getData(),a=n.mapDimensionsAll("defaultedTooltip"),o=a.length,s=t.getRawValue(e),u=k(s),l=Bw(t,e),f,h,c,v;if(o>1||u&&!o){var d=Fw(s,t,e,a,l);f=d.inlineValues,h=d.inlineValueTypes,c=d.blocks,v=d.inlineValues[0]}else if(o){var y=n.getDimensionInfo(a[0]);v=f=Za(n,e,a[0]),h=y.type}else v=f=u?s[0]:s;var p=rl(t),g=p&&t.name||"",_=n.getName(e),m=i?g:_;return Sn("section",{header:g,noHeader:i||!p,sortParam:v,blocks:[Sn("nameValue",{markerType:"item",markerColor:l,name:m,noName:!ye(m),value:f,valueType:h,dataIndex:e})].concat(c||[])})}function Fw(r,t,e,i,n){var a=t.getData(),o=Ze(r,function(h,c,v){var d=a.getDimensionInfo(v);return h=h||d&&d.tooltip!==!1&&d.displayName!=null},!1),s=[],u=[],l=[];i.length?D(i,function(h){f(Za(a,e,h),h)}):D(r,f);function f(h,c){var v=a.getDimensionInfo(c);!v||v.otherDims.tooltip===!1||(o?l.push(Sn("nameValue",{markerType:"subItem",markerColor:n,name:v.displayName,value:h,valueType:v.type})):(s.push(h),u.push(v.type)))}return{inlineValues:s,inlineValueTypes:u,blocks:l}}var ze=mt();function ea(r,t){return r.getName(t)||r.getId(t)}var zw="__universalTransitionEnabled",Fr=function(r){N(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e._selectedDataIndicesMap={},e}return t.prototype.init=function(e,i,n){this.seriesIndex=this.componentIndex,this.dataTask=sn({count:Gw,reset:Vw}),this.dataTask.context={model:this},this.mergeDefaultAndTheme(e,n);var a=ze(this).sourceManager=new hp(this);a.prepareSource();var o=this.getInitialData(e,n);Yh(o,this),this.dataTask.context.data=o,ze(this).dataBeforeProcessed=o,Uh(this),this._initSelectedMapFromData(o)},t.prototype.mergeDefaultAndTheme=function(e,i){var n=qa(this),a=n?Ml(e):{},o=this.subType;tt.hasClass(o)&&(o+="Series"),st(e,i.getTheme().get(this.subType)),st(e,this.getDefaultOption()),Af(e,"label",["show"]),this.fillDataTextStyle(e.data),n&&mn(e,a,n)},t.prototype.mergeOption=function(e,i){e=st(this.option,e,!0),this.fillDataTextStyle(e.data);var n=qa(this);n&&mn(this.option,e,n);var a=ze(this).sourceManager;a.dirty(),a.prepareSource();var o=this.getInitialData(e,i);Yh(o,this),this.dataTask.dirty(),this.dataTask.context.data=o,ze(this).dataBeforeProcessed=o,Uh(this),this._initSelectedMapFromData(o)},t.prototype.fillDataTextStyle=function(e){if(e&&!Ht(e))for(var i=["show"],n=0;n<e.length;n++)e[n]&&e[n].label&&Af(e[n],"label",i)},t.prototype.getInitialData=function(e,i){},t.prototype.appendData=function(e){var i=this.getRawData();i.appendData(e.data)},t.prototype.getData=function(e){var i=Mu(this);if(i){var n=i.context.data;return e==null||!n.getLinkedData?n:n.getLinkedData(e)}else return ze(this).data},t.prototype.getAllData=function(){var e=this.getData();return e&&e.getLinkedDataAll?e.getLinkedDataAll():[{data:e}]},t.prototype.setData=function(e){var i=Mu(this);if(i){var n=i.context;n.outputData=e,i!==this.dataTask&&(n.data=e)}ze(this).data=e},t.prototype.getEncode=function(){var e=this.get("encode",!0);if(e)return X(e)},t.prototype.getSourceManager=function(){return ze(this).sourceManager},t.prototype.getSource=function(){return this.getSourceManager().getSource()},t.prototype.getRawData=function(){return ze(this).dataBeforeProcessed},t.prototype.getColorBy=function(){var e=this.get("colorBy");return e||"series"},t.prototype.isColorBySeries=function(){return this.getColorBy()==="series"},t.prototype.getBaseAxis=function(){var e=this.coordinateSystem;return e&&e.getBaseAxis&&e.getBaseAxis()},t.prototype.formatTooltip=function(e,i,n){return Nw({series:this,dataIndex:e,multipleSeries:i})},t.prototype.isAnimationEnabled=function(){var e=this.ecModel;if(H.node&&!(e&&e.ssr))return!1;var i=this.getShallow("animation");return i&&this.getData().count()>this.getShallow("animationThreshold")&&(i=!1),!!i},t.prototype.restoreData=function(){this.dataTask.dirty()},t.prototype.getColorFromPalette=function(e,i,n){var a=this.ecModel,o=Pl.prototype.getColorFromPalette.call(this,e,i,n);return o||(o=a.getColorFromPalette(e,i,n)),o},t.prototype.coordDimToDataDim=function(e){return this.getRawData().mapDimensionsAll(e)},t.prototype.getProgressive=function(){return this.get("progressive")},t.prototype.getProgressiveThreshold=function(){return this.get("progressiveThreshold")},t.prototype.select=function(e,i){this._innerSelect(this.getData(i),e)},t.prototype.unselect=function(e,i){var n=this.option.selectedMap;if(n){var a=this.option.selectedMode,o=this.getData(i);if(a==="series"||n==="all"){this.option.selectedMap={},this._selectedDataIndicesMap={};return}for(var s=0;s<e.length;s++){var u=e[s],l=ea(o,u);n[l]=!1,this._selectedDataIndicesMap[l]=-1}}},t.prototype.toggleSelect=function(e,i){for(var n=[],a=0;a<e.length;a++)n[0]=e[a],this.isSelected(e[a],i)?this.unselect(n,i):this.select(n,i)},t.prototype.getSelectedDataIndices=function(){if(this.option.selectedMap==="all")return[].slice.call(this.getData().getIndices());for(var e=this._selectedDataIndicesMap,i=ft(e),n=[],a=0;a<i.length;a++){var o=e[i[a]];o>=0&&n.push(o)}return n},t.prototype.isSelected=function(e,i){var n=this.option.selectedMap;if(!n)return!1;var a=this.getData(i);return(n==="all"||n[ea(a,e)])&&!a.getItemModel(e).get(["select","disabled"])},t.prototype.isUniversalTransitionEnabled=function(){if(this[zw])return!0;var e=this.option.universalTransition;return e?e===!0?!0:e&&e.enabled:!1},t.prototype._innerSelect=function(e,i){var n,a,o=this.option,s=o.selectedMode,u=i.length;if(!(!s||!u)){if(s==="series")o.selectedMap="all";else if(s==="multiple"){F(o.selectedMap)||(o.selectedMap={});for(var l=o.selectedMap,f=0;f<u;f++){var h=i[f],c=ea(e,h);l[c]=!0,this._selectedDataIndicesMap[c]=e.getRawIndex(h)}}else if(s==="single"||s===!0){var v=i[u-1],c=ea(e,v);o.selectedMap=(n={},n[c]=!0,n),this._selectedDataIndicesMap=(a={},a[c]=e.getRawIndex(v),a)}}},t.prototype._initSelectedMapFromData=function(e){if(!this.option.selectedMap){var i=[];e.hasItemOption&&e.each(function(n){var a=e.getRawDataItem(n);a&&a.selected&&i.push(n)}),i.length>0&&this._innerSelect(e,i)}},t.registerClass=function(e){return tt.registerClass(e)},t.protoInitialize=function(){var e=t.prototype;e.type="series.__base__",e.seriesIndex=0,e.ignoreStyleOnData=!1,e.hasSymbolVisual=!1,e.defaultSymbol="circle",e.visualStyleAccessPath="itemStyle",e.visualDrawType="fill"}(),t}(tt);Ee(Fr,lw);Ee(Fr,Pl);$c(Fr,tt);function Uh(r){var t=r.name;rl(r)||(r.name=Hw(r)||t)}function Hw(r){var t=r.getRawData(),e=t.mapDimensionsAll("seriesName"),i=[];return D(e,function(n){var a=t.getDimensionInfo(n);a.displayName&&i.push(a.displayName)}),i.join(" ")}function Gw(r){return r.model.getRawData().count()}function Vw(r){var t=r.model;return t.setData(t.getRawData().cloneShallow()),Ww}function Ww(r,t){t.outputData&&r.end>t.outputData.count()&&t.model.getRawData().cloneShallow(t.outputData)}function Yh(r,t){D(iy(r.CHANGABLE_METHODS,r.DOWNSAMPLE_METHODS),function(e){r.wrapMethod(e,yt(Uw,t))})}function Uw(r,t){var e=Mu(r);return e&&e.setOutputEnd((t||this).count()),t}function Mu(r){var t=(r.ecModel||{}).scheduler,e=t&&t.getPipeline(r.uid);if(e){var i=e.currentTask;if(i){var n=i.agentStubMap;n&&(i=n.get(r.uid))}return i}}var le=function(){function r(){this.group=new ue,this.uid=mo("viewComponent")}return r.prototype.init=function(t,e){},r.prototype.render=function(t,e,i,n){},r.prototype.dispose=function(t,e){},r.prototype.updateView=function(t,e,i,n){},r.prototype.updateLayout=function(t,e,i,n){},r.prototype.updateVisual=function(t,e,i,n){},r.prototype.toggleBlurSeries=function(t,e,i){},r.prototype.eachRendered=function(t){var e=this.group;e&&e.traverse(t)},r}();nl(le);uo(le);function _p(){var r=mt();return function(t){var e=r(t),i=t.pipelineContext,n=!!e.large,a=!!e.progressiveRender,o=e.large=!!(i&&i.large),s=e.progressiveRender=!!(i&&i.progressiveRender);return(n!==o||a!==s)&&"reset"}}var mp=mt(),Yw=_p(),kr=function(){function r(){this.group=new ue,this.uid=mo("viewChart"),this.renderTask=sn({plan:Xw,reset:$w}),this.renderTask.context={view:this}}return r.prototype.init=function(t,e){},r.prototype.render=function(t,e,i,n){},r.prototype.highlight=function(t,e,i,n){var a=t.getData(n&&n.dataType);a&&$h(a,n,"emphasis")},r.prototype.downplay=function(t,e,i,n){var a=t.getData(n&&n.dataType);a&&$h(a,n,"normal")},r.prototype.remove=function(t,e){this.group.removeAll()},r.prototype.dispose=function(t,e){},r.prototype.updateView=function(t,e,i,n){this.render(t,e,i,n)},r.prototype.updateLayout=function(t,e,i,n){this.render(t,e,i,n)},r.prototype.updateVisual=function(t,e,i,n){this.render(t,e,i,n)},r.prototype.eachRendered=function(t){Dd(this.group,t)},r.markUpdateMethod=function(t,e){mp(t).updateMethod=e},r.protoInitialize=function(){var t=r.prototype;t.type="chart"}(),r}();function Xh(r,t,e){r&&gu(r)&&(t==="emphasis"?hu:vu)(r,e)}function $h(r,t,e){var i=Mn(r,t),n=t&&t.highlightKey!=null?sm(t.highlightKey):null;i!=null?D(_t(i),function(a){Xh(r.getItemGraphicEl(a),e,n)}):r.eachItemGraphicEl(function(a){Xh(a,e,n)})}nl(kr);uo(kr);function Xw(r){return Yw(r.model)}function $w(r){var t=r.model,e=r.ecModel,i=r.api,n=r.payload,a=t.pipelineContext.progressiveRender,o=r.view,s=n&&mp(n).updateMethod,u=a?"incrementalPrepareRender":s&&o[s]?s:"render";return u!=="render"&&o[u](t,e,i,n),qw[u]}var qw={incrementalPrepareRender:{progress:function(r,t){t.view.incrementalRender(r,t.model,t.ecModel,t.api,t.payload)}},render:{forceFirstProgress:!0,progress:function(r,t){t.view.render(t.model,t.ecModel,t.api,t.payload)}}},Ka="\0__throttleOriginMethod",qh="\0__throttleRate",Zh="\0__throttleType";function Ol(r,t,e){var i,n=0,a=0,o=null,s,u,l,f;t=t||0;function h(){a=new Date().getTime(),o=null,r.apply(u,l||[])}var c=function(){for(var v=[],d=0;d<arguments.length;d++)v[d]=arguments[d];i=new Date().getTime(),u=this,l=v;var y=f||t,p=f||e;f=null,s=i-(p?n:a)-y,clearTimeout(o),p?o=setTimeout(h,y):s>=0?h():o=setTimeout(h,-s),n=i};return c.clear=function(){o&&(clearTimeout(o),o=null)},c.debounceNextCall=function(v){f=v},c}function wp(r,t,e,i){var n=r[t];if(n){var a=n[Ka]||n,o=n[Zh],s=n[qh];if(s!==e||o!==i){if(e==null||!i)return r[t]=a;n=r[t]=Ol(a,e,i==="debounce"),n[Ka]=a,n[Zh]=i,n[qh]=e}return n}}function Au(r,t){var e=r[t];e&&e[Ka]&&(e.clear&&e.clear(),r[t]=e[Ka])}var Kh=mt(),Qh={itemStyle:gn(Pd,!0),lineStyle:gn(Ad,!0)},Zw={lineStyle:"stroke",itemStyle:"fill"};function Sp(r,t){var e=r.visualStyleMapper||Qh[t];return e||(console.warn("Unknown style type '"+t+"'."),Qh.itemStyle)}function Tp(r,t){var e=r.visualDrawType||Zw[t];return e||(console.warn("Unknown style type '"+t+"'."),"fill")}var Kw={createOnAllSeries:!0,performRawSeries:!0,reset:function(r,t){var e=r.getData(),i=r.visualStyleAccessPath||"itemStyle",n=r.getModel(i),a=Sp(r,i),o=a(n),s=n.getShallow("decal");s&&(e.setVisual("decal",s),s.dirty=!0);var u=Tp(r,i),l=o[u],f=K(l)?l:null,h=o.fill==="auto"||o.stroke==="auto";if(!o[u]||f||h){var c=r.getColorFromPalette(r.name,null,t.getSeriesCount());o[u]||(o[u]=c,e.setVisual("colorFromPalette",!0)),o.fill=o.fill==="auto"||K(o.fill)?c:o.fill,o.stroke=o.stroke==="auto"||K(o.stroke)?c:o.stroke}if(e.setVisual("style",o),e.setVisual("drawType",u),!t.isSeriesFiltered(r)&&f)return e.setVisual("colorFromPalette",!1),{dataEach:function(v,d){var y=r.getDataParams(d),p=O({},o);p[u]=f(y),v.setItemVisual(d,"style",p)}}}},Oi=new ht,Qw={createOnAllSeries:!0,performRawSeries:!0,reset:function(r,t){if(!(r.ignoreStyleOnData||t.isSeriesFiltered(r))){var e=r.getData(),i=r.visualStyleAccessPath||"itemStyle",n=Sp(r,i),a=e.getVisual("drawType");return{dataEach:e.hasItemOption?function(o,s){var u=o.getRawDataItem(s);if(u&&u[i]){Oi.option=u[i];var l=n(Oi),f=o.ensureUniqueItemVisual(s,"style");O(f,l),Oi.option.decal&&(o.setItemVisual(s,"decal",Oi.option.decal),Oi.option.decal.dirty=!0),a in l&&o.setItemVisual(s,"colorFromPalette",!1)}}:null}}}},Jw={performRawSeries:!0,overallReset:function(r){var t=X();r.eachSeries(function(e){var i=e.getColorBy();if(!e.isColorBySeries()){var n=e.type+"-"+i,a=t.get(n);a||(a={},t.set(n,a)),Kh(e).scope=a}}),r.eachSeries(function(e){if(!(e.isColorBySeries()||r.isSeriesFiltered(e))){var i=e.getRawData(),n={},a=e.getData(),o=Kh(e).scope,s=e.visualStyleAccessPath||"itemStyle",u=Tp(e,s);a.each(function(l){var f=a.getRawIndex(l);n[f]=l}),i.each(function(l){var f=n[l],h=a.getItemVisual(f,"colorFromPalette");if(h){var c=a.ensureUniqueItemVisual(f,"style"),v=i.getName(l)||l+"",d=i.count();c[u]=e.getColorFromPalette(v,o,d)}})}})}},ra=Math.PI;function jw(r,t){t=t||{},ot(t,{text:"loading",textColor:"#000",fontSize:12,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif",maskColor:"rgba(255, 255, 255, 0.8)",showSpinner:!0,color:"#5470c6",spinnerRadius:10,lineWidth:5,zlevel:0});var e=new ue,i=new Pt({style:{fill:t.maskColor},zlevel:t.zlevel,z:1e4});e.add(i);var n=new Lt({style:{text:t.text,fill:t.textColor,fontSize:t.fontSize,fontWeight:t.fontWeight,fontStyle:t.fontStyle,fontFamily:t.fontFamily},zlevel:t.zlevel,z:10001}),a=new Pt({style:{fill:"none"},textContent:n,textConfig:{position:"right",distance:10},zlevel:t.zlevel,z:10001});e.add(a);var o;return t.showSpinner&&(o=new go({shape:{startAngle:-ra/2,endAngle:-ra/2+.1,r:t.spinnerRadius},style:{stroke:t.color,lineCap:"round",lineWidth:t.lineWidth},zlevel:t.zlevel,z:10001}),o.animateShape(!0).when(1e3,{endAngle:ra*3/2}).start("circularInOut"),o.animateShape(!0).when(1e3,{startAngle:ra*3/2}).delay(300).start("circularInOut"),e.add(o)),e.resize=function(){var s=n.getBoundingRect().width,u=t.showSpinner?t.spinnerRadius:0,l=(r.getWidth()-u*2-(t.showSpinner&&s?10:0)-s)/2-(t.showSpinner&&s?0:5+s/2)+(t.showSpinner?0:s/2)+(s?0:u),f=r.getHeight()/2;t.showSpinner&&o.setShape({cx:l,cy:f}),a.setShape({x:l-u,y:f-u,width:u*2,height:u*2}),i.setShape({x:0,y:0,width:r.getWidth(),height:r.getHeight()})},e.resize(),e}var bp=function(){function r(t,e,i,n){this._stageTaskMap=X(),this.ecInstance=t,this.api=e,i=this._dataProcessorHandlers=i.slice(),n=this._visualHandlers=n.slice(),this._allHandlers=i.concat(n)}return r.prototype.restoreData=function(t,e){t.restoreData(e),this._stageTaskMap.each(function(i){var n=i.overallTask;n&&n.dirty()})},r.prototype.getPerformArgs=function(t,e){if(t.__pipeline){var i=this._pipelineMap.get(t.__pipeline.id),n=i.context,a=!e&&i.progressiveEnabled&&(!n||n.progressiveRender)&&t.__idxInPipeline>i.blockIndex,o=a?i.step:null,s=n&&n.modDataCount,u=s!=null?Math.ceil(s/o):null;return{step:o,modBy:u,modDataCount:s}}},r.prototype.getPipeline=function(t){return this._pipelineMap.get(t)},r.prototype.updateStreamModes=function(t,e){var i=this._pipelineMap.get(t.uid),n=t.getData(),a=n.count(),o=i.progressiveEnabled&&e.incrementalPrepareRender&&a>=i.threshold,s=t.get("large")&&a>=t.get("largeThreshold"),u=t.get("progressiveChunkMode")==="mod"?a:null;t.pipelineContext=i.context={progressiveRender:o,modDataCount:u,large:s}},r.prototype.restorePipelines=function(t){var e=this,i=e._pipelineMap=X();t.eachSeries(function(n){var a=n.getProgressive(),o=n.uid;i.set(o,{id:o,head:null,tail:null,threshold:n.getProgressiveThreshold(),progressiveEnabled:a&&!(n.preventIncremental&&n.preventIncremental()),blockIndex:-1,step:Math.round(a||700),count:0}),e._pipe(n,n.dataTask)})},r.prototype.prepareStageTasks=function(){var t=this._stageTaskMap,e=this.api.getModel(),i=this.api;D(this._allHandlers,function(n){var a=t.get(n.uid)||t.set(n.uid,{}),o="";Te(!(n.reset&&n.overallReset),o),n.reset&&this._createSeriesStageTask(n,a,e,i),n.overallReset&&this._createOverallStageTask(n,a,e,i)},this)},r.prototype.prepareView=function(t,e,i,n){var a=t.renderTask,o=a.context;o.model=e,o.ecModel=i,o.api=n,a.__block=!t.incrementalPrepareRender,this._pipe(e,a)},r.prototype.performDataProcessorTasks=function(t,e){this._performStageTasks(this._dataProcessorHandlers,t,e,{block:!0})},r.prototype.performVisualTasks=function(t,e,i){this._performStageTasks(this._visualHandlers,t,e,i)},r.prototype._performStageTasks=function(t,e,i,n){n=n||{};var a=!1,o=this;D(t,function(u,l){if(!(n.visualType&&n.visualType!==u.visualType)){var f=o._stageTaskMap.get(u.uid),h=f.seriesTaskMap,c=f.overallTask;if(c){var v,d=c.agentStubMap;d.each(function(p){s(n,p)&&(p.dirty(),v=!0)}),v&&c.dirty(),o.updatePayload(c,i);var y=o.getPerformArgs(c,n.block);d.each(function(p){p.perform(y)}),c.perform(y)&&(a=!0)}else h&&h.each(function(p,g){s(n,p)&&p.dirty();var _=o.getPerformArgs(p,n.block);_.skip=!u.performRawSeries&&e.isSeriesFiltered(p.context.model),o.updatePayload(p,i),p.perform(_)&&(a=!0)})}});function s(u,l){return u.setDirty&&(!u.dirtyMap||u.dirtyMap.get(l.__pipeline.id))}this.unfinished=a||this.unfinished},r.prototype.performSeriesTasks=function(t){var e;t.eachSeries(function(i){e=i.dataTask.perform()||e}),this.unfinished=e||this.unfinished},r.prototype.plan=function(){this._pipelineMap.each(function(t){var e=t.tail;do{if(e.__block){t.blockIndex=e.__idxInPipeline;break}e=e.getUpstream()}while(e)})},r.prototype.updatePayload=function(t,e){e!=="remain"&&(t.context.payload=e)},r.prototype._createSeriesStageTask=function(t,e,i,n){var a=this,o=e.seriesTaskMap,s=e.seriesTaskMap=X(),u=t.seriesType,l=t.getTargetSeries;t.createOnAllSeries?i.eachRawSeries(f):u?i.eachRawSeriesByType(u,f):l&&l(i,n).each(f);function f(h){var c=h.uid,v=s.set(c,o&&o.get(c)||sn({plan:nS,reset:aS,count:sS}));v.context={model:h,ecModel:i,api:n,useClearVisual:t.isVisual&&!t.isLayout,plan:t.plan,reset:t.reset,scheduler:a},a._pipe(h,v)}},r.prototype._createOverallStageTask=function(t,e,i,n){var a=this,o=e.overallTask=e.overallTask||sn({reset:tS});o.context={ecModel:i,api:n,overallReset:t.overallReset,scheduler:a};var s=o.agentStubMap,u=o.agentStubMap=X(),l=t.seriesType,f=t.getTargetSeries,h=!0,c=!1,v="";Te(!t.createOnAllSeries,v),l?i.eachRawSeriesByType(l,d):f?f(i,n).each(d):(h=!1,D(i.getSeries(),d));function d(y){var p=y.uid,g=u.set(p,s&&s.get(p)||(c=!0,sn({reset:eS,onDirty:iS})));g.context={model:y,overallProgress:h},g.agent=o,g.__block=h,a._pipe(y,g)}c&&o.dirty()},r.prototype._pipe=function(t,e){var i=t.uid,n=this._pipelineMap.get(i);!n.head&&(n.head=e),n.tail&&n.tail.pipe(e),n.tail=e,e.__idxInPipeline=n.count++,e.__pipeline=n},r.wrapStageHandler=function(t,e){return K(t)&&(t={overallReset:t,seriesType:uS(t)}),t.uid=mo("stageHandler"),e&&(t.visualType=e),t},r}();function tS(r){r.overallReset(r.ecModel,r.api,r.payload)}function eS(r){return r.overallProgress&&rS}function rS(){this.agent.dirty(),this.getDownstream().dirty()}function iS(){this.agent&&this.agent.dirty()}function nS(r){return r.plan?r.plan(r.model,r.ecModel,r.api,r.payload):null}function aS(r){r.useClearVisual&&r.data.clearAllVisual();var t=r.resetDefines=_t(r.reset(r.model,r.ecModel,r.api,r.payload));return t.length>1?Y(t,function(e,i){return xp(i)}):oS}var oS=xp(0);function xp(r){return function(t,e){var i=e.data,n=e.resetDefines[r];if(n&&n.dataEach)for(var a=t.start;a<t.end;a++)n.dataEach(i,a);else n&&n.progress&&n.progress(t,i)}}function sS(r){return r.data.count()}function uS(r){Qa=null;try{r(Tn,Cp)}catch{}return Qa}var Tn={},Cp={},Qa;Dp(Tn,Ll);Dp(Cp,Kd);Tn.eachSeriesByType=Tn.eachRawSeriesByType=function(r){Qa=r};Tn.eachComponent=function(r){r.mainType==="series"&&r.subType&&(Qa=r.subType)};function Dp(r,t){for(var e in t.prototype)r[e]=Ft}var Jh=["#37A2DA","#32C5E9","#67E0E3","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#E062AE","#E690D1","#e7bcf3","#9d96f5","#8378EA","#96BFFF"];const lS={color:Jh,colorLayer:[["#37A2DA","#ffd85c","#fd7b5f"],["#37A2DA","#67E0E3","#FFDB5C","#ff9f7f","#E062AE","#9d96f5"],["#37A2DA","#32C5E9","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#e7bcf3","#8378EA","#96BFFF"],Jh]};var bt="#B9B8CE",jh="#100C2A",ia=function(){return{axisLine:{lineStyle:{color:bt}},splitLine:{lineStyle:{color:"#484753"}},splitArea:{areaStyle:{color:["rgba(255,255,255,0.02)","rgba(255,255,255,0.05)"]}},minorSplitLine:{lineStyle:{color:"#20203B"}}}},tv=["#4992ff","#7cffb2","#fddd60","#ff6e76","#58d9f9","#05c091","#ff8a45","#8d48e3","#dd79ff"],Mp={darkMode:!0,color:tv,backgroundColor:jh,axisPointer:{lineStyle:{color:"#817f91"},crossStyle:{color:"#817f91"},label:{color:"#fff"}},legend:{textStyle:{color:bt},pageTextStyle:{color:bt}},textStyle:{color:bt},title:{textStyle:{color:"#EEF1FA"},subtextStyle:{color:"#B9B8CE"}},toolbox:{iconStyle:{borderColor:bt}},dataZoom:{borderColor:"#71708A",textStyle:{color:bt},brushStyle:{color:"rgba(135,163,206,0.3)"},handleStyle:{color:"#353450",borderColor:"#C5CBE3"},moveHandleStyle:{color:"#B0B6C3",opacity:.3},fillerColor:"rgba(135,163,206,0.2)",emphasis:{handleStyle:{borderColor:"#91B7F2",color:"#4D587D"},moveHandleStyle:{color:"#636D9A",opacity:.7}},dataBackground:{lineStyle:{color:"#71708A",width:1},areaStyle:{color:"#71708A"}},selectedDataBackground:{lineStyle:{color:"#87A3CE"},areaStyle:{color:"#87A3CE"}}},visualMap:{textStyle:{color:bt}},timeline:{lineStyle:{color:bt},label:{color:bt},controlStyle:{color:bt,borderColor:bt}},calendar:{itemStyle:{color:jh},dayLabel:{color:bt},monthLabel:{color:bt},yearLabel:{color:bt}},timeAxis:ia(),logAxis:ia(),valueAxis:ia(),categoryAxis:ia(),line:{symbol:"circle"},graph:{color:tv},gauge:{title:{color:bt},axisLine:{lineStyle:{color:[[1,"rgba(207,212,219,0.2)"]]}},axisLabel:{color:bt},detail:{color:"#EEF1FA"}},candlestick:{itemStyle:{color:"#f64e56",color0:"#54ea92",borderColor:"#f64e56",borderColor0:"#54ea92"}}};Mp.categoryAxis.splitLine.show=!1;var fS=function(){function r(){}return r.prototype.normalizeQuery=function(t){var e={},i={},n={};if(B(t)){var a=_e(t);e.mainType=a.main||null,e.subType=a.sub||null}else{var o=["Index","Name","Id"],s={name:1,dataIndex:1,dataType:1};D(t,function(u,l){for(var f=!1,h=0;h<o.length;h++){var c=o[h],v=l.lastIndexOf(c);if(v>0&&v===l.length-c.length){var d=l.slice(0,v);d!=="data"&&(e.mainType=d,e[c.toLowerCase()]=u,f=!0)}}s.hasOwnProperty(l)&&(i[l]=u,f=!0),f||(n[l]=u)})}return{cptQuery:e,dataQuery:i,otherQuery:n}},r.prototype.filter=function(t,e){var i=this.eventInfo;if(!i)return!0;var n=i.targetEl,a=i.packedEvent,o=i.model,s=i.view;if(!o||!s)return!0;var u=e.cptQuery,l=e.dataQuery;return f(u,o,"mainType")&&f(u,o,"subType")&&f(u,o,"index","componentIndex")&&f(u,o,"name")&&f(u,o,"id")&&f(l,a,"name")&&f(l,a,"dataIndex")&&f(l,a,"dataType")&&(!s.filterForExposedEvent||s.filterForExposedEvent(t,e.otherQuery,n,a));function f(h,c,v,d){return h[v]==null||c[d||v]===h[v]}},r.prototype.afterTrigger=function(){this.eventInfo=null},r}(),Pu=["symbol","symbolSize","symbolRotate","symbolOffset"],ev=Pu.concat(["symbolKeepAspect"]),hS={createOnAllSeries:!0,performRawSeries:!0,reset:function(r,t){var e=r.getData();if(r.legendIcon&&e.setVisual("legendIcon",r.legendIcon),!r.hasSymbolVisual)return;for(var i={},n={},a=!1,o=0;o<Pu.length;o++){var s=Pu[o],u=r.get(s);K(u)?(a=!0,n[s]=u):i[s]=u}if(i.symbol=i.symbol||r.defaultSymbol,e.setVisual(O({legendIcon:r.legendIcon||i.symbol,symbolKeepAspect:r.get("symbolKeepAspect")},i)),t.isSeriesFiltered(r))return;var l=ft(n);function f(h,c){for(var v=r.getRawValue(c),d=r.getDataParams(c),y=0;y<l.length;y++){var p=l[y];h.setItemVisual(c,p,n[p](v,d))}}return{dataEach:a?f:null}}},vS={createOnAllSeries:!0,performRawSeries:!0,reset:function(r,t){if(!r.hasSymbolVisual||t.isSeriesFiltered(r))return;var e=r.getData();function i(n,a){for(var o=n.getItemModel(a),s=0;s<ev.length;s++){var u=ev[s],l=o.getShallow(u,!0);l!=null&&n.setItemVisual(a,u,l)}}return{dataEach:e.hasItemOption?i:null}}};function cS(r,t,e){switch(e){case"color":var i=r.getItemVisual(t,"style");return i[r.getVisual("drawType")];case"opacity":return r.getItemVisual(t,"style").opacity;case"symbol":case"symbolSize":case"liftZ":return r.getItemVisual(t,e)}}function dS(r,t){switch(t){case"color":var e=r.getVisual("style");return e[r.getVisual("drawType")];case"opacity":return r.getVisual("style").opacity;case"symbol":case"symbolSize":case"liftZ":return r.getVisual(t)}}function JC(r,t,e,i){switch(e){case"color":var n=r.ensureUniqueItemVisual(t,"style");n[r.getVisual("drawType")]=i,r.setItemVisual(t,"colorFromPalette",!1);break;case"opacity":r.ensureUniqueItemVisual(t,"style").opacity=i;break;case"symbol":case"symbolSize":case"liftZ":r.setItemVisual(t,e,i);break}}function jC(r,t){function e(i,n){var a=[];return i.eachComponent({mainType:"series",subType:r,query:n},function(o){a.push(o.seriesIndex)}),a}D([[r+"ToggleSelect","toggleSelect"],[r+"Select","select"],[r+"UnSelect","unselect"]],function(i){t(i[0],function(n,a,o){n=O({},n),o.dispatchAction(O(n,{type:i[1],seriesIndex:e(a,n)}))})})}function jr(r,t,e,i,n){var a=r+t;e.isSilent(a)||i.eachComponent({mainType:"series",subType:"pie"},function(o){for(var s=o.seriesIndex,u=o.option.selectedMap,l=n.selected,f=0;f<l.length;f++)if(l[f].seriesIndex===s){var h=o.getData(),c=Mn(h,n.fromActionPayload);e.trigger(a,{type:a,seriesId:o.id,name:k(c)?h.getName(c[0]):h.getName(c),selected:B(u)?u:O({},u)})}})}function pS(r,t,e){r.on("selectchanged",function(i){var n=e.getModel();i.isFromClick?(jr("map","selectchanged",t,n,i),jr("pie","selectchanged",t,n,i)):i.fromAction==="select"?(jr("map","selected",t,n,i),jr("pie","selected",t,n,i)):i.fromAction==="unselect"&&(jr("map","unselected",t,n,i),jr("pie","unselected",t,n,i))})}function Qi(r,t,e){for(var i;r&&!(t(r)&&(i=r,e));)r=r.__hostTarget||r.parent;return i}var gS=Math.round(Math.random()*9),yS=typeof Object.defineProperty=="function",_S=function(){function r(){this._id="__ec_inner_"+gS++}return r.prototype.get=function(t){return this._guard(t)[this._id]},r.prototype.set=function(t,e){var i=this._guard(t);return yS?Object.defineProperty(i,this._id,{value:e,enumerable:!1,configurable:!0}):i[this._id]=e,this},r.prototype.delete=function(t){return this.has(t)?(delete this._guard(t)[this._id],!0):!1},r.prototype.has=function(t){return!!this._guard(t)[this._id]},r.prototype._guard=function(t){if(t!==Object(t))throw TypeError("Value of WeakMap is not a non-null object.");return t},r}(),mS=nt.extend({type:"triangle",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(r,t){var e=t.cx,i=t.cy,n=t.width/2,a=t.height/2;r.moveTo(e,i-a),r.lineTo(e+n,i+a),r.lineTo(e-n,i+a),r.closePath()}}),wS=nt.extend({type:"diamond",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(r,t){var e=t.cx,i=t.cy,n=t.width/2,a=t.height/2;r.moveTo(e,i-a),r.lineTo(e+n,i),r.lineTo(e,i+a),r.lineTo(e-n,i),r.closePath()}}),SS=nt.extend({type:"pin",shape:{x:0,y:0,width:0,height:0},buildPath:function(r,t){var e=t.x,i=t.y,n=t.width/5*3,a=Math.max(n,t.height),o=n/2,s=o*o/(a-o),u=i-a+o+s,l=Math.asin(s/o),f=Math.cos(l)*o,h=Math.sin(l),c=Math.cos(l),v=o*.6,d=o*.7;r.moveTo(e-f,u+s),r.arc(e,u,o,Math.PI-l,Math.PI*2+l),r.bezierCurveTo(e+f-h*v,u+s+c*v,e,i-d,e,i),r.bezierCurveTo(e,i-d,e-f+h*v,u+s+c*v,e-f,u+s),r.closePath()}}),TS=nt.extend({type:"arrow",shape:{x:0,y:0,width:0,height:0},buildPath:function(r,t){var e=t.height,i=t.width,n=t.x,a=t.y,o=i/3*2;r.moveTo(n,a),r.lineTo(n+o,a+e),r.lineTo(n,a+e/4*3),r.lineTo(n-o,a+e),r.lineTo(n,a),r.closePath()}}),bS={line:Si,rect:Pt,roundRect:Pt,square:Pt,circle:po,diamond:wS,pin:SS,arrow:TS,triangle:mS},xS={line:function(r,t,e,i,n){n.x1=r,n.y1=t+i/2,n.x2=r+e,n.y2=t+i/2},rect:function(r,t,e,i,n){n.x=r,n.y=t,n.width=e,n.height=i},roundRect:function(r,t,e,i,n){n.x=r,n.y=t,n.width=e,n.height=i,n.r=Math.min(e,i)/4},square:function(r,t,e,i,n){var a=Math.min(e,i);n.x=r,n.y=t,n.width=a,n.height=a},circle:function(r,t,e,i,n){n.cx=r+e/2,n.cy=t+i/2,n.r=Math.min(e,i)/2},diamond:function(r,t,e,i,n){n.cx=r+e/2,n.cy=t+i/2,n.width=e,n.height=i},pin:function(r,t,e,i,n){n.x=r+e/2,n.y=t+i/2,n.width=e,n.height=i},arrow:function(r,t,e,i,n){n.x=r+e/2,n.y=t+i/2,n.width=e,n.height=i},triangle:function(r,t,e,i,n){n.cx=r+e/2,n.cy=t+i/2,n.width=e,n.height=i}},Lu={};D(bS,function(r,t){Lu[t]=new r});var CS=nt.extend({type:"symbol",shape:{symbolType:"",x:0,y:0,width:0,height:0},calculateTextPosition:function(r,t,e){var i=Oc(r,t,e),n=this.shape;return n&&n.symbolType==="pin"&&t.position==="inside"&&(i.y=e.y+e.height*.4),i},buildPath:function(r,t,e){var i=t.symbolType;if(i!=="none"){var n=Lu[i];n||(i="rect",n=Lu[i]),xS[i](t.x,t.y,t.width,t.height,n.shape),n.buildPath(r,n.shape,e)}}});function DS(r,t){if(this.type!=="image"){var e=this.style;this.__isEmptyBrush?(e.stroke=r,e.fill=t||"#fff",e.lineWidth=2):this.shape.symbolType==="line"?e.stroke=r:e.fill=r,this.markRedraw()}}function kl(r,t,e,i,n,a,o){var s=r.indexOf("empty")===0;s&&(r=r.substr(5,1).toLowerCase()+r.substr(6));var u;return r.indexOf("image://")===0?u=Td(r.slice(8),new J(t,e,i,n),o?"center":"cover"):r.indexOf("path://")===0?u=gl(r.slice(7),{},new J(t,e,i,n),o?"center":"cover"):u=new CS({shape:{symbolType:r,x:t,y:e,width:i,height:n}}),u.__isEmptyBrush=s,u.setColor=DS,a&&u.setColor(a),u}function tD(r){return k(r)||(r=[+r,+r]),[r[0]||0,r[1]||0]}function MS(r,t){if(r!=null)return k(r)||(r=[r,r]),[ct(r[0],t[0])||0,ct(W(r[1],r[0]),t[1])||0]}function Pr(r){return isFinite(r)}function AS(r,t,e){var i=t.x==null?0:t.x,n=t.x2==null?1:t.x2,a=t.y==null?0:t.y,o=t.y2==null?0:t.y2;t.global||(i=i*e.width+e.x,n=n*e.width+e.x,a=a*e.height+e.y,o=o*e.height+e.y),i=Pr(i)?i:0,n=Pr(n)?n:1,a=Pr(a)?a:0,o=Pr(o)?o:0;var s=r.createLinearGradient(i,a,n,o);return s}function PS(r,t,e){var i=e.width,n=e.height,a=Math.min(i,n),o=t.x==null?.5:t.x,s=t.y==null?.5:t.y,u=t.r==null?.5:t.r;t.global||(o=o*i+e.x,s=s*n+e.y,u=u*a),o=Pr(o)?o:.5,s=Pr(s)?s:.5,u=u>=0&&Pr(u)?u:.5;var l=r.createRadialGradient(o,s,0,o,s,u);return l}function Iu(r,t,e){for(var i=t.type==="radial"?PS(r,t,e):AS(r,t,e),n=t.colorStops,a=0;a<n.length;a++)i.addColorStop(n[a].offset,n[a].color);return i}function LS(r,t){if(r===t||!r&&!t)return!1;if(!r||!t||r.length!==t.length)return!0;for(var e=0;e<r.length;e++)if(r[e]!==t[e])return!0;return!1}function na(r){return parseInt(r,10)}function aa(r,t,e){var i=["width","height"][t],n=["clientWidth","clientHeight"][t],a=["paddingLeft","paddingTop"][t],o=["paddingRight","paddingBottom"][t];if(e[i]!=null&&e[i]!=="auto")return parseFloat(e[i]);var s=document.defaultView.getComputedStyle(r);return(r[n]||na(s[i])||na(r.style[i]))-(na(s[a])||0)-(na(s[o])||0)|0}function IS(r,t){return!r||r==="solid"||!(t>0)?null:r==="dashed"?[4*t,2*t]:r==="dotted"?[t]:ut(r)?[r]:k(r)?r:null}function Ap(r){var t=r.style,e=t.lineDash&&t.lineWidth>0&&IS(t.lineDash,t.lineWidth),i=t.lineDashOffset;if(e){var n=t.strokeNoScale&&r.getLineScale?r.getLineScale():1;n&&n!==1&&(e=Y(e,function(a){return a/n}),i/=n)}return[e,i]}var RS=new pi(!0);function Ja(r){var t=r.stroke;return!(t==null||t==="none"||!(r.lineWidth>0))}function rv(r){return typeof r=="string"&&r!=="none"}function ja(r){var t=r.fill;return t!=null&&t!=="none"}function iv(r,t){if(t.fillOpacity!=null&&t.fillOpacity!==1){var e=r.globalAlpha;r.globalAlpha=t.fillOpacity*t.opacity,r.fill(),r.globalAlpha=e}else r.fill()}function nv(r,t){if(t.strokeOpacity!=null&&t.strokeOpacity!==1){var e=r.globalAlpha;r.globalAlpha=t.strokeOpacity*t.opacity,r.stroke(),r.globalAlpha=e}else r.stroke()}function Ru(r,t,e){var i=qc(t.image,t.__image,e);if(lo(i)){var n=r.createPattern(i,t.repeat||"repeat");if(typeof DOMMatrix=="function"&&n&&n.setTransform){var a=new DOMMatrix;a.translateSelf(t.x||0,t.y||0),a.rotateSelf(0,0,(t.rotation||0)*_a),a.scaleSelf(t.scaleX||1,t.scaleY||1),n.setTransform(a)}return n}}function ES(r,t,e,i){var n,a=Ja(e),o=ja(e),s=e.strokePercent,u=s<1,l=!t.path;(!t.silent||u)&&l&&t.createPathProxy();var f=t.path||RS,h=t.__dirty;if(!i){var c=e.fill,v=e.stroke,d=o&&!!c.colorStops,y=a&&!!v.colorStops,p=o&&!!c.image,g=a&&!!v.image,_=void 0,m=void 0,w=void 0,T=void 0,S=void 0;(d||y)&&(S=t.getBoundingRect()),d&&(_=h?Iu(r,c,S):t.__canvasFillGradient,t.__canvasFillGradient=_),y&&(m=h?Iu(r,v,S):t.__canvasStrokeGradient,t.__canvasStrokeGradient=m),p&&(w=h||!t.__canvasFillPattern?Ru(r,c,t):t.__canvasFillPattern,t.__canvasFillPattern=w),g&&(T=h||!t.__canvasStrokePattern?Ru(r,v,t):t.__canvasStrokePattern,t.__canvasStrokePattern=w),d?r.fillStyle=_:p&&(w?r.fillStyle=w:o=!1),y?r.strokeStyle=m:g&&(T?r.strokeStyle=T:a=!1)}var b=t.getGlobalScale();f.setScale(b[0],b[1],t.segmentIgnoreThreshold);var M,x;r.setLineDash&&e.lineDash&&(n=Ap(t),M=n[0],x=n[1]);var C=!0;(l||h&Yi)&&(f.setDPR(r.dpr),u?f.setContext(null):(f.setContext(r),C=!1),f.reset(),t.buildPath(f,t.shape,i),f.toStatic(),t.pathUpdated()),C&&f.rebuildPath(r,u?s:1),M&&(r.setLineDash(M),r.lineDashOffset=x),i||(e.strokeFirst?(a&&nv(r,e),o&&iv(r,e)):(o&&iv(r,e),a&&nv(r,e))),M&&r.setLineDash([])}function OS(r,t,e){var i=t.__image=qc(e.image,t.__image,t,t.onload);if(!(!i||!lo(i))){var n=e.x||0,a=e.y||0,o=t.getWidth(),s=t.getHeight(),u=i.width/i.height;if(o==null&&s!=null?o=s*u:s==null&&o!=null?s=o/u:o==null&&s==null&&(o=i.width,s=i.height),e.sWidth&&e.sHeight){var l=e.sx||0,f=e.sy||0;r.drawImage(i,l,f,e.sWidth,e.sHeight,n,a,o,s)}else if(e.sx&&e.sy){var l=e.sx,f=e.sy,h=o-l,c=s-f;r.drawImage(i,l,f,h,c,n,a,o,s)}else r.drawImage(i,n,a,o,s)}}function kS(r,t,e){var i,n=e.text;if(n!=null&&(n+=""),n){r.font=e.font||Nr,r.textAlign=e.textAlign,r.textBaseline=e.textBaseline;var a=void 0,o=void 0;r.setLineDash&&e.lineDash&&(i=Ap(t),a=i[0],o=i[1]),a&&(r.setLineDash(a),r.lineDashOffset=o),e.strokeFirst?(Ja(e)&&r.strokeText(n,e.x,e.y),ja(e)&&r.fillText(n,e.x,e.y)):(ja(e)&&r.fillText(n,e.x,e.y),Ja(e)&&r.strokeText(n,e.x,e.y)),a&&r.setLineDash([])}}var av=["shadowBlur","shadowOffsetX","shadowOffsetY"],ov=[["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]];function Pp(r,t,e,i,n){var a=!1;if(!i&&(e=e||{},t===e))return!1;if(i||t.opacity!==e.opacity){Nt(r,n),a=!0;var o=Math.max(Math.min(t.opacity,1),0);r.globalAlpha=isNaN(o)?Er.opacity:o}(i||t.blend!==e.blend)&&(a||(Nt(r,n),a=!0),r.globalCompositeOperation=t.blend||Er.blend);for(var s=0;s<av.length;s++){var u=av[s];(i||t[u]!==e[u])&&(a||(Nt(r,n),a=!0),r[u]=r.dpr*(t[u]||0))}return(i||t.shadowColor!==e.shadowColor)&&(a||(Nt(r,n),a=!0),r.shadowColor=t.shadowColor||Er.shadowColor),a}function sv(r,t,e,i,n){var a=bn(t,n.inHover),o=i?null:e&&bn(e,n.inHover)||{};if(a===o)return!1;var s=Pp(r,a,o,i,n);if((i||a.fill!==o.fill)&&(s||(Nt(r,n),s=!0),rv(a.fill)&&(r.fillStyle=a.fill)),(i||a.stroke!==o.stroke)&&(s||(Nt(r,n),s=!0),rv(a.stroke)&&(r.strokeStyle=a.stroke)),(i||a.opacity!==o.opacity)&&(s||(Nt(r,n),s=!0),r.globalAlpha=a.opacity==null?1:a.opacity),t.hasStroke()){var u=a.lineWidth,l=u/(a.strokeNoScale&&t.getLineScale?t.getLineScale():1);r.lineWidth!==l&&(s||(Nt(r,n),s=!0),r.lineWidth=l)}for(var f=0;f<ov.length;f++){var h=ov[f],c=h[0];(i||a[c]!==o[c])&&(s||(Nt(r,n),s=!0),r[c]=a[c]||h[1])}return s}function BS(r,t,e,i,n){return Pp(r,bn(t,n.inHover),e&&bn(e,n.inHover),i,n)}function Lp(r,t){var e=t.transform,i=r.dpr||1;e?r.setTransform(i*e[0],i*e[1],i*e[2],i*e[3],i*e[4],i*e[5]):r.setTransform(i,0,0,i,0,0)}function NS(r,t,e){for(var i=!1,n=0;n<r.length;n++){var a=r[n];i=i||a.isZeroArea(),Lp(t,a),t.beginPath(),a.buildPath(t,a.shape),t.clip()}e.allClipped=i}function FS(r,t){return r&&t?r[0]!==t[0]||r[1]!==t[1]||r[2]!==t[2]||r[3]!==t[3]||r[4]!==t[4]||r[5]!==t[5]:!(!r&&!t)}var uv=1,lv=2,fv=3,hv=4;function zS(r){var t=ja(r),e=Ja(r);return!(r.lineDash||!(+t^+e)||t&&typeof r.fill!="string"||e&&typeof r.stroke!="string"||r.strokePercent<1||r.strokeOpacity<1||r.fillOpacity<1)}function Nt(r,t){t.batchFill&&r.fill(),t.batchStroke&&r.stroke(),t.batchFill="",t.batchStroke=""}function bn(r,t){return t&&r.__hoverStyle||r.style}function Ip(r,t){Lr(r,t,{inHover:!1,viewWidth:0,viewHeight:0},!0)}function Lr(r,t,e,i){var n=t.transform;if(!t.shouldBePainted(e.viewWidth,e.viewHeight,!1,!1)){t.__dirty&=-2,t.__isRendered=!1;return}var a=t.__clipPaths,o=e.prevElClipPaths,s=!1,u=!1;if((!o||LS(a,o))&&(o&&o.length&&(Nt(r,e),r.restore(),u=s=!0,e.prevElClipPaths=null,e.allClipped=!1,e.prevEl=null),a&&a.length&&(Nt(r,e),r.save(),NS(a,r,e),s=!0),e.prevElClipPaths=a),e.allClipped){t.__isRendered=!1;return}t.beforeBrush&&t.beforeBrush(),t.innerBeforeBrush();var l=e.prevEl;l||(u=s=!0);var f=t instanceof nt&&t.autoBatch&&zS(t.style);s||FS(n,l.transform)?(Nt(r,e),Lp(r,t)):f||Nt(r,e);var h=bn(t,e.inHover);t instanceof nt?(e.lastDrawType!==uv&&(u=!0,e.lastDrawType=uv),sv(r,t,l,u,e),(!f||!e.batchFill&&!e.batchStroke)&&r.beginPath(),ES(r,t,h,f),f&&(e.batchFill=h.fill||"",e.batchStroke=h.stroke||"")):t instanceof Wa?(e.lastDrawType!==fv&&(u=!0,e.lastDrawType=fv),sv(r,t,l,u,e),kS(r,t,h)):t instanceof zr?(e.lastDrawType!==lv&&(u=!0,e.lastDrawType=lv),BS(r,t,l,u,e),OS(r,t,h)):t.getTemporalDisplayables&&(e.lastDrawType!==hv&&(u=!0,e.lastDrawType=hv),HS(r,t,e)),f&&i&&Nt(r,e),t.innerAfterBrush(),t.afterBrush&&t.afterBrush(),e.prevEl=t,t.__dirty=0,t.__isRendered=!0}function HS(r,t,e){var i=t.getDisplayables(),n=t.getTemporalDisplayables();r.save();var a={prevElClipPaths:null,prevEl:null,allClipped:!1,viewWidth:e.viewWidth,viewHeight:e.viewHeight,inHover:e.inHover},o,s;for(o=t.getCursor(),s=i.length;o<s;o++){var u=i[o];u.beforeBrush&&u.beforeBrush(),u.innerBeforeBrush(),Lr(r,u,a,o===s-1),u.innerAfterBrush(),u.afterBrush&&u.afterBrush(),a.prevEl=u}for(var l=0,f=n.length;l<f;l++){var u=n[l];u.beforeBrush&&u.beforeBrush(),u.innerBeforeBrush(),Lr(r,u,a,l===f-1),u.innerAfterBrush(),u.afterBrush&&u.afterBrush(),a.prevEl=u}t.clearTemporalDisplayables(),t.notClear=!0,r.restore()}var Ds=new _S,vv=new Dn(100),cv=["symbol","symbolSize","symbolKeepAspect","color","backgroundColor","dashArrayX","dashArrayY","maxTileWidth","maxTileHeight"];function Eu(r,t){if(r==="none")return null;var e=t.getDevicePixelRatio(),i=t.getZr(),n=i.painter.type==="svg";r.dirty&&Ds.delete(r);var a=Ds.get(r);if(a)return a;var o=ot(r,{symbol:"rect",symbolSize:1,symbolKeepAspect:!0,color:"rgba(0, 0, 0, 0.2)",backgroundColor:null,dashArrayX:5,dashArrayY:5,rotation:0,maxTileWidth:512,maxTileHeight:512});o.backgroundColor==="none"&&(o.backgroundColor=null);var s={repeat:"repeat"};return u(s),s.rotation=o.rotation,s.scaleX=s.scaleY=n?1:1/e,Ds.set(r,s),r.dirty=!1,s;function u(l){for(var f=[e],h=!0,c=0;c<cv.length;++c){var v=o[cv[c]];if(v!=null&&!k(v)&&!B(v)&&!ut(v)&&typeof v!="boolean"){h=!1;break}f.push(v)}var d;if(h){d=f.join(",")+(n?"-svg":"");var y=vv.get(d);y&&(n?l.svgElement=y:l.image=y)}var p=Ep(o.dashArrayX),g=GS(o.dashArrayY),_=Rp(o.symbol),m=VS(p),w=Op(g),T=!n&&yi.createCanvas(),S=n&&{tag:"g",attrs:{},key:"dcl",children:[]},b=x(),M;T&&(T.width=b.width*e,T.height=b.height*e,M=T.getContext("2d")),C(),h&&vv.put(d,T||S),l.image=T,l.svgElement=S,l.svgWidth=b.width,l.svgHeight=b.height;function x(){for(var A=1,L=0,I=m.length;L<I;++L)A=Df(A,m[L]);for(var P=1,L=0,I=_.length;L<I;++L)P=Df(P,_[L].length);A*=P;var R=w*m.length*_.length;return{width:Math.max(1,Math.min(A,o.maxTileWidth)),height:Math.max(1,Math.min(R,o.maxTileHeight))}}function C(){M&&(M.clearRect(0,0,T.width,T.height),o.backgroundColor&&(M.fillStyle=o.backgroundColor,M.fillRect(0,0,T.width,T.height)));for(var A=0,L=0;L<g.length;++L)A+=g[L];if(A<=0)return;for(var I=-w,P=0,R=0,E=0;I<b.height;){if(P%2===0){for(var U=R/2%_.length,z=0,G=0,$=0;z<b.width*2;){for(var it=0,L=0;L<p[E].length;++L)it+=p[E][L];if(it<=0)break;if(G%2===0){var j=(1-o.symbolSize)*.5,wt=z+p[E][G]*j,Ut=I+g[P]*j,ke=p[E][G]*o.symbolSize,er=g[P]*o.symbolSize,rr=$/2%_[U].length;Gr(wt,Ut,ke,er,_[U][rr])}z+=p[E][G],++$,++G,G===p[E].length&&(G=0)}++E,E===p.length&&(E=0)}I+=g[P],++R,++P,P===g.length&&(P=0)}function Gr(Vt,dt,V,q,ir){var Dt=n?1:e,$l=kl(ir,Vt*Dt,dt*Dt,V*Dt,q*Dt,o.color,o.symbolKeepAspect);if(n){var ql=i.painter.renderOneToVNode($l);ql&&S.children.push(ql)}else Ip(M,$l)}}}}function Rp(r){if(!r||r.length===0)return[["rect"]];if(B(r))return[[r]];for(var t=!0,e=0;e<r.length;++e)if(!B(r[e])){t=!1;break}if(t)return Rp([r]);for(var i=[],e=0;e<r.length;++e)B(r[e])?i.push([r[e]]):i.push(r[e]);return i}function Ep(r){if(!r||r.length===0)return[[0,0]];if(ut(r)){var t=Math.ceil(r);return[[t,t]]}for(var e=!0,i=0;i<r.length;++i)if(!ut(r[i])){e=!1;break}if(e)return Ep([r]);for(var n=[],i=0;i<r.length;++i)if(ut(r[i])){var t=Math.ceil(r[i]);n.push([t,t])}else{var t=Y(r[i],function(s){return Math.ceil(s)});t.length%2===1?n.push(t.concat(t)):n.push(t)}return n}function GS(r){if(!r||typeof r=="object"&&r.length===0)return[0,0];if(ut(r)){var t=Math.ceil(r);return[t,t]}var e=Y(r,function(i){return Math.ceil(i)});return r.length%2?e.concat(e):e}function VS(r){return Y(r,function(t){return Op(t)})}function Op(r){for(var t=0,e=0;e<r.length;++e)t+=r[e];return r.length%2===1?t*2:t}function WS(r,t){r.eachRawSeries(function(e){if(!r.isSeriesFiltered(e)){var i=e.getData();i.hasItemVisual()&&i.each(function(o){var s=i.getItemVisual(o,"decal");if(s){var u=i.ensureUniqueItemVisual(o,"style");u.decal=Eu(s,t)}});var n=i.getVisual("decal");if(n){var a=i.getVisual("style");a.decal=Eu(n,t)}}})}var ne=new be,kp={};function US(r,t){kp[r]=t}function YS(r){return kp[r]}var XS=1,$S=800,qS=900,ZS=1e3,KS=2e3,QS=5e3,Bp=1e3,JS=1100,Bl=2e3,Np=3e3,jS=4e3,Mo=4500,tT=4600,eT=5e3,rT=6e3,Fp=7e3,iT={PROCESSOR:{FILTER:ZS,SERIES_FILTER:$S,STATISTIC:QS},VISUAL:{LAYOUT:Bp,PROGRESSIVE_LAYOUT:JS,GLOBAL:Bl,CHART:Np,POST_CHART_LAYOUT:tT,COMPONENT:jS,BRUSH:eT,CHART_ITEM:Mo,ARIA:rT,DECAL:Fp}},Tt="__flagInMainProcess",Rt="__pendingUpdate",Ms="__needsUpdateStatus",dv=/^[a-zA-Z0-9_]+$/,As="__connectUpdateStatus",pv=0,nT=1,aT=2;function zp(r){return function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];if(this.isDisposed()){this.id;return}return Gp(this,r,t)}}function Hp(r){return function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return Gp(this,r,t)}}function Gp(r,t,e){return e[0]=e[0]&&e[0].toLowerCase(),be.prototype[t].apply(r,e)}var Vp=function(r){N(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t}(be),Wp=Vp.prototype;Wp.on=Hp("on");Wp.off=Hp("off");var ti,Ps,oa,He,Ls,Is,Rs,ki,Bi,gv,yv,Es,_v,sa,mv,Up,Yt,wv,Yp=function(r){N(t,r);function t(e,i,n){var a=r.call(this,new fS)||this;a._chartsViews=[],a._chartsMap={},a._componentsViews=[],a._componentsMap={},a._pendingActions=[],n=n||{},B(i)&&(i=Xp[i]),a._dom=e;var o="canvas",s="auto",u=!1;n.ssr&&T_(function(c){var v=rt(c),d=v.dataIndex;if(d!=null){var y=X();return y.set("series_index",v.seriesIndex),y.set("data_index",d),v.ssrType&&y.set("ssr_type",v.ssrType),y}});var l=a._zr=Cf(e,{renderer:n.renderer||o,devicePixelRatio:n.devicePixelRatio,width:n.width,height:n.height,ssr:n.ssr,useDirtyRect:W(n.useDirtyRect,u),useCoarsePointer:W(n.useCoarsePointer,s),pointerSize:n.pointerSize});a._ssr=n.ssr,a._throttledZrFlush=Ol(lt(l.flush,l),17),i=Z(i),i&&jd(i,!0),a._theme=i,a._locale=y1(n.locale||Ld),a._coordSysMgr=new Qd;var f=a._api=mv(a);function h(c,v){return c.__prio-v.__prio}return ma(eo,h),ma(Ou,h),a._scheduler=new bp(a,f,Ou,eo),a._messageCenter=new Vp,a._initEvents(),a.resize=lt(a.resize,a),l.animation.on("frame",a._onframe,a),gv(l,a),yv(l,a),ka(a),a}return t.prototype._onframe=function(){if(!this._disposed){wv(this);var e=this._scheduler;if(this[Rt]){var i=this[Rt].silent;this[Tt]=!0;try{ti(this),He.update.call(this,null,this[Rt].updateParams)}catch(u){throw this[Tt]=!1,this[Rt]=null,u}this._zr.flush(),this[Tt]=!1,this[Rt]=null,ki.call(this,i),Bi.call(this,i)}else if(e.unfinished){var n=XS,a=this._model,o=this._api;e.unfinished=!1;do{var s=+new Date;e.performSeriesTasks(a),e.performDataProcessorTasks(a),Is(this,a),e.performVisualTasks(a),sa(this,this._model,o,"remain",{}),n-=+new Date-s}while(n>0&&e.unfinished);e.unfinished||this._zr.flush()}}},t.prototype.getDom=function(){return this._dom},t.prototype.getId=function(){return this.id},t.prototype.getZr=function(){return this._zr},t.prototype.isSSR=function(){return this._ssr},t.prototype.setOption=function(e,i,n){if(!this[Tt]){if(this._disposed){this.id;return}var a,o,s;if(F(i)&&(n=i.lazyUpdate,a=i.silent,o=i.replaceMerge,s=i.transition,i=i.notMerge),this[Tt]=!0,!this._model||i){var u=new V1(this._api),l=this._theme,f=this._model=new Ll;f.scheduler=this._scheduler,f.ssr=this._ssr,f.init(null,null,null,l,this._locale,u)}this._model.setOption(e,{replaceMerge:o},ku);var h={seriesTransition:s,optionChanged:!0};if(n)this[Rt]={silent:a,updateParams:h},this[Tt]=!1,this.getZr().wakeUp();else{try{ti(this),He.update.call(this,null,h)}catch(c){throw this[Rt]=null,this[Tt]=!1,c}this._ssr||this._zr.flush(),this[Rt]=null,this[Tt]=!1,ki.call(this,a),Bi.call(this,a)}}},t.prototype.setTheme=function(){},t.prototype.getModel=function(){return this._model},t.prototype.getOption=function(){return this._model&&this._model.getOption()},t.prototype.getWidth=function(){return this._zr.getWidth()},t.prototype.getHeight=function(){return this._zr.getHeight()},t.prototype.getDevicePixelRatio=function(){return this._zr.painter.dpr||H.hasGlobalWindow&&window.devicePixelRatio||1},t.prototype.getRenderedCanvas=function(e){return this.renderToCanvas(e)},t.prototype.renderToCanvas=function(e){e=e||{};var i=this._zr.painter;return i.getRenderedCanvas({backgroundColor:e.backgroundColor||this._model.get("backgroundColor"),pixelRatio:e.pixelRatio||this.getDevicePixelRatio()})},t.prototype.renderToSVGString=function(e){e=e||{};var i=this._zr.painter;return i.renderToString({useViewBox:e.useViewBox})},t.prototype.getSvgDataURL=function(){if(H.svgSupported){var e=this._zr,i=e.storage.getDisplayList();return D(i,function(n){n.stopAnimation(null,!0)}),e.painter.toDataURL()}},t.prototype.getDataURL=function(e){if(this._disposed){this.id;return}e=e||{};var i=e.excludeComponents,n=this._model,a=[],o=this;D(i,function(u){n.eachComponent({mainType:u},function(l){var f=o._componentsMap[l.__viewId];f.group.ignore||(a.push(f),f.group.ignore=!0)})});var s=this._zr.painter.getType()==="svg"?this.getSvgDataURL():this.renderToCanvas(e).toDataURL("image/"+(e&&e.type||"png"));return D(a,function(u){u.group.ignore=!1}),s},t.prototype.getConnectedDataURL=function(e){if(this._disposed){this.id;return}var i=e.type==="svg",n=this.group,a=Math.min,o=Math.max,s=1/0;if(Sv[n]){var u=s,l=s,f=-s,h=-s,c=[],v=e&&e.pixelRatio||this.getDevicePixelRatio();D(ln,function(m,w){if(m.group===n){var T=i?m.getZr().painter.getSvgDom().innerHTML:m.renderToCanvas(Z(e)),S=m.getDom().getBoundingClientRect();u=a(S.left,u),l=a(S.top,l),f=o(S.right,f),h=o(S.bottom,h),c.push({dom:T,left:S.left,top:S.top})}}),u*=v,l*=v,f*=v,h*=v;var d=f-u,y=h-l,p=yi.createCanvas(),g=Cf(p,{renderer:i?"svg":"canvas"});if(g.resize({width:d,height:y}),i){var _="";return D(c,function(m){var w=m.left-u,T=m.top-l;_+='<g transform="translate('+w+","+T+')">'+m.dom+"</g>"}),g.painter.getSvgRoot().innerHTML=_,e.connectedBackgroundColor&&g.painter.setBackgroundColor(e.connectedBackgroundColor),g.refreshImmediately(),g.painter.toDataURL()}else return e.connectedBackgroundColor&&g.add(new Pt({shape:{x:0,y:0,width:d,height:y},style:{fill:e.connectedBackgroundColor}})),D(c,function(m){var w=new zr({style:{x:m.left*v-u,y:m.top*v-l,image:m.dom}});g.add(w)}),g.refreshImmediately(),p.toDataURL("image/"+(e&&e.type||"png"))}else return this.getDataURL(e)},t.prototype.convertToPixel=function(e,i){return Ls(this,"convertToPixel",e,i)},t.prototype.convertFromPixel=function(e,i){return Ls(this,"convertFromPixel",e,i)},t.prototype.containPixel=function(e,i){if(this._disposed){this.id;return}var n=this._model,a,o=jo(n,e);return D(o,function(s,u){u.indexOf("Models")>=0&&D(s,function(l){var f=l.coordinateSystem;if(f&&f.containPoint)a=a||!!f.containPoint(i);else if(u==="seriesModels"){var h=this._chartsMap[l.__viewId];h&&h.containPoint&&(a=a||h.containPoint(i,l))}},this)},this),!!a},t.prototype.getVisual=function(e,i){var n=this._model,a=jo(n,e,{defaultMainType:"series"}),o=a.seriesModel,s=o.getData(),u=a.hasOwnProperty("dataIndexInside")?a.dataIndexInside:a.hasOwnProperty("dataIndex")?s.indexOfRawIndex(a.dataIndex):null;return u!=null?cS(s,u,i):dS(s,i)},t.prototype.getViewOfComponentModel=function(e){return this._componentsMap[e.__viewId]},t.prototype.getViewOfSeriesModel=function(e){return this._chartsMap[e.__viewId]},t.prototype._initEvents=function(){var e=this;D(oT,function(i){var n=function(a){var o=e.getModel(),s=a.target,u,l=i==="globalout";if(l?u={}:s&&Qi(s,function(d){var y=rt(d);if(y&&y.dataIndex!=null){var p=y.dataModel||o.getSeriesByIndex(y.seriesIndex);return u=p&&p.getDataParams(y.dataIndex,y.dataType,s)||{},!0}else if(y.eventData)return u=O({},y.eventData),!0},!0),u){var f=u.componentType,h=u.componentIndex;(f==="markLine"||f==="markPoint"||f==="markArea")&&(f="series",h=u.seriesIndex);var c=f&&h!=null&&o.getComponent(f,h),v=c&&e[c.mainType==="series"?"_chartsMap":"_componentsMap"][c.__viewId];u.event=a,u.type=i,e._$eventProcessor.eventInfo={targetEl:s,packedEvent:u,model:c,view:v},e.trigger(i,u)}};n.zrEventfulCallAtLast=!0,e._zr.on(i,n,e)}),D(un,function(i,n){e._messageCenter.on(n,function(a){this.trigger(n,a)},e)}),D(["selectchanged"],function(i){e._messageCenter.on(i,function(n){this.trigger(i,n)},e)}),pS(this._messageCenter,this,this._api)},t.prototype.isDisposed=function(){return this._disposed},t.prototype.clear=function(){if(this._disposed){this.id;return}this.setOption({series:[]},!0)},t.prototype.dispose=function(){if(this._disposed){this.id;return}this._disposed=!0;var e=this.getDom();e&&Yc(this.getDom(),Fl,"");var i=this,n=i._api,a=i._model;D(i._componentsViews,function(o){o.dispose(a,n)}),D(i._chartsViews,function(o){o.dispose(a,n)}),i._zr.dispose(),i._dom=i._model=i._chartsMap=i._componentsMap=i._chartsViews=i._componentsViews=i._scheduler=i._api=i._zr=i._throttledZrFlush=i._theme=i._coordSysMgr=i._messageCenter=null,delete ln[i.id]},t.prototype.resize=function(e){if(!this[Tt]){if(this._disposed){this.id;return}this._zr.resize(e);var i=this._model;if(this._loadingFX&&this._loadingFX.resize(),!!i){var n=i.resetOption("media"),a=e&&e.silent;this[Rt]&&(a==null&&(a=this[Rt].silent),n=!0,this[Rt]=null),this[Tt]=!0;try{n&&ti(this),He.update.call(this,{type:"resize",animation:O({duration:0},e&&e.animation)})}catch(o){throw this[Tt]=!1,o}this[Tt]=!1,ki.call(this,a),Bi.call(this,a)}}},t.prototype.showLoading=function(e,i){if(this._disposed){this.id;return}if(F(e)&&(i=e,e=""),e=e||"default",this.hideLoading(),!!Bu[e]){var n=Bu[e](this._api,i),a=this._zr;this._loadingFX=n,a.add(n)}},t.prototype.hideLoading=function(){if(this._disposed){this.id;return}this._loadingFX&&this._zr.remove(this._loadingFX),this._loadingFX=null},t.prototype.makeActionFromEvent=function(e){var i=O({},e);return i.type=un[e.type],i},t.prototype.dispatchAction=function(e,i){if(this._disposed){this.id;return}if(F(i)||(i={silent:!!i}),!!to[e.type]&&this._model){if(this[Tt]){this._pendingActions.push(e);return}var n=i.silent;Rs.call(this,e,n);var a=i.flush;a?this._zr.flush():a!==!1&&H.browser.weChat&&this._throttledZrFlush(),ki.call(this,n),Bi.call(this,n)}},t.prototype.updateLabelLayout=function(){ne.trigger("series:layoutlabels",this._model,this._api,{updatedSeries:[]})},t.prototype.appendData=function(e){if(this._disposed){this.id;return}var i=e.seriesIndex,n=this.getModel(),a=n.getSeriesByIndex(i);a.appendData(e),this._scheduler.unfinished=!0,this.getZr().wakeUp()},t.internalField=function(){ti=function(h){var c=h._scheduler;c.restorePipelines(h._model),c.prepareStageTasks(),Ps(h,!0),Ps(h,!1),c.plan()},Ps=function(h,c){for(var v=h._model,d=h._scheduler,y=c?h._componentsViews:h._chartsViews,p=c?h._componentsMap:h._chartsMap,g=h._zr,_=h._api,m=0;m<y.length;m++)y[m].__alive=!1;c?v.eachComponent(function(S,b){S!=="series"&&w(b)}):v.eachSeries(w);function w(S){var b=S.__requireNewView;S.__requireNewView=!1;var M="_ec_"+S.id+"_"+S.type,x=!b&&p[M];if(!x){var C=_e(S.type),A=c?le.getClass(C.main,C.sub):kr.getClass(C.sub);x=new A,x.init(v,_),p[M]=x,y.push(x),g.add(x.group)}S.__viewId=x.__id=M,x.__alive=!0,x.__model=S,x.group.__ecComponentInfo={mainType:S.mainType,index:S.componentIndex},!c&&d.prepareView(x,S,v,_)}for(var m=0;m<y.length;){var T=y[m];T.__alive?m++:(!c&&T.renderTask.dispose(),g.remove(T.group),T.dispose(v,_),y.splice(m,1),p[T.__id]===T&&delete p[T.__id],T.__id=T.group.__ecComponentInfo=null)}},oa=function(h,c,v,d,y){var p=h._model;if(p.setUpdatePayload(v),!d){D([].concat(h._componentsViews).concat(h._chartsViews),T);return}var g={};g[d+"Id"]=v[d+"Id"],g[d+"Index"]=v[d+"Index"],g[d+"Name"]=v[d+"Name"];var _={mainType:d,query:g};y&&(_.subType=y);var m=v.excludeSeriesId,w;m!=null&&(w=X(),D(_t(m),function(S){var b=se(S,null);b!=null&&w.set(b,!0)})),p&&p.eachComponent(_,function(S){var b=w&&w.get(S.id)!=null;if(!b)if(th(v))if(S instanceof Fr)v.type===Or&&!v.notBlur&&!S.get(["emphasis","disabled"])&&j0(S,v,h._api);else{var M=ul(S.mainType,S.componentIndex,v.name,h._api),x=M.focusSelf,C=M.dispatchers;v.type===Or&&x&&!v.notBlur&&du(S.mainType,S.componentIndex,h._api),C&&D(C,function(A){v.type===Or?hu(A):vu(A)})}else yu(v)&&S instanceof Fr&&(rm(S,v,h._api),Jf(S),Yt(h))},h),p&&p.eachComponent(_,function(S){var b=w&&w.get(S.id)!=null;b||T(h[d==="series"?"_chartsMap":"_componentsMap"][S.__viewId])},h);function T(S){S&&S.__alive&&S[c]&&S[c](S.__model,p,h._api,v)}},He={prepareAndUpdate:function(h){ti(this),He.update.call(this,h,{optionChanged:h.newOption!=null})},update:function(h,c){var v=this._model,d=this._api,y=this._zr,p=this._coordSysMgr,g=this._scheduler;if(v){v.setUpdatePayload(h),g.restoreData(v,h),g.performSeriesTasks(v),p.create(v,d),g.performDataProcessorTasks(v,h),Is(this,v),p.update(v,d),e(v),g.performVisualTasks(v,h),Es(this,v,d,h,c);var _=v.get("backgroundColor")||"transparent",m=v.get("darkMode");y.setBackgroundColor(_),m!=null&&m!=="auto"&&y.setDarkMode(m),ne.trigger("afterupdate",v,d)}},updateTransform:function(h){var c=this,v=this._model,d=this._api;if(v){v.setUpdatePayload(h);var y=[];v.eachComponent(function(g,_){if(g!=="series"){var m=c.getViewOfComponentModel(_);if(m&&m.__alive)if(m.updateTransform){var w=m.updateTransform(_,v,d,h);w&&w.update&&y.push(m)}else y.push(m)}});var p=X();v.eachSeries(function(g){var _=c._chartsMap[g.__viewId];if(_.updateTransform){var m=_.updateTransform(g,v,d,h);m&&m.update&&p.set(g.uid,1)}else p.set(g.uid,1)}),e(v),this._scheduler.performVisualTasks(v,h,{setDirty:!0,dirtyMap:p}),sa(this,v,d,h,{},p),ne.trigger("afterupdate",v,d)}},updateView:function(h){var c=this._model;c&&(c.setUpdatePayload(h),kr.markUpdateMethod(h,"updateView"),e(c),this._scheduler.performVisualTasks(c,h,{setDirty:!0}),Es(this,c,this._api,h,{}),ne.trigger("afterupdate",c,this._api))},updateVisual:function(h){var c=this,v=this._model;v&&(v.setUpdatePayload(h),v.eachSeries(function(d){d.getData().clearAllVisual()}),kr.markUpdateMethod(h,"updateVisual"),e(v),this._scheduler.performVisualTasks(v,h,{visualType:"visual",setDirty:!0}),v.eachComponent(function(d,y){if(d!=="series"){var p=c.getViewOfComponentModel(y);p&&p.__alive&&p.updateVisual(y,v,c._api,h)}}),v.eachSeries(function(d){var y=c._chartsMap[d.__viewId];y.updateVisual(d,v,c._api,h)}),ne.trigger("afterupdate",v,this._api))},updateLayout:function(h){He.update.call(this,h)}},Ls=function(h,c,v,d){if(h._disposed){h.id;return}for(var y=h._model,p=h._coordSysMgr.getCoordinateSystems(),g,_=jo(y,v),m=0;m<p.length;m++){var w=p[m];if(w[c]&&(g=w[c](y,_,d))!=null)return g}},Is=function(h,c){var v=h._chartsMap,d=h._scheduler;c.eachSeries(function(y){d.updateStreamModes(y,v[y.__viewId])})},Rs=function(h,c){var v=this,d=this.getModel(),y=h.type,p=h.escapeConnect,g=to[y],_=g.actionInfo,m=(_.update||"update").split(":"),w=m.pop(),T=m[0]!=null&&_e(m[0]);this[Tt]=!0;var S=[h],b=!1;h.batch&&(b=!0,S=Y(h.batch,function(P){return P=ot(O({},P),h),P.batch=null,P}));var M=[],x,C=yu(h),A=th(h);if(A&&fd(this._api),D(S,function(P){if(x=g.action(P,v._model,v._api),x=x||O({},P),x.type=_.event||x.type,M.push(x),A){var R=il(h),E=R.queryOptionMap,U=R.mainTypeSpecified,z=U?E.keys()[0]:"series";oa(v,w,P,z),Yt(v)}else C?(oa(v,w,P,"series"),Yt(v)):T&&oa(v,w,P,T.main,T.sub)}),w!=="none"&&!A&&!C&&!T)try{this[Rt]?(ti(this),He.update.call(this,h),this[Rt]=null):He[w].call(this,h)}catch(P){throw this[Tt]=!1,P}if(b?x={type:_.event||y,escapeConnect:p,batch:M}:x=M[0],this[Tt]=!1,!c){var L=this._messageCenter;if(L.trigger(x.type,x),C){var I={type:"selectchanged",escapeConnect:p,selected:im(d),isFromClick:h.isFromClick||!1,fromAction:h.type,fromActionPayload:h};L.trigger(I.type,I)}}},ki=function(h){for(var c=this._pendingActions;c.length;){var v=c.shift();Rs.call(this,v,h)}},Bi=function(h){!h&&this.trigger("updated")},gv=function(h,c){h.on("rendered",function(v){c.trigger("rendered",v),h.animation.isFinished()&&!c[Rt]&&!c._scheduler.unfinished&&!c._pendingActions.length&&c.trigger("finished")})},yv=function(h,c){h.on("mouseover",function(v){var d=v.target,y=Qi(d,gu);y&&(tm(y,v,c._api),Yt(c))}).on("mouseout",function(v){var d=v.target,y=Qi(d,gu);y&&(em(y,v,c._api),Yt(c))}).on("click",function(v){var d=v.target,y=Qi(d,function(_){return rt(_).dataIndex!=null},!0);if(y){var p=y.selected?"unselect":"select",g=rt(y);c._api.dispatchAction({type:p,dataType:g.dataType,dataIndexInside:g.dataIndex,seriesIndex:g.seriesIndex,isFromClick:!0})}})};function e(h){h.clearColorPalette(),h.eachSeries(function(c){c.clearColorPalette()})}function i(h){var c=[],v=[],d=!1;if(h.eachComponent(function(_,m){var w=m.get("zlevel")||0,T=m.get("z")||0,S=m.getZLevelKey();d=d||!!S,(_==="series"?v:c).push({zlevel:w,z:T,idx:m.componentIndex,type:_,key:S})}),d){var y=c.concat(v),p,g;ma(y,function(_,m){return _.zlevel===m.zlevel?_.z-m.z:_.zlevel-m.zlevel}),D(y,function(_){var m=h.getComponent(_.type,_.idx),w=_.zlevel,T=_.key;p!=null&&(w=Math.max(p,w)),T?(w===p&&T!==g&&w++,g=T):g&&(w===p&&w++,g=""),p=w,m.setZLevel(w)})}}Es=function(h,c,v,d,y){i(c),_v(h,c,v,d,y),D(h._chartsViews,function(p){p.__alive=!1}),sa(h,c,v,d,y),D(h._chartsViews,function(p){p.__alive||p.remove(c,v)})},_v=function(h,c,v,d,y,p){D(p||h._componentsViews,function(g){var _=g.__model;l(_,g),g.render(_,c,v,d),s(_,g),f(_,g)})},sa=function(h,c,v,d,y,p){var g=h._scheduler;y=O(y||{},{updatedSeries:c.getSeries()}),ne.trigger("series:beforeupdate",c,v,y);var _=!1;c.eachSeries(function(m){var w=h._chartsMap[m.__viewId];w.__alive=!0;var T=w.renderTask;g.updatePayload(T,d),l(m,w),p&&p.get(m.uid)&&T.dirty(),T.perform(g.getPerformArgs(T))&&(_=!0),w.group.silent=!!m.get("silent"),o(m,w),Jf(m)}),g.unfinished=_||g.unfinished,ne.trigger("series:layoutlabels",c,v,y),ne.trigger("series:transition",c,v,y),c.eachSeries(function(m){var w=h._chartsMap[m.__viewId];s(m,w),f(m,w)}),a(h,c),ne.trigger("series:afterupdate",c,v,y)},Yt=function(h){h[Ms]=!0,h.getZr().wakeUp()},wv=function(h){h[Ms]&&(h.getZr().storage.traverse(function(c){an(c)||n(c)}),h[Ms]=!1)};function n(h){for(var c=[],v=h.currentStates,d=0;d<v.length;d++){var y=v[d];y==="emphasis"||y==="blur"||y==="select"||c.push(y)}h.selected&&h.states.select&&c.push("select"),h.hoverState===vo&&h.states.emphasis?c.push("emphasis"):h.hoverState===ho&&h.states.blur&&c.push("blur"),h.useStates(c)}function a(h,c){var v=h._zr,d=v.storage,y=0;d.traverse(function(p){p.isGroup||y++}),y>c.get("hoverLayerThreshold")&&!H.node&&!H.worker&&c.eachSeries(function(p){if(!p.preventUsingHoverLayer){var g=h._chartsMap[p.__viewId];g.__alive&&g.eachRendered(function(_){_.states.emphasis&&(_.states.emphasis.hoverLayer=!0)})}})}function o(h,c){var v=h.get("blendMode")||null;c.eachRendered(function(d){d.isGroup||(d.style.blend=v)})}function s(h,c){if(!h.preventAutoZ){var v=h.get("z")||0,d=h.get("zlevel")||0;c.eachRendered(function(y){return u(y,v,d,-1/0),!0})}}function u(h,c,v,d){var y=h.getTextContent(),p=h.getTextGuideLine(),g=h.isGroup;if(g)for(var _=h.childrenRef(),m=0;m<_.length;m++)d=Math.max(u(_[m],c,v,d),d);else h.z=c,h.zlevel=v,d=Math.max(h.z2,d);if(y&&(y.z=c,y.zlevel=v,isFinite(d)&&(y.z2=d+2)),p){var w=h.textGuideLineConfig;p.z=c,p.zlevel=v,isFinite(d)&&(p.z2=d+(w&&w.showAbove?1:-1))}return d}function l(h,c){c.eachRendered(function(v){if(!an(v)){var d=v.getTextContent(),y=v.getTextGuideLine();v.stateTransition&&(v.stateTransition=null),d&&d.stateTransition&&(d.stateTransition=null),y&&y.stateTransition&&(y.stateTransition=null),v.hasState()?(v.prevStates=v.currentStates,v.clearStates()):v.prevStates&&(v.prevStates=null)}})}function f(h,c){var v=h.getModel("stateAnimation"),d=h.isAnimationEnabled(),y=v.get("duration"),p=y>0?{duration:y,delay:v.get("delay"),easing:v.get("easing")}:null;c.eachRendered(function(g){if(g.states&&g.states.emphasis){if(an(g))return;if(g instanceof nt&&um(g),g.__dirty){var _=g.prevStates;_&&g.useStates(_)}if(d){g.stateTransition=p;var m=g.getTextContent(),w=g.getTextGuideLine();m&&(m.stateTransition=p),w&&(w.stateTransition=p)}g.__dirty&&n(g)}})}mv=function(h){return new(function(c){N(v,c);function v(){return c!==null&&c.apply(this,arguments)||this}return v.prototype.getCoordinateSystems=function(){return h._coordSysMgr.getCoordinateSystems()},v.prototype.getComponentByElement=function(d){for(;d;){var y=d.__ecComponentInfo;if(y!=null)return h._model.getComponent(y.mainType,y.index);d=d.parent}},v.prototype.enterEmphasis=function(d,y){hu(d,y),Yt(h)},v.prototype.leaveEmphasis=function(d,y){vu(d,y),Yt(h)},v.prototype.enterBlur=function(d){J0(d),Yt(h)},v.prototype.leaveBlur=function(d){od(d),Yt(h)},v.prototype.enterSelect=function(d){sd(d),Yt(h)},v.prototype.leaveSelect=function(d){ud(d),Yt(h)},v.prototype.getModel=function(){return h.getModel()},v.prototype.getViewOfComponentModel=function(d){return h.getViewOfComponentModel(d)},v.prototype.getViewOfSeriesModel=function(d){return h.getViewOfSeriesModel(d)},v}(Kd))(h)},Up=function(h){function c(v,d){for(var y=0;y<v.length;y++){var p=v[y];p[As]=d}}D(un,function(v,d){h._messageCenter.on(d,function(y){if(Sv[h.group]&&h[As]!==pv){if(y&&y.escapeConnect)return;var p=h.makeActionFromEvent(y),g=[];D(ln,function(_){_!==h&&_.group===h.group&&g.push(_)}),c(g,pv),D(g,function(_){_[As]!==nT&&_.dispatchAction(p)}),c(g,aT)}})})}}(),t}(be),Nl=Yp.prototype;Nl.on=zp("on");Nl.off=zp("off");Nl.one=function(r,t,e){var i=this;function n(){for(var a=[],o=0;o<arguments.length;o++)a[o]=arguments[o];t&&t.apply&&t.apply(this,a),i.off(r,n)}this.on.call(this,r,n,e)};var oT=["click","dblclick","mouseover","mouseout","mousemove","mousedown","mouseup","globalout","contextmenu"];var to={},un={},Ou=[],ku=[],eo=[],Xp={},Bu={},ln={},Sv={},sT=+new Date-0,Fl="_echarts_instance_";function uT(r,t,e){var i=!(e&&e.ssr);if(i){var n=lT(r);if(n)return n}var a=new Yp(r,t,e);return a.id="ec_"+sT++,ln[a.id]=a,i&&Yc(r,Fl,a.id),Up(a),ne.trigger("afterinit",a),a}function lT(r){return ln[G_(r,Fl)]}function $p(r,t){Xp[r]=t}function qp(r){at(ku,r)<0&&ku.push(r)}function Zp(r,t){Hl(Ou,r,t,KS)}function fT(r){zl("afterinit",r)}function hT(r){zl("afterupdate",r)}function zl(r,t){ne.on(r,t)}function bi(r,t,e){K(t)&&(e=t,t="");var i=F(r)?r.type:[r,r={event:t}][0];r.event=(r.event||i).toLowerCase(),t=r.event,!un[t]&&(Te(dv.test(i)&&dv.test(t)),to[i]||(to[i]={action:e,actionInfo:r}),un[t]=i)}function vT(r,t){Qd.register(r,t)}function cT(r,t){Hl(eo,r,t,Bp,"layout")}function Hr(r,t){Hl(eo,r,t,Np,"visual")}var Tv=[];function Hl(r,t,e,i,n){if((K(t)||F(t))&&(e=t,t=i),!(at(Tv,e)>=0)){Tv.push(e);var a=bp.wrapStageHandler(e,n);a.__prio=t,a.__raw=e,r.push(a)}}function Kp(r,t){Bu[r]=t}function dT(r,t,e){var i=YS("registerMap");i&&i(r,t,e)}var pT=Sw;Hr(Bl,Kw);Hr(Mo,Qw);Hr(Mo,Jw);Hr(Bl,hS);Hr(Mo,vS);Hr(Fp,WS);qp(jd);Zp(qS,tw);Kp("default",jw);bi({type:Or,event:Or,update:Or},Ft);bi({type:Da,event:Da,update:Da},Ft);bi({type:en,event:en,update:en},Ft);bi({type:Ma,event:Ma,update:Ma},Ft);bi({type:rn,event:rn,update:rn},Ft);$p("light",lS);$p("dark",Mp);function Ni(r){return r==null?0:r.length||1}function bv(r){return r}var gT=function(){function r(t,e,i,n,a,o){this._old=t,this._new=e,this._oldKeyGetter=i||bv,this._newKeyGetter=n||bv,this.context=a,this._diffModeMultiple=o==="multiple"}return r.prototype.add=function(t){return this._add=t,this},r.prototype.update=function(t){return this._update=t,this},r.prototype.updateManyToOne=function(t){return this._updateManyToOne=t,this},r.prototype.updateOneToMany=function(t){return this._updateOneToMany=t,this},r.prototype.updateManyToMany=function(t){return this._updateManyToMany=t,this},r.prototype.remove=function(t){return this._remove=t,this},r.prototype.execute=function(){this[this._diffModeMultiple?"_executeMultiple":"_executeOneToOne"]()},r.prototype._executeOneToOne=function(){var t=this._old,e=this._new,i={},n=new Array(t.length),a=new Array(e.length);this._initIndexMap(t,null,n,"_oldKeyGetter"),this._initIndexMap(e,i,a,"_newKeyGetter");for(var o=0;o<t.length;o++){var s=n[o],u=i[s],l=Ni(u);if(l>1){var f=u.shift();u.length===1&&(i[s]=u[0]),this._update&&this._update(f,o)}else l===1?(i[s]=null,this._update&&this._update(u,o)):this._remove&&this._remove(o)}this._performRestAdd(a,i)},r.prototype._executeMultiple=function(){var t=this._old,e=this._new,i={},n={},a=[],o=[];this._initIndexMap(t,i,a,"_oldKeyGetter"),this._initIndexMap(e,n,o,"_newKeyGetter");for(var s=0;s<a.length;s++){var u=a[s],l=i[u],f=n[u],h=Ni(l),c=Ni(f);if(h>1&&c===1)this._updateManyToOne&&this._updateManyToOne(f,l),n[u]=null;else if(h===1&&c>1)this._updateOneToMany&&this._updateOneToMany(f,l),n[u]=null;else if(h===1&&c===1)this._update&&this._update(f,l),n[u]=null;else if(h>1&&c>1)this._updateManyToMany&&this._updateManyToMany(f,l),n[u]=null;else if(h>1)for(var v=0;v<h;v++)this._remove&&this._remove(l[v]);else this._remove&&this._remove(l)}this._performRestAdd(o,n)},r.prototype._performRestAdd=function(t,e){for(var i=0;i<t.length;i++){var n=t[i],a=e[n],o=Ni(a);if(o>1)for(var s=0;s<o;s++)this._add&&this._add(a[s]);else o===1&&this._add&&this._add(a);e[n]=null}},r.prototype._initIndexMap=function(t,e,i,n){for(var a=this._diffModeMultiple,o=0;o<t.length;o++){var s="_ec_"+this[n](t[o],o);if(a||(i[o]=s),!!e){var u=e[s],l=Ni(u);l===0?(e[s]=o,a&&i.push(s)):l===1?e[s]=[u,o]:u.push(o)}}},r}(),yT=function(){function r(t,e){this._encode=t,this._schema=e}return r.prototype.get=function(){return{fullDimensions:this._getFullDimensionNames(),encode:this._encode}},r.prototype._getFullDimensionNames=function(){return this._cachedDimNames||(this._cachedDimNames=this._schema?this._schema.makeOutputDimensionNames():[]),this._cachedDimNames},r}();function _T(r,t){var e={},i=e.encode={},n=X(),a=[],o=[],s={};D(r.dimensions,function(c){var v=r.getDimensionInfo(c),d=v.coordDim;if(d){var y=v.coordDimIndex;Os(i,d)[y]=c,v.isExtraCoord||(n.set(d,1),mT(v.type)&&(a[0]=c),Os(s,d)[y]=r.getDimensionIndex(v.name)),v.defaultTooltip&&o.push(c)}Ud.each(function(p,g){var _=Os(i,g),m=v.otherDims[g];m!=null&&m!==!1&&(_[m]=v.name)})});var u=[],l={};n.each(function(c,v){var d=i[v];l[v]=d[0],u=u.concat(d)}),e.dataDimsOnCoord=u,e.dataDimIndicesOnCoord=Y(u,function(c){return r.getDimensionInfo(c).storeDimIndex}),e.encodeFirstDimNotExtra=l;var f=i.label;f&&f.length&&(a=f.slice());var h=i.tooltip;return h&&h.length?o=h.slice():o.length||(o=a.slice()),i.defaultedLabel=a,i.defaultedTooltip=o,e.userOutput=new yT(s,t),e}function Os(r,t){return r.hasOwnProperty(t)||(r[t]=[]),r[t]}function eD(r){return r==="category"?"ordinal":r==="time"?"time":"float"}function mT(r){return!(r==="ordinal"||r==="time")}var Ia=function(){function r(t){this.otherDims={},t!=null&&O(this,t)}return r}(),wT=mt(),ST={float:"f",int:"i",ordinal:"o",number:"n",time:"t"},Qp=function(){function r(t){this.dimensions=t.dimensions,this._dimOmitted=t.dimensionOmitted,this.source=t.source,this._fullDimCount=t.fullDimensionCount,this._updateDimOmitted(t.dimensionOmitted)}return r.prototype.isDimensionOmitted=function(){return this._dimOmitted},r.prototype._updateDimOmitted=function(t){this._dimOmitted=t,t&&(this._dimNameMap||(this._dimNameMap=tg(this.source)))},r.prototype.getSourceDimensionIndex=function(t){return W(this._dimNameMap.get(t),-1)},r.prototype.getSourceDimension=function(t){var e=this.source.dimensionsDefine;if(e)return e[t]},r.prototype.makeStoreSchema=function(){for(var t=this._fullDimCount,e=rp(this.source),i=!eg(t),n="",a=[],o=0,s=0;o<t;o++){var u=void 0,l=void 0,f=void 0,h=this.dimensions[s];if(h&&h.storeDimIndex===o)u=e?h.name:null,l=h.type,f=h.ordinalMeta,s++;else{var c=this.getSourceDimension(o);c&&(u=e?c.name:null,l=c.type)}a.push({property:u,type:l,ordinalMeta:f}),e&&u!=null&&(!h||!h.isCalculationCoord)&&(n+=i?u.replace(/\`/g,"`1").replace(/\$/g,"`2"):u),n+="$",n+=ST[l]||"f",f&&(n+=f.uid),n+="$"}var v=this.source,d=[v.seriesLayoutBy,v.startIndex,n].join("$$");return{dimensions:a,hash:d}},r.prototype.makeOutputDimensionNames=function(){for(var t=[],e=0,i=0;e<this._fullDimCount;e++){var n=void 0,a=this.dimensions[i];if(a&&a.storeDimIndex===e)a.isCalculationCoord||(n=a.name),i++;else{var o=this.getSourceDimension(e);o&&(n=o.name)}t.push(n)}return t},r.prototype.appendCalculationDimension=function(t){this.dimensions.push(t),t.isCalculationCoord=!0,this._fullDimCount++,this._updateDimOmitted(!0)},r}();function Jp(r){return r instanceof Qp}function jp(r){for(var t=X(),e=0;e<(r||[]).length;e++){var i=r[e],n=F(i)?i.name:i;n!=null&&t.get(n)==null&&t.set(n,e)}return t}function tg(r){var t=wT(r);return t.dimNameMap||(t.dimNameMap=jp(r.dimensionsDefine))}function eg(r){return r>30}var Fi=F,Ge=Y,TT=typeof Int32Array>"u"?Array:Int32Array,bT="e\0\0",xv=-1,xT=["hasItemOption","_nameList","_idList","_invertedIndicesMap","_dimSummary","userOutput","_rawData","_dimValueGetter","_nameDimIdx","_idDimIdx","_nameRepeatCount"],CT=["_approximateExtent"],Cv,ua,zi,Hi,ks,Gi,Bs,DT=function(){function r(t,e){this.type="list",this._dimOmitted=!1,this._nameList=[],this._idList=[],this._visual={},this._layout={},this._itemVisuals=[],this._itemLayouts=[],this._graphicEls=[],this._approximateExtent={},this._calculationInfo={},this.hasItemOption=!1,this.TRANSFERABLE_METHODS=["cloneShallow","downSample","minmaxDownSample","lttbDownSample","map"],this.CHANGABLE_METHODS=["filterSelf","selectRange"],this.DOWNSAMPLE_METHODS=["downSample","minmaxDownSample","lttbDownSample"];var i,n=!1;Jp(t)?(i=t.dimensions,this._dimOmitted=t.isDimensionOmitted(),this._schema=t):(n=!0,i=t),i=i||["x","y"];for(var a={},o=[],s={},u=!1,l={},f=0;f<i.length;f++){var h=i[f],c=B(h)?new Ia({name:h}):h instanceof Ia?h:new Ia(h),v=c.name;c.type=c.type||"float",c.coordDim||(c.coordDim=v,c.coordDimIndex=0);var d=c.otherDims=c.otherDims||{};o.push(v),a[v]=c,l[v]!=null&&(u=!0),c.createInvertedIndices&&(s[v]=[]),d.itemName===0&&(this._nameDimIdx=f),d.itemId===0&&(this._idDimIdx=f),n&&(c.storeDimIndex=f)}if(this.dimensions=o,this._dimInfos=a,this._initGetDimensionInfo(u),this.hostModel=e,this._invertedIndicesMap=s,this._dimOmitted){var y=this._dimIdxToName=X();D(o,function(p){y.set(a[p].storeDimIndex,p)})}}return r.prototype.getDimension=function(t){var e=this._recognizeDimIndex(t);if(e==null)return t;if(e=t,!this._dimOmitted)return this.dimensions[e];var i=this._dimIdxToName.get(e);if(i!=null)return i;var n=this._schema.getSourceDimension(e);if(n)return n.name},r.prototype.getDimensionIndex=function(t){var e=this._recognizeDimIndex(t);if(e!=null)return e;if(t==null)return-1;var i=this._getDimInfo(t);return i?i.storeDimIndex:this._dimOmitted?this._schema.getSourceDimensionIndex(t):-1},r.prototype._recognizeDimIndex=function(t){if(ut(t)||t!=null&&!isNaN(t)&&!this._getDimInfo(t)&&(!this._dimOmitted||this._schema.getSourceDimensionIndex(t)<0))return+t},r.prototype._getStoreDimIndex=function(t){var e=this.getDimensionIndex(t);return e},r.prototype.getDimensionInfo=function(t){return this._getDimInfo(this.getDimension(t))},r.prototype._initGetDimensionInfo=function(t){var e=this._dimInfos;this._getDimInfo=t?function(i){return e.hasOwnProperty(i)?e[i]:void 0}:function(i){return e[i]}},r.prototype.getDimensionsOnCoord=function(){return this._dimSummary.dataDimsOnCoord.slice()},r.prototype.mapDimension=function(t,e){var i=this._dimSummary;if(e==null)return i.encodeFirstDimNotExtra[t];var n=i.encode[t];return n?n[e]:null},r.prototype.mapDimensionsAll=function(t){var e=this._dimSummary,i=e.encode[t];return(i||[]).slice()},r.prototype.getStore=function(){return this._store},r.prototype.initData=function(t,e,i){var n=this,a;if(t instanceof xu&&(a=t),!a){var o=this.dimensions,s=Il(t)||zt(t)?new ip(t,o.length):t;a=new xu;var u=Ge(o,function(l){return{type:n._dimInfos[l].type,property:l}});a.initData(s,u,i)}this._store=a,this._nameList=(e||[]).slice(),this._idList=[],this._nameRepeatCount={},this._doInit(0,a.count()),this._dimSummary=_T(this,this._schema),this.userOutput=this._dimSummary.userOutput},r.prototype.appendData=function(t){var e=this._store.appendData(t);this._doInit(e[0],e[1])},r.prototype.appendValues=function(t,e){var i=this._store.appendValues(t,e&&e.length),n=i.start,a=i.end,o=this._shouldMakeIdFromName();if(this._updateOrdinalMeta(),e)for(var s=n;s<a;s++){var u=s-n;this._nameList[s]=e[u],o&&Bs(this,s)}},r.prototype._updateOrdinalMeta=function(){for(var t=this._store,e=this.dimensions,i=0;i<e.length;i++){var n=this._dimInfos[e[i]];n.ordinalMeta&&t.collectOrdinalMeta(n.storeDimIndex,n.ordinalMeta)}},r.prototype._shouldMakeIdFromName=function(){var t=this._store.getProvider();return this._idDimIdx==null&&t.getSource().sourceFormat!==qe&&!t.fillStorage},r.prototype._doInit=function(t,e){if(!(t>=e)){var i=this._store,n=i.getProvider();this._updateOrdinalMeta();var a=this._nameList,o=this._idList,s=n.getSource().sourceFormat,u=s===xe;if(u&&!n.pure)for(var l=[],f=t;f<e;f++){var h=n.getItem(f,l);if(!this.hasItemOption&&L_(h)&&(this.hasItemOption=!0),h){var c=h.name;a[f]==null&&c!=null&&(a[f]=se(c,null));var v=h.id;o[f]==null&&v!=null&&(o[f]=se(v,null))}}if(this._shouldMakeIdFromName())for(var f=t;f<e;f++)Bs(this,f);Cv(this)}},r.prototype.getApproximateExtent=function(t){return this._approximateExtent[t]||this._store.getDataExtent(this._getStoreDimIndex(t))},r.prototype.setApproximateExtent=function(t,e){e=this.getDimension(e),this._approximateExtent[e]=t.slice()},r.prototype.getCalculationInfo=function(t){return this._calculationInfo[t]},r.prototype.setCalculationInfo=function(t,e){Fi(t)?O(this._calculationInfo,t):this._calculationInfo[t]=e},r.prototype.getName=function(t){var e=this.getRawIndex(t),i=this._nameList[e];return i==null&&this._nameDimIdx!=null&&(i=zi(this,this._nameDimIdx,e)),i==null&&(i=""),i},r.prototype._getCategory=function(t,e){var i=this._store.get(t,e),n=this._store.getOrdinalMeta(t);return n?n.categories[i]:i},r.prototype.getId=function(t){return ua(this,this.getRawIndex(t))},r.prototype.count=function(){return this._store.count()},r.prototype.get=function(t,e){var i=this._store,n=this._dimInfos[t];if(n)return i.get(n.storeDimIndex,e)},r.prototype.getByRawIndex=function(t,e){var i=this._store,n=this._dimInfos[t];if(n)return i.getByRawIndex(n.storeDimIndex,e)},r.prototype.getIndices=function(){return this._store.getIndices()},r.prototype.getDataExtent=function(t){return this._store.getDataExtent(this._getStoreDimIndex(t))},r.prototype.getSum=function(t){return this._store.getSum(this._getStoreDimIndex(t))},r.prototype.getMedian=function(t){return this._store.getMedian(this._getStoreDimIndex(t))},r.prototype.getValues=function(t,e){var i=this,n=this._store;return k(t)?n.getValues(Ge(t,function(a){return i._getStoreDimIndex(a)}),e):n.getValues(t)},r.prototype.hasValue=function(t){for(var e=this._dimSummary.dataDimIndicesOnCoord,i=0,n=e.length;i<n;i++)if(isNaN(this._store.get(e[i],t)))return!1;return!0},r.prototype.indexOfName=function(t){for(var e=0,i=this._store.count();e<i;e++)if(this.getName(e)===t)return e;return-1},r.prototype.getRawIndex=function(t){return this._store.getRawIndex(t)},r.prototype.indexOfRawIndex=function(t){return this._store.indexOfRawIndex(t)},r.prototype.rawIndexOf=function(t,e){var i=t&&this._invertedIndicesMap[t],n=i&&i[e];return n==null||isNaN(n)?xv:n},r.prototype.indicesOfNearest=function(t,e,i){return this._store.indicesOfNearest(this._getStoreDimIndex(t),e,i)},r.prototype.each=function(t,e,i){K(t)&&(i=e,e=t,t=[]);var n=i||this,a=Ge(Hi(t),this._getStoreDimIndex,this);this._store.each(a,n?lt(e,n):e)},r.prototype.filterSelf=function(t,e,i){K(t)&&(i=e,e=t,t=[]);var n=i||this,a=Ge(Hi(t),this._getStoreDimIndex,this);return this._store=this._store.filter(a,n?lt(e,n):e),this},r.prototype.selectRange=function(t){var e=this,i={},n=ft(t);return D(n,function(a){var o=e._getStoreDimIndex(a);i[o]=t[a]}),this._store=this._store.selectRange(i),this},r.prototype.mapArray=function(t,e,i){K(t)&&(i=e,e=t,t=[]),i=i||this;var n=[];return this.each(t,function(){n.push(e&&e.apply(this,arguments))},i),n},r.prototype.map=function(t,e,i,n){var a=i||n||this,o=Ge(Hi(t),this._getStoreDimIndex,this),s=Gi(this);return s._store=this._store.map(o,a?lt(e,a):e),s},r.prototype.modify=function(t,e,i,n){var a=i||n||this,o=Ge(Hi(t),this._getStoreDimIndex,this);this._store.modify(o,a?lt(e,a):e)},r.prototype.downSample=function(t,e,i,n){var a=Gi(this);return a._store=this._store.downSample(this._getStoreDimIndex(t),e,i,n),a},r.prototype.minmaxDownSample=function(t,e){var i=Gi(this);return i._store=this._store.minmaxDownSample(this._getStoreDimIndex(t),e),i},r.prototype.lttbDownSample=function(t,e){var i=Gi(this);return i._store=this._store.lttbDownSample(this._getStoreDimIndex(t),e),i},r.prototype.getRawDataItem=function(t){return this._store.getRawDataItem(t)},r.prototype.getItemModel=function(t){var e=this.hostModel,i=this.getRawDataItem(t);return new ht(i,e,e&&e.ecModel)},r.prototype.diff=function(t){var e=this;return new gT(t?t.getStore().getIndices():[],this.getStore().getIndices(),function(i){return ua(t,i)},function(i){return ua(e,i)})},r.prototype.getVisual=function(t){var e=this._visual;return e&&e[t]},r.prototype.setVisual=function(t,e){this._visual=this._visual||{},Fi(t)?O(this._visual,t):this._visual[t]=e},r.prototype.getItemVisual=function(t,e){var i=this._itemVisuals[t],n=i&&i[e];return n??this.getVisual(e)},r.prototype.hasItemVisual=function(){return this._itemVisuals.length>0},r.prototype.ensureUniqueItemVisual=function(t,e){var i=this._itemVisuals,n=i[t];n||(n=i[t]={});var a=n[e];return a==null&&(a=this.getVisual(e),k(a)?a=a.slice():Fi(a)&&(a=O({},a)),n[e]=a),a},r.prototype.setItemVisual=function(t,e,i){var n=this._itemVisuals[t]||{};this._itemVisuals[t]=n,Fi(e)?O(n,e):n[e]=i},r.prototype.clearAllVisual=function(){this._visual={},this._itemVisuals=[]},r.prototype.setLayout=function(t,e){Fi(t)?O(this._layout,t):this._layout[t]=e},r.prototype.getLayout=function(t){return this._layout[t]},r.prototype.getItemLayout=function(t){return this._itemLayouts[t]},r.prototype.setItemLayout=function(t,e,i){this._itemLayouts[t]=i?O(this._itemLayouts[t]||{},e):e},r.prototype.clearItemLayouts=function(){this._itemLayouts.length=0},r.prototype.setItemGraphicEl=function(t,e){var i=this.hostModel&&this.hostModel.seriesIndex;V0(i,this.dataType,t,e),this._graphicEls[t]=e},r.prototype.getItemGraphicEl=function(t){return this._graphicEls[t]},r.prototype.eachItemGraphicEl=function(t,e){D(this._graphicEls,function(i,n){i&&t&&t.call(e,i,n)})},r.prototype.cloneShallow=function(t){return t||(t=new r(this._schema?this._schema:Ge(this.dimensions,this._getDimInfo,this),this.hostModel)),ks(t,this),t._store=this._store,t},r.prototype.wrapMethod=function(t,e){var i=this[t];K(i)&&(this.__wrappedMethods=this.__wrappedMethods||[],this.__wrappedMethods.push(t),this[t]=function(){var n=i.apply(this,arguments);return e.apply(this,[n].concat(qu(arguments)))})},r.internalField=function(){Cv=function(t){var e=t._invertedIndicesMap;D(e,function(i,n){var a=t._dimInfos[n],o=a.ordinalMeta,s=t._store;if(o){i=e[n]=new TT(o.categories.length);for(var u=0;u<i.length;u++)i[u]=xv;for(var u=0;u<s.count();u++)i[s.get(a.storeDimIndex,u)]=u}})},zi=function(t,e,i){return se(t._getCategory(e,i),null)},ua=function(t,e){var i=t._idList[e];return i==null&&t._idDimIdx!=null&&(i=zi(t,t._idDimIdx,e)),i==null&&(i=bT+e),i},Hi=function(t){return k(t)||(t=t!=null?[t]:[]),t},Gi=function(t){var e=new r(t._schema?t._schema:Ge(t.dimensions,t._getDimInfo,t),t.hostModel);return ks(e,t),e},ks=function(t,e){D(xT.concat(e.__wrappedMethods||[]),function(i){e.hasOwnProperty(i)&&(t[i]=e[i])}),t.__wrappedMethods=e.__wrappedMethods,D(CT,function(i){t[i]=Z(e[i])}),t._calculationInfo=O({},e._calculationInfo)},Bs=function(t,e){var i=t._nameList,n=t._idList,a=t._nameDimIdx,o=t._idDimIdx,s=i[e],u=n[e];if(s==null&&a!=null&&(i[e]=s=zi(t,a,e)),u==null&&o!=null&&(n[e]=u=zi(t,o,e)),u==null&&s!=null){var l=t._nameRepeatCount,f=l[s]=(l[s]||0)+1;u=s,f>1&&(u+="__ec__"+f),n[e]=u}}}(),r}();function MT(r,t){Il(r)||(r=tp(r)),t=t||{};var e=t.coordDimensions||[],i=t.dimensionsDefine||r.dimensionsDefine||[],n=X(),a=[],o=PT(r,e,i,t.dimensionsCount),s=t.canOmitUnusedDimensions&&eg(o),u=i===r.dimensionsDefine,l=u?tg(r):jp(i),f=t.encodeDefine;!f&&t.encodeDefaulter&&(f=t.encodeDefaulter(r,o));for(var h=X(f),c=new lp(o),v=0;v<c.length;v++)c[v]=-1;function d(x){var C=c[x];if(C<0){var A=i[x],L=F(A)?A:{name:A},I=new Ia,P=L.name;P!=null&&l.get(P)!=null&&(I.name=I.displayName=P),L.type!=null&&(I.type=L.type),L.displayName!=null&&(I.displayName=L.displayName);var R=a.length;return c[x]=R,I.storeDimIndex=x,a.push(I),I}return a[C]}if(!s)for(var v=0;v<o;v++)d(v);h.each(function(x,C){var A=_t(x).slice();if(A.length===1&&!B(A[0])&&A[0]<0){h.set(C,!1);return}var L=h.set(C,[]);D(A,function(I,P){var R=B(I)?l.get(I):I;R!=null&&R<o&&(L[P]=R,p(d(R),C,P))})});var y=0;D(e,function(x){var C,A,L,I;if(B(x))C=x,I={};else{I=x,C=I.name;var P=I.ordinalMeta;I.ordinalMeta=null,I=O({},I),I.ordinalMeta=P,A=I.dimsDef,L=I.otherDims,I.name=I.coordDim=I.coordDimIndex=I.dimsDef=I.otherDims=null}var R=h.get(C);if(R!==!1){if(R=_t(R),!R.length)for(var E=0;E<(A&&A.length||1);E++){for(;y<o&&d(y).coordDim!=null;)y++;y<o&&R.push(y++)}D(R,function(U,z){var G=d(U);if(u&&I.type!=null&&(G.type=I.type),p(ot(G,I),C,z),G.name==null&&A){var $=A[z];!F($)&&($={name:$}),G.name=G.displayName=$.name,G.defaultTooltip=$.defaultTooltip}L&&ot(G.otherDims,L)})}});function p(x,C,A){Ud.get(C)!=null?x.otherDims[C]=A:(x.coordDim=C,x.coordDimIndex=A,n.set(C,!0))}var g=t.generateCoord,_=t.generateCoordCount,m=_!=null;_=g?_||1:0;var w=g||"value";function T(x){x.name==null&&(x.name=x.coordDim)}if(s)D(a,function(x){T(x)}),a.sort(function(x,C){return x.storeDimIndex-C.storeDimIndex});else for(var S=0;S<o;S++){var b=d(S),M=b.coordDim;M==null&&(b.coordDim=LT(w,n,m),b.coordDimIndex=0,(!g||_<=0)&&(b.isExtraCoord=!0),_--),T(b),b.type==null&&($d(r,S)===pt.Must||b.isExtraCoord&&(b.otherDims.itemName!=null||b.otherDims.seriesName!=null))&&(b.type="ordinal")}return AT(a),new Qp({source:r,dimensions:a,fullDimensionCount:o,dimensionOmitted:s})}function AT(r){for(var t=X(),e=0;e<r.length;e++){var i=r[e],n=i.name,a=t.get(n)||0;a>0&&(i.name=n+(a-1)),a++,t.set(n,a)}}function PT(r,t,e,i){var n=Math.max(r.dimensionsDetectedCount||1,t.length,e.length,i||0);return D(t,function(a){var o;F(a)&&(o=a.dimsDef)&&(n=Math.max(n,o.length))}),n}function LT(r,t,e){if(e||t.hasKey(r)){for(var i=0;t.hasKey(r+i);)i++;r+=i}return t.set(r,!0),r}function rD(r,t,e){e=e||{};var i=e.byIndex,n=e.stackedCoordDimension,a,o,s;IT(t)?a=t:(o=t.schema,a=o.dimensions,s=t.store);var u=!!(r&&r.get("stack")),l,f,h,c;if(D(a,function(_,m){B(_)&&(a[m]=_={name:_}),u&&!_.isExtraCoord&&(!i&&!l&&_.ordinalMeta&&(l=_),!f&&_.type!=="ordinal"&&_.type!=="time"&&(!n||n===_.coordDim)&&(f=_))}),f&&!i&&!l&&(i=!0),f){h="__\0ecstackresult_"+r.id,c="__\0ecstackedover_"+r.id,l&&(l.createInvertedIndices=!0);var v=f.coordDim,d=f.type,y=0;D(a,function(_){_.coordDim===v&&y++});var p={name:h,coordDim:v,coordDimIndex:y,type:d,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:a.length},g={name:c,coordDim:c,coordDimIndex:y+1,type:d,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:a.length+1};o?(s&&(p.storeDimIndex=s.ensureCalculationDimension(c,d),g.storeDimIndex=s.ensureCalculationDimension(h,d)),o.appendCalculationDimension(p),o.appendCalculationDimension(g)):(a.push(p),a.push(g))}return{stackedDimension:f&&f.name,stackedByDimension:l&&l.name,isStackedByIndex:i,stackedOverDimension:c,stackResultDimension:h}}function IT(r){return!Jp(r.schema)}function rg(r,t){return!!t&&t===r.getCalculationInfo("stackedDimension")}function RT(r,t){return rg(r,t)?r.getCalculationInfo("stackResultDimension"):t}var De=function(){function r(t){this._setting=t||{},this._extent=[1/0,-1/0]}return r.prototype.getSetting=function(t){return this._setting[t]},r.prototype.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1])},r.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},r.prototype.getExtent=function(){return this._extent.slice()},r.prototype.setExtent=function(t,e){var i=this._extent;isNaN(t)||(i[0]=t),isNaN(e)||(i[1]=e)},r.prototype.isInExtentRange=function(t){return this._extent[0]<=t&&this._extent[1]>=t},r.prototype.isBlank=function(){return this._isBlank},r.prototype.setBlank=function(t){this._isBlank=t},r}();uo(De);var ET=0,Dv=function(){function r(t){this.categories=t.categories||[],this._needCollect=t.needCollect,this._deduplication=t.deduplication,this.uid=++ET}return r.createByAxisModel=function(t){var e=t.option,i=e.data,n=i&&Y(i,OT);return new r({categories:n,needCollect:!n,deduplication:e.dedplication!==!1})},r.prototype.getOrdinal=function(t){return this._getOrCreateMap().get(t)},r.prototype.parseAndCollect=function(t){var e,i=this._needCollect;if(!B(t)&&!i)return t;if(i&&!this._deduplication)return e=this.categories.length,this.categories[e]=t,e;var n=this._getOrCreateMap();return e=n.get(t),e==null&&(i?(e=this.categories.length,this.categories[e]=t,n.set(t,e)):e=NaN),e},r.prototype._getOrCreateMap=function(){return this._map||(this._map=X(this.categories))},r}();function OT(r){return F(r)&&r.value!=null?r.value:r+""}function iD(r){return r.type==="interval"||r.type==="log"}function kT(r,t,e,i){var n={},a=r[1]-r[0],o=n.interval=zc(a/t);e!=null&&o<e&&(o=n.interval=e),i!=null&&o>i&&(o=n.interval=i);var s=n.intervalPrecision=ig(o),u=n.niceTickExtent=[oe(Math.ceil(r[0]/o)*o,s),oe(Math.floor(r[1]/o)*o,s)];return BT(u,r),n}function nD(r){var t=Math.pow(10,el(r)),e=r/t;return e?e===2?e=3:e===3?e=5:e*=2:e=1,oe(e*t)}function ig(r){return Le(r)+2}function Mv(r,t,e){r[t]=Math.max(Math.min(r[t],e[1]),e[0])}function BT(r,t){!isFinite(r[0])&&(r[0]=t[0]),!isFinite(r[1])&&(r[1]=t[1]),Mv(r,0,t),Mv(r,1,t),r[0]>r[1]&&(r[0]=r[1])}function Ao(r,t){return r>=t[0]&&r<=t[1]}function Po(r,t){return t[1]===t[0]?.5:(r-t[0])/(t[1]-t[0])}function Lo(r,t){return r*(t[1]-t[0])+t[0]}var Gl=function(r){N(t,r);function t(e){var i=r.call(this,e)||this;i.type="ordinal";var n=i.getSetting("ordinalMeta");return n||(n=new Dv({})),k(n)&&(n=new Dv({categories:Y(n,function(a){return F(a)?a.value:a})})),i._ordinalMeta=n,i._extent=i.getSetting("extent")||[0,n.categories.length-1],i}return t.prototype.parse=function(e){return e==null?NaN:B(e)?this._ordinalMeta.getOrdinal(e):Math.round(e)},t.prototype.contain=function(e){return e=this.parse(e),Ao(e,this._extent)&&this._ordinalMeta.categories[e]!=null},t.prototype.normalize=function(e){return e=this._getTickNumber(this.parse(e)),Po(e,this._extent)},t.prototype.scale=function(e){return e=Math.round(Lo(e,this._extent)),this.getRawOrdinalNumber(e)},t.prototype.getTicks=function(){for(var e=[],i=this._extent,n=i[0];n<=i[1];)e.push({value:n}),n++;return e},t.prototype.getMinorTicks=function(e){},t.prototype.setSortInfo=function(e){if(e==null){this._ordinalNumbersByTick=this._ticksByOrdinalNumber=null;return}for(var i=e.ordinalNumbers,n=this._ordinalNumbersByTick=[],a=this._ticksByOrdinalNumber=[],o=0,s=this._ordinalMeta.categories.length,u=Math.min(s,i.length);o<u;++o){var l=i[o];n[o]=l,a[l]=o}for(var f=0;o<s;++o){for(;a[f]!=null;)f++;n.push(f),a[f]=o}},t.prototype._getTickNumber=function(e){var i=this._ticksByOrdinalNumber;return i&&e>=0&&e<i.length?i[e]:e},t.prototype.getRawOrdinalNumber=function(e){var i=this._ordinalNumbersByTick;return i&&e>=0&&e<i.length?i[e]:e},t.prototype.getLabel=function(e){if(!this.isBlank()){var i=this.getRawOrdinalNumber(e.value),n=this._ordinalMeta.categories[i];return n==null?"":n+""}},t.prototype.count=function(){return this._extent[1]-this._extent[0]+1},t.prototype.unionExtentFromData=function(e,i){this.unionExtent(e.getApproximateExtent(i))},t.prototype.isInExtentRange=function(e){return e=this._getTickNumber(e),this._extent[0]<=e&&this._extent[1]>=e},t.prototype.getOrdinalMeta=function(){return this._ordinalMeta},t.prototype.calcNiceTicks=function(){},t.prototype.calcNiceExtent=function(){},t.type="ordinal",t}(De);De.registerClass(Gl);var xr=oe,Rn=function(r){N(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type="interval",e._interval=0,e._intervalPrecision=2,e}return t.prototype.parse=function(e){return e},t.prototype.contain=function(e){return Ao(e,this._extent)},t.prototype.normalize=function(e){return Po(e,this._extent)},t.prototype.scale=function(e){return Lo(e,this._extent)},t.prototype.setExtent=function(e,i){var n=this._extent;isNaN(e)||(n[0]=parseFloat(e)),isNaN(i)||(n[1]=parseFloat(i))},t.prototype.unionExtent=function(e){var i=this._extent;e[0]<i[0]&&(i[0]=e[0]),e[1]>i[1]&&(i[1]=e[1]),this.setExtent(i[0],i[1])},t.prototype.getInterval=function(){return this._interval},t.prototype.setInterval=function(e){this._interval=e,this._niceExtent=this._extent.slice(),this._intervalPrecision=ig(e)},t.prototype.getTicks=function(e){var i=this._interval,n=this._extent,a=this._niceExtent,o=this._intervalPrecision,s=[];if(!i)return s;var u=1e4;n[0]<a[0]&&(e?s.push({value:xr(a[0]-i,o)}):s.push({value:n[0]}));for(var l=a[0];l<=a[1]&&(s.push({value:l}),l=xr(l+i,o),l!==s[s.length-1].value);)if(s.length>u)return[];var f=s.length?s[s.length-1].value:a[1];return n[1]>f&&(e?s.push({value:xr(f+i,o)}):s.push({value:n[1]})),s},t.prototype.getMinorTicks=function(e){for(var i=this.getTicks(!0),n=[],a=this.getExtent(),o=1;o<i.length;o++){for(var s=i[o],u=i[o-1],l=0,f=[],h=s.value-u.value,c=h/e;l<e-1;){var v=xr(u.value+(l+1)*c);v>a[0]&&v<a[1]&&f.push(v),l++}n.push(f)}return n},t.prototype.getLabel=function(e,i){if(e==null)return"";var n=i&&i.precision;n==null?n=Le(e.value)||0:n==="auto"&&(n=this._intervalPrecision);var a=xr(e.value,n,!0);return Hd(a)},t.prototype.calcNiceTicks=function(e,i,n){e=e||5;var a=this._extent,o=a[1]-a[0];if(isFinite(o)){o<0&&(o=-o,a.reverse());var s=kT(a,e,i,n);this._intervalPrecision=s.intervalPrecision,this._interval=s.interval,this._niceExtent=s.niceTickExtent}},t.prototype.calcNiceExtent=function(e){var i=this._extent;if(i[0]===i[1])if(i[0]!==0){var n=Math.abs(i[0]);e.fixMax||(i[1]+=n/2),i[0]-=n/2}else i[1]=1;var a=i[1]-i[0];isFinite(a)||(i[0]=0,i[1]=1),this.calcNiceTicks(e.splitNumber,e.minInterval,e.maxInterval);var o=this._interval;e.fixMin||(i[0]=xr(Math.floor(i[0]/o)*o)),e.fixMax||(i[1]=xr(Math.ceil(i[1]/o)*o))},t.prototype.setNiceExtent=function(e,i){this._niceExtent=[e,i]},t.type="interval",t}(De);De.registerClass(Rn);var ng=typeof Float32Array<"u",NT=ng?Float32Array:Array;function Ns(r){return k(r)?ng?new Float32Array(r):r:new NT(r)}var Nu="__ec_stack_";function ag(r){return r.get("stack")||Nu+r.seriesIndex}function Vl(r){return r.dim+r.index}function aD(r){var t=[],e=r.axis,i="axis0";if(e.type==="category"){for(var n=e.getBandWidth(),a=0;a<r.count;a++)t.push(ot({bandWidth:n,axisKey:i,stackId:Nu+a},r));for(var o=ug(t),s=[],a=0;a<r.count;a++){var u=o[i][Nu+a];u.offsetCenter=u.offset+u.width/2,s.push(u)}return s}}function og(r,t){var e=[];return t.eachSeriesByType(r,function(i){lg(i)&&e.push(i)}),e}function FT(r){var t={};D(r,function(u){var l=u.coordinateSystem,f=l.getBaseAxis();if(!(f.type!=="time"&&f.type!=="value"))for(var h=u.getData(),c=f.dim+"_"+f.index,v=h.getDimensionIndex(h.mapDimension(f.dim)),d=h.getStore(),y=0,p=d.count();y<p;++y){var g=d.get(v,y);t[c]?t[c].push(g):t[c]=[g]}});var e={};for(var i in t)if(t.hasOwnProperty(i)){var n=t[i];if(n){n.sort(function(u,l){return u-l});for(var a=null,o=1;o<n.length;++o){var s=n[o]-n[o-1];s>0&&(a=a===null?s:Math.min(a,s))}e[i]=a}}return e}function sg(r){var t=FT(r),e=[];return D(r,function(i){var n=i.coordinateSystem,a=n.getBaseAxis(),o=a.getExtent(),s;if(a.type==="category")s=a.getBandWidth();else if(a.type==="value"||a.type==="time"){var u=a.dim+"_"+a.index,l=t[u],f=Math.abs(o[1]-o[0]),h=a.scale.getExtent(),c=Math.abs(h[1]-h[0]);s=l?f/c*l:f}else{var v=i.getData();s=Math.abs(o[1]-o[0])/v.count()}var d=ct(i.get("barWidth"),s),y=ct(i.get("barMaxWidth"),s),p=ct(i.get("barMinWidth")||(fg(i)?.5:1),s),g=i.get("barGap"),_=i.get("barCategoryGap");e.push({bandWidth:s,barWidth:d,barMaxWidth:y,barMinWidth:p,barGap:g,barCategoryGap:_,axisKey:Vl(a),stackId:ag(i)})}),ug(e)}function ug(r){var t={};D(r,function(i,n){var a=i.axisKey,o=i.bandWidth,s=t[a]||{bandWidth:o,remainedWidth:o,autoWidthCount:0,categoryGap:null,gap:"20%",stacks:{}},u=s.stacks;t[a]=s;var l=i.stackId;u[l]||s.autoWidthCount++,u[l]=u[l]||{width:0,maxWidth:0};var f=i.barWidth;f&&!u[l].width&&(u[l].width=f,f=Math.min(s.remainedWidth,f),s.remainedWidth-=f);var h=i.barMaxWidth;h&&(u[l].maxWidth=h);var c=i.barMinWidth;c&&(u[l].minWidth=c);var v=i.barGap;v!=null&&(s.gap=v);var d=i.barCategoryGap;d!=null&&(s.categoryGap=d)});var e={};return D(t,function(i,n){e[n]={};var a=i.stacks,o=i.bandWidth,s=i.categoryGap;if(s==null){var u=ft(a).length;s=Math.max(35-u*4,15)+"%"}var l=ct(s,o),f=ct(i.gap,1),h=i.remainedWidth,c=i.autoWidthCount,v=(h-l)/(c+(c-1)*f);v=Math.max(v,0),D(a,function(g){var _=g.maxWidth,m=g.minWidth;if(g.width){var w=g.width;_&&(w=Math.min(w,_)),m&&(w=Math.max(w,m)),g.width=w,h-=w+f*w,c--}else{var w=v;_&&_<w&&(w=Math.min(_,h)),m&&m>w&&(w=m),w!==v&&(g.width=w,h-=w+f*w,c--)}}),v=(h-l)/(c+(c-1)*f),v=Math.max(v,0);var d=0,y;D(a,function(g,_){g.width||(g.width=v),y=g,d+=g.width*(1+f)}),y&&(d-=y.width*f);var p=-d/2;D(a,function(g,_){e[n][_]=e[n][_]||{bandWidth:o,offset:p,width:g.width},p+=g.width*(1+f)})}),e}function zT(r,t,e){if(r&&t){var i=r[Vl(t)];return i}}function oD(r,t){var e=og(r,t),i=sg(e);D(e,function(n){var a=n.getData(),o=n.coordinateSystem,s=o.getBaseAxis(),u=ag(n),l=i[Vl(s)][u],f=l.offset,h=l.width;a.setLayout({bandWidth:l.bandWidth,offset:f,size:h})})}function sD(r){return{seriesType:r,plan:_p(),reset:function(t){if(lg(t)){var e=t.getData(),i=t.coordinateSystem,n=i.getBaseAxis(),a=i.getOtherAxis(n),o=e.getDimensionIndex(e.mapDimension(a.dim)),s=e.getDimensionIndex(e.mapDimension(n.dim)),u=t.get("showBackground",!0),l=e.mapDimension(a.dim),f=e.getCalculationInfo("stackResultDimension"),h=rg(e,l)&&!!e.getCalculationInfo("stackedOnSeries"),c=a.isHorizontal(),v=HT(n,a),d=fg(t),y=t.get("barMinHeight")||0,p=f&&e.getDimensionIndex(f),g=e.getLayout("size"),_=e.getLayout("offset");return{progress:function(m,w){for(var T=m.count,S=d&&Ns(T*3),b=d&&u&&Ns(T*3),M=d&&Ns(T),x=i.master.getRect(),C=c?x.width:x.height,A,L=w.getStore(),I=0;(A=m.next())!=null;){var P=L.get(h?p:o,A),R=L.get(s,A),E=v,U=void 0;h&&(U=+P-L.get(o,A));var z=void 0,G=void 0,$=void 0,it=void 0;if(c){var j=i.dataToPoint([P,R]);if(h){var wt=i.dataToPoint([U,R]);E=wt[0]}z=E,G=j[1]+_,$=j[0]-E,it=g,Math.abs($)<y&&($=($<0?-1:1)*y)}else{var j=i.dataToPoint([R,P]);if(h){var wt=i.dataToPoint([R,U]);E=wt[1]}z=j[0]+_,G=E,$=g,it=j[1]-E,Math.abs(it)<y&&(it=(it<=0?-1:1)*y)}d?(S[I]=z,S[I+1]=G,S[I+2]=c?$:it,b&&(b[I]=c?x.x:z,b[I+1]=c?G:x.y,b[I+2]=C),M[A]=A):w.setItemLayout(A,{x:z,y:G,width:$,height:it}),I+=3}d&&w.setLayout({largePoints:S,largeDataIndices:M,largeBackgroundPoints:b,valueAxisHorizontal:c})}}}}}}function lg(r){return r.coordinateSystem&&r.coordinateSystem.type==="cartesian2d"}function fg(r){return r.pipelineContext&&r.pipelineContext.large}function HT(r,t){var e=t.model.get("startValue");return e||(e=0),t.toGlobalCoord(t.dataToCoord(t.type==="log"?e>0?e:1:e))}var GT=function(r,t,e,i){for(;e<i;){var n=e+i>>>1;r[n][1]<t?e=n+1:i=n}return e},hg=function(r){N(t,r);function t(e){var i=r.call(this,e)||this;return i.type="time",i}return t.prototype.getLabel=function(e){var i=this.getSetting("useUTC");return wo(e.value,gh[S1(vi(this._minLevelUnit))]||gh.second,i,this.getSetting("locale"))},t.prototype.getFormattedLabel=function(e,i,n){var a=this.getSetting("useUTC"),o=this.getSetting("locale");return T1(e,i,n,o,a)},t.prototype.getTicks=function(){var e=this._interval,i=this._extent,n=[];if(!e)return n;n.push({value:i[0],level:0});var a=this.getSetting("useUTC"),o=qT(this._minLevelUnit,this._approxInterval,a,i);return n=n.concat(o),n.push({value:i[1],level:0}),n},t.prototype.calcNiceExtent=function(e){var i=this._extent;if(i[0]===i[1]&&(i[0]-=Jt,i[1]+=Jt),i[1]===-1/0&&i[0]===1/0){var n=new Date;i[1]=+new Date(n.getFullYear(),n.getMonth(),n.getDate()),i[0]=i[1]-Jt}this.calcNiceTicks(e.splitNumber,e.minInterval,e.maxInterval)},t.prototype.calcNiceTicks=function(e,i,n){e=e||10;var a=this._extent,o=a[1]-a[0];this._approxInterval=o/e,i!=null&&this._approxInterval<i&&(this._approxInterval=i),n!=null&&this._approxInterval>n&&(this._approxInterval=n);var s=la.length,u=Math.min(GT(la,this._approxInterval,0,s),s-1);this._interval=la[u][1],this._minLevelUnit=la[Math.max(u-1,0)][0]},t.prototype.parse=function(e){return ut(e)?e:+Re(e)},t.prototype.contain=function(e){return Ao(this.parse(e),this._extent)},t.prototype.normalize=function(e){return Po(this.parse(e),this._extent)},t.prototype.scale=function(e){return Lo(e,this._extent)},t.type="time",t}(Rn),la=[["second",bl],["minute",xl],["hour",on],["quarter-day",on*6],["half-day",on*12],["day",Jt*1.2],["half-week",Jt*3.5],["week",Jt*7],["month",Jt*31],["quarter",Jt*95],["half-year",ph/2],["year",ph]];function VT(r,t,e,i){var n=Re(t),a=Re(e),o=function(d){return yh(n,d,i)===yh(a,d,i)},s=function(){return o("year")},u=function(){return s()&&o("month")},l=function(){return u()&&o("day")},f=function(){return l()&&o("hour")},h=function(){return f()&&o("minute")},c=function(){return h()&&o("second")},v=function(){return c()&&o("millisecond")};switch(r){case"year":return s();case"month":return u();case"day":return l();case"hour":return f();case"minute":return h();case"second":return c();case"millisecond":return v()}}function WT(r,t){return r/=Jt,r>16?16:r>7.5?7:r>3.5?4:r>1.5?2:1}function UT(r){var t=30*Jt;return r/=t,r>6?6:r>3?3:r>2?2:1}function YT(r){return r/=on,r>12?12:r>6?6:r>3.5?4:r>2?2:1}function Av(r,t){return r/=t?xl:bl,r>30?30:r>20?20:r>15?15:r>10?10:r>5?5:r>2?2:1}function XT(r){return zc(r)}function $T(r,t,e){var i=new Date(r);switch(vi(t)){case"year":case"month":i[Od(e)](0);case"day":i[kd(e)](1);case"hour":i[Bd(e)](0);case"minute":i[Nd(e)](0);case"second":i[Fd(e)](0),i[zd(e)](0)}return i.getTime()}function qT(r,t,e,i){var n=1e4,a=Rd,o=0;function s(C,A,L,I,P,R,E){for(var U=new Date(A),z=A,G=U[I]();z<L&&z<=i[1];)E.push({value:z}),G+=C,U[P](G),z=U.getTime();E.push({value:z,notAdd:!0})}function u(C,A,L){var I=[],P=!A.length;if(!VT(vi(C),i[0],i[1],e)){P&&(A=[{value:$T(new Date(i[0]),C,e)},{value:i[1]}]);for(var R=0;R<A.length-1;R++){var E=A[R].value,U=A[R+1].value;if(E!==U){var z=void 0,G=void 0,$=void 0,it=!1;switch(C){case"year":z=Math.max(1,Math.round(t/Jt/365)),G=Cl(e),$=b1(e);break;case"half-year":case"quarter":case"month":z=UT(t),G=ci(e),$=Od(e);break;case"week":case"half-week":case"day":z=WT(t),G=So(e),$=kd(e),it=!0;break;case"half-day":case"quarter-day":case"hour":z=YT(t),G=yn(e),$=Bd(e);break;case"minute":z=Av(t,!0),G=To(e),$=Nd(e);break;case"second":z=Av(t,!1),G=bo(e),$=Fd(e);break;case"millisecond":z=XT(t),G=xo(e),$=zd(e);break}s(z,E,U,G,$,it,I),C==="year"&&L.length>1&&R===0&&L.unshift({value:L[0].value-z})}}for(var R=0;R<I.length;R++)L.push(I[R]);return I}}for(var l=[],f=[],h=0,c=0,v=0;v<a.length&&o++<n;++v){var d=vi(a[v]);if(w1(a[v])){u(a[v],l[l.length-1]||[],f);var y=a[v+1]?vi(a[v+1]):null;if(d!==y){if(f.length){c=h,f.sort(function(C,A){return C.value-A.value});for(var p=[],g=0;g<f.length;++g){var _=f[g].value;(g===0||f[g-1].value!==_)&&(p.push(f[g]),_>=i[0]&&_<=i[1]&&h++)}var m=(i[1]-i[0])/t;if(h>m*1.5&&c>m/1.5||(l.push(p),h>m||r===a[v]))break}f=[]}}}for(var w=Et(Y(l,function(C){return Et(C,function(A){return A.value>=i[0]&&A.value<=i[1]&&!A.notAdd})}),function(C){return C.length>0}),T=[],S=w.length-1,v=0;v<w.length;++v)for(var b=w[v],M=0;M<b.length;++M)T.push({value:b[M].value,level:S-v});T.sort(function(C,A){return C.value-A.value});for(var x=[],v=0;v<T.length;++v)(v===0||T[v].value!==T[v-1].value)&&x.push(T[v]);return x}De.registerClass(hg);var Pv=De.prototype,fn=Rn.prototype,ZT=oe,KT=Math.floor,QT=Math.ceil,fa=Math.pow,ee=Math.log,Wl=function(r){N(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type="log",e.base=10,e._originalScale=new Rn,e._interval=0,e}return t.prototype.getTicks=function(e){var i=this._originalScale,n=this._extent,a=i.getExtent(),o=fn.getTicks.call(this,e);return Y(o,function(s){var u=s.value,l=oe(fa(this.base,u));return l=u===n[0]&&this._fixMin?ha(l,a[0]):l,l=u===n[1]&&this._fixMax?ha(l,a[1]):l,{value:l}},this)},t.prototype.setExtent=function(e,i){var n=ee(this.base);e=ee(Math.max(0,e))/n,i=ee(Math.max(0,i))/n,fn.setExtent.call(this,e,i)},t.prototype.getExtent=function(){var e=this.base,i=Pv.getExtent.call(this);i[0]=fa(e,i[0]),i[1]=fa(e,i[1]);var n=this._originalScale,a=n.getExtent();return this._fixMin&&(i[0]=ha(i[0],a[0])),this._fixMax&&(i[1]=ha(i[1],a[1])),i},t.prototype.unionExtent=function(e){this._originalScale.unionExtent(e);var i=this.base;e[0]=ee(e[0])/ee(i),e[1]=ee(e[1])/ee(i),Pv.unionExtent.call(this,e)},t.prototype.unionExtentFromData=function(e,i){this.unionExtent(e.getApproximateExtent(i))},t.prototype.calcNiceTicks=function(e){e=e||10;var i=this._extent,n=i[1]-i[0];if(!(n===1/0||n<=0)){var a=A_(n),o=e/n*a;for(o<=.5&&(a*=10);!isNaN(a)&&Math.abs(a)<1&&Math.abs(a)>0;)a*=10;var s=[oe(QT(i[0]/a)*a),oe(KT(i[1]/a)*a)];this._interval=a,this._niceExtent=s}},t.prototype.calcNiceExtent=function(e){fn.calcNiceExtent.call(this,e),this._fixMin=e.fixMin,this._fixMax=e.fixMax},t.prototype.parse=function(e){return e},t.prototype.contain=function(e){return e=ee(e)/ee(this.base),Ao(e,this._extent)},t.prototype.normalize=function(e){return e=ee(e)/ee(this.base),Po(e,this._extent)},t.prototype.scale=function(e){return e=Lo(e,this._extent),fa(this.base,e)},t.type="log",t}(De),vg=Wl.prototype;vg.getMinorTicks=fn.getMinorTicks;vg.getLabel=fn.getLabel;function ha(r,t){return ZT(r,Le(t))}De.registerClass(Wl);var JT=function(){function r(t,e,i){this._prepareParams(t,e,i)}return r.prototype._prepareParams=function(t,e,i){i[1]<i[0]&&(i=[NaN,NaN]),this._dataMin=i[0],this._dataMax=i[1];var n=this._isOrdinal=t.type==="ordinal";this._needCrossZero=t.type==="interval"&&e.getNeedCrossZero&&e.getNeedCrossZero();var a=e.get("min",!0);a==null&&(a=e.get("startValue",!0));var o=this._modelMinRaw=a;K(o)?this._modelMinNum=va(t,o({min:i[0],max:i[1]})):o!=="dataMin"&&(this._modelMinNum=va(t,o));var s=this._modelMaxRaw=e.get("max",!0);if(K(s)?this._modelMaxNum=va(t,s({min:i[0],max:i[1]})):s!=="dataMax"&&(this._modelMaxNum=va(t,s)),n)this._axisDataLen=e.getCategories().length;else{var u=e.get("boundaryGap"),l=k(u)?u:[u||0,u||0];typeof l[0]=="boolean"||typeof l[1]=="boolean"?this._boundaryGapInner=[0,0]:this._boundaryGapInner=[Qe(l[0],1),Qe(l[1],1)]}},r.prototype.calculate=function(){var t=this._isOrdinal,e=this._dataMin,i=this._dataMax,n=this._axisDataLen,a=this._boundaryGapInner,o=t?null:i-e||Math.abs(e),s=this._modelMinRaw==="dataMin"?e:this._modelMinNum,u=this._modelMaxRaw==="dataMax"?i:this._modelMaxNum,l=s!=null,f=u!=null;s==null&&(s=t?n?0:NaN:e-a[0]*o),u==null&&(u=t?n?n-1:NaN:i+a[1]*o),(s==null||!isFinite(s))&&(s=NaN),(u==null||!isFinite(u))&&(u=NaN);var h=Oa(s)||Oa(u)||t&&!n;this._needCrossZero&&(s>0&&u>0&&!l&&(s=0),s<0&&u<0&&!f&&(u=0));var c=this._determinedMin,v=this._determinedMax;return c!=null&&(s=c,l=!0),v!=null&&(u=v,f=!0),{min:s,max:u,minFixed:l,maxFixed:f,isBlank:h}},r.prototype.modifyDataMinMax=function(t,e){this[tb[t]]=e},r.prototype.setDeterminedMinMax=function(t,e){var i=jT[t];this[i]=e},r.prototype.freeze=function(){this.frozen=!0},r}(),jT={min:"_determinedMin",max:"_determinedMax"},tb={min:"_dataMin",max:"_dataMax"};function eb(r,t,e){var i=r.rawExtentInfo;return i||(i=new JT(r,t,e),r.rawExtentInfo=i,i)}function va(r,t){return t==null?null:Oa(t)?NaN:r.parse(t)}function rb(r,t){var e=r.type,i=eb(r,t,r.getExtent()).calculate();r.setBlank(i.isBlank);var n=i.min,a=i.max,o=t.ecModel;if(o&&e==="time"){var s=og("bar",o),u=!1;if(D(s,function(h){u=u||h.getBaseAxis()===t.axis}),u){var l=sg(s),f=ib(n,a,t,l);n=f.min,a=f.max}}return{extent:[n,a],fixMin:i.minFixed,fixMax:i.maxFixed}}function ib(r,t,e,i){var n=e.axis.getExtent(),a=Math.abs(n[1]-n[0]),o=zT(i,e.axis);if(o===void 0)return{min:r,max:t};var s=1/0;D(o,function(v){s=Math.min(v.offset,s)});var u=-1/0;D(o,function(v){u=Math.max(v.offset+v.width,u)}),s=Math.abs(s),u=Math.abs(u);var l=s+u,f=t-r,h=1-(s+u)/a,c=f/h-f;return t+=c*(u/l),r-=c*(s/l),{min:r,max:t}}function uD(r,t){var e=t,i=rb(r,e),n=i.extent,a=e.get("splitNumber");r instanceof Wl&&(r.base=e.get("logBase"));var o=r.type,s=e.get("interval"),u=o==="interval"||o==="time";r.setExtent(n[0],n[1]),r.calcNiceExtent({splitNumber:a,fixMin:i.fixMin,fixMax:i.fixMax,minInterval:u?e.get("minInterval"):null,maxInterval:u?e.get("maxInterval"):null}),s!=null&&r.setInterval&&r.setInterval(s)}function lD(r,t){if(t=t||r.get("type"),t)switch(t){case"category":return new Gl({ordinalMeta:r.getOrdinalMeta?r.getOrdinalMeta():r.getCategories(),extent:[1/0,-1/0]});case"time":return new hg({locale:r.ecModel.getLocaleModel(),useUTC:r.ecModel.get("useUTC")});default:return new(De.getClass(t)||Rn)}}function fD(r){var t=r.scale.getExtent(),e=t[0],i=t[1];return!(e>0&&i>0||e<0&&i<0)}function nb(r){var t=r.getLabelModel().get("formatter"),e=r.type==="category"?r.scale.getExtent()[0]:null;return r.scale.type==="time"?function(i){return function(n,a){return r.scale.getFormattedLabel(n,a,i)}}(t):B(t)?function(i){return function(n){var a=r.scale.getLabel(n),o=i.replace("{value}",a??"");return o}}(t):K(t)?function(i){return function(n,a){return e!=null&&(a=n.value-e),i(Ul(r,n),a,n.level!=null?{level:n.level}:null)}}(t):function(i){return r.scale.getLabel(i)}}function Ul(r,t){return r.type==="category"?r.scale.getLabel(t):t.value}function hD(r){var t=r.model,e=r.scale;if(!(!t.get(["axisLabel","show"])||e.isBlank())){var i,n,a=e.getExtent();e instanceof Gl?n=e.count():(i=e.getTicks(),n=i.length);var o=r.getLabelModel(),s=nb(r),u,l=1;n>40&&(l=Math.ceil(n/40));for(var f=0;f<n;f+=l){var h=i?i[f]:{value:a[0]+f},c=s(h,f),v=o.getTextRect(c),d=ab(v,o.get("rotate")||0);u?u.union(d):u=d}return u}}function ab(r,t){var e=t*Math.PI/180,i=r.width,n=r.height,a=i*Math.abs(Math.cos(e))+Math.abs(n*Math.sin(e)),o=i*Math.abs(Math.sin(e))+Math.abs(n*Math.cos(e)),s=new J(r.x,r.y,a,o);return s}function ob(r){var t=r.get("interval");return t??"auto"}function sb(r){return r.type==="category"&&ob(r.getLabelModel())===0}function ub(r,t){var e={};return D(r.mapDimensionsAll(t),function(i){e[RT(r,i)]=!0}),ft(e)}function vD(r,t,e){t&&D(ub(t,e),function(i){var n=t.getApproximateExtent(i);n[0]<r[0]&&(r[0]=n[0]),n[1]>r[1]&&(r[1]=n[1])})}var Lv=[],lb={registerPreprocessor:qp,registerProcessor:Zp,registerPostInit:fT,registerPostUpdate:hT,registerUpdateLifecycle:zl,registerAction:bi,registerCoordinateSystem:vT,registerLayout:cT,registerVisual:Hr,registerTransform:pT,registerLoading:Kp,registerMap:dT,registerImpl:US,PRIORITY:iT,ComponentModel:tt,ComponentView:le,SeriesModel:Fr,ChartView:kr,registerComponentModel:function(r){tt.registerClass(r)},registerComponentView:function(r){le.registerClass(r)},registerSeriesModel:function(r){Fr.registerClass(r)},registerChartView:function(r){kr.registerClass(r)},registerSubTypeDefaulter:function(r,t){tt.registerSubTypeDefaulter(r,t)},registerPainter:function(r,t){S_(r,t)}};function xn(r){if(k(r)){D(r,function(t){xn(t)});return}at(Lv,r)>=0||(Lv.push(r),K(r)&&(r={install:r}),r.install(lb))}function fb(r){for(var t=[],e=0;e<r.length;e++){var i=r[e];if(!i.defaultAttr.ignore){var n=i.label,a=n.getComputedTransform(),o=n.getBoundingRect(),s=!a||a[1]<1e-5&&a[2]<1e-5,u=n.style.margin||0,l=o.clone();l.applyTransform(a),l.x-=u/2,l.y-=u/2,l.width+=u,l.height+=u;var f=s?new Ua(o,a):null;t.push({label:n,labelLine:i.labelLine,rect:l,localRect:o,obb:f,priority:i.priority,defaultAttr:i.defaultAttr,layoutOption:i.computedLayoutOption,axisAligned:s,transform:a})}}return t}function cg(r,t,e,i,n,a){var o=r.length;if(o<2)return;r.sort(function(S,b){return S.rect[t]-b.rect[t]});for(var s=0,u,l=!1,f=0;f<o;f++){var h=r[f],c=h.rect;u=c[t]-s,u<0&&(c[t]-=u,h.label[t]-=u,l=!0),s=c[t]+c[e]}var v=r[0],d=r[o-1],y,p;g(),y<0&&w(-y,.8),p<0&&w(p,.8),g(),_(y,p,1),_(p,y,-1),g(),y<0&&T(-y),p<0&&T(p);function g(){y=v.rect[t]-i,p=n-d.rect[t]-d.rect[e]}function _(S,b,M){if(S<0){var x=Math.min(b,-S);if(x>0){m(x*M,0,o);var C=x+S;C<0&&w(-C*M,1)}else w(-S*M,1)}}function m(S,b,M){S!==0&&(l=!0);for(var x=b;x<M;x++){var C=r[x],A=C.rect;A[t]+=S,C.label[t]+=S}}function w(S,b){for(var M=[],x=0,C=1;C<o;C++){var A=r[C-1].rect,L=Math.max(r[C].rect[t]-A[t]-A[e],0);M.push(L),x+=L}if(x){var I=Math.min(Math.abs(S)/x,b);if(S>0)for(var C=0;C<o-1;C++){var P=M[C]*I;m(P,0,C+1)}else for(var C=o-1;C>0;C--){var P=M[C-1]*I;m(-P,C,o)}}}function T(S){var b=S<0?-1:1;S=Math.abs(S);for(var M=Math.ceil(S/(o-1)),x=0;x<o-1;x++)if(b>0?m(M,0,x+1):m(-M,o-x-1,o),S-=M,S<=0)return}return l}function cD(r,t,e,i){return cg(r,"x","width",t,e)}function dD(r,t,e,i){return cg(r,"y","height",t,e)}function hb(r){var t=[];r.sort(function(y,p){return p.priority-y.priority});var e=new J(0,0,0,0);function i(y){if(!y.ignore){var p=y.ensureState("emphasis");p.ignore==null&&(p.ignore=!1)}y.ignore=!0}for(var n=0;n<r.length;n++){var a=r[n],o=a.axisAligned,s=a.localRect,u=a.transform,l=a.label,f=a.labelLine;e.copy(a.rect),e.width-=.1,e.height-=.1,e.x+=.05,e.y+=.05;for(var h=a.obb,c=!1,v=0;v<t.length;v++){var d=t[v];if(e.intersect(d.rect)){if(o&&d.axisAligned){c=!0;break}if(d.obb||(d.obb=new Ua(d.localRect,d.transform)),h||(h=new Ua(s,u)),h.intersect(d.obb)){c=!0;break}}}c?(i(l),f&&i(f)):(l.attr("ignore",a.defaultAttr.ignore),f&&f.attr("ignore",a.defaultAttr.labelGuideIgnore),t.push(a))}}const vb=["getWidth","getHeight","getDom","getOption","resize","dispatchAction","convertToPixel","convertFromPixel","containPixel","getDataURL","getConnectedDataURL","appendData","clear","isDisposed","dispose"];function cb(r){function t(i){return(...n)=>{if(!r.value)throw new Error("ECharts is not initialized yet.");return r.value[i].apply(r.value,n)}}function e(){const i=Object.create(null);return vb.forEach(n=>{i[n]=t(n)}),i}return e()}function db(r,t,e){ga([e,r,t],([i,n,a],o,s)=>{let u=null;if(i&&n&&a){const{offsetWidth:l,offsetHeight:f}=i,h=a===!0?{}:a,{throttle:c=100,onResize:v}=h;let d=!1;const y=()=>{n.resize(),v==null||v()},p=c?Ol(y,c):y;u=new ResizeObserver(()=>{!d&&(d=!0,i.offsetWidth===l&&i.offsetHeight===f)||p()}),u.observe(i)}s(()=>{u&&(u.disconnect(),u=null)})})}const pb={autoresize:[Boolean,Object]},gb=/^on[^a-z]/,dg=r=>gb.test(r);function yb(r){const t={};for(const e in r)dg(e)||(t[e]=r[e]);return t}function Ra(r,t){const e=Bg(r)?Ng(r):r;return e&&typeof e=="object"&&"value"in e?e.value||t:e||t}const _b="ecLoadingOptions";function mb(r,t,e){const i=pa(_b,{}),n=ri(()=>({...Ra(i,{}),...e==null?void 0:e.value}));ac(()=>{const a=r.value;a&&(t.value?a.showLoading(n.value):a.hideLoading())})}const wb={loading:Boolean,loadingOptions:Object};let Vi=null;const pg="x-vue-echarts";function Sb(){if(Vi!=null)return Vi;if(typeof HTMLElement>"u"||typeof customElements>"u")return Vi=!1;try{new Function("tag","class EChartsElement extends HTMLElement{__dispose=null;disconnectedCallback(){this.__dispose&&(this.__dispose(),this.__dispose=null)}}customElements.get(tag)==null&&customElements.define(tag,EChartsElement);")(pg)}catch{return Vi=!1}return Vi=!0}document.head.appendChild(document.createElement("style")).textContent=`x-vue-echarts{display:block;width:100%;height:100%;min-width:0}
`;const Tb=Sb(),bb="ecTheme",xb="ecInitOptions",Cb="ecUpdateOptions",Iv=/(^&?~?!?)native:/;var pD=Pg({name:"echarts",props:{option:Object,theme:{type:[Object,String]},initOptions:Object,updateOptions:Object,group:String,manualUpdate:Boolean,...pb,...wb},emits:{},inheritAttrs:!1,setup(r,{attrs:t}){const e=Io(),i=Io(),n=Io(),a=pa(bb,null),o=pa(xb,null),s=pa(Cb,null),{autoresize:u,manualUpdate:l,loading:f,loadingOptions:h}=Ig(r),c=ri(()=>n.value||r.option||null),v=ri(()=>r.theme||Ra(a,{})),d=ri(()=>r.initOptions||Ra(o,{})),y=ri(()=>r.updateOptions||Ra(s,{})),p=ri(()=>yb(t)),g={},_=Rg().proxy.$listeners,m={};_?Object.keys(_).forEach(x=>{Iv.test(x)?g[x.replace(Iv,"$1")]=_[x]:m[x]=_[x]}):Object.keys(t).filter(x=>dg(x)).forEach(x=>{let C=x.charAt(2).toLowerCase()+x.slice(3);if(C.indexOf("native:")===0){const A=`on${C.charAt(7).toUpperCase()}${C.slice(8)}`;g[A]=t[x];return}C.substring(C.length-4)==="Once"&&(C=`~${C.substring(0,C.length-4)}`),m[C]=t[x]});function w(x){if(!e.value)return;const C=i.value=uT(e.value,v.value,d.value);r.group&&(C.group=r.group),Object.keys(m).forEach(I=>{let P=m[I];if(!P)return;let R=I.toLowerCase();R.charAt(0)==="~"&&(R=R.substring(1),P.__once__=!0);let E=C;if(R.indexOf("zr:")===0&&(E=C.getZr(),R=R.substring(3)),P.__once__){delete P.__once__;const U=P;P=(...z)=>{U(...z),E.off(R,P)}}E.on(R,P)});function A(){C&&!C.isDisposed()&&C.resize()}function L(){const I=x||c.value;I&&C.setOption(I,y.value)}u.value?kg(()=>{A(),L()}):L()}function T(x,C){r.manualUpdate&&(n.value=x),i.value?i.value.setOption(x,C||{}):w(x)}function S(){i.value&&(i.value.dispose(),i.value=void 0)}let b=null;ga(l,x=>{typeof b=="function"&&(b(),b=null),x||(b=ga(()=>r.option,(C,A)=>{C&&(i.value?i.value.setOption(C,{notMerge:C!==A,...y.value}):w())},{deep:!0}))},{immediate:!0}),ga([v,d],()=>{S(),w()},{deep:!0}),ac(()=>{r.group&&i.value&&(i.value.group=r.group)});const M=cb(i);return mb(i,f,h),db(i,u,e),Eg(()=>{w()}),Og(()=>{Tb&&e.value?e.value.__dispose=S:S()}),{chart:i,root:e,setOption:T,nonEventAttrs:p,nativeListeners:g,...M}},render(){const r={...this.nonEventAttrs,...this.nativeListeners};return r.ref="root",r.class=r.class?["echarts"].concat(r.class):"echarts",Lg(pg,r)}});function gD(r,t,e){var i=r.get("borderRadius");if(i==null)return e?{cornerRadius:0}:null;k(i)||(i=[i,i,i,i]);var n=Math.abs(t.r||0-t.r0||0);return{cornerRadius:Y(i,function(a){return Qe(a,n)})}}function yD(r,t,e){t=k(t)&&{coordDimensions:t}||O({encodeDefine:r.getEncode()},t);var i=r.getSource(),n=MT(i,t).dimensions,a=new DT(n,r);return a.initData(i,e),a}function Rv(r,t,e){e=e||{};var i=r.coordinateSystem,n=t.axis,a={},o=n.getAxesOnZeroOf()[0],s=n.position,u=o?"onZero":s,l=n.dim,f=i.getRect(),h=[f.x,f.x+f.width,f.y,f.y+f.height],c={left:0,right:1,top:0,bottom:1,onZero:2},v=t.get("offset")||0,d=l==="x"?[h[2]-v,h[3]+v]:[h[0]-v,h[1]+v];if(o){var y=o.toGlobalCoord(o.dataToCoord(0));d[c.onZero]=Math.max(Math.min(y,d[1]),d[0])}a.position=[l==="y"?d[c[u]]:h[0],l==="x"?d[c[u]]:h[3]],a.rotation=Math.PI/2*(l==="x"?0:1);var p={top:-1,bottom:1,left:-1,right:1};a.labelDirection=a.tickDirection=a.nameDirection=p[s],a.labelOffset=o?d[c[s]]-d[c.onZero]:0,t.get(["axisTick","inside"])&&(a.tickDirection=-a.tickDirection),vn(e.labelInside,t.get(["axisLabel","inside"]))&&(a.labelDirection=-a.labelDirection);var g=t.get(["axisLabel","rotate"]);return a.labelRotate=u==="top"?-g:g,a.z2=1,a}function _D(r){return r.get("coordinateSystem")==="cartesian2d"}function mD(r){var t={xAxisModel:null,yAxisModel:null};return D(t,function(e,i){var n=i.replace(/Model$/,""),a=r.getReferringComponents(n,so).models[0];t[i]=a}),t}var Xe=Math.PI,Br=function(){function r(t,e){this.group=new ue,this.opt=e,this.axisModel=t,ot(e,{labelOffset:0,nameDirection:1,tickDirection:1,labelDirection:1,silent:!0,handleAutoShown:function(){return!0}});var i=new ue({x:e.position[0],y:e.position[1],rotation:e.rotation});i.updateTransform(),this._transformGroup=i}return r.prototype.hasBuilder=function(t){return!!Ev[t]},r.prototype.add=function(t){Ev[t](this.opt,this.axisModel,this.group,this._transformGroup)},r.prototype.getGroup=function(){return this.group},r.innerTextLayout=function(t,e,i){var n=Fc(e-t),a,o;return Va(n)?(o=i>0?"top":"bottom",a="center"):Va(n-Xe)?(o=i>0?"bottom":"top",a="center"):(o="middle",n>0&&n<Xe?a=i>0?"right":"left":a=i>0?"left":"right"),{rotation:n,textAlign:a,textVerticalAlign:o}},r.makeAxisEventDataBase=function(t){var e={componentType:t.mainType,componentIndex:t.componentIndex};return e[t.mainType+"Index"]=t.componentIndex,e},r.isLabelSilent=function(t){var e=t.get("tooltip");return t.get("silent")||!(t.get("triggerEvent")||e&&e.show)},r}(),Ev={axisLine:function(r,t,e,i){var n=t.get(["axisLine","show"]);if(n==="auto"&&r.handleAutoShown&&(n=r.handleAutoShown("axisLine")),!!n){var a=t.axis.getExtent(),o=i.transform,s=[a[0],0],u=[a[1],0],l=s[0]>u[0];o&&(me(s,s,o),me(u,u,o));var f=O({lineCap:"round"},t.getModel(["axisLine","lineStyle"]).getLineStyle()),h=new Si({shape:{x1:s[0],y1:s[1],x2:u[0],y2:u[1]},style:f,strokeContainThreshold:r.strokeContainThreshold||5,silent:!0,z2:1});yl(h.shape,h.style.lineWidth),h.anid="line",e.add(h);var c=t.get(["axisLine","symbol"]);if(c!=null){var v=t.get(["axisLine","symbolSize"]);B(c)&&(c=[c,c]),(B(v)||ut(v))&&(v=[v,v]);var d=MS(t.get(["axisLine","symbolOffset"])||0,v),y=v[0],p=v[1];D([{rotate:r.rotation+Math.PI/2,offset:d[0],r:0},{rotate:r.rotation-Math.PI/2,offset:d[1],r:Math.sqrt((s[0]-u[0])*(s[0]-u[0])+(s[1]-u[1])*(s[1]-u[1]))}],function(g,_){if(c[_]!=="none"&&c[_]!=null){var m=kl(c[_],-y/2,-p/2,y,p,f.stroke,!0),w=g.r+g.offset,T=l?u:s;m.attr({rotation:g.rotate,x:T[0]+w*Math.cos(r.rotation),y:T[1]-w*Math.sin(r.rotation),silent:!0,z2:11}),e.add(m)}})}}},axisTickLabel:function(r,t,e,i){var n=Ab(e,i,t,r),a=Lb(e,i,t,r);if(Mb(t,a,n),Pb(e,i,t,r.tickDirection),t.get(["axisLabel","hideOverlap"])){var o=fb(Y(a,function(s){return{label:s,priority:s.z2,defaultAttr:{ignore:s.ignore}}}));hb(o)}},axisName:function(r,t,e,i){var n=vn(r.axisName,t.get("name"));if(n){var a=t.get("nameLocation"),o=r.nameDirection,s=t.getModel("nameTextStyle"),u=t.get("nameGap")||0,l=t.axis.getExtent(),f=l[0]>l[1]?-1:1,h=[a==="start"?l[0]-f*u:a==="end"?l[1]+f*u:(l[0]+l[1])/2,kv(a)?r.labelOffset+o*u:0],c,v=t.get("nameRotate");v!=null&&(v=v*Xe/180);var d;kv(a)?c=Br.innerTextLayout(r.rotation,v??r.rotation,o):(c=Db(r.rotation,a,v||0,l),d=r.axisNameAvailableWidth,d!=null&&(d=Math.abs(d/Math.sin(c.rotation)),!isFinite(d)&&(d=null)));var y=s.getFont(),p=t.get("nameTruncate",!0)||{},g=p.ellipsis,_=vn(r.nameTruncateMaxWidth,p.maxWidth,d),m=new Lt({x:h[0],y:h[1],rotation:c.rotation,silent:Br.isLabelSilent(t),style:tr(s,{text:n,font:y,overflow:"truncate",width:_,ellipsis:g,fill:s.getTextColor()||t.get(["axisLine","lineStyle","color"]),align:s.get("align")||c.textAlign,verticalAlign:s.get("verticalAlign")||c.textVerticalAlign}),z2:1});if(yo({el:m,componentModel:t,itemName:n}),m.__fullText=n,m.anid="name",t.get("triggerEvent")){var w=Br.makeAxisEventDataBase(t);w.targetType="axisName",w.name=n,rt(m).eventData=w}i.add(m),m.updateTransform(),e.add(m),m.decomposeTransform()}}};function Db(r,t,e,i){var n=Fc(e-r),a,o,s=i[0]>i[1],u=t==="start"&&!s||t!=="start"&&s;return Va(n-Xe/2)?(o=u?"bottom":"top",a="center"):Va(n-Xe*1.5)?(o=u?"top":"bottom",a="center"):(o="middle",n<Xe*1.5&&n>Xe/2?a=u?"left":"right":a=u?"right":"left"),{rotation:n,textAlign:a,textVerticalAlign:o}}function Mb(r,t,e){if(!sb(r.axis)){var i=r.get(["axisLabel","showMinLabel"]),n=r.get(["axisLabel","showMaxLabel"]);t=t||[],e=e||[];var a=t[0],o=t[1],s=t[t.length-1],u=t[t.length-2],l=e[0],f=e[1],h=e[e.length-1],c=e[e.length-2];i===!1?(Xt(a),Xt(l)):Ov(a,o)&&(i?(Xt(o),Xt(f)):(Xt(a),Xt(l))),n===!1?(Xt(s),Xt(h)):Ov(u,s)&&(n?(Xt(u),Xt(c)):(Xt(s),Xt(h)))}}function Xt(r){r&&(r.ignore=!0)}function Ov(r,t){var e=r&&r.getBoundingRect().clone(),i=t&&t.getBoundingRect().clone();if(!(!e||!i)){var n=Ku([]);return Qu(n,n,-r.rotation),e.applyTransform(li([],n,r.getLocalTransform())),i.applyTransform(li([],n,t.getLocalTransform())),e.intersect(i)}}function kv(r){return r==="middle"||r==="center"}function gg(r,t,e,i,n){for(var a=[],o=[],s=[],u=0;u<r.length;u++){var l=r[u].coord;o[0]=l,o[1]=0,s[0]=l,s[1]=e,t&&(me(o,o,t),me(s,s,t));var f=new Si({shape:{x1:o[0],y1:o[1],x2:s[0],y2:s[1]},style:i,z2:2,autoBatch:!0,silent:!0});yl(f.shape,f.style.lineWidth),f.anid=n+"_"+r[u].tickValue,a.push(f)}return a}function Ab(r,t,e,i){var n=e.axis,a=e.getModel("axisTick"),o=a.get("show");if(o==="auto"&&i.handleAutoShown&&(o=i.handleAutoShown("axisTick")),!(!o||n.scale.isBlank())){for(var s=a.getModel("lineStyle"),u=i.tickDirection*a.get("length"),l=n.getTicksCoords(),f=gg(l,t.transform,u,ot(s.getLineStyle(),{stroke:e.get(["axisLine","lineStyle","color"])}),"ticks"),h=0;h<f.length;h++)r.add(f[h]);return f}}function Pb(r,t,e,i){var n=e.axis,a=e.getModel("minorTick");if(!(!a.get("show")||n.scale.isBlank())){var o=n.getMinorTicksCoords();if(o.length)for(var s=a.getModel("lineStyle"),u=i*a.get("length"),l=ot(s.getLineStyle(),ot(e.getModel("axisTick").getLineStyle(),{stroke:e.get(["axisLine","lineStyle","color"])})),f=0;f<o.length;f++)for(var h=gg(o[f],t.transform,u,l,"minorticks_"+f),c=0;c<h.length;c++)r.add(h[c])}}function Lb(r,t,e,i){var n=e.axis,a=vn(i.axisLabelShow,e.get(["axisLabel","show"]));if(!(!a||n.scale.isBlank())){var o=e.getModel("axisLabel"),s=o.get("margin"),u=n.getViewLabels(),l=(vn(i.labelRotate,o.get("rotate"))||0)*Xe/180,f=Br.innerTextLayout(i.rotation,l,i.labelDirection),h=e.getCategories&&e.getCategories(!0),c=[],v=Br.isLabelSilent(e),d=e.get("triggerEvent");return D(u,function(y,p){var g=n.scale.type==="ordinal"?n.scale.getRawOrdinalNumber(y.tickValue):y.tickValue,_=y.formattedLabel,m=y.rawLabel,w=o;if(h&&h[g]){var T=h[g];F(T)&&T.textStyle&&(w=new ht(T.textStyle,o,e.ecModel))}var S=w.getTextColor()||e.get(["axisLine","lineStyle","color"]),b=n.dataToCoord(g),M=w.getShallow("align",!0)||f.textAlign,x=W(w.getShallow("alignMinLabel",!0),M),C=W(w.getShallow("alignMaxLabel",!0),M),A=w.getShallow("verticalAlign",!0)||w.getShallow("baseline",!0)||f.textVerticalAlign,L=W(w.getShallow("verticalAlignMinLabel",!0),A),I=W(w.getShallow("verticalAlignMaxLabel",!0),A),P=new Lt({x:b,y:i.labelOffset+i.labelDirection*s,rotation:f.rotation,silent:v,z2:10+(y.level||0),style:tr(w,{text:_,align:p===0?x:p===u.length-1?C:M,verticalAlign:p===0?L:p===u.length-1?I:A,fill:K(S)?S(n.type==="category"?m:n.type==="value"?g+"":g,p):S})});if(P.anid="label_"+g,yo({el:P,componentModel:e,itemName:_,formatterParamsExtra:{isTruncated:function(){return P.isTruncated},value:m,tickIndex:p}}),d){var R=Br.makeAxisEventDataBase(e);R.targetType="axisLabel",R.value=m,R.tickIndex=p,n.type==="category"&&(R.dataIndex=g),rt(P).eventData=R}t.add(P),P.updateTransform(),c.push(P),r.add(P),P.decomposeTransform()}),c}}function Ib(r,t){var e={axesInfo:{},seriesInvolved:!1,coordSysAxesInfo:{},coordSysMap:{}};return Rb(e,r,t),e.seriesInvolved&&Ob(e,r),e}function Rb(r,t,e){var i=t.getComponent("tooltip"),n=t.getComponent("axisPointer"),a=n.get("link",!0)||[],o=[];D(e.getCoordinateSystems(),function(s){if(!s.axisPointerEnabled)return;var u=Cn(s.model),l=r.coordSysAxesInfo[u]={};r.coordSysMap[u]=s;var f=s.model,h=f.getModel("tooltip",i);if(D(s.getAxes(),yt(y,!1,null)),s.getTooltipAxes&&i&&h.get("show")){var c=h.get("trigger")==="axis",v=h.get(["axisPointer","type"])==="cross",d=s.getTooltipAxes(h.get(["axisPointer","axis"]));(c||v)&&D(d.baseAxes,yt(y,v?"cross":!0,c)),v&&D(d.otherAxes,yt(y,"cross",!1))}function y(p,g,_){var m=_.model.getModel("axisPointer",n),w=m.get("show");if(!(!w||w==="auto"&&!p&&!Fu(m))){g==null&&(g=m.get("triggerTooltip")),m=p?Eb(_,h,n,t,p,g):m;var T=m.get("snap"),S=m.get("triggerEmphasis"),b=Cn(_.model),M=g||T||_.type==="category",x=r.axesInfo[b]={key:b,axis:_,coordSys:s,axisPointerModel:m,triggerTooltip:g,triggerEmphasis:S,involveSeries:M,snap:T,useHandle:Fu(m),seriesModels:[],linkGroup:null};l[b]=x,r.seriesInvolved=r.seriesInvolved||M;var C=kb(a,_);if(C!=null){var A=o[C]||(o[C]={axesInfo:{}});A.axesInfo[b]=x,A.mapper=a[C].mapper,x.linkGroup=A}}}})}function Eb(r,t,e,i,n,a){var o=t.getModel("axisPointer"),s=["type","snap","lineStyle","shadowStyle","label","animation","animationDurationUpdate","animationEasingUpdate","z"],u={};D(s,function(c){u[c]=Z(o.get(c))}),u.snap=r.type!=="category"&&!!a,o.get("type")==="cross"&&(u.type="line");var l=u.label||(u.label={});if(l.show==null&&(l.show=!1),n==="cross"){var f=o.get(["label","show"]);if(l.show=f??!0,!a){var h=u.lineStyle=o.get("crossStyle");h&&ot(l,h.textStyle)}}return r.model.getModel("axisPointer",new ht(u,e,i))}function Ob(r,t){t.eachSeries(function(e){var i=e.coordinateSystem,n=e.get(["tooltip","trigger"],!0),a=e.get(["tooltip","show"],!0);!i||n==="none"||n===!1||n==="item"||a===!1||e.get(["axisPointer","show"],!0)===!1||D(r.coordSysAxesInfo[Cn(i.model)],function(o){var s=o.axis;i.getAxis(s.dim)===s&&(o.seriesModels.push(e),o.seriesDataCount==null&&(o.seriesDataCount=0),o.seriesDataCount+=e.getData().count())})})}function kb(r,t){for(var e=t.model,i=t.dim,n=0;n<r.length;n++){var a=r[n]||{};if(Fs(a[i+"AxisId"],e.id)||Fs(a[i+"AxisIndex"],e.componentIndex)||Fs(a[i+"AxisName"],e.name))return n}}function Fs(r,t){return r==="all"||k(r)&&at(r,t)>=0||r===t}function Bb(r){var t=Yl(r);if(t){var e=t.axisPointerModel,i=t.axis.scale,n=e.option,a=e.get("status"),o=e.get("value");o!=null&&(o=i.parse(o));var s=Fu(e);a==null&&(n.status=s?"show":"hide");var u=i.getExtent().slice();u[0]>u[1]&&u.reverse(),(o==null||o>u[1])&&(o=u[1]),o<u[0]&&(o=u[0]),n.value=o,s&&(n.status=t.axis.scale.isBlank()?"hide":"show")}}function Yl(r){var t=(r.ecModel.getComponent("axisPointer")||{}).coordSysAxesInfo;return t&&t.axesInfo[Cn(r)]}function Nb(r){var t=Yl(r);return t&&t.axisPointerModel}function Fu(r){return!!r.get(["handle","show"])}function Cn(r){return r.type+"||"+r.id}var Bv={},Fb=function(r){N(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.prototype.render=function(e,i,n,a){this.axisPointerClass&&Bb(e),r.prototype.render.apply(this,arguments),this._doUpdateAxisPointerClass(e,n,!0)},t.prototype.updateAxisPointer=function(e,i,n,a){this._doUpdateAxisPointerClass(e,n,!1)},t.prototype.remove=function(e,i){var n=this._axisPointer;n&&n.remove(i)},t.prototype.dispose=function(e,i){this._disposeAxisPointer(i),r.prototype.dispose.apply(this,arguments)},t.prototype._doUpdateAxisPointerClass=function(e,i,n){var a=t.getAxisPointerClass(this.axisPointerClass);if(a){var o=Nb(e);o?(this._axisPointer||(this._axisPointer=new a)).render(e,o,i,n):this._disposeAxisPointer(i)}},t.prototype._disposeAxisPointer=function(e){this._axisPointer&&this._axisPointer.dispose(e),this._axisPointer=null},t.registerAxisPointerClass=function(e,i){Bv[e]=i},t.getAxisPointerClass=function(e){return e&&Bv[e]},t.type="axis",t}(le),Dr=mt(),Nv=Z,zs=lt,zb=function(){function r(){this._dragging=!1,this.animationThreshold=15}return r.prototype.render=function(t,e,i,n){var a=e.get("value"),o=e.get("status");if(this._axisModel=t,this._axisPointerModel=e,this._api=i,!(!n&&this._lastValue===a&&this._lastStatus===o)){this._lastValue=a,this._lastStatus=o;var s=this._group,u=this._handle;if(!o||o==="hide"){s&&s.hide(),u&&u.hide();return}s&&s.show(),u&&u.show();var l={};this.makeElOption(l,a,t,e,i);var f=l.graphicKey;f!==this._lastGraphicKey&&this.clear(i),this._lastGraphicKey=f;var h=this._moveAnimation=this.determineAnimation(t,e);if(!s)s=this._group=new ue,this.createPointerEl(s,l,t,e),this.createLabelEl(s,l,t,e),i.getZr().add(s);else{var c=yt(Fv,e,h);this.updatePointerEl(s,l,c),this.updateLabelEl(s,l,c,e)}Hv(s,e,!0),this._renderHandle(a)}},r.prototype.remove=function(t){this.clear(t)},r.prototype.dispose=function(t){this.clear(t)},r.prototype.determineAnimation=function(t,e){var i=e.get("animation"),n=t.axis,a=n.type==="category",o=e.get("snap");if(!o&&!a)return!1;if(i==="auto"||i==null){var s=this.animationThreshold;if(a&&n.getBandWidth()>s)return!0;if(o){var u=Yl(t).seriesDataCount,l=n.getExtent();return Math.abs(l[0]-l[1])/u>s}return!1}return i===!0},r.prototype.makeElOption=function(t,e,i,n,a){},r.prototype.createPointerEl=function(t,e,i,n){var a=e.pointer;if(a){var o=Dr(t).pointerEl=new t1[a.type](Nv(e.pointer));t.add(o)}},r.prototype.createLabelEl=function(t,e,i,n){if(e.label){var a=Dr(t).labelEl=new Lt(Nv(e.label));t.add(a),zv(a,n)}},r.prototype.updatePointerEl=function(t,e,i){var n=Dr(t).pointerEl;n&&e.pointer&&(n.setStyle(e.pointer.style),i(n,{shape:e.pointer.shape}))},r.prototype.updateLabelEl=function(t,e,i,n){var a=Dr(t).labelEl;a&&(a.setStyle(e.label.style),i(a,{x:e.label.x,y:e.label.y}),zv(a,n))},r.prototype._renderHandle=function(t){if(!(this._dragging||!this.updateHandleTransform)){var e=this._axisPointerModel,i=this._api.getZr(),n=this._handle,a=e.getModel("handle"),o=e.get("status");if(!a.get("show")||!o||o==="hide"){n&&i.remove(n),this._handle=null;return}var s;this._handle||(s=!0,n=this._handle=ml(a.get("icon"),{cursor:"move",draggable:!0,onmousemove:function(l){dc(l.event)},onmousedown:zs(this._onHandleDragMove,this,0,0),drift:zs(this._onHandleDragMove,this),ondragend:zs(this._onHandleDragEnd,this)}),i.add(n)),Hv(n,e,!1),n.setStyle(a.getItemStyle(null,["color","borderColor","borderWidth","opacity","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"]));var u=a.get("size");k(u)||(u=[u,u]),n.scaleX=u[0]/2,n.scaleY=u[1]/2,wp(this,"_doDispatchAxisPointer",a.get("throttle")||0,"fixRate"),this._moveHandleToValue(t,s)}},r.prototype._moveHandleToValue=function(t,e){Fv(this._axisPointerModel,!e&&this._moveAnimation,this._handle,Hs(this.getHandleTransform(t,this._axisModel,this._axisPointerModel)))},r.prototype._onHandleDragMove=function(t,e){var i=this._handle;if(i){this._dragging=!0;var n=this.updateHandleTransform(Hs(i),[t,e],this._axisModel,this._axisPointerModel);this._payloadInfo=n,i.stopAnimation(),i.attr(Hs(n)),Dr(i).lastProp=null,this._doDispatchAxisPointer()}},r.prototype._doDispatchAxisPointer=function(){var t=this._handle;if(t){var e=this._payloadInfo,i=this._axisModel;this._api.dispatchAction({type:"updateAxisPointer",x:e.cursorPoint[0],y:e.cursorPoint[1],tooltipOption:e.tooltipOption,axesInfo:[{axisDim:i.axis.dim,axisIndex:i.componentIndex}]})}},r.prototype._onHandleDragEnd=function(){this._dragging=!1;var t=this._handle;if(t){var e=this._axisPointerModel.get("value");this._moveHandleToValue(e),this._api.dispatchAction({type:"hideTip"})}},r.prototype.clear=function(t){this._lastValue=null,this._lastStatus=null;var e=t.getZr(),i=this._group,n=this._handle;e&&i&&(this._lastGraphicKey=null,i&&e.remove(i),n&&e.remove(n),this._group=null,this._handle=null,this._payloadInfo=null),Au(this,"_doDispatchAxisPointer")},r.prototype.doClear=function(){},r.prototype.buildLabel=function(t,e,i){return i=i||0,{x:t[i],y:t[1-i],width:e[i],height:e[1-i]}},r}();function Fv(r,t,e,i){yg(Dr(e).lastProp,i)||(Dr(e).lastProp=i,t?Ln(e,i,r):(e.stopAnimation(),e.attr(i)))}function yg(r,t){if(F(r)&&F(t)){var e=!0;return D(t,function(i,n){e=e&&yg(r[n],i)}),!!e}else return r===t}function zv(r,t){r[t.get(["label","show"])?"show":"hide"]()}function Hs(r){return{x:r.x||0,y:r.y||0,rotation:r.rotation||0}}function Hv(r,t,e){var i=t.get("z"),n=t.get("zlevel");r&&r.traverse(function(a){a.type!=="group"&&(i!=null&&(a.z=i),n!=null&&(a.zlevel=n),a.silent=e)})}function Hb(r){var t=r.get("type"),e=r.getModel(t+"Style"),i;return t==="line"?(i=e.getLineStyle(),i.fill=null):t==="shadow"&&(i=e.getAreaStyle(),i.stroke=null),i}function Gb(r,t,e,i,n){var a=e.get("value"),o=_g(a,t.axis,t.ecModel,e.get("seriesDataIndices"),{precision:e.get(["label","precision"]),formatter:e.get(["label","formatter"])}),s=e.getModel("label"),u=In(s.get("padding")||0),l=s.getFont(),f=Ec(o,l),h=n.position,c=f.width+u[1]+u[3],v=f.height+u[0]+u[2],d=n.align;d==="right"&&(h[0]-=c),d==="center"&&(h[0]-=c/2);var y=n.verticalAlign;y==="bottom"&&(h[1]-=v),y==="middle"&&(h[1]-=v/2),Vb(h,c,v,i);var p=s.get("backgroundColor");(!p||p==="auto")&&(p=t.get(["axisLine","lineStyle","color"])),r.label={x:h[0],y:h[1],style:tr(s,{text:o,font:l,fill:s.getTextColor(),padding:u,backgroundColor:p}),z2:10}}function Vb(r,t,e,i){var n=i.getWidth(),a=i.getHeight();r[0]=Math.min(r[0]+t,n)-t,r[1]=Math.min(r[1]+e,a)-e,r[0]=Math.max(r[0],0),r[1]=Math.max(r[1],0)}function _g(r,t,e,i,n){r=t.scale.parse(r);var a=t.scale.getLabel({value:r},{precision:n.precision}),o=n.formatter;if(o){var s={value:Ul(t,{value:r}),axisDimension:t.dim,axisIndex:t.index,seriesData:[]};D(i,function(u){var l=e.getSeriesByIndex(u.seriesIndex),f=u.dataIndexInside,h=l&&l.getDataParams(f);h&&s.seriesData.push(h)}),B(o)?a=o.replace("{value}",a):K(o)&&(a=o(s))}return a}function mg(r,t,e){var i=Ir();return Qu(i,i,e.rotation),Ks(i,i,e.position),_l([r.dataToCoord(t),(e.labelOffset||0)+(e.labelDirection||1)*(e.labelMargin||0)],i)}function Wb(r,t,e,i,n,a){var o=Br.innerTextLayout(e.rotation,0,e.labelDirection);e.labelMargin=n.get(["label","margin"]),Gb(t,i,n,a,{position:mg(i.axis,r,e),align:o.textAlign,verticalAlign:o.textVerticalAlign})}function Ub(r,t,e){return e=e||0,{x1:r[e],y1:r[1-e],x2:t[e],y2:t[1-e]}}function Yb(r,t,e){return e=e||0,{x:r[e],y:r[1-e],width:t[e],height:t[1-e]}}function wD(r,t,e,i,n,a){return{cx:r,cy:t,r0:e,r:i,startAngle:n,endAngle:a,clockwise:!0}}var Xb=function(r){N(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.makeElOption=function(e,i,n,a,o){var s=n.axis,u=s.grid,l=a.get("type"),f=Gv(u,s).getOtherAxis(s).getGlobalExtent(),h=s.toGlobalCoord(s.dataToCoord(i,!0));if(l&&l!=="none"){var c=Hb(a),v=$b[l](s,h,f);v.style=c,e.graphicKey=v.type,e.pointer=v}var d=Rv(u.model,n);Wb(i,e,d,n,a,o)},t.prototype.getHandleTransform=function(e,i,n){var a=Rv(i.axis.grid.model,i,{labelInside:!1});a.labelMargin=n.get(["handle","margin"]);var o=mg(i.axis,e,a);return{x:o[0],y:o[1],rotation:a.rotation+(a.labelDirection<0?Math.PI:0)}},t.prototype.updateHandleTransform=function(e,i,n,a){var o=n.axis,s=o.grid,u=o.getGlobalExtent(!0),l=Gv(s,o).getOtherAxis(o).getGlobalExtent(),f=o.dim==="x"?0:1,h=[e.x,e.y];h[f]+=i[f],h[f]=Math.min(u[1],h[f]),h[f]=Math.max(u[0],h[f]);var c=(l[1]+l[0])/2,v=[c,c];v[f]=h[f];var d=[{verticalAlign:"middle"},{align:"center"}];return{x:h[0],y:h[1],rotation:e.rotation,cursorPoint:v,tooltipOption:d[f]}},t}(zb);function Gv(r,t){var e={};return e[t.dim+"AxisIndex"]=t.index,r.getCartesian(e)}var $b={line:function(r,t,e){var i=Ub([t,e[0]],[t,e[1]],Vv(r));return{type:"Line",subPixelOptimize:!0,shape:i}},shadow:function(r,t,e){var i=Math.max(1,r.getBandWidth()),n=e[1]-e[0];return{type:"Rect",shape:Yb([t-i/2,e[0]],[i,n],Vv(r))}}};function Vv(r){return r.dim==="x"?0:1}var qb=function(r){N(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.type="axisPointer",t.defaultOption={show:"auto",z:50,type:"line",snap:!1,triggerTooltip:!0,triggerEmphasis:!0,value:null,status:null,link:[],animation:null,animationDurationUpdate:200,lineStyle:{color:"#B9BEC9",width:1,type:"dashed"},shadowStyle:{color:"rgba(210,219,238,0.2)"},label:{show:!0,formatter:null,precision:"auto",margin:3,color:"#fff",padding:[5,7,5,7],backgroundColor:"auto",borderColor:null,borderWidth:0,borderRadius:3},handle:{show:!1,icon:"M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7v-1.2h6.6z M13.3,22H6.7v-1.2h6.6z M13.3,19.6H6.7v-1.2h6.6z",size:45,margin:50,color:"#333",shadowBlur:3,shadowColor:"#aaa",shadowOffsetX:0,shadowOffsetY:2,throttle:40}},t}(tt),Ie=mt(),Zb=D;function wg(r,t,e){if(!H.node){var i=t.getZr();Ie(i).records||(Ie(i).records={}),Kb(i,t);var n=Ie(i).records[r]||(Ie(i).records[r]={});n.handler=e}}function Kb(r,t){if(Ie(r).initialized)return;Ie(r).initialized=!0,e("click",yt(Wv,"click")),e("mousemove",yt(Wv,"mousemove")),e("globalout",Jb);function e(i,n){r.on(i,function(a){var o=jb(t);Zb(Ie(r).records,function(s){s&&n(s,a,o.dispatchAction)}),Qb(o.pendings,t)})}}function Qb(r,t){var e=r.showTip.length,i=r.hideTip.length,n;e?n=r.showTip[e-1]:i&&(n=r.hideTip[i-1]),n&&(n.dispatchAction=null,t.dispatchAction(n))}function Jb(r,t,e){r.handler("leave",null,e)}function Wv(r,t,e,i){t.handler(r,e,i)}function jb(r){var t={showTip:[],hideTip:[]},e=function(i){var n=t[i.type];n?n.push(i):(i.dispatchAction=e,r.dispatchAction(i))};return{dispatchAction:e,pendings:t}}function zu(r,t){if(!H.node){var e=t.getZr(),i=(Ie(e).records||{})[r];i&&(Ie(e).records[r]=null)}}var tx=function(r){N(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.prototype.render=function(e,i,n){var a=i.getComponent("tooltip"),o=e.get("triggerOn")||a&&a.get("triggerOn")||"mousemove|click";wg("axisPointer",n,function(s,u,l){o!=="none"&&(s==="leave"||o.indexOf(s)>=0)&&l({type:"updateAxisPointer",currTrigger:s,x:u&&u.offsetX,y:u&&u.offsetY})})},t.prototype.remove=function(e,i){zu("axisPointer",i)},t.prototype.dispose=function(e,i){zu("axisPointer",i)},t.type="axisPointer",t}(le);function Sg(r,t){var e=[],i=r.seriesIndex,n;if(i==null||!(n=t.getSeriesByIndex(i)))return{point:[]};var a=n.getData(),o=Mn(a,r);if(o==null||o<0||k(o))return{point:[]};var s=a.getItemGraphicEl(o),u=n.coordinateSystem;if(n.getTooltipPosition)e=n.getTooltipPosition(o)||[];else if(u&&u.dataToPoint)if(r.isStacked){var l=u.getBaseAxis(),f=u.getOtherAxis(l),h=f.dim,c=l.dim,v=h==="x"||h==="radius"?1:0,d=a.mapDimension(c),y=[];y[v]=a.get(d,o),y[1-v]=a.get(a.getCalculationInfo("stackResultDimension"),o),e=u.dataToPoint(y)||[]}else e=u.dataToPoint(a.getValues(Y(u.dimensions,function(g){return a.mapDimension(g)}),o))||[];else if(s){var p=s.getBoundingRect().clone();p.applyTransform(s.transform),e=[p.x+p.width/2,p.y+p.height/2]}return{point:e,el:s}}var Uv=mt();function ex(r,t,e){var i=r.currTrigger,n=[r.x,r.y],a=r,o=r.dispatchAction||lt(e.dispatchAction,e),s=t.getComponent("axisPointer").coordSysAxesInfo;if(s){Ea(n)&&(n=Sg({seriesIndex:a.seriesIndex,dataIndex:a.dataIndex},t).point);var u=Ea(n),l=a.axesInfo,f=s.axesInfo,h=i==="leave"||Ea(n),c={},v={},d={list:[],map:{}},y={showPointer:yt(ix,v),showTooltip:yt(nx,d)};D(s.coordSysMap,function(g,_){var m=u||g.containPoint(n);D(s.coordSysAxesInfo[_],function(w,T){var S=w.axis,b=ux(l,w);if(!h&&m&&(!l||b)){var M=b&&b.value;M==null&&!u&&(M=S.pointToData(n)),M!=null&&Yv(w,M,y,!1,c)}})});var p={};return D(f,function(g,_){var m=g.linkGroup;m&&!v[_]&&D(m.axesInfo,function(w,T){var S=v[T];if(w!==g&&S){var b=S.value;m.mapper&&(b=g.axis.scale.parse(m.mapper(b,Xv(w),Xv(g)))),p[g.key]=b}})}),D(p,function(g,_){Yv(f[_],g,y,!0,c)}),ax(v,f,c),ox(d,n,r,o),sx(f,o,e),c}}function Yv(r,t,e,i,n){var a=r.axis;if(!(a.scale.isBlank()||!a.containData(t))){if(!r.involveSeries){e.showPointer(r,t);return}var o=rx(t,r),s=o.payloadBatch,u=o.snapToValue;s[0]&&n.seriesIndex==null&&O(n,s[0]),!i&&r.snap&&a.containData(u)&&u!=null&&(t=u),e.showPointer(r,t,s),e.showTooltip(r,o,u)}}function rx(r,t){var e=t.axis,i=e.dim,n=r,a=[],o=Number.MAX_VALUE,s=-1;return D(t.seriesModels,function(u,l){var f=u.getData().mapDimensionsAll(i),h,c;if(u.getAxisTooltipData){var v=u.getAxisTooltipData(f,r,e);c=v.dataIndices,h=v.nestestValue}else{if(c=u.getData().indicesOfNearest(f[0],r,e.type==="category"?.5:null),!c.length)return;h=u.getData().get(f[0],c[0])}if(!(h==null||!isFinite(h))){var d=r-h,y=Math.abs(d);y<=o&&((y<o||d>=0&&s<0)&&(o=y,s=d,n=h,a.length=0),D(c,function(p){a.push({seriesIndex:u.seriesIndex,dataIndexInside:p,dataIndex:u.getData().getRawIndex(p)})}))}}),{payloadBatch:a,snapToValue:n}}function ix(r,t,e,i){r[t.key]={value:e,payloadBatch:i}}function nx(r,t,e,i){var n=e.payloadBatch,a=t.axis,o=a.model,s=t.axisPointerModel;if(!(!t.triggerTooltip||!n.length)){var u=t.coordSys.model,l=Cn(u),f=r.map[l];f||(f=r.map[l]={coordSysId:u.id,coordSysIndex:u.componentIndex,coordSysType:u.type,coordSysMainType:u.mainType,dataByAxis:[]},r.list.push(f)),f.dataByAxis.push({axisDim:a.dim,axisIndex:o.componentIndex,axisType:o.type,axisId:o.id,value:i,valueLabelOpt:{precision:s.get(["label","precision"]),formatter:s.get(["label","formatter"])},seriesDataIndices:n.slice()})}}function ax(r,t,e){var i=e.axesInfo=[];D(t,function(n,a){var o=n.axisPointerModel.option,s=r[a];s?(!n.useHandle&&(o.status="show"),o.value=s.value,o.seriesDataIndices=(s.payloadBatch||[]).slice()):!n.useHandle&&(o.status="hide"),o.status==="show"&&i.push({axisDim:n.axis.dim,axisIndex:n.axis.model.componentIndex,value:o.value})})}function ox(r,t,e,i){if(Ea(t)||!r.list.length){i({type:"hideTip"});return}var n=((r.list[0].dataByAxis[0]||{}).seriesDataIndices||[])[0]||{};i({type:"showTip",escapeConnect:!0,x:t[0],y:t[1],tooltipOption:e.tooltipOption,position:e.position,dataIndexInside:n.dataIndexInside,dataIndex:n.dataIndex,seriesIndex:n.seriesIndex,dataByCoordSys:r.list})}function sx(r,t,e){var i=e.getZr(),n="axisPointerLastHighlights",a=Uv(i)[n]||{},o=Uv(i)[n]={};D(r,function(l,f){var h=l.axisPointerModel.option;h.status==="show"&&l.triggerEmphasis&&D(h.seriesDataIndices,function(c){var v=c.seriesIndex+" | "+c.dataIndex;o[v]=c})});var s=[],u=[];D(a,function(l,f){!o[f]&&u.push(l)}),D(o,function(l,f){!a[f]&&s.push(l)}),u.length&&e.dispatchAction({type:"downplay",escapeConnect:!0,notBlur:!0,batch:u}),s.length&&e.dispatchAction({type:"highlight",escapeConnect:!0,notBlur:!0,batch:s})}function ux(r,t){for(var e=0;e<(r||[]).length;e++){var i=r[e];if(t.axis.dim===i.axisDim&&t.axis.model.componentIndex===i.axisIndex)return i}}function Xv(r){var t=r.axis.model,e={},i=e.axisDim=r.axis.dim;return e.axisIndex=e[i+"AxisIndex"]=t.componentIndex,e.axisName=e[i+"AxisName"]=t.name,e.axisId=e[i+"AxisId"]=t.id,e}function Ea(r){return!r||r[0]==null||isNaN(r[0])||r[1]==null||isNaN(r[1])}function lx(r){Fb.registerAxisPointerClass("CartesianAxisPointer",Xb),r.registerComponentModel(qb),r.registerComponentView(tx),r.registerPreprocessor(function(t){if(t){(!t.axisPointer||t.axisPointer.length===0)&&(t.axisPointer={});var e=t.axisPointer.link;e&&!k(e)&&(t.axisPointer.link=[e])}}),r.registerProcessor(r.PRIORITY.PROCESSOR.STATISTIC,function(t,e){t.getComponent("axisPointer").coordSysAxesInfo=Ib(t,e)}),r.registerAction({type:"updateAxisPointer",event:"updateAxisPointer",update:":updateAxisPointer"},ex)}function SD(r,t,e){var i=t.getBoxLayoutParams(),n=t.get("padding"),a={width:e.getWidth(),height:e.getHeight()},o=gi(i,a,n);di(t.get("orient"),r,t.get("itemGap"),o.width,o.height),D1(r,i,a,n)}function fx(r,t){var e=In(t.get("padding")),i=t.getItemStyle(["color","opacity"]);return i.fill=t.get("backgroundColor"),r=new Pt({shape:{x:r.x-e[3],y:r.y-e[0],width:r.width+e[1]+e[3],height:r.height+e[0]+e[2],r:t.get("borderRadius")},style:i,silent:!0,z2:-1}),r}var hx=function(r){N(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.type="tooltip",t.dependencies=["axisPointer"],t.defaultOption={z:60,show:!0,showContent:!0,trigger:"item",triggerOn:"mousemove|click",alwaysShowContent:!1,displayMode:"single",renderMode:"auto",confine:null,showDelay:0,hideDelay:100,transitionDuration:.4,enterable:!1,backgroundColor:"#fff",shadowBlur:10,shadowColor:"rgba(0, 0, 0, .2)",shadowOffsetX:1,shadowOffsetY:2,borderRadius:4,borderWidth:1,padding:null,extraCssText:"",axisPointer:{type:"line",axis:"auto",animation:"auto",animationDurationUpdate:200,animationEasingUpdate:"exponentialOut",crossStyle:{color:"#999",width:1,type:"dashed",textStyle:{}}},textStyle:{color:"#666",fontSize:14}},t}(tt);function Tg(r){var t=r.get("confine");return t!=null?!!t:r.get("renderMode")==="richText"}function bg(r){if(H.domSupported){for(var t=document.documentElement.style,e=0,i=r.length;e<i;e++)if(r[e]in t)return r[e]}}var xg=bg(["transform","webkitTransform","OTransform","MozTransform","msTransform"]),vx=bg(["webkitTransition","transition","OTransition","MozTransition","msTransition"]);function Cg(r,t){if(!r)return t;t=Gd(t,!0);var e=r.indexOf(t);return r=e===-1?t:"-"+r.slice(0,e)+"-"+t,r.toLowerCase()}function cx(r,t){var e=r.currentStyle||document.defaultView&&document.defaultView.getComputedStyle(r);return e?e[t]:null}var dx=Cg(vx,"transition"),Xl=Cg(xg,"transform"),px="position:absolute;display:block;border-style:solid;white-space:nowrap;z-index:9999999;"+(H.transform3dSupported?"will-change:transform;":"");function gx(r){return r=r==="left"?"right":r==="right"?"left":r==="top"?"bottom":"top",r}function yx(r,t,e){if(!B(e)||e==="inside")return"";var i=r.get("backgroundColor"),n=r.get("borderWidth");t=_n(t);var a=gx(e),o=Math.max(Math.round(n)*1.5,6),s="",u=Xl+":",l;at(["left","right"],a)>-1?(s+="top:50%",u+="translateY(-50%) rotate("+(l=a==="left"?-225:-45)+"deg)"):(s+="left:50%",u+="translateX(-50%) rotate("+(l=a==="top"?225:45)+"deg)");var f=l*Math.PI/180,h=o+n,c=h*Math.abs(Math.cos(f))+h*Math.abs(Math.sin(f)),v=Math.round(((c-Math.SQRT2*n)/2+Math.SQRT2*n-(c-h)/2)*100)/100;s+=";"+a+":-"+v+"px";var d=t+" solid "+n+"px;",y=["position:absolute;width:"+o+"px;height:"+o+"px;z-index:-1;",s+";"+u+";","border-bottom:"+d,"border-right:"+d,"background-color:"+i+";"];return'<div style="'+y.join("")+'"></div>'}function _x(r,t){var e="cubic-bezier(0.23,1,0.32,1)",i=" "+r/2+"s "+e,n="opacity"+i+",visibility"+i;return t||(i=" "+r+"s "+e,n+=H.transformSupported?","+Xl+i:",left"+i+",top"+i),dx+":"+n}function $v(r,t,e){var i=r.toFixed(0)+"px",n=t.toFixed(0)+"px";if(!H.transformSupported)return e?"top:"+n+";left:"+i+";":[["top",n],["left",i]];var a=H.transform3dSupported,o="translate"+(a?"3d":"")+"("+i+","+n+(a?",0":"")+")";return e?"top:0;left:0;"+Xl+":"+o+";":[["top",0],["left",0],[xg,o]]}function mx(r){var t=[],e=r.get("fontSize"),i=r.getTextColor();i&&t.push("color:"+i),t.push("font:"+r.getFont());var n=W(r.get("lineHeight"),Math.round(e*3/2));e&&t.push("line-height:"+n+"px");var a=r.get("textShadowColor"),o=r.get("textShadowBlur")||0,s=r.get("textShadowOffsetX")||0,u=r.get("textShadowOffsetY")||0;return a&&o&&t.push("text-shadow:"+s+"px "+u+"px "+o+"px "+a),D(["decoration","align"],function(l){var f=r.get(l);f&&t.push("text-"+l+":"+f)}),t.join(";")}function Sx(r,t,e){var i=[],n=r.get("transitionDuration"),a=r.get("backgroundColor"),o=r.get("shadowBlur"),s=r.get("shadowColor"),u=r.get("shadowOffsetX"),l=r.get("shadowOffsetY"),f=r.getModel("textStyle"),h=yp(r,"html"),c=u+"px "+l+"px "+o+"px "+s;return i.push("box-shadow:"+c),t&&n&&i.push(_x(n,e)),a&&i.push("background-color:"+a),D(["width","color","radius"],function(v){var d="border-"+v,y=Gd(d),p=r.get(y);p!=null&&i.push(d+":"+p+(v==="color"?"":"px"))}),i.push(mx(f)),h!=null&&i.push("padding:"+In(h).join("px ")+"px"),i.join(";")+";"}function qv(r,t,e,i,n){var a=t&&t.painter;if(e){var o=a&&a.getViewportRoot();o&&cy(r,o,e,i,n)}else{r[0]=i,r[1]=n;var s=a&&a.getViewportRootOffset();s&&(r[0]+=s.offsetLeft,r[1]+=s.offsetTop)}r[2]=r[0]/t.getWidth(),r[3]=r[1]/t.getHeight()}var Tx=function(){function r(t,e){if(this._show=!1,this._styleCoord=[0,0,0,0],this._enterable=!0,this._alwaysShowContent=!1,this._firstShow=!0,this._longHide=!0,H.wxa)return null;var i=document.createElement("div");i.domBelongToZr=!0,this.el=i;var n=this._zr=t.getZr(),a=e.appendTo,o=a&&(B(a)?document.querySelector(a):hn(a)?a:K(a)&&a(t.getDom()));qv(this._styleCoord,n,o,t.getWidth()/2,t.getHeight()/2),(o||t.getDom()).appendChild(i),this._api=t,this._container=o;var s=this;i.onmouseenter=function(){s._enterable&&(clearTimeout(s._hideTimeout),s._show=!0),s._inContent=!0},i.onmousemove=function(u){if(u=u||window.event,!s._enterable){var l=n.handler,f=n.painter.getViewportRoot();qt(f,u,!0),l.dispatch("mousemove",u)}},i.onmouseleave=function(){s._inContent=!1,s._enterable&&s._show&&s.hideLater(s._hideDelay)}}return r.prototype.update=function(t){if(!this._container){var e=this._api.getDom(),i=cx(e,"position"),n=e.style;n.position!=="absolute"&&i!=="absolute"&&(n.position="relative")}var a=t.get("alwaysShowContent");a&&this._moveIfResized(),this._alwaysShowContent=a,this.el.className=t.get("className")||""},r.prototype.show=function(t,e){clearTimeout(this._hideTimeout),clearTimeout(this._longHideTimeout);var i=this.el,n=i.style,a=this._styleCoord;i.innerHTML?n.cssText=px+Sx(t,!this._firstShow,this._longHide)+$v(a[0],a[1],!0)+("border-color:"+_n(e)+";")+(t.get("extraCssText")||"")+(";pointer-events:"+(this._enterable?"auto":"none")):n.display="none",this._show=!0,this._firstShow=!1,this._longHide=!1},r.prototype.setContent=function(t,e,i,n,a){var o=this.el;if(t==null){o.innerHTML="";return}var s="";if(B(a)&&i.get("trigger")==="item"&&!Tg(i)&&(s=yx(i,n,a)),B(t))o.innerHTML=t+s;else if(t){o.innerHTML="",k(t)||(t=[t]);for(var u=0;u<t.length;u++)hn(t[u])&&t[u].parentNode!==o&&o.appendChild(t[u]);if(s&&o.childNodes.length){var l=document.createElement("div");l.innerHTML=s,o.appendChild(l)}}},r.prototype.setEnterable=function(t){this._enterable=t},r.prototype.getSize=function(){var t=this.el;return t?[t.offsetWidth,t.offsetHeight]:[0,0]},r.prototype.moveTo=function(t,e){if(this.el){var i=this._styleCoord;if(qv(i,this._zr,this._container,t,e),i[0]!=null&&i[1]!=null){var n=this.el.style,a=$v(i[0],i[1]);D(a,function(o){n[o[0]]=o[1]})}}},r.prototype._moveIfResized=function(){var t=this._styleCoord[2],e=this._styleCoord[3];this.moveTo(t*this._zr.getWidth(),e*this._zr.getHeight())},r.prototype.hide=function(){var t=this,e=this.el.style;e.visibility="hidden",e.opacity="0",H.transform3dSupported&&(e.willChange=""),this._show=!1,this._longHideTimeout=setTimeout(function(){return t._longHide=!0},500)},r.prototype.hideLater=function(t){this._show&&!(this._inContent&&this._enterable)&&!this._alwaysShowContent&&(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(lt(this.hide,this),t)):this.hide())},r.prototype.isShow=function(){return this._show},r.prototype.dispose=function(){clearTimeout(this._hideTimeout),clearTimeout(this._longHideTimeout);var t=this.el.parentNode;t&&t.removeChild(this.el),this.el=this._container=null},r}(),bx=function(){function r(t){this._show=!1,this._styleCoord=[0,0,0,0],this._alwaysShowContent=!1,this._enterable=!0,this._zr=t.getZr(),Kv(this._styleCoord,this._zr,t.getWidth()/2,t.getHeight()/2)}return r.prototype.update=function(t){var e=t.get("alwaysShowContent");e&&this._moveIfResized(),this._alwaysShowContent=e},r.prototype.show=function(){this._hideTimeout&&clearTimeout(this._hideTimeout),this.el.show(),this._show=!0},r.prototype.setContent=function(t,e,i,n,a){var o=this;F(t)&&At(""),this.el&&this._zr.remove(this.el);var s=i.getModel("textStyle");this.el=new Lt({style:{rich:e.richTextStyles,text:t,lineHeight:22,borderWidth:1,borderColor:n,textShadowColor:s.get("textShadowColor"),fill:i.get(["textStyle","color"]),padding:yp(i,"richText"),verticalAlign:"top",align:"left"},z:i.get("z")}),D(["backgroundColor","borderRadius","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"],function(l){o.el.style[l]=i.get(l)}),D(["textShadowBlur","textShadowOffsetX","textShadowOffsetY"],function(l){o.el.style[l]=s.get(l)||0}),this._zr.add(this.el);var u=this;this.el.on("mouseover",function(){u._enterable&&(clearTimeout(u._hideTimeout),u._show=!0),u._inContent=!0}),this.el.on("mouseout",function(){u._enterable&&u._show&&u.hideLater(u._hideDelay),u._inContent=!1})},r.prototype.setEnterable=function(t){this._enterable=t},r.prototype.getSize=function(){var t=this.el,e=this.el.getBoundingRect(),i=Zv(t.style);return[e.width+i.left+i.right,e.height+i.top+i.bottom]},r.prototype.moveTo=function(t,e){var i=this.el;if(i){var n=this._styleCoord;Kv(n,this._zr,t,e),t=n[0],e=n[1];var a=i.style,o=We(a.borderWidth||0),s=Zv(a);i.x=t+o+s.left,i.y=e+o+s.top,i.markRedraw()}},r.prototype._moveIfResized=function(){var t=this._styleCoord[2],e=this._styleCoord[3];this.moveTo(t*this._zr.getWidth(),e*this._zr.getHeight())},r.prototype.hide=function(){this.el&&this.el.hide(),this._show=!1},r.prototype.hideLater=function(t){this._show&&!(this._inContent&&this._enterable)&&!this._alwaysShowContent&&(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(lt(this.hide,this),t)):this.hide())},r.prototype.isShow=function(){return this._show},r.prototype.dispose=function(){this._zr.remove(this.el)},r}();function We(r){return Math.max(0,r)}function Zv(r){var t=We(r.shadowBlur||0),e=We(r.shadowOffsetX||0),i=We(r.shadowOffsetY||0);return{left:We(t-e),right:We(t+e),top:We(t-i),bottom:We(t+i)}}function Kv(r,t,e,i){r[0]=e,r[1]=i,r[2]=r[0]/t.getWidth(),r[3]=r[1]/t.getHeight()}var xx=new Pt({shape:{x:-1,y:-1,width:2,height:2}}),Cx=function(r){N(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.prototype.init=function(e,i){if(!(H.node||!i.getDom())){var n=e.getComponent("tooltip"),a=this._renderMode=V_(n.get("renderMode"));this._tooltipContent=a==="richText"?new bx(i):new Tx(i,{appendTo:n.get("appendToBody",!0)?"body":n.get("appendTo",!0)})}},t.prototype.render=function(e,i,n){if(!(H.node||!n.getDom())){this.group.removeAll(),this._tooltipModel=e,this._ecModel=i,this._api=n;var a=this._tooltipContent;a.update(e),a.setEnterable(e.get("enterable")),this._initGlobalListener(),this._keepShow(),this._renderMode!=="richText"&&e.get("transitionDuration")?wp(this,"_updatePosition",50,"fixRate"):Au(this,"_updatePosition")}},t.prototype._initGlobalListener=function(){var e=this._tooltipModel,i=e.get("triggerOn");wg("itemTooltip",this._api,lt(function(n,a,o){i!=="none"&&(i.indexOf(n)>=0?this._tryShow(a,o):n==="leave"&&this._hide(o))},this))},t.prototype._keepShow=function(){var e=this._tooltipModel,i=this._ecModel,n=this._api,a=e.get("triggerOn");if(this._lastX!=null&&this._lastY!=null&&a!=="none"&&a!=="click"){var o=this;clearTimeout(this._refreshUpdateTimeout),this._refreshUpdateTimeout=setTimeout(function(){!n.isDisposed()&&o.manuallyShowTip(e,i,n,{x:o._lastX,y:o._lastY,dataByCoordSys:o._lastDataByCoordSys})})}},t.prototype.manuallyShowTip=function(e,i,n,a){if(!(a.from===this.uid||H.node||!n.getDom())){var o=Qv(a,n);this._ticket="";var s=a.dataByCoordSys,u=Px(a,i,n);if(u){var l=u.el.getBoundingRect().clone();l.applyTransform(u.el.transform),this._tryShow({offsetX:l.x+l.width/2,offsetY:l.y+l.height/2,target:u.el,position:a.position,positionDefault:"bottom"},o)}else if(a.tooltip&&a.x!=null&&a.y!=null){var f=xx;f.x=a.x,f.y=a.y,f.update(),rt(f).tooltipConfig={name:null,option:a.tooltip},this._tryShow({offsetX:a.x,offsetY:a.y,target:f},o)}else if(s)this._tryShow({offsetX:a.x,offsetY:a.y,position:a.position,dataByCoordSys:s,tooltipOption:a.tooltipOption},o);else if(a.seriesIndex!=null){if(this._manuallyAxisShowTip(e,i,n,a))return;var h=Sg(a,i),c=h.point[0],v=h.point[1];c!=null&&v!=null&&this._tryShow({offsetX:c,offsetY:v,target:h.el,position:a.position,positionDefault:"bottom"},o)}else a.x!=null&&a.y!=null&&(n.dispatchAction({type:"updateAxisPointer",x:a.x,y:a.y}),this._tryShow({offsetX:a.x,offsetY:a.y,position:a.position,target:n.getZr().findHover(a.x,a.y).target},o))}},t.prototype.manuallyHideTip=function(e,i,n,a){var o=this._tooltipContent;this._tooltipModel&&o.hideLater(this._tooltipModel.get("hideDelay")),this._lastX=this._lastY=this._lastDataByCoordSys=null,a.from!==this.uid&&this._hide(Qv(a,n))},t.prototype._manuallyAxisShowTip=function(e,i,n,a){var o=a.seriesIndex,s=a.dataIndex,u=i.getComponent("axisPointer").coordSysAxesInfo;if(!(o==null||s==null||u==null)){var l=i.getSeriesByIndex(o);if(l){var f=l.getData(),h=Wi([f.getItemModel(s),l,(l.coordinateSystem||{}).model],this._tooltipModel);if(h.get("trigger")==="axis")return n.dispatchAction({type:"updateAxisPointer",seriesIndex:o,dataIndex:s,position:a.position}),!0}}},t.prototype._tryShow=function(e,i){var n=e.target,a=this._tooltipModel;if(a){this._lastX=e.offsetX,this._lastY=e.offsetY;var o=e.dataByCoordSys;if(o&&o.length)this._showAxisTooltip(o,e);else if(n){var s=rt(n);if(s.ssrType==="legend")return;this._lastDataByCoordSys=null;var u,l;Qi(n,function(f){if(rt(f).dataIndex!=null)return u=f,!0;if(rt(f).tooltipConfig!=null)return l=f,!0},!0),u?this._showSeriesItemTooltip(e,u,i):l?this._showComponentItemTooltip(e,l,i):this._hide(i)}else this._lastDataByCoordSys=null,this._hide(i)}},t.prototype._showOrMove=function(e,i){var n=e.get("showDelay");i=lt(i,this),clearTimeout(this._showTimout),n>0?this._showTimout=setTimeout(i,n):i()},t.prototype._showAxisTooltip=function(e,i){var n=this._ecModel,a=this._tooltipModel,o=[i.offsetX,i.offsetY],s=Wi([i.tooltipOption],a),u=this._renderMode,l=[],f=Sn("section",{blocks:[],noHeader:!0}),h=[],c=new Cs;D(e,function(_){D(_.dataByAxis,function(m){var w=n.getComponent(m.axisDim+"Axis",m.axisIndex),T=m.value;if(!(!w||T==null)){var S=_g(T,w.axis,n,m.seriesDataIndices,m.valueLabelOpt),b=Sn("section",{header:S,noHeader:!ye(S),sortBlocks:!0,blocks:[]});f.blocks.push(b),D(m.seriesDataIndices,function(M){var x=n.getSeriesByIndex(M.seriesIndex),C=M.dataIndexInside,A=x.getDataParams(C);if(!(A.dataIndex<0)){A.axisDim=m.axisDim,A.axisIndex=m.axisIndex,A.axisType=m.axisType,A.axisId=m.axisId,A.axisValue=Ul(w.axis,{value:T}),A.axisValueLabel=S,A.marker=c.makeTooltipMarker("item",_n(A.color),u);var L=Nh(x.formatTooltip(C,!0,null)),I=L.frag;if(I){var P=Wi([x],a).get("valueFormatter");b.blocks.push(P?O({valueFormatter:P},I):I)}L.text&&h.push(L.text),l.push(A)}})}})}),f.blocks.reverse(),h.reverse();var v=i.position,d=s.get("order"),y=Wh(f,c,u,d,n.get("useUTC"),s.get("textStyle"));y&&h.unshift(y);var p=u==="richText"?`

`:"<br/>",g=h.join(p);this._showOrMove(s,function(){this._updateContentNotChangedOnAxis(e,l)?this._updatePosition(s,v,o[0],o[1],this._tooltipContent,l):this._showTooltipContent(s,g,l,Math.random()+"",o[0],o[1],v,null,c)})},t.prototype._showSeriesItemTooltip=function(e,i,n){var a=this._ecModel,o=rt(i),s=o.seriesIndex,u=a.getSeriesByIndex(s),l=o.dataModel||u,f=o.dataIndex,h=o.dataType,c=l.getData(h),v=this._renderMode,d=e.positionDefault,y=Wi([c.getItemModel(f),l,u&&(u.coordinateSystem||{}).model],this._tooltipModel,d?{position:d}:null),p=y.get("trigger");if(!(p!=null&&p!=="item")){var g=l.getDataParams(f,h),_=new Cs;g.marker=_.makeTooltipMarker("item",_n(g.color),v);var m=Nh(l.formatTooltip(f,!1,h)),w=y.get("order"),T=y.get("valueFormatter"),S=m.frag,b=S?Wh(T?O({valueFormatter:T},S):S,_,v,w,a.get("useUTC"),y.get("textStyle")):m.text,M="item_"+l.name+"_"+f;this._showOrMove(y,function(){this._showTooltipContent(y,b,g,M,e.offsetX,e.offsetY,e.position,e.target,_)}),n({type:"showTip",dataIndexInside:f,dataIndex:c.getRawIndex(f),seriesIndex:s,from:this.uid})}},t.prototype._showComponentItemTooltip=function(e,i,n){var a=this._renderMode==="html",o=rt(i),s=o.tooltipConfig,u=s.option||{},l=u.encodeHTMLContent;if(B(u)){var f=u;u={content:f,formatter:f},l=!0}l&&a&&u.content&&(u=Z(u),u.content=Ot(u.content));var h=[u],c=this._ecModel.getComponent(o.componentMainType,o.componentIndex);c&&h.push(c),h.push({formatter:u.content});var v=e.positionDefault,d=Wi(h,this._tooltipModel,v?{position:v}:null),y=d.get("content"),p=Math.random()+"",g=new Cs;this._showOrMove(d,function(){var _=Z(d.get("formatterParams")||{});this._showTooltipContent(d,y,_,p,e.offsetX,e.offsetY,e.position,i,g)}),n({type:"showTip",from:this.uid})},t.prototype._showTooltipContent=function(e,i,n,a,o,s,u,l,f){if(this._ticket="",!(!e.get("showContent")||!e.get("show"))){var h=this._tooltipContent;h.setEnterable(e.get("enterable"));var c=e.get("formatter");u=u||e.get("position");var v=i,d=this._getNearestPoint([o,s],n,e.get("trigger"),e.get("borderColor")),y=d.color;if(c)if(B(c)){var p=e.ecModel.get("useUTC"),g=k(n)?n[0]:n,_=g&&g.axisType&&g.axisType.indexOf("time")>=0;v=c,_&&(v=wo(g.axisValue,v,p)),v=Vd(v,n,!0)}else if(K(c)){var m=lt(function(w,T){w===this._ticket&&(h.setContent(T,f,e,y,u),this._updatePosition(e,u,o,s,h,n,l))},this);this._ticket=a,v=c(n,a,m)}else v=c;h.setContent(v,f,e,y,u),h.show(e,y),this._updatePosition(e,u,o,s,h,n,l)}},t.prototype._getNearestPoint=function(e,i,n,a){if(n==="axis"||k(i))return{color:a||(this._renderMode==="html"?"#fff":"none")};if(!k(i))return{color:a||i.color||i.borderColor}},t.prototype._updatePosition=function(e,i,n,a,o,s,u){var l=this._api.getWidth(),f=this._api.getHeight();i=i||e.get("position");var h=o.getSize(),c=e.get("align"),v=e.get("verticalAlign"),d=u&&u.getBoundingRect().clone();if(u&&d.applyTransform(u.transform),K(i)&&(i=i([n,a],s,o.el,d,{viewSize:[l,f],contentSize:h.slice()})),k(i))n=ct(i[0],l),a=ct(i[1],f);else if(F(i)){var y=i;y.width=h[0],y.height=h[1];var p=gi(y,{width:l,height:f});n=p.x,a=p.y,c=null,v=null}else if(B(i)&&u){var g=Ax(i,d,h,e.get("borderWidth"));n=g[0],a=g[1]}else{var g=Dx(n,a,o,l,f,c?null:20,v?null:20);n=g[0],a=g[1]}if(c&&(n-=Jv(c)?h[0]/2:c==="right"?h[0]:0),v&&(a-=Jv(v)?h[1]/2:v==="bottom"?h[1]:0),Tg(e)){var g=Mx(n,a,o,l,f);n=g[0],a=g[1]}o.moveTo(n,a)},t.prototype._updateContentNotChangedOnAxis=function(e,i){var n=this._lastDataByCoordSys,a=this._cbParamsList,o=!!n&&n.length===e.length;return o&&D(n,function(s,u){var l=s.dataByAxis||[],f=e[u]||{},h=f.dataByAxis||[];o=o&&l.length===h.length,o&&D(l,function(c,v){var d=h[v]||{},y=c.seriesDataIndices||[],p=d.seriesDataIndices||[];o=o&&c.value===d.value&&c.axisType===d.axisType&&c.axisId===d.axisId&&y.length===p.length,o&&D(y,function(g,_){var m=p[_];o=o&&g.seriesIndex===m.seriesIndex&&g.dataIndex===m.dataIndex}),a&&D(c.seriesDataIndices,function(g){var _=g.seriesIndex,m=i[_],w=a[_];m&&w&&w.data!==m.data&&(o=!1)})})}),this._lastDataByCoordSys=e,this._cbParamsList=i,!!o},t.prototype._hide=function(e){this._lastDataByCoordSys=null,e({type:"hideTip",from:this.uid})},t.prototype.dispose=function(e,i){H.node||!i.getDom()||(Au(this,"_updatePosition"),this._tooltipContent.dispose(),zu("itemTooltip",i))},t.type="tooltip",t}(le);function Wi(r,t,e){var i=t.ecModel,n;e?(n=new ht(e,i,i),n=new ht(t.option,n,i)):n=t;for(var a=r.length-1;a>=0;a--){var o=r[a];o&&(o instanceof ht&&(o=o.get("tooltip",!0)),B(o)&&(o={formatter:o}),o&&(n=new ht(o,n,i)))}return n}function Qv(r,t){return r.dispatchAction||lt(t.dispatchAction,t)}function Dx(r,t,e,i,n,a,o){var s=e.getSize(),u=s[0],l=s[1];return a!=null&&(r+u+a+2>i?r-=u+a:r+=a),o!=null&&(t+l+o>n?t-=l+o:t+=o),[r,t]}function Mx(r,t,e,i,n){var a=e.getSize(),o=a[0],s=a[1];return r=Math.min(r+o,i)-o,t=Math.min(t+s,n)-s,r=Math.max(r,0),t=Math.max(t,0),[r,t]}function Ax(r,t,e,i){var n=e[0],a=e[1],o=Math.ceil(Math.SQRT2*i)+8,s=0,u=0,l=t.width,f=t.height;switch(r){case"inside":s=t.x+l/2-n/2,u=t.y+f/2-a/2;break;case"top":s=t.x+l/2-n/2,u=t.y-a-o;break;case"bottom":s=t.x+l/2-n/2,u=t.y+f+o;break;case"left":s=t.x-n-o,u=t.y+f/2-a/2;break;case"right":s=t.x+l+o,u=t.y+f/2-a/2}return[s,u]}function Jv(r){return r==="center"||r==="middle"}function Px(r,t,e){var i=il(r).queryOptionMap,n=i.keys()[0];if(!(!n||n==="series")){var a=An(t,n,i.get(n),{useDefault:!1,enableAll:!1,enableNone:!1}),o=a.models[0];if(o){var s=e.getViewOfComponentModel(o),u;if(s.group.traverse(function(l){var f=rt(l).tooltipConfig;if(f&&f.name===r.name)return u=l,!0}),u)return{componentMainType:n,componentIndex:o.componentIndex,el:u}}}}function TD(r){xn(lx),r.registerComponentModel(hx),r.registerComponentView(Cx),r.registerAction({type:"showTip",event:"showTip",update:"tooltip:manuallyShowTip"},Ft),r.registerAction({type:"hideTip",event:"hideTip",update:"tooltip:manuallyHideTip"},Ft)}var Lx=function(r){N(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e.layoutMode={type:"box",ignoreSize:!0},e}return t.type="title",t.defaultOption={z:6,show:!0,text:"",target:"blank",subtext:"",subtarget:"blank",left:0,top:0,backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderWidth:0,padding:5,itemGap:10,textStyle:{fontSize:18,fontWeight:"bold",color:"#464646"},subtextStyle:{fontSize:12,color:"#6E7079"}},t}(tt),Ix=function(r){N(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.prototype.render=function(e,i,n){if(this.group.removeAll(),!!e.get("show")){var a=this.group,o=e.getModel("textStyle"),s=e.getModel("subtextStyle"),u=e.get("textAlign"),l=W(e.get("textBaseline"),e.get("textVerticalAlign")),f=new Lt({style:tr(o,{text:e.get("text"),fill:o.getTextColor()},{disableBox:!0}),z2:10}),h=f.getBoundingRect(),c=e.get("subtext"),v=new Lt({style:tr(s,{text:c,fill:s.getTextColor(),y:h.height+e.get("itemGap"),verticalAlign:"top"},{disableBox:!0}),z2:10}),d=e.get("link"),y=e.get("sublink"),p=e.get("triggerEvent",!0);f.silent=!d&&!p,v.silent=!y&&!p,d&&f.on("click",function(){mh(d,"_"+e.get("target"))}),y&&v.on("click",function(){mh(y,"_"+e.get("subtarget"))}),rt(f).eventData=rt(v).eventData=p?{componentType:"title",componentIndex:e.componentIndex}:null,a.add(f),c&&a.add(v);var g=a.getBoundingRect(),_=e.getBoxLayoutParams();_.width=g.width,_.height=g.height;var m=gi(_,{width:n.getWidth(),height:n.getHeight()},e.get("padding"));u||(u=e.get("left")||e.get("right"),u==="middle"&&(u="center"),u==="right"?m.x+=m.width:u==="center"&&(m.x+=m.width/2)),l||(l=e.get("top")||e.get("bottom"),l==="center"&&(l="middle"),l==="bottom"?m.y+=m.height:l==="middle"&&(m.y+=m.height/2),l=l||"top"),a.x=m.x,a.y=m.y,a.markRedraw();var w={align:u,verticalAlign:l};f.setStyle(w),v.setStyle(w),g=a.getBoundingRect();var T=m.margin,S=e.getItemStyle(["color","opacity"]);S.fill=e.get("backgroundColor");var b=new Pt({shape:{x:g.x-T[3],y:g.y-T[0],width:g.width+T[1]+T[3],height:g.height+T[0]+T[2],r:e.get("borderRadius")},style:S,subPixelOptimize:!0,silent:!0});a.add(b)}},t.type="title",t}(le);function bD(r){r.registerComponentModel(Lx),r.registerComponentView(Ix)}var Rx=function(r,t){if(t==="all")return{type:"all",title:r.getLocaleModel().get(["legend","selector","all"])};if(t==="inverse")return{type:"inverse",title:r.getLocaleModel().get(["legend","selector","inverse"])}},Hu=function(r){N(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e.layoutMode={type:"box",ignoreSize:!0},e}return t.prototype.init=function(e,i,n){this.mergeDefaultAndTheme(e,n),e.selected=e.selected||{},this._updateSelector(e)},t.prototype.mergeOption=function(e,i){r.prototype.mergeOption.call(this,e,i),this._updateSelector(e)},t.prototype._updateSelector=function(e){var i=e.selector,n=this.ecModel;i===!0&&(i=e.selector=["all","inverse"]),k(i)&&D(i,function(a,o){B(a)&&(a={type:a}),i[o]=st(a,Rx(n,a.type))})},t.prototype.optionUpdated=function(){this._updateData(this.ecModel);var e=this._data;if(e[0]&&this.get("selectedMode")==="single"){for(var i=!1,n=0;n<e.length;n++){var a=e[n].get("name");if(this.isSelected(a)){this.select(a),i=!0;break}}!i&&this.select(e[0].get("name"))}},t.prototype._updateData=function(e){var i=[],n=[];e.eachRawSeries(function(u){var l=u.name;n.push(l);var f;if(u.legendVisualProvider){var h=u.legendVisualProvider,c=h.getAllNames();e.isSeriesFiltered(u)||(n=n.concat(c)),c.length?i=i.concat(c):f=!0}else f=!0;f&&rl(u)&&i.push(u.name)}),this._availableNames=n;var a=this.get("data")||i,o=X(),s=Y(a,function(u){return(B(u)||ut(u))&&(u={name:u}),o.get(u.name)?null:(o.set(u.name,!0),new ht(u,this,this.ecModel))},this);this._data=Et(s,function(u){return!!u})},t.prototype.getData=function(){return this._data},t.prototype.select=function(e){var i=this.option.selected,n=this.get("selectedMode");if(n==="single"){var a=this._data;D(a,function(o){i[o.get("name")]=!1})}i[e]=!0},t.prototype.unSelect=function(e){this.get("selectedMode")!=="single"&&(this.option.selected[e]=!1)},t.prototype.toggleSelected=function(e){var i=this.option.selected;i.hasOwnProperty(e)||(i[e]=!0),this[i[e]?"unSelect":"select"](e)},t.prototype.allSelect=function(){var e=this._data,i=this.option.selected;D(e,function(n){i[n.get("name",!0)]=!0})},t.prototype.inverseSelect=function(){var e=this._data,i=this.option.selected;D(e,function(n){var a=n.get("name",!0);i.hasOwnProperty(a)||(i[a]=!0),i[a]=!i[a]})},t.prototype.isSelected=function(e){var i=this.option.selected;return!(i.hasOwnProperty(e)&&!i[e])&&at(this._availableNames,e)>=0},t.prototype.getOrient=function(){return this.get("orient")==="vertical"?{index:1,name:"vertical"}:{index:0,name:"horizontal"}},t.type="legend.plain",t.dependencies=["series"],t.defaultOption={z:4,show:!0,orient:"horizontal",left:"center",top:0,align:"auto",backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderRadius:0,borderWidth:0,padding:5,itemGap:10,itemWidth:25,itemHeight:14,symbolRotate:"inherit",symbolKeepAspect:!0,inactiveColor:"#ccc",inactiveBorderColor:"#ccc",inactiveBorderWidth:"auto",itemStyle:{color:"inherit",opacity:"inherit",borderColor:"inherit",borderWidth:"auto",borderCap:"inherit",borderJoin:"inherit",borderDashOffset:"inherit",borderMiterLimit:"inherit"},lineStyle:{width:"auto",color:"inherit",inactiveColor:"#ccc",inactiveWidth:2,opacity:"inherit",type:"inherit",cap:"inherit",join:"inherit",dashOffset:"inherit",miterLimit:"inherit"},textStyle:{color:"#333"},selectedMode:!0,selector:!1,selectorLabel:{show:!0,borderRadius:10,padding:[3,5,3,5],fontSize:12,fontFamily:"sans-serif",color:"#666",borderWidth:1,borderColor:"#666"},emphasis:{selectorLabel:{show:!0,color:"#eee",backgroundColor:"#666"}},selectorPosition:"auto",selectorItemGap:7,selectorButtonGap:10,tooltip:{show:!1}},t}(tt),ei=yt,Gu=D,ca=ue,Dg=function(r){N(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e.newlineDisabled=!1,e}return t.prototype.init=function(){this.group.add(this._contentGroup=new ca),this.group.add(this._selectorGroup=new ca),this._isFirstRender=!0},t.prototype.getContentGroup=function(){return this._contentGroup},t.prototype.getSelectorGroup=function(){return this._selectorGroup},t.prototype.render=function(e,i,n){var a=this._isFirstRender;if(this._isFirstRender=!1,this.resetInner(),!!e.get("show",!0)){var o=e.get("align"),s=e.get("orient");(!o||o==="auto")&&(o=e.get("left")==="right"&&s==="vertical"?"right":"left");var u=e.get("selector",!0),l=e.get("selectorPosition",!0);u&&(!l||l==="auto")&&(l=s==="horizontal"?"end":"start"),this.renderInner(o,e,i,n,u,s,l);var f=e.getBoxLayoutParams(),h={width:n.getWidth(),height:n.getHeight()},c=e.get("padding"),v=gi(f,h,c),d=this.layoutInner(e,o,v,a,u,l),y=gi(ot({width:d.width,height:d.height},f),h,c);this.group.x=y.x-d.x,this.group.y=y.y-d.y,this.group.markRedraw(),this.group.add(this._backgroundEl=fx(d,e))}},t.prototype.resetInner=function(){this.getContentGroup().removeAll(),this._backgroundEl&&this.group.remove(this._backgroundEl),this.getSelectorGroup().removeAll()},t.prototype.renderInner=function(e,i,n,a,o,s,u){var l=this.getContentGroup(),f=X(),h=i.get("selectedMode"),c=[];n.eachRawSeries(function(v){!v.get("legendHoverLink")&&c.push(v.id)}),Gu(i.getData(),function(v,d){var y=v.get("name");if(!this.newlineDisabled&&(y===""||y===`
`)){var p=new ca;p.newline=!0,l.add(p);return}var g=n.getSeriesByName(y)[0];if(!f.get(y))if(g){var _=g.getData(),m=_.getVisual("legendLineStyle")||{},w=_.getVisual("legendIcon"),T=_.getVisual("style"),S=this._createItem(g,y,d,v,i,e,m,T,w,h,a);S.on("click",ei(jv,y,null,a,c)).on("mouseover",ei(Vu,g.name,null,a,c)).on("mouseout",ei(Wu,g.name,null,a,c)),n.ssr&&S.eachChild(function(b){var M=rt(b);M.seriesIndex=g.seriesIndex,M.dataIndex=d,M.ssrType="legend"}),f.set(y,!0)}else n.eachRawSeries(function(b){if(!f.get(y)&&b.legendVisualProvider){var M=b.legendVisualProvider;if(!M.containName(y))return;var x=M.indexOfName(y),C=M.getItemVisual(x,"style"),A=M.getItemVisual(x,"legendIcon"),L=jt(C.fill);L&&L[3]===0&&(L[3]=.2,C=O(O({},C),{fill:wi(L,"rgba")}));var I=this._createItem(b,y,d,v,i,e,{},C,A,h,a);I.on("click",ei(jv,null,y,a,c)).on("mouseover",ei(Vu,null,y,a,c)).on("mouseout",ei(Wu,null,y,a,c)),n.ssr&&I.eachChild(function(P){var R=rt(P);R.seriesIndex=b.seriesIndex,R.dataIndex=d,R.ssrType="legend"}),f.set(y,!0)}},this)},this),o&&this._createSelector(o,i,a,s,u)},t.prototype._createSelector=function(e,i,n,a,o){var s=this.getSelectorGroup();Gu(e,function(l){var f=l.type,h=new Lt({style:{x:0,y:0,align:"center",verticalAlign:"middle"},onclick:function(){n.dispatchAction({type:f==="all"?"legendAllSelect":"legendInverseSelect",legendId:i.id})}});s.add(h);var c=i.getModel("selectorLabel"),v=i.getModel(["emphasis","selectorLabel"]);e1(h,{normal:c,emphasis:v},{defaultText:l.title}),pu(h)})},t.prototype._createItem=function(e,i,n,a,o,s,u,l,f,h,c){var v=e.visualDrawType,d=o.get("itemWidth"),y=o.get("itemHeight"),p=o.isSelected(i),g=a.get("symbolRotate"),_=a.get("symbolKeepAspect"),m=a.get("icon");f=m||f||"roundRect";var w=Ex(f,a,u,l,v,p,c),T=new ca,S=a.getModel("textStyle");if(K(e.getLegendIcon)&&(!m||m==="inherit"))T.add(e.getLegendIcon({itemWidth:d,itemHeight:y,icon:f,iconRotate:g,itemStyle:w.itemStyle,lineStyle:w.lineStyle,symbolKeepAspect:_}));else{var b=m==="inherit"&&e.getData().getVisual("symbol")?g==="inherit"?e.getData().getVisual("symbolRotate"):g:0;T.add(Ox({itemWidth:d,itemHeight:y,icon:f,iconRotate:b,itemStyle:w.itemStyle,symbolKeepAspect:_}))}var M=s==="left"?d+5:-5,x=s,C=o.get("formatter"),A=i;B(C)&&C?A=C.replace("{name}",i??""):K(C)&&(A=C(i));var L=p?S.getTextColor():a.get("inactiveColor");T.add(new Lt({style:tr(S,{text:A,x:M,y:y/2,fill:L,align:x,verticalAlign:"middle"},{inheritColor:L})}));var I=new Pt({shape:T.getBoundingRect(),style:{fill:"transparent"}}),P=a.getModel("tooltip");return P.get("show")&&yo({el:I,componentModel:o,itemName:i,itemTooltipOption:P.option}),T.add(I),T.eachChild(function(R){R.silent=!0}),I.silent=!h,this.getContentGroup().add(T),pu(T),T.__legendDataIndex=n,T},t.prototype.layoutInner=function(e,i,n,a,o,s){var u=this.getContentGroup(),l=this.getSelectorGroup();di(e.get("orient"),u,e.get("itemGap"),n.width,n.height);var f=u.getBoundingRect(),h=[-f.x,-f.y];if(l.markRedraw(),u.markRedraw(),o){di("horizontal",l,e.get("selectorItemGap",!0));var c=l.getBoundingRect(),v=[-c.x,-c.y],d=e.get("selectorButtonGap",!0),y=e.getOrient().index,p=y===0?"width":"height",g=y===0?"height":"width",_=y===0?"y":"x";s==="end"?v[y]+=f[p]+d:h[y]+=c[p]+d,v[1-y]+=f[g]/2-c[g]/2,l.x=v[0],l.y=v[1],u.x=h[0],u.y=h[1];var m={x:0,y:0};return m[p]=f[p]+d+c[p],m[g]=Math.max(f[g],c[g]),m[_]=Math.min(0,c[_]+v[1-y]),m}else return u.x=h[0],u.y=h[1],this.group.getBoundingRect()},t.prototype.remove=function(){this.getContentGroup().removeAll(),this._isFirstRender=!0},t.type="legend.plain",t}(le);function Ex(r,t,e,i,n,a,o){function s(p,g){p.lineWidth==="auto"&&(p.lineWidth=g.lineWidth>0?2:0),Gu(p,function(_,m){p[m]==="inherit"&&(p[m]=g[m])})}var u=t.getModel("itemStyle"),l=u.getItemStyle(),f=r.lastIndexOf("empty",0)===0?"fill":"stroke",h=u.getShallow("decal");l.decal=!h||h==="inherit"?i.decal:Eu(h,o),l.fill==="inherit"&&(l.fill=i[n]),l.stroke==="inherit"&&(l.stroke=i[f]),l.opacity==="inherit"&&(l.opacity=(n==="fill"?i:e).opacity),s(l,i);var c=t.getModel("lineStyle"),v=c.getLineStyle();if(s(v,e),l.fill==="auto"&&(l.fill=i.fill),l.stroke==="auto"&&(l.stroke=i.fill),v.stroke==="auto"&&(v.stroke=i.fill),!a){var d=t.get("inactiveBorderWidth"),y=l[f];l.lineWidth=d==="auto"?i.lineWidth>0&&y?2:0:l.lineWidth,l.fill=t.get("inactiveColor"),l.stroke=t.get("inactiveBorderColor"),v.stroke=c.get("inactiveColor"),v.lineWidth=c.get("inactiveWidth")}return{itemStyle:l,lineStyle:v}}function Ox(r){var t=r.icon||"roundRect",e=kl(t,0,0,r.itemWidth,r.itemHeight,r.itemStyle.fill,r.symbolKeepAspect);return e.setStyle(r.itemStyle),e.rotation=(r.iconRotate||0)*Math.PI/180,e.setOrigin([r.itemWidth/2,r.itemHeight/2]),t.indexOf("empty")>-1&&(e.style.stroke=e.style.fill,e.style.fill="#fff",e.style.lineWidth=2),e}function jv(r,t,e,i){Wu(r,t,e,i),e.dispatchAction({type:"legendToggleSelect",name:r??t}),Vu(r,t,e,i)}function Mg(r){for(var t=r.getZr().storage.getDisplayList(),e,i=0,n=t.length;i<n&&!(e=t[i].states.emphasis);)i++;return e&&e.hoverLayer}function Vu(r,t,e,i){Mg(e)||e.dispatchAction({type:"highlight",seriesName:r,name:t,excludeSeriesId:i})}function Wu(r,t,e,i){Mg(e)||e.dispatchAction({type:"downplay",seriesName:r,name:t,excludeSeriesId:i})}function kx(r){var t=r.findComponents({mainType:"legend"});t&&t.length&&r.filterSeries(function(e){for(var i=0;i<t.length;i++)if(!t[i].isSelected(e.name))return!1;return!0})}function Ui(r,t,e){var i=r==="allSelect"||r==="inverseSelect",n={},a=[];e.eachComponent({mainType:"legend",query:t},function(s){i?s[r]():s[r](t.name),tc(s,n),a.push(s.componentIndex)});var o={};return e.eachComponent("legend",function(s){D(n,function(u,l){s[u?"select":"unSelect"](l)}),tc(s,o)}),i?{selected:o,legendIndex:a}:{name:t.name,selected:o}}function tc(r,t){var e=t||{};return D(r.getData(),function(i){var n=i.get("name");if(!(n===`
`||n==="")){var a=r.isSelected(n);Ke(e,n)?e[n]=e[n]&&a:e[n]=a}}),e}function Bx(r){r.registerAction("legendToggleSelect","legendselectchanged",yt(Ui,"toggleSelected")),r.registerAction("legendAllSelect","legendselectall",yt(Ui,"allSelect")),r.registerAction("legendInverseSelect","legendinverseselect",yt(Ui,"inverseSelect")),r.registerAction("legendSelect","legendselected",yt(Ui,"select")),r.registerAction("legendUnSelect","legendunselected",yt(Ui,"unSelect"))}function Ag(r){r.registerComponentModel(Hu),r.registerComponentView(Dg),r.registerProcessor(r.PRIORITY.PROCESSOR.SERIES_FILTER,kx),r.registerSubTypeDefaulter("legend",function(){return"plain"}),Bx(r)}var Nx=function(r){N(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e}return t.prototype.setScrollDataIndex=function(e){this.option.scrollDataIndex=e},t.prototype.init=function(e,i,n){var a=Ml(e);r.prototype.init.call(this,e,i,n),ec(this,e,a)},t.prototype.mergeOption=function(e,i){r.prototype.mergeOption.call(this,e,i),ec(this,this.option,e)},t.type="legend.scroll",t.defaultOption=d1(Hu.defaultOption,{scrollDataIndex:0,pageButtonItemGap:5,pageButtonGap:null,pageButtonPosition:"end",pageFormatter:"{current}/{total}",pageIcons:{horizontal:["M0,0L12,-10L12,10z","M0,0L-12,-10L-12,10z"],vertical:["M0,0L20,0L10,-20z","M0,0L20,0L10,20z"]},pageIconColor:"#2f4554",pageIconInactiveColor:"#aaa",pageIconSize:15,pageTextStyle:{color:"#333"},animationDurationUpdate:800}),t}(Hu);function ec(r,t,e){var i=r.getOrient(),n=[1,1];n[i.index]=0,mn(t,e,{type:"box",ignoreSize:!!n})}var rc=ue,Gs=["width","height"],Vs=["x","y"],Fx=function(r){N(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type=t.type,e.newlineDisabled=!0,e._currentIndex=0,e}return t.prototype.init=function(){r.prototype.init.call(this),this.group.add(this._containerGroup=new rc),this._containerGroup.add(this.getContentGroup()),this.group.add(this._controllerGroup=new rc)},t.prototype.resetInner=function(){r.prototype.resetInner.call(this),this._controllerGroup.removeAll(),this._containerGroup.removeClipPath(),this._containerGroup.__rectSize=null},t.prototype.renderInner=function(e,i,n,a,o,s,u){var l=this;r.prototype.renderInner.call(this,e,i,n,a,o,s,u);var f=this._controllerGroup,h=i.get("pageIconSize",!0),c=k(h)?h:[h,h];d("pagePrev",0);var v=i.getModel("pageTextStyle");f.add(new Lt({name:"pageText",style:{text:"xx/xx",fill:v.getTextColor(),font:v.getFont(),verticalAlign:"middle",align:"center"},silent:!0})),d("pageNext",1);function d(y,p){var g=y+"DataIndex",_=ml(i.get("pageIcons",!0)[i.getOrient().name][p],{onclick:lt(l._pageGo,l,g,i,a)},{x:-c[0]/2,y:-c[1]/2,width:c[0],height:c[1]});_.name=y,f.add(_)}},t.prototype.layoutInner=function(e,i,n,a,o,s){var u=this.getSelectorGroup(),l=e.getOrient().index,f=Gs[l],h=Vs[l],c=Gs[1-l],v=Vs[1-l];o&&di("horizontal",u,e.get("selectorItemGap",!0));var d=e.get("selectorButtonGap",!0),y=u.getBoundingRect(),p=[-y.x,-y.y],g=Z(n);o&&(g[f]=n[f]-y[f]-d);var _=this._layoutContentAndController(e,a,g,l,f,c,v,h);if(o){if(s==="end")p[l]+=_[f]+d;else{var m=y[f]+d;p[l]-=m,_[h]-=m}_[f]+=y[f]+d,p[1-l]+=_[v]+_[c]/2-y[c]/2,_[c]=Math.max(_[c],y[c]),_[v]=Math.min(_[v],y[v]+p[1-l]),u.x=p[0],u.y=p[1],u.markRedraw()}return _},t.prototype._layoutContentAndController=function(e,i,n,a,o,s,u,l){var f=this.getContentGroup(),h=this._containerGroup,c=this._controllerGroup;di(e.get("orient"),f,e.get("itemGap"),a?n.width:null,a?null:n.height),di("horizontal",c,e.get("pageButtonItemGap",!0));var v=f.getBoundingRect(),d=c.getBoundingRect(),y=this._showController=v[o]>n[o],p=[-v.x,-v.y];i||(p[a]=f[l]);var g=[0,0],_=[-d.x,-d.y],m=W(e.get("pageButtonGap",!0),e.get("itemGap",!0));if(y){var w=e.get("pageButtonPosition",!0);w==="end"?_[a]+=n[o]-d[o]:g[a]+=d[o]+m}_[1-a]+=v[s]/2-d[s]/2,f.setPosition(p),h.setPosition(g),c.setPosition(_);var T={x:0,y:0};if(T[o]=y?n[o]:v[o],T[s]=Math.max(v[s],d[s]),T[u]=Math.min(0,d[u]+_[1-a]),h.__rectSize=n[o],y){var S={x:0,y:0};S[o]=Math.max(n[o]-d[o]-m,0),S[s]=T[s],h.setClipPath(new Pt({shape:S})),h.__rectSize=S[o]}else c.eachChild(function(M){M.attr({invisible:!0,silent:!0})});var b=this._getPageInfo(e);return b.pageIndex!=null&&Ln(f,{x:b.contentPosition[0],y:b.contentPosition[1]},y?e:null),this._updatePageInfoView(e,b),T},t.prototype._pageGo=function(e,i,n){var a=this._getPageInfo(i)[e];a!=null&&n.dispatchAction({type:"legendScroll",scrollDataIndex:a,legendId:i.id})},t.prototype._updatePageInfoView=function(e,i){var n=this._controllerGroup;D(["pagePrev","pageNext"],function(f){var h=f+"DataIndex",c=i[h]!=null,v=n.childOfName(f);v&&(v.setStyle("fill",c?e.get("pageIconColor",!0):e.get("pageIconInactiveColor",!0)),v.cursor=c?"pointer":"default")});var a=n.childOfName("pageText"),o=e.get("pageFormatter"),s=i.pageIndex,u=s!=null?s+1:0,l=i.pageCount;a&&o&&a.setStyle("text",B(o)?o.replace("{current}",u==null?"":u+"").replace("{total}",l==null?"":l+""):o({current:u,total:l}))},t.prototype._getPageInfo=function(e){var i=e.get("scrollDataIndex",!0),n=this.getContentGroup(),a=this._containerGroup.__rectSize,o=e.getOrient().index,s=Gs[o],u=Vs[o],l=this._findTargetItemIndex(i),f=n.children(),h=f[l],c=f.length,v=c?1:0,d={contentPosition:[n.x,n.y],pageCount:v,pageIndex:v-1,pagePrevDataIndex:null,pageNextDataIndex:null};if(!h)return d;var y=w(h);d.contentPosition[o]=-y.s;for(var p=l+1,g=y,_=y,m=null;p<=c;++p)m=w(f[p]),(!m&&_.e>g.s+a||m&&!T(m,g.s))&&(_.i>g.i?g=_:g=m,g&&(d.pageNextDataIndex==null&&(d.pageNextDataIndex=g.i),++d.pageCount)),_=m;for(var p=l-1,g=y,_=y,m=null;p>=-1;--p)m=w(f[p]),(!m||!T(_,m.s))&&g.i<_.i&&(_=g,d.pagePrevDataIndex==null&&(d.pagePrevDataIndex=g.i),++d.pageCount,++d.pageIndex),g=m;return d;function w(S){if(S){var b=S.getBoundingRect(),M=b[u]+S[u];return{s:M,e:M+b[s],i:S.__legendDataIndex}}}function T(S,b){return S.e>=b&&S.s<=b+a}},t.prototype._findTargetItemIndex=function(e){if(!this._showController)return 0;var i,n=this.getContentGroup(),a;return n.eachChild(function(o,s){var u=o.__legendDataIndex;a==null&&u!=null&&(a=s),u===e&&(i=s)}),i??a},t.type="legend.scroll",t}(Dg);function zx(r){r.registerAction("legendScroll","legendscroll",function(t,e){var i=t.scrollDataIndex;i!=null&&e.eachComponent({mainType:"legend",subType:"scroll",query:t},function(n){n.setScrollDataIndex(i)})})}function Hx(r){xn(Ag),r.registerComponentModel(Nx),r.registerComponentView(Fx),zx(r)}function xD(r){xn(Ag),xn(Hx)}var Gx=function(r){N(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type="dataset",e}return t.prototype.init=function(e,i,n){r.prototype.init.call(this,e,i,n),this._sourceManager=new hp(this),Gh(this)},t.prototype.mergeOption=function(e,i){r.prototype.mergeOption.call(this,e,i),Gh(this)},t.prototype.optionUpdated=function(){this._sourceManager.dirty()},t.prototype.getSourceManager=function(){return this._sourceManager},t.type="dataset",t.defaultOption={seriesLayoutBy:Se},t}(tt),Vx=function(r){N(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type="dataset",e}return t.type="dataset",t}(le);function CD(r){r.registerComponentModel(Gx),r.registerComponentView(Vx)}function ic(r,t,e){var i=yi.createCanvas(),n=t.getWidth(),a=t.getHeight(),o=i.style;return o&&(o.position="absolute",o.left="0",o.top="0",o.width=n+"px",o.height=a+"px",i.setAttribute("data-zr-dom-id",r)),i.width=n*e,i.height=a*e,i}var Ws=function(r){N(t,r);function t(e,i,n){var a=r.call(this)||this;a.motionBlur=!1,a.lastFrameAlpha=.7,a.dpr=1,a.virtual=!1,a.config={},a.incremental=!1,a.zlevel=0,a.maxRepaintRectCount=5,a.__dirty=!0,a.__firstTimePaint=!0,a.__used=!1,a.__drawIndex=0,a.__startIndex=0,a.__endIndex=0,a.__prevStartIndex=null,a.__prevEndIndex=null;var o;n=n||Ga,typeof e=="string"?o=ic(e,i,n):F(e)&&(o=e,e=o.id),a.id=e,a.dom=o;var s=o.style;return s&&(vc(o),o.onselectstart=function(){return!1},s.padding="0",s.margin="0",s.borderWidth="0"),a.painter=i,a.dpr=n,a}return t.prototype.getElementCount=function(){return this.__endIndex-this.__startIndex},t.prototype.afterBrush=function(){this.__prevStartIndex=this.__startIndex,this.__prevEndIndex=this.__endIndex},t.prototype.initContext=function(){this.ctx=this.dom.getContext("2d"),this.ctx.dpr=this.dpr},t.prototype.setUnpainted=function(){this.__firstTimePaint=!0},t.prototype.createBackBuffer=function(){var e=this.dpr;this.domBack=ic("back-"+this.id,this.painter,e),this.ctxBack=this.domBack.getContext("2d"),e!==1&&this.ctxBack.scale(e,e)},t.prototype.createRepaintRects=function(e,i,n,a){if(this.__firstTimePaint)return this.__firstTimePaint=!1,null;var o=[],s=this.maxRepaintRectCount,u=!1,l=new J(0,0,0,0);function f(_){if(!(!_.isFinite()||_.isZero()))if(o.length===0){var m=new J(0,0,0,0);m.copy(_),o.push(m)}else{for(var w=!1,T=1/0,S=0,b=0;b<o.length;++b){var M=o[b];if(M.intersect(_)){var x=new J(0,0,0,0);x.copy(M),x.union(_),o[b]=x,w=!0;break}else if(u){l.copy(_),l.union(M);var C=_.width*_.height,A=M.width*M.height,L=l.width*l.height,I=L-C-A;I<T&&(T=I,S=b)}}if(u&&(o[S].union(_),w=!0),!w){var m=new J(0,0,0,0);m.copy(_),o.push(m)}u||(u=o.length>=s)}}for(var h=this.__startIndex;h<this.__endIndex;++h){var c=e[h];if(c){var v=c.shouldBePainted(n,a,!0,!0),d=c.__isRendered&&(c.__dirty&we||!v)?c.getPrevPaintRect():null;d&&f(d);var y=v&&(c.__dirty&we||!c.__isRendered)?c.getPaintRect():null;y&&f(y)}}for(var h=this.__prevStartIndex;h<this.__prevEndIndex;++h){var c=i[h],v=c&&c.shouldBePainted(n,a,!0,!0);if(c&&(!v||!c.__zr)&&c.__isRendered){var d=c.getPrevPaintRect();d&&f(d)}}var p;do{p=!1;for(var h=0;h<o.length;){if(o[h].isZero()){o.splice(h,1);continue}for(var g=h+1;g<o.length;)o[h].intersect(o[g])?(p=!0,o[h].union(o[g]),o.splice(g,1)):g++;h++}}while(p);return this._paintRects=o,o},t.prototype.debugGetPaintRects=function(){return(this._paintRects||[]).slice()},t.prototype.resize=function(e,i){var n=this.dpr,a=this.dom,o=a.style,s=this.domBack;o&&(o.width=e+"px",o.height=i+"px"),a.width=e*n,a.height=i*n,s&&(s.width=e*n,s.height=i*n,n!==1&&this.ctxBack.scale(n,n))},t.prototype.clear=function(e,i,n){var a=this.dom,o=this.ctx,s=a.width,u=a.height;i=i||this.clearColor;var l=this.motionBlur&&!e,f=this.lastFrameAlpha,h=this.dpr,c=this;l&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(a,0,0,s/h,u/h));var v=this.domBack;function d(y,p,g,_){if(o.clearRect(y,p,g,_),i&&i!=="transparent"){var m=void 0;if(io(i)){var w=i.global||i.__width===g&&i.__height===_;m=w&&i.__canvasGradient||Iu(o,i,{x:0,y:0,width:g,height:_}),i.__canvasGradient=m,i.__width=g,i.__height=_}else jg(i)&&(i.scaleX=i.scaleX||h,i.scaleY=i.scaleY||h,m=Ru(o,i,{dirty:function(){c.setUnpainted(),c.painter.refresh()}}));o.save(),o.fillStyle=m||i,o.fillRect(y,p,g,_),o.restore()}l&&(o.save(),o.globalAlpha=f,o.drawImage(v,y,p,g,_),o.restore())}!n||l?d(0,0,s,u):n.length&&D(n,function(y){d(y.x*h,y.y*h,y.width*h,y.height*h)})},t}(be),nc=1e5,Cr=314159,da=.01,Wx=.001;function Ux(r){return r?r.__builtin__?!0:!(typeof r.resize!="function"||typeof r.refresh!="function"):!1}function Yx(r,t){var e=document.createElement("div");return e.style.cssText=["position:relative","width:"+r+"px","height:"+t+"px","padding:0","margin:0","border-width:0"].join(";")+";",e}var Xx=function(){function r(t,e,i,n){this.type="canvas",this._zlevelList=[],this._prevDisplayList=[],this._layers={},this._layerConfig={},this._needsManuallyCompositing=!1,this.type="canvas";var a=!t.nodeName||t.nodeName.toUpperCase()==="CANVAS";this._opts=i=O({},i||{}),this.dpr=i.devicePixelRatio||Ga,this._singleCanvas=a,this.root=t;var o=t.style;o&&(vc(t),t.innerHTML=""),this.storage=e;var s=this._zlevelList;this._prevDisplayList=[];var u=this._layers;if(a){var f=t,h=f.width,c=f.height;i.width!=null&&(h=i.width),i.height!=null&&(c=i.height),this.dpr=i.devicePixelRatio||1,f.width=h*this.dpr,f.height=c*this.dpr,this._width=h,this._height=c;var v=new Ws(f,this,this.dpr);v.__builtin__=!0,v.initContext(),u[Cr]=v,v.zlevel=Cr,s.push(Cr),this._domRoot=t}else{this._width=aa(t,0,i),this._height=aa(t,1,i);var l=this._domRoot=Yx(this._width,this._height);t.appendChild(l)}}return r.prototype.getType=function(){return"canvas"},r.prototype.isSingleCanvas=function(){return this._singleCanvas},r.prototype.getViewportRoot=function(){return this._domRoot},r.prototype.getViewportRootOffset=function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},r.prototype.refresh=function(t){var e=this.storage.getDisplayList(!0),i=this._prevDisplayList,n=this._zlevelList;this._redrawId=Math.random(),this._paintList(e,i,t,this._redrawId);for(var a=0;a<n.length;a++){var o=n[a],s=this._layers[o];if(!s.__builtin__&&s.refresh){var u=a===0?this._backgroundColor:null;s.refresh(u)}}return this._opts.useDirtyRect&&(this._prevDisplayList=e.slice()),this},r.prototype.refreshHover=function(){this._paintHoverList(this.storage.getDisplayList(!1))},r.prototype._paintHoverList=function(t){var e=t.length,i=this._hoverlayer;if(i&&i.clear(),!!e){for(var n={inHover:!0,viewWidth:this._width,viewHeight:this._height},a,o=0;o<e;o++){var s=t[o];s.__inHover&&(i||(i=this._hoverlayer=this.getLayer(nc)),a||(a=i.ctx,a.save()),Lr(a,s,n,o===e-1))}a&&a.restore()}},r.prototype.getHoverLayer=function(){return this.getLayer(nc)},r.prototype.paintOne=function(t,e){Ip(t,e)},r.prototype._paintList=function(t,e,i,n){if(this._redrawId===n){i=i||!1,this._updateLayerStatus(t);var a=this._doPaintList(t,e,i),o=a.finished,s=a.needsRefreshHover;if(this._needsManuallyCompositing&&this._compositeManually(),s&&this._paintHoverList(t),o)this.eachLayer(function(l){l.afterBrush&&l.afterBrush()});else{var u=this;Ba(function(){u._paintList(t,e,i,n)})}}},r.prototype._compositeManually=function(){var t=this.getLayer(Cr).ctx,e=this._domRoot.width,i=this._domRoot.height;t.clearRect(0,0,e,i),this.eachBuiltinLayer(function(n){n.virtual&&t.drawImage(n.dom,0,0,e,i)})},r.prototype._doPaintList=function(t,e,i){for(var n=this,a=[],o=this._opts.useDirtyRect,s=0;s<this._zlevelList.length;s++){var u=this._zlevelList[s],l=this._layers[u];l.__builtin__&&l!==this._hoverlayer&&(l.__dirty||i)&&a.push(l)}for(var f=!0,h=!1,c=function(y){var p=a[y],g=p.ctx,_=o&&p.createRepaintRects(t,e,v._width,v._height),m=i?p.__startIndex:p.__drawIndex,w=!i&&p.incremental&&Date.now,T=w&&Date.now(),S=p.zlevel===v._zlevelList[0]?v._backgroundColor:null;if(p.__startIndex===p.__endIndex)p.clear(!1,S,_);else if(m===p.__startIndex){var b=t[m];(!b.incremental||!b.notClear||i)&&p.clear(!1,S,_)}m===-1&&(console.error("For some unknown reason. drawIndex is -1"),m=p.__startIndex);var M,x=function(I){var P={inHover:!1,allClipped:!1,prevEl:null,viewWidth:n._width,viewHeight:n._height};for(M=m;M<p.__endIndex;M++){var R=t[M];if(R.__inHover&&(h=!0),n._doPaintEl(R,p,o,I,P,M===p.__endIndex-1),w){var E=Date.now()-T;if(E>15)break}}P.prevElClipPaths&&g.restore()};if(_)if(_.length===0)M=p.__endIndex;else for(var C=v.dpr,A=0;A<_.length;++A){var L=_[A];g.save(),g.beginPath(),g.rect(L.x*C,L.y*C,L.width*C,L.height*C),g.clip(),x(L),g.restore()}else g.save(),x(),g.restore();p.__drawIndex=M,p.__drawIndex<p.__endIndex&&(f=!1)},v=this,d=0;d<a.length;d++)c(d);return H.wxa&&D(this._layers,function(y){y&&y.ctx&&y.ctx.draw&&y.ctx.draw()}),{finished:f,needsRefreshHover:h}},r.prototype._doPaintEl=function(t,e,i,n,a,o){var s=e.ctx;if(i){var u=t.getPaintRect();(!n||u&&u.intersect(n))&&(Lr(s,t,a,o),t.setPrevPaintRect(u))}else Lr(s,t,a,o)},r.prototype.getLayer=function(t,e){this._singleCanvas&&!this._needsManuallyCompositing&&(t=Cr);var i=this._layers[t];return i||(i=new Ws("zr_"+t,this,this.dpr),i.zlevel=t,i.__builtin__=!0,this._layerConfig[t]?st(i,this._layerConfig[t],!0):this._layerConfig[t-da]&&st(i,this._layerConfig[t-da],!0),e&&(i.virtual=e),this.insertLayer(t,i),i.initContext()),i},r.prototype.insertLayer=function(t,e){var i=this._layers,n=this._zlevelList,a=n.length,o=this._domRoot,s=null,u=-1;if(!i[t]&&Ux(e)){if(a>0&&t>n[0]){for(u=0;u<a-1&&!(n[u]<t&&n[u+1]>t);u++);s=i[n[u]]}if(n.splice(u+1,0,t),i[t]=e,!e.virtual)if(s){var l=s.dom;l.nextSibling?o.insertBefore(e.dom,l.nextSibling):o.appendChild(e.dom)}else o.firstChild?o.insertBefore(e.dom,o.firstChild):o.appendChild(e.dom);e.painter||(e.painter=this)}},r.prototype.eachLayer=function(t,e){for(var i=this._zlevelList,n=0;n<i.length;n++){var a=i[n];t.call(e,this._layers[a],a)}},r.prototype.eachBuiltinLayer=function(t,e){for(var i=this._zlevelList,n=0;n<i.length;n++){var a=i[n],o=this._layers[a];o.__builtin__&&t.call(e,o,a)}},r.prototype.eachOtherLayer=function(t,e){for(var i=this._zlevelList,n=0;n<i.length;n++){var a=i[n],o=this._layers[a];o.__builtin__||t.call(e,o,a)}},r.prototype.getLayers=function(){return this._layers},r.prototype._updateLayerStatus=function(t){this.eachBuiltinLayer(function(h,c){h.__dirty=h.__used=!1});function e(h){a&&(a.__endIndex!==h&&(a.__dirty=!0),a.__endIndex=h)}if(this._singleCanvas)for(var i=1;i<t.length;i++){var n=t[i];if(n.zlevel!==t[i-1].zlevel||n.incremental){this._needsManuallyCompositing=!0;break}}var a=null,o=0,s,u;for(u=0;u<t.length;u++){var n=t[u],l=n.zlevel,f=void 0;s!==l&&(s=l,o=0),n.incremental?(f=this.getLayer(l+Wx,this._needsManuallyCompositing),f.incremental=!0,o=1):f=this.getLayer(l+(o>0?da:0),this._needsManuallyCompositing),f.__builtin__||$u("ZLevel "+l+" has been used by unkown layer "+f.id),f!==a&&(f.__used=!0,f.__startIndex!==u&&(f.__dirty=!0),f.__startIndex=u,f.incremental?f.__drawIndex=-1:f.__drawIndex=u,e(u),a=f),n.__dirty&we&&!n.__inHover&&(f.__dirty=!0,f.incremental&&f.__drawIndex<0&&(f.__drawIndex=u))}e(u),this.eachBuiltinLayer(function(h,c){!h.__used&&h.getElementCount()>0&&(h.__dirty=!0,h.__startIndex=h.__endIndex=h.__drawIndex=0),h.__dirty&&h.__drawIndex<0&&(h.__drawIndex=h.__startIndex)})},r.prototype.clear=function(){return this.eachBuiltinLayer(this._clearLayer),this},r.prototype._clearLayer=function(t){t.clear()},r.prototype.setBackgroundColor=function(t){this._backgroundColor=t,D(this._layers,function(e){e.setUnpainted()})},r.prototype.configLayer=function(t,e){if(e){var i=this._layerConfig;i[t]?st(i[t],e,!0):i[t]=e;for(var n=0;n<this._zlevelList.length;n++){var a=this._zlevelList[n];if(a===t||a===t+da){var o=this._layers[a];st(o,i[t],!0)}}}},r.prototype.delLayer=function(t){var e=this._layers,i=this._zlevelList,n=e[t];n&&(n.dom.parentNode.removeChild(n.dom),delete e[t],i.splice(at(i,t),1))},r.prototype.resize=function(t,e){if(this._domRoot.style){var i=this._domRoot;i.style.display="none";var n=this._opts,a=this.root;if(t!=null&&(n.width=t),e!=null&&(n.height=e),t=aa(a,0,n),e=aa(a,1,n),i.style.display="",this._width!==t||e!==this._height){i.style.width=t+"px",i.style.height=e+"px";for(var o in this._layers)this._layers.hasOwnProperty(o)&&this._layers[o].resize(t,e);this.refresh(!0)}this._width=t,this._height=e}else{if(t==null||e==null)return;this._width=t,this._height=e,this.getLayer(Cr).resize(t,e)}return this},r.prototype.clearLayer=function(t){var e=this._layers[t];e&&e.clear()},r.prototype.dispose=function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},r.prototype.getRenderedCanvas=function(t){if(t=t||{},this._singleCanvas&&!this._compositeManually)return this._layers[Cr].dom;var e=new Ws("image",this,t.pixelRatio||this.dpr);e.initContext(),e.clear(!1,t.backgroundColor||this._backgroundColor);var i=e.ctx;if(t.pixelRatio<=this.dpr){this.refresh();var n=e.dom.width,a=e.dom.height;this.eachLayer(function(h){h.__builtin__?i.drawImage(h.dom,0,0,n,a):h.renderToCanvas&&(i.save(),h.renderToCanvas(i),i.restore())})}else for(var o={inHover:!1,viewWidth:this._width,viewHeight:this._height},s=this.storage.getDisplayList(!0),u=0,l=s.length;u<l;u++){var f=s[u];Lr(i,f,o,u===l-1)}return e.dom},r.prototype.getWidth=function(){return this._width},r.prototype.getHeight=function(){return this._height},r}();function DD(r){r.registerPainter("canvas",Xx)}export{hl as $,Z as A,J as B,e1 as C,HC as D,pD as E,OC as F,ue as G,kr as H,t1 as I,tD as J,kl as K,lt as L,yD as M,Bw as N,Sn as O,vl as P,st as Q,B as R,Fr as S,K as T,ht as U,Ee as V,tt as W,Br as X,po as Y,zr as Z,N as _,CD as a,gT as a$,Wm as a0,le as a1,ct as a2,Rn as a3,ut as a4,ft as a5,Pt as a6,Ke as a7,Wa as a8,dm as a9,Qx as aA,W as aB,so as aC,qx as aD,vn as aE,gi as aF,Af as aG,Ze as aH,Qi as aI,jC as aJ,RC as aK,iy as aL,ho as aM,Sd as aN,dl as aO,nt as aP,se as aQ,MT as aR,DT as aS,at as aT,Ft as aU,ZC as aV,_t as aW,D1 as aX,UC as aY,Lt as aZ,tr as a_,ll as aa,Si as ab,Om as ac,Em as ad,ye as ae,Qu as af,Cy as ag,Ks as ah,Ir as ai,X as aj,Te as ak,mo as al,mt as am,Rm as an,Pn as ao,Q0 as ap,Eu as aq,rt as ar,yo as as,BC as at,W0 as au,EC as av,yt as aw,$C as ax,ju as ay,pc as az,xD as b,je as b$,mh as b0,gu as b1,hd as b2,am as b3,gn as b4,lc as b5,aC as b6,oC as b7,CC as b8,ya as b9,Au as bA,lD as bB,uD as bC,_l as bD,TC as bE,Zm as bF,hu as bG,vu as bH,IC as bI,DC as bJ,Gt as bK,At as bL,MS as bM,rC as bN,ly as bO,ff as bP,Xr as bQ,_0 as bR,_p as bS,yi as bT,Dd as bU,P_ as bV,d1 as bW,oD as bX,sD as bY,eD as bZ,gD as b_,ny as ba,uy as bb,Ro as bc,mi as bd,ay as be,oy as bf,jx as bg,Jx as bh,SC as bi,Fa as bj,xt as bk,ui as bl,Qd as bm,Nw as bn,g0 as bo,fl as bp,oe as bq,V0 as br,GC as bs,VC as bt,Fm as bu,et as bv,Je as bw,Oa as bx,F as by,wp as bz,bD as c,Na as c$,Xn as c0,Va as c1,qf as c2,vf as c3,fh as c4,aD as c5,n1 as c6,gl as c7,Vm as c8,Hb as c9,Mc as cA,h_ as cB,_C as cC,yC as cD,pi as cE,df as cF,gC as cG,Nr as cH,hC as cI,tl as cJ,fC as cK,H0 as cL,z0 as cM,Gg as cN,qy as cO,Zy as cP,pf as cQ,Xy as cR,qc as cS,lC as cT,pC as cU,dC as cV,wC as cW,vC as cX,cC as cY,aa as cZ,mC as c_,Gb as ca,zb as cb,wD as cc,Ub as cd,Ec as ce,ub as cf,Fb as cg,rg as ch,yl as ci,Wb as cj,mg as ck,Yb as cl,Jm as cm,dS as cn,La as co,zt as cp,KC as cq,QC as cr,Kx as cs,cw as ct,he as cu,uC as cv,sC as cw,Ap as cx,jc as cy,Ot as cz,TD as d,SD as d$,NC as d0,tC as d1,Nm as d2,zw as d3,zC as d4,pT as d5,xe as d6,XC as d7,rD as d8,oo as d9,iD as dA,fD as dB,Rv as dC,bi as dD,eC as dE,dc as dF,be as dG,wi as dH,iC as dI,jt as dJ,$m as dK,Xm as dL,Km as dM,Ym as dN,dn as dO,Sa as dP,YC as dQ,WC as dR,_1 as dS,Re as dT,I_ as dU,M1 as dV,C1 as dW,LC as dX,vD as dY,eb as dZ,ml as d_,tp as da,nb as db,ob as dc,sb as dd,bC as de,Za as df,Ns as dg,xc as dh,gt as di,_n as dj,Mn as dk,W_ as dl,wl as dm,nC as dn,Oc as dp,Ol as dq,qa as dr,Ml as ds,mn as dt,Dv as du,rb as dv,nD as dw,hD as dx,_D as dy,mD as dz,lx as e,fx as e0,H as e1,hn as e2,Sy as e3,jo as e4,qC as e5,AC as e6,cS as e7,JC as e8,lw as e9,pu as ea,Qe as eb,hg as ec,Gl as ed,Le as ee,J0 as ef,od as eg,RT as eh,Lu as ei,In as ej,PC as ek,MC as el,di as em,Fy as en,ky as eo,fb as ep,cD as eq,dD as er,hb as es,an as et,xC as eu,Ku as f,gc as g,me as h,DD as i,D as j,ni as k,ai as l,li as m,Y as n,Et as o,Zx as p,k as q,cl as r,wd as s,FC as t,xn as u,Ln as v,gr as w,ot as x,kC as y,O as z};
//# sourceMappingURL=installCanvasRenderer-DkI93HUo.js.map
