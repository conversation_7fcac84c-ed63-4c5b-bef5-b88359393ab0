<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LLM Debata Simulátor</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body { padding-top: 20px; background-color: #f8f9fa; }
        .container { max-width: 800px; background-color: #fff; padding: 30px; border-radius: 8px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }
        .past-debates { margin-top: 40px; }
        .debate-list-item { display: flex; justify-content: space-between; align-items: center; padding: 10px; border-bottom: 1px solid #eee; }
        .debate-list-item:last-child { border-bottom: none; }
        .status-badge { font-size: 0.8em; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-4">LLM Debata Simulátor</h1>

        <form id="debate-form">
            <div class="mb-3">
                <label for="topic" class="form-label">Téma debaty:</label>
                <input type="text" class="form-control" id="topic" name="topic" required>
            </div>

            <div class="row g-3 mb-3">
                <div class="col-md-6">
                    <label for="model_a" class="form-label">Model A:</label>
                    <select class="form-select" id="model_a" name="model_a" required>
                        <!-- Pouze povolené modely -->
                        <option value="gemini-2.0-flash-thinking-exp-01-21">gemini-2.0-flash-thinking-exp-01-21</option>
                        <option value="deepseek/deepseek-r1:free">deepseek/deepseek-r1:free</option>
                    </select>
                </div>
                <div class="col-md-6">
                    <label for="model_b" class="form-label">Model B:</label>
                    <select class="form-select" id="model_b" name="model_b" required>
                         <!-- Pouze povolené modely -->
                         <option value="deepseek/deepseek-r1:free">deepseek/deepseek-r1:free</option>
                        <option value="gemini-2.0-flash-thinking-exp-01-21">gemini-2.0-flash-thinking-exp-01-21</option>
                    </select>
                </div>
            </div>

            <div class="row g-3 mb-3">
                 <div class="col-md-6">
                    <label for="temperature_a" class="form-label">Teplota A (0-2):</label>
                    <input type="number" class="form-control" id="temperature_a" name="temperature_a" min="0" max="2" step="0.1" value="0.7">
                </div>
                 <div class="col-md-6">
                    <label for="temperature_b" class="form-label">Teplota B (0-2):</label>
                    <input type="number" class="form-control" id="temperature_b" name="temperature_b" min="0" max="2" step="0.1" value="0.7">
                </div>
            </div>
             <div class="row g-3 mb-3">
                 <div class="col-md-6">
                    <label for="max_tokens_a" class="form-label">Max tokenů A (volitelné):</label>
                    <input type="number" class="form-control" id="max_tokens_a" name="max_tokens_a" min="10" placeholder="neomezeno">
                </div>
                 <div class="col-md-6">
                    <label for="max_tokens_b" class="form-label">Max tokenů B (volitelné):</label>
                    <input type="number" class="form-control" id="max_tokens_b" name="max_tokens_b" min="10" placeholder="neomezeno">
                </div>
            </div>

            <div class="mb-3">
                <label for="duration" class="form-label">Délka debaty (minuty):</label>
                <input type="number" class="form-control" id="duration" name="duration" min="1" max="120" value="5" required>
            </div>

            <div class="d-grid">
                <button type="submit" class="btn btn-primary btn-lg">Spustit debatu</button>
            </div>
        </form>
        <div id="form-error" class="alert alert-danger mt-3 d-none" role="alert"></div>

        <div class="past-debates">
            <h2 class="fs-5 mb-3">Minulé debaty</h2>
            <div id="debate-list">
                {% if past_debates %}
                    {% for debate in past_debates %}
                    <div class="debate-list-item">
                        <div>
                            <a href="{{ url_for('debate_page', debate_id=debate.id) }}">{{ debate.topic }}</a>
                            <small class="d-block text-muted">{{ debate.start_time.strftime('%Y-%m-%d %H:%M') if debate.start_time else 'N/A' }}</small>
                        </div>
                        <div class="d-flex align-items-center">
                            <span class="badge rounded-pill me-2
                                {% if debate.status == 'completed' %} bg-success
                                {% elif debate.status == 'running' %} bg-info text-dark
                                {% elif debate.status == 'error' %} bg-danger
                                {% elif debate.status == 'cancelled' %} bg-warning text-dark
                                {% else %} bg-secondary {% endif %} status-badge">
                                {{ debate.status }}
                            </span>
                            <button class="btn btn-sm btn-outline-danger delete-debate-btn"
                                    data-debate-id="{{ debate.id }}"
                                    data-debate-topic="{{ debate.topic }}"
                                    title="Odstranit debatu">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted">Zatím žádné debaty neproběhly.</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Modal pro potvrzení odstranění debaty -->
    <div class="modal fade" id="deleteDebateModal" tabindex="-1" aria-labelledby="deleteDebateModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteDebateModalLabel">Potvrdit odstranění</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Zavřít"></button>
                </div>
                <div class="modal-body">
                    <p>Opravdu chcete odstranit debatu "<span id="debateTopicToDelete"></span>"?</p>
                    <p class="text-danger">Tato akce je nevratná a odstraní všechny zprávy a shrnutí spojené s touto debatou.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Zrušit</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Odstranit</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Skript pro odeslání formuláře pomocí Fetch API a přesměrování
        const form = document.getElementById('debate-form');
        const errorDiv = document.getElementById('form-error');

        form.addEventListener('submit', function(event) {
            event.preventDefault(); // Zabráníme standardnímu odeslání
            errorDiv.classList.add('d-none'); // Skryjeme případnou starou chybu

            const formData = new FormData(form);
            const submitButton = form.querySelector('button[type="submit"]');
            submitButton.disabled = true; // Deaktivujeme tlačítko
            submitButton.textContent = 'Spouštím...';

            fetch('/start-debate', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.debate_id) {
                    // Úspěch -> přesměrování na stránku debaty
                    window.location.href = `/debate/${data.debate_id}`;
                } else {
                    // Zobrazíme chybu
                    errorDiv.textContent = data.error || 'Nastala neznámá chyba.';
                    errorDiv.classList.remove('d-none');
                    submitButton.disabled = false; // Znovu aktivujeme tlačítko
                    submitButton.textContent = 'Spustit debatu';
                }
            })
            .catch(error => {
                console.error('Chyba při odesílání formuláře:', error);
                errorDiv.textContent = 'Chyba při komunikaci se serverem.';
                errorDiv.classList.remove('d-none');
                submitButton.disabled = false;
                submitButton.textContent = 'Spustit debatu';
            });
        });

        // Skript pro obsluhu tlačítek odstranění debaty
        document.addEventListener('DOMContentLoaded', function() {
            // Získáme modální okno a jeho komponenty
            const deleteModal = new bootstrap.Modal(document.getElementById('deleteDebateModal'));
            const debateTopicSpan = document.getElementById('debateTopicToDelete');
            const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
            let debateIdToDelete = null;

            // Přidáme event listener na všechna tlačítka pro odstranění
            document.querySelectorAll('.delete-debate-btn').forEach(button => {
                button.addEventListener('click', function() {
                    // Získáme ID a téma debaty z data atributů
                    debateIdToDelete = this.getAttribute('data-debate-id');
                    const debateTopic = this.getAttribute('data-debate-topic');

                    // Nastavíme téma do modálního okna
                    debateTopicSpan.textContent = debateTopic;

                    // Zobrazíme modální okno
                    deleteModal.show();
                });
            });

            // Přidáme event listener na tlačítko pro potvrzení odstranění
            confirmDeleteBtn.addEventListener('click', function() {
                if (!debateIdToDelete) return;

                // Odešleme požadavek na odstranění debaty
                fetch(`/delete-debate/${debateIdToDelete}`, {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    // Skryjeme modální okno
                    deleteModal.hide();

                    if (data.success) {
                        // Odstraníme debatu z DOM
                        const debateElement = document.querySelector(`.delete-debate-btn[data-debate-id="${debateIdToDelete}"]`).closest('.debate-list-item');
                        debateElement.remove();

                        // Pokud jsme odstranili poslední debatu, zobrazíme zprávu
                        const debateList = document.getElementById('debate-list');
                        if (!debateList.querySelector('.debate-list-item')) {
                            debateList.innerHTML = '<p class="text-muted">Zatím žádné debaty neproběhly.</p>';
                        }
                    } else {
                        // Zobrazíme chybu
                        alert(`Chyba: ${data.error || 'Nastala neznámá chyba při odstraňování debaty.'}`);
                    }
                })
                .catch(error => {
                    console.error('Chyba při odstraňování debaty:', error);
                    alert('Chyba při komunikaci se serverem.');
                    deleteModal.hide();
                });
            });
        });
    </script>
</body>
</html>