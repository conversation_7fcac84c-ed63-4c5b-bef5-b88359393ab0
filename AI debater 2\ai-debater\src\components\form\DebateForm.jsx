import React, { useState } from 'react';
import { FaR<PERSON><PERSON>, FaArrowRight } from 'react-icons/fa';

const expertiseOptions = [
  "Economics & Business",
  "Politics & Public Affairs",
  "Technology & Development",
  "Marketing & UX Design",
  "Law, Ethics & Compliance",
  "Science & Research",
  "History & Philosophy",
  "Arts & Culture"
];

const DebateForm = ({ onStartDebate }) => {
  const [topic, setTopic] = useState('');
  const [expert1, setExpert1] = useState(expertiseOptions[0]);
  const [expert2, setExpert2] = useState(expertiseOptions[1]);
  const [rounds, setRounds] = useState(3);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = (e) => {
    e.preventDefault();
    
    // Validate form
    if (!topic.trim()) {
      setError('Please enter a debate topic');
      return;
    }
    
    if (expert1 === expert2) {
      setError('Please select different expertise areas for the experts');
      return;
    }
    
    setError('');
    setIsLoading(true);
    
    // Simulate API call delay
    setTimeout(() => {
      onStartDebate({
        topic,
        expert1,
        expert2,
        rounds
      });
      setIsLoading(false);
    }, 1000);
  };

  return (
    <div className="max-w-2xl mx-auto">
      <div className="card">
        <h2 className="text-2xl font-bold mb-6 text-center">Create a New AI Debate</h2>
        
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}
        
        <form onSubmit={handleSubmit}>
          <div className="mb-6">
            <label htmlFor="topic" className="block text-sm font-medium mb-2">
              Debate Topic
            </label>
            <input
              type="text"
              id="topic"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., Should artificial intelligence be regulated?"
              value={topic}
              onChange={(e) => setTopic(e.target.value)}
              required
            />
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div>
              <label htmlFor="expert1" className="block text-sm font-medium mb-2">
                Expert 1 Expertise
              </label>
              <div className="relative">
                <select
                  id="expert1"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 appearance-none"
                  value={expert1}
                  onChange={(e) => setExpert1(e.target.value)}
                >
                  {expertiseOptions.map((option) => (
                    <option key={option} value={option}>
                      {option}
                    </option>
                  ))}
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                  <FaRobot className="text-gray-400" />
                </div>
              </div>
            </div>
            
            <div>
              <label htmlFor="expert2" className="block text-sm font-medium mb-2">
                Expert 2 Expertise
              </label>
              <div className="relative">
                <select
                  id="expert2"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 appearance-none"
                  value={expert2}
                  onChange={(e) => setExpert2(e.target.value)}
                >
                  {expertiseOptions.map((option) => (
                    <option key={option} value={option}>
                      {option}
                    </option>
                  ))}
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                  <FaRobot className="text-gray-400" />
                </div>
              </div>
            </div>
          </div>
          
          <div className="mb-6">
            <label htmlFor="rounds" className="block text-sm font-medium mb-2">
              Number of Rounds
            </label>
            <input
              type="number"
              id="rounds"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              min="1"
              max="5"
              value={rounds}
              onChange={(e) => setRounds(parseInt(e.target.value))}
            />
            <p className="text-xs text-gray-500 mt-1">
              Each expert will speak once per round (1-5 rounds)
            </p>
          </div>
          
          <div className="flex justify-center">
            <button
              type="submit"
              className="btn btn-primary flex items-center space-x-2"
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <span>Preparing Debate</span>
                  <div className="typing-indicator">
                    <span></span>
                    <span></span>
                    <span></span>
                  </div>
                </>
              ) : (
                <>
                  <span>Start Debate</span>
                  <FaArrowRight />
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default DebateForm;
