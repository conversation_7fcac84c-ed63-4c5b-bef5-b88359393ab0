{"version": 3, "file": "StrategySelect.vue_vue_type_script_setup_true_lang-DwwznCrA.js", "sources": ["../../src/components/ftbot/TimeframeSelect.vue", "../../src/components/ftbot/StrategySelect.vue"], "sourcesContent": ["<script setup lang=\"ts\">\ninterface Props {\n  value?: string;\n  belowTimeframe?: string;\n  size?: undefined | 'small' | 'large';\n}\n\nconst props = withDefaults(defineProps<Props>(), {\n  value: '',\n  belowTimeframe: '',\n  size: undefined,\n});\nconst emit = defineEmits<{ input: [value: string] }>();\n\nconst selectedTimeframe = ref('');\n// The below list must always remain sorted correctly!\nconst availableTimeframesBase = [\n  // Placeholder value\n  { value: '', text: 'Use strategy default' },\n  '1m',\n  '3m',\n  '5m',\n  '15m',\n  '30m',\n  '1h',\n  '2h',\n  '4h',\n  '6h',\n  '8h',\n  '12h',\n  '1d',\n  '3d',\n  '1w',\n  '2w',\n  '1M',\n  '1y',\n];\n\nconst availableTimeframes = computed(() => {\n  if (!props.belowTimeframe) {\n    return availableTimeframesBase;\n  }\n  const idx = availableTimeframesBase.findIndex((v) => v === props.belowTimeframe);\n\n  return [...availableTimeframesBase].splice(0, idx);\n});\n\nconst emitSelectedTimeframe = () => {\n  emit('input', selectedTimeframe.value);\n};\n</script>\n\n<template>\n  <Select\n    v-model=\"selectedTimeframe\"\n    placeholder=\"Use strategy default\"\n    :option-label=\"(option) => option.text || option\"\n    :option-value=\"(option) => option.value ?? option\"\n    :size=\"size\"\n    :options=\"availableTimeframes\"\n    @change=\"emitSelectedTimeframe\"\n  ></Select>\n</template>\n", "<script setup lang=\"ts\">\nimport { useBotStore } from '@/stores/ftbotwrapper';\n\nconst props = defineProps({\n  modelValue: { type: String, required: true },\n  showDetails: { default: false, required: false, type: <PERSON>olean },\n});\nconst emit = defineEmits<{ 'update:modelValue': [value: string] }>();\n\nconst botStore = useBotStore();\n\nconst strategyCode = computed((): string => botStore.activeBot.strategy?.code);\nconst locStrategy = computed({\n  get() {\n    return props.modelValue;\n  },\n  set(strategy: string) {\n    botStore.activeBot.getStrategy(strategy);\n    emit('update:modelValue', strategy);\n  },\n});\n\nonMounted(() => {\n  if (botStore.activeBot.strategyList.length === 0) {\n    botStore.activeBot.getStrategyList();\n  }\n});\n</script>\n\n<template>\n  <div>\n    <div class=\"w-full flex\">\n      <Select\n        id=\"strategy-select\"\n        v-model=\"locStrategy\"\n        filter\n        fluid\n        :options=\"botStore.activeBot.strategyList\"\n      >\n      </Select>\n      <div class=\"ms-1\">\n        <Button severity=\"secondary\" variant=\"outlined\" @click=\"botStore.activeBot.getStrategyList\">\n          <template #icon>\n            <i-mdi-refresh />\n          </template>\n        </Button>\n      </div>\n    </div>\n\n    <textarea\n      v-if=\"showDetails && botStore.activeBot.strategy\"\n      v-model=\"strategyCode\"\n      class=\"w-full h-full\"\n    ></textarea>\n  </div>\n</template>\n"], "names": ["props", "__props", "emit", "__emit", "selectedTimeframe", "ref", "availableTimeframesBase", "availableTimeframes", "computed", "idx", "v", "emitSelectedTimeframe", "botStore", "useBotStore", "strategyCode", "_a", "locStrategy", "strategy", "onMounted"], "mappings": "sTAOA,MAAMA,EAAQC,EAKRC,EAAOC,EAEPC,EAAoBC,EAAI,EAAE,EAE1BC,EAA0B,CAE9B,CAAE,MAAO,GAAI,KAAM,sBAAuB,EAC1C,KACA,KACA,KACA,MACA,MACA,KACA,KACA,KACA,KACA,KACA,MACA,KACA,KACA,KACA,KACA,KACA,IACF,EAEMC,EAAsBC,EAAS,IAAM,CACrC,GAAA,CAACR,EAAM,eACF,OAAAM,EAET,MAAMG,EAAMH,EAAwB,UAAWI,GAAMA,IAAMV,EAAM,cAAc,EAE/E,MAAO,CAAC,GAAGM,CAAuB,EAAE,OAAO,EAAGG,CAAG,CAAA,CAClD,EAEKE,EAAwB,IAAM,CAC7BT,EAAA,QAASE,EAAkB,KAAK,CACvC,khBC9CA,MAAMJ,EAAQC,EAIRC,EAAOC,EAEPS,EAAWC,EAAY,EAEvBC,EAAeN,EAAS,IAAA,OAAc,OAAAO,EAAAH,EAAS,UAAU,WAAnB,YAAAG,EAA6B,KAAI,EACvEC,EAAcR,EAAS,CAC3B,KAAM,CACJ,OAAOR,EAAM,UACf,EACA,IAAIiB,EAAkB,CACXL,EAAA,UAAU,YAAYK,CAAQ,EACvCf,EAAK,oBAAqBe,CAAQ,CAAA,CACpC,CACD,EAED,OAAAC,EAAU,IAAM,CACVN,EAAS,UAAU,aAAa,SAAW,GAC7CA,EAAS,UAAU,gBAAgB,CACrC,CACD"}