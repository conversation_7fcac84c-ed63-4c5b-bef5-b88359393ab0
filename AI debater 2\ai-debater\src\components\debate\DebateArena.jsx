import React, { useState, useEffect } from 'react';
import { <PERSON>aR<PERSON>ot, FaArrowLeft, FaSync } from 'react-icons/fa';
import ExpertMessage from './ExpertMessage';
import TypingIndicator from './TypingIndicator';

// Mock API call to simulate getting AI responses
const getAIResponse = (topic, expertise, previousMessages) => {
  return new Promise((resolve) => {
    // In a real app, this would be an API call to an LLM service
    setTimeout(() => {
      // Generate a mock response based on the expertise
      const responses = {
        "Economics & Business": [
          "From an economic perspective, this issue requires careful market analysis.",
          "The market forces at play suggest a different conclusion than my counterpart proposed.",
          "Looking at historical economic data, we can see patterns that support my position."
        ],
        "Politics & Public Affairs": [
          "The political implications of this topic are far-reaching and complex.",
          "Public policy considerations must take precedence in this discussion.",
          "The balance of power between different stakeholders is a critical factor here."
        ],
        "Technology & Development": [
          "The technological feasibility of this approach is questionable at best.",
          "From a development standpoint, we need to consider scalability and maintenance.",
          "The rate of technological change makes this a moving target for regulation."
        ],
        "Marketing & UX Design": [
          "User experience should be the central consideration in this debate.",
          "Brand perception and market positioning would be significantly affected.",
          "Customer journey mapping reveals important insights about this topic."
        ],
        "Law, Ethics & Compliance": [
          "The legal framework surrounding this issue is quite clear.",
          "Ethical considerations must guide our approach to this complex topic.",
          "Compliance requirements create boundaries that cannot be ignored."
        ],
        "Science & Research": [
          "The empirical evidence strongly suggests a different conclusion.",
          "Research studies in this area point to several important factors.",
          "The scientific method demands we consider alternative hypotheses."
        ],
        "History & Philosophy": [
          "Historical precedents provide valuable context for this discussion.",
          "Philosophical inquiry leads us to question the fundamental assumptions.",
          "The historical evolution of this concept reveals important nuances."
        ],
        "Arts & Culture": [
          "Cultural context is essential to understanding this topic fully.",
          "Artistic expression has long engaged with this theme in meaningful ways.",
          "The cultural implications of this debate extend beyond the obvious."
        ]
      };
      
      // Get random response for the expertise
      const expertiseResponses = responses[expertise] || responses["Science & Research"];
      const randomIndex = Math.floor(Math.random() * expertiseResponses.length);
      
      // Add some context based on the topic and previous messages
      let response = expertiseResponses[randomIndex];
      
      if (previousMessages.length > 0) {
        response += " In response to the previous point, I would add that this perspective overlooks key considerations in my field of expertise.";
      }
      
      if (topic) {
        response += ` Regarding "${topic}", specialists in ${expertise} would typically emphasize the importance of methodical analysis and evidence-based conclusions.`;
      }
      
      resolve(response);
    }, 2000 + Math.random() * 2000); // Random delay between 2-4 seconds
  });
};

const DebateArena = ({ config, history, setHistory, onReset }) => {
  const [currentRound, setCurrentRound] = useState(1);
  const [isExpert1Turn, setIsExpert1Turn] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [isComplete, setIsComplete] = useState(false);

  const fetchNextResponse = async () => {
    if (isComplete || isLoading) return;
    
    setIsLoading(true);
    
    const currentExpertise = isExpert1Turn ? config.expert1 : config.expert2;
    const expertNumber = isExpert1Turn ? 1 : 2;
    
    try {
      const response = await getAIResponse(
        config.topic,
        currentExpertise,
        history
      );
      
      const newMessage = {
        id: history.length + 1,
        content: response,
        expertNumber,
        expertise: currentExpertise,
        round: currentRound
      };
      
      setHistory([...history, newMessage]);
      
      // Update turn and round
      if (!isExpert1Turn) {
        if (currentRound >= config.rounds) {
          setIsComplete(true);
        } else {
          setCurrentRound(currentRound + 1);
        }
      }
      
      setIsExpert1Turn(!isExpert1Turn);
    } catch (error) {
      console.error("Error fetching AI response:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Auto-fetch first response when component mounts
  useEffect(() => {
    if (history.length === 0) {
      fetchNextResponse();
    }
  }, []);

  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-6 flex justify-between items-center">
        <button
          onClick={onReset}
          className="flex items-center space-x-2 text-gray-600 hover:text-gray-900"
        >
          <FaArrowLeft />
          <span>Back to Setup</span>
        </button>
        
        <h2 className="text-xl font-bold text-center">
          {config.topic}
        </h2>
        
        <div className="text-sm text-gray-600">
          Round {currentRound} of {config.rounds}
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <div className="card">
          <div className="flex items-center space-x-2 mb-2">
            <FaRobot className="text-blue-600" />
            <h3 className="font-medium">Expert 1: {config.expert1}</h3>
          </div>
          <div className="text-sm text-gray-600 mb-4">
            Speaking from the perspective of {config.expert1}
          </div>
        </div>
        
        <div className="card">
          <div className="flex items-center space-x-2 mb-2">
            <FaRobot className="text-red-600" />
            <h3 className="font-medium">Expert 2: {config.expert2}</h3>
          </div>
          <div className="text-sm text-gray-600 mb-4">
            Speaking from the perspective of {config.expert2}
          </div>
        </div>
      </div>
      
      <div className="card mb-6 debate-container">
        <h3 className="text-lg font-medium mb-4">Debate Progress</h3>
        
        {history.length === 0 ? (
          <div className="text-center py-8">
            <TypingIndicator />
            <p className="text-gray-500 mt-2">Preparing the debate...</p>
          </div>
        ) : (
          <div className="space-y-6">
            {history.map((message) => (
              <ExpertMessage
                key={message.id}
                message={message}
                config={config}
              />
            ))}
            
            {isLoading && (
              <div className="pl-4 border-l-4 border-gray-300 py-2">
                <div className="flex items-center space-x-2 text-gray-600">
                  <FaRobot className={isExpert1Turn ? "text-blue-600" : "text-red-600"} />
                  <span className="font-medium">
                    Expert {isExpert1Turn ? "1" : "2"} is thinking...
                  </span>
                  <TypingIndicator />
                </div>
              </div>
            )}
          </div>
        )}
      </div>
      
      <div className="flex justify-center space-x-4">
        {!isComplete && (
          <button
            onClick={fetchNextResponse}
            className="btn btn-primary"
            disabled={isLoading}
          >
            {isLoading ? "AI is responding..." : "Continue Debate"}
          </button>
        )}
        
        {isComplete && (
          <button
            onClick={onReset}
            className="btn flex items-center space-x-2 bg-green-600 text-white hover:bg-green-700"
          >
            <FaSync />
            <span>Start New Debate</span>
          </button>
        )}
      </div>
    </div>
  );
};

export default DebateArena;
