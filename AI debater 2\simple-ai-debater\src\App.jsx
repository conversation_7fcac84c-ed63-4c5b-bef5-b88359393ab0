import { useState } from 'react'
import './App.css'

function App() {
  const [topic, setTopic] = useState('')
  const [expert1Field, setExpert1Field] = useState('')
  const [expert2Field, setExpert2Field] = useState('')
  const [rounds, setRounds] = useState(3)
  const [debateStarted, setDebateStarted] = useState(false)
  const [messages, setMessages] = useState([])
  const [isLoading, setIsLoading] = useState(false)

  const startDebate = () => {
    if (!topic || !expert1Field || !expert2Field) {
      alert('Please fill in all fields')
      return
    }

    setDebateStarted(true)
    setIsLoading(true)

    // Simulate API call to get debate responses
    setTimeout(() => {
      const simulatedMessages = []

      for (let i = 0; i < rounds; i++) {
        // Expert 1 message
        simulatedMessages.push({
          id: `round-${i}-expert1`,
          expert: 1,
          content: `As an expert in ${expert1Field}, I would argue that ${topic} is beneficial because of reason ${i+1}A. Furthermore, studies have shown that...`,
          timestamp: new Date().toISOString()
        })

        // Expert 2 message
        simulatedMessages.push({
          id: `round-${i}-expert2`,
          expert: 2,
          content: `From my perspective as a ${expert2Field} specialist, I must disagree. ${topic} actually presents challenges such as issue ${i+1}B. Research indicates that...`,
          timestamp: new Date().toISOString()
        })
      }

      // Conclusion from Expert 1
      simulatedMessages.push({
        id: 'conclusion-expert1',
        expert: 1,
        content: `In conclusion, as a ${expert1Field} expert, I maintain that ${topic} has more benefits than drawbacks for the reasons I've outlined.`,
        timestamp: new Date().toISOString()
      })

      // Conclusion from Expert 2
      simulatedMessages.push({
        id: 'conclusion-expert2',
        expert: 2,
        content: `To conclude from a ${expert2Field} perspective, I believe the evidence shows that ${topic} requires more careful consideration of the issues I've raised.`,
        timestamp: new Date().toISOString()
      })

      setMessages(simulatedMessages)
      setIsLoading(false)
    }, 2000)
  }

  const resetDebate = () => {
    setDebateStarted(false)
    setMessages([])
    setTopic('')
    setExpert1Field('')
    setExpert2Field('')
    setRounds(3)
  }

  return (
    <div className="app-container">
      <header>
        <h1>AI Expert Debate Platform</h1>
        <p>Watch two AI experts debate a topic from different perspectives</p>
      </header>

      {!debateStarted ? (
        <div className="setup-container">
          <div className="form-group">
            <label htmlFor="topic">Debate Topic:</label>
            <input
              type="text"
              id="topic"
              value={topic}
              onChange={(e) => setTopic(e.target.value)}
              placeholder="e.g., The impact of artificial intelligence on society"
            />
          </div>

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="expert1">Expert 1 Field:</label>
              <input
                type="text"
                id="expert1"
                value={expert1Field}
                onChange={(e) => setExpert1Field(e.target.value)}
                placeholder="e.g., Technology Ethics"
              />
            </div>

            <div className="form-group">
              <label htmlFor="expert2">Expert 2 Field:</label>
              <input
                type="text"
                id="expert2"
                value={expert2Field}
                onChange={(e) => setExpert2Field(e.target.value)}
                placeholder="e.g., Economic Policy"
              />
            </div>
          </div>

          <div className="form-group">
            <label htmlFor="rounds">Number of Rounds:</label>
            <input
              type="number"
              id="rounds"
              min="1"
              max="5"
              value={rounds}
              onChange={(e) => setRounds(parseInt(e.target.value))}
            />
          </div>

          <button className="primary-button" onClick={startDebate}>
            Start Debate
          </button>
        </div>
      ) : (
        <div className="debate-container">
          <div className="debate-header">
            <h2>Topic: {topic}</h2>
            <div className="experts-info">
              <div className="expert expert-1">
                <span className="expert-label">Expert 1:</span> {expert1Field}
              </div>
              <div className="expert expert-2">
                <span className="expert-label">Expert 2:</span> {expert2Field}
              </div>
            </div>
          </div>

          {isLoading ? (
            <div className="loading">
              <p>Experts are preparing their arguments...</p>
              <div className="typing-indicator">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          ) : (
            <div className="messages-container">
              {messages.map((message) => (
                <div
                  key={message.id}
                  className={`message expert-${message.expert}`}
                >
                  <div className="message-header">
                    <strong>
                      {message.expert === 1 ? expert1Field : expert2Field} Expert
                    </strong>
                  </div>
                  <div className="message-content">
                    {message.content}
                  </div>
                </div>
              ))}
            </div>
          )}

          <button className="secondary-button" onClick={resetDebate}>
            Start New Debate
          </button>
        </div>
      )}

      <footer>
        <p>AI Expert Debate Platform &copy; 2024</p>
      </footer>
    </div>
  )
}

export default App
