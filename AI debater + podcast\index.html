<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Debatní Platforma</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.2.1/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap');
        
        body {
            font-family: 'Roboto', sans-serif;
            background-color: #f9fafb;
        }
        
        .debate-bubble {
            position: relative;
            border-radius: 1rem;
            padding: 1rem;
            margin-bottom: 1rem;
            max-width: 80%;
        }
        
        .model-a {
            background-color: #e3f2fd;
            margin-right: auto;
            border-bottom-left-radius: 0;
        }
        
        .model-b {
            background-color: #e8f5e9;
            margin-left: auto;
            border-bottom-right-radius: 0;
        }
        
        .typing-animation::after {
            content: '|';
            animation: blink 1s infinite;
        }
        
        @keyframes blink {
            0%, 100% { opacity: 1; }
            50% { opacity: 0; }
        }
        
        .btn-primary {
            background-color: #3b82f6;
            color: white;
            transition: all 0.3s;
        }
        
        .btn-primary:hover {
            background-color: #2563eb;
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background-color: #e5e7eb;
            color: #374151;
            transition: all 0.3s;
        }
        
        .btn-secondary:hover {
            background-color: #d1d5db;
            transform: translateY(-2px);
        }
        
        .card-hover {
            transition: all 0.3s ease;
        }
        
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        .progress-bar {
            transition: width 0.5s ease;
        }
        
        .summary-point {
            position: relative;
            padding-left: 1.5rem;
        }
        
        .summary-point::before {
            content: "";
            position: absolute;
            left: 0;
            top: 0.5rem;
            width: 0.75rem;
            height: 0.75rem;
            border-radius: 50%;
        }
        
        .model-a-point::before {
            background-color: #bbdefb;
        }
        
        .model-b-point::before {
            background-color: #c8e6c9;
        }
    </style>
</head>
<body>
    <div class="container mx-auto px-4 py-8 max-w-6xl">
        <header class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-800 mb-4">AI Debatní Platforma</h1>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">Interaktivní platforma pro debaty mezi dvěma modely umělé inteligence na jakékoliv téma</p>
        </header>
        
        <div class="bg-white rounded-2xl shadow-lg p-6 mb-8">
            <h2 class="text-2xl font-semibold text-gray-800 mb-4">Začněte novou debatu</h2>
            
            <div class="mb-6">
                <label for="topic" class="block text-gray-700 font-medium mb-2">Zadejte téma debaty</label>
                <input type="text" id="topic" class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Například: Jsou elektromobily budoucností dopravy?">
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                <div class="card-hover bg-gray-50 rounded-xl p-4">
                    <h3 class="text-lg font-medium text-gray-800 mb-2">Formát debaty</h3>
                    <select id="format" class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="casual">Neformální diskuze</option>
                        <option value="formal">Formální debata</option>
                        <option value="pointcounterpoint">Bod a protibod</option>
                    </select>
                </div>
                
                <div class="card-hover bg-gray-50 rounded-xl p-4">
                    <h3 class="text-lg font-medium text-gray-800 mb-2">Délka debaty</h3>
                    <select id="length" class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="short">Krátká (3 výměny)</option>
                        <option value="medium" selected>Střední (5 výměn)</option>
                        <option value="long">Rozsáhlá (8 výměn)</option>
                    </select>
                </div>
                
                <div class="card-hover bg-gray-50 rounded-xl p-4">
                    <h3 class="text-lg font-medium text-gray-800 mb-2">Perspektivy modelů</h3>
                    <select id="perspectives" class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="balanced">Vyvážené názory</option>
                        <option value="opposing">Protichůdné názory</option>
                        <option value="complementary">Doplňující se perspektivy</option>
                    </select>
                </div>
            </div>
            
            <div class="flex justify-center">
                <button id="startDebate" class="btn-primary px-8 py-3 rounded-lg font-medium flex items-center shadow-md">
                    <i class="fas fa-play mr-2"></i>
                    Zahájit debatu
                </button>
            </div>
        </div>
        
        <div id="debateSection" class="bg-white rounded-2xl shadow-lg p-6 mb-8 hidden">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-semibold text-gray-800">Probíhající debata</h2>
                <div class="flex items-center">
                    <span class="text-gray-600 mr-2" id="debateProgress">0%</span>
                    <div class="w-32 h-2 bg-gray-200 rounded-full">
                        <div id="progressBar" class="h-2 bg-blue-500 rounded-full progress-bar" style="width: 0%"></div>
                    </div>
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="bg-blue-50 rounded-xl p-4 text-center">
                    <div class="w-16 h-16 rounded-full bg-blue-100 flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-robot text-blue-500 text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-800">Model A</h3>
                    <p class="text-gray-600 text-sm">Analytický přístup</p>
                </div>
                
                <div class="bg-green-50 rounded-xl p-4 text-center">
                    <div class="w-16 h-16 rounded-full bg-green-100 flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-robot text-green-500 text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-800">Model B</h3>
                    <p class="text-gray-600 text-sm">Kritické myšlení</p>
                </div>
            </div>
            
            <div id="debateContainer" class="border border-gray-200 rounded-xl p-4 mb-6 max-h-[600px] overflow-y-auto">
                <div id="debateContent"></div>
                <div id="currentTyping" class="typing-animation hidden"></div>
            </div>
            
            <div class="flex justify-center space-x-4">
                <button id="pauseDebate" class="btn-secondary px-6 py-2 rounded-lg font-medium flex items-center">
                    <i class="fas fa-pause mr-2"></i>
                    Pozastavit
                </button>
                <button id="skipToEnd" class="btn-secondary px-6 py-2 rounded-lg font-medium flex items-center">
                    <i class="fas fa-forward mr-2"></i>
                    Přeskočit na konec
                </button>
            </div>
        </div>
        
        <div id="summarySection" class="bg-white rounded-2xl shadow-lg p-6 mb-8 hidden">
            <h2 class="text-2xl font-semibold text-gray-800 mb-4">Shrnutí debaty</h2>
            
            <div class="mb-6">
                <h3 class="text-xl font-medium text-gray-800 mb-3">Hlavní body diskuze</h3>
                <div id="mainPoints" class="grid grid-cols-1 md:grid-cols-2 gap-6"></div>
            </div>
            
            <div class="mb-6">
                <h3 class="text-xl font-medium text-gray-800 mb-3">Oblasti shody</h3>
                <div id="agreements" class="bg-gray-50 rounded-xl p-4"></div>
            </div>
            
            <div class="mb-6">
                <h3 class="text-xl font-medium text-gray-800 mb-3">Oblasti neshody</h3>
                <div id="disagreements" class="bg-gray-50 rounded-xl p-4"></div>
            </div>
            
            <div class="border-t border-gray-200 pt-6">
                <h3 class="text-xl font-medium text-gray-800 mb-3">Celkové shrnutí</h3>
                <p id="overallSummary" class="text-gray-700 leading-relaxed"></p>
            </div>
        </div>
        
        <div id="podcastSection" class="bg-white rounded-2xl shadow-lg p-6 mb-8 hidden">
            <h2 class="text-2xl font-semibold text-gray-800 mb-4">Vytvořit podcast</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="card-hover bg-gray-50 rounded-xl p-4">
                    <h3 class="text-lg font-medium text-gray-800 mb-2">Název podcastu</h3>
                    <input type="text" id="podcastName" class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Zadejte název podcastu">
                </div>
                
                <div class="card-hover bg-gray-50 rounded-xl p-4">
                    <h3 class="text-lg font-medium text-gray-800 mb-2">Formát podcastu</h3>
                    <select id="podcastFormat" class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="standard">Standardní podcast</option>
                        <option value="narrative">Narativní podcast</option>
                        <option value="interview">Interview formát</option>
                    </select>
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="card-hover bg-gray-50 rounded-xl p-4">
                    <h3 class="text-lg font-medium text-gray-800 mb-2">Hlas pro Model A</h3>
                    <select id="voiceA" class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="male1">Muž 1</option>
                        <option value="female1">Žena 1</option>
                        <option value="male2">Muž 2</option>
                        <option value="female2">Žena 2</option>
                    </select>
                </div>
                
                <div class="card-hover bg-gray-50 rounded-xl p-4">
                    <h3 class="text-lg font-medium text-gray-800 mb-2">Hlas pro Model B</h3>
                    <select id="voiceB" class="w-full px-3 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="male1">Muž 1</option>
                        <option value="female1" selected>Žena 1</option>
                        <option value="male2">Muž 2</option>
                        <option value="female2">Žena 2</option>
                    </select>
                </div>
            </div>
            
            <div class="card-hover bg-gray-50 rounded-xl p-4 mb-6">
                <h3 class="text-lg font-medium text-gray-800 mb-2">Zahrnout do podcastu</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="flex items-center">
                        <input type="checkbox" id="includeIntro" checked class="w-5 h-5 text-blue-600 rounded focus:ring-blue-500">
                        <label for="includeIntro" class="ml-2 text-gray-700">Úvod</label>
                    </div>
                    <div class="flex items-center">
                        <input type="checkbox" id="includeDebate" checked class="w-5 h-5 text-blue-600 rounded focus:ring-blue-500">
                        <label for="includeDebate" class="ml-2 text-gray-700">Debata</label>
                    </div>
                    <div class="flex items-center">
                        <input type="checkbox" id="includeSummary" checked class="w-5 h-5 text-blue-600 rounded focus:ring-blue-500">
                        <label for="includeSummary" class="ml-2 text-gray-700">Shrnutí</label>
                    </div>
                    <div class="flex items-center">
                        <input type="checkbox" id="includeMusic" checked class="w-5 h-5 text-blue-600 rounded focus:ring-blue-500">
                        <label for="includeMusic" class="ml-2 text-gray-700">Hudební přechody</label>
                    </div>
                    <div class="flex items-center">
                        <input type="checkbox" id="includeOutro" checked class="w-5 h-5 text-blue-600 rounded focus:ring-blue-500">
                        <label for="includeOutro" class="ml-2 text-gray-700">Závěr</label>
                    </div>
                </div>
            </div>
            
            <div id="podcastPreview" class="border border-gray-200 rounded-xl p-4 mb-6 hidden">
                <h3 class="text-lg font-medium text-gray-800 mb-2">Náhled podcastu</h3>
                <div class="flex items-center bg-gray-50 rounded-xl p-4">
                    <div class="bg-gray-200 rounded-lg w-20 h-20 flex items-center justify-center mr-4">
                        <i class="fas fa-podcast text-gray-500 text-2xl"></i>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-800" id="previewTitle">Název podcastu</h4>
                        <p class="text-gray-600 text-sm" id="previewDetails">0:00 • Formát: Standardní</p>
                        <div class="mt-2 flex items-center">
                            <div class="w-48 h-2 bg-gray-200 rounded-full mr-2">
                                <div class="h-2 bg-blue-500 rounded-full" style="width: 0%"></div>
                            </div>
                            <span class="text-xs text-gray-600">0:00</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="flex justify-center">
                <button id="createPodcast" class="btn-primary px-8 py-3 rounded-lg font-medium flex items-center shadow-md">
                    <i class="fas fa-podcast mr-2"></i>
                    Vytvořit podcast
                </button>
            </div>
        </div>
        
        <div id="downloadSection" class="bg-white rounded-2xl shadow-lg p-6 mb-8 hidden">
            <h2 class="text-2xl font-semibold text-gray-800 mb-4">Stáhnout výsledky</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="card-hover bg-gray-50 rounded-xl p-6 text-center">
                    <div class="w-16 h-16 rounded-full bg-blue-100 flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-file-alt text-blue-500 text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-800 mb-2">Textový přepis</h3>
                    <p class="text-gray-600 text-sm mb-4">Stáhněte si kompletní textový přepis debaty</p>
                    <button class="btn-secondary px-4 py-2 rounded-lg font-medium w-full">
                        <i class="fas fa-download mr-2"></i>
                        Stáhnout TXT
                    </button>
                </div>
                
                <div class="card-hover bg-gray-50 rounded-xl p-6 text-center">
                    <div class="w-16 h-16 rounded-full bg-green-100 flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-podcast text-green-500 text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-800 mb-2">Audio podcast</h3>
                    <p class="text-gray-600 text-sm mb-4">Stáhněte si debatu ve formátu MP3</p>
                    <button class="btn-secondary px-4 py-2 rounded-lg font-medium w-full">
                        <i class="fas fa-download mr-2"></i>
                        Stáhnout MP3
                    </button>
                </div>
                
                <div class="card-hover bg-gray-50 rounded-xl p-6 text-center">
                    <div class="w-16 h-16 rounded-full bg-purple-100 flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-share-alt text-purple-500 text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-800 mb-2">Sdílet debatu</h3>
                    <p class="text-gray-600 text-sm mb-4">Sdílejte debatu na sociálních sítích</p>
                    <div class="flex justify-center space-x-2">
                        <button class="w-10 h-10 rounded-full bg-blue-500 flex items-center justify-center text-white">
                            <i class="fab fa-facebook-f"></i>
                        </button>
                        <button class="w-10 h-10 rounded-full bg-blue-400 flex items-center justify-center text-white">
                            <i class="fab fa-twitter"></i>
                        </button>
                        <button class="w-10 h-10 rounded-full bg-pink-500 flex items-center justify-center text-white">
                            <i class="fab fa-instagram"></i>
                        </button>
                        <button class="w-10 h-10 rounded-full bg-green-500 flex items-center justify-center text-white">
                            <i class="fas fa-link"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <footer class="text-center text-gray-600 mt-12 mb-4">
            <p>&copy; 2023 AI Debatní Platforma | Všechna práva vyhrazena</p>
        </footer>
    </div>
    
    <script>
        // Simulované debatní texty a odpovědi (v češtině)
        const debateResponses = {
            // Pro elektromobily
            "elektromobily_pro": [
                "Z mého pohledu jsou elektromobily jednoznačně budoucností dopravy. Nabízejí několik zásadních výhod: nulové emise při jízdě, nižší provozní náklady a celkově vyšší energetickou účinnost. Elektromotory dokážou přeměnit více než 90% energie na pohyb, zatímco spalovací motory pouze kolem 40%.",
                "Souhlasím, že infrastruktura je výzvou, ale investice do ní rychle rostou. Jen v ČR se počet dobíjecích stanic každoročně zdvojnásobuje. Co se týče výroby baterií, technologie se neustále zlepšují a jejich environmentální dopad klesá. Recyklace lithiových baterií dosahuje již 95% účinnosti.",
                "Cenová dostupnost elektromobilů se každým rokem zlepšuje. Podle studie Bloomberg NEF dojde k cenovému vyrovnání s konvenčními vozidly již kolem roku 2025. Navíc při započtení celkových nákladů na vlastnictví (TCO) jsou elektromobily již nyní výhodnější díky nižším nákladům na údržbu a provoz.",
                "Argument ohledně dojezdu je stále méně relevantní. Současné modely běžně nabízejí dojezd přes 400 km, což pokrývá potřeby většiny řidičů. Rychlonabíjecí stanice nyní umožňují dobít baterii na 80% kapacity za méně než 30 minut."
            ],
            
            // Proti elektromobilům
            "elektromobily_proti": [
                "Nemohu souhlasit s tím, že jsou elektromobily jediným řešením budoucí mobility. Přehlížíme několik zásadních problémů: nedostatečnou infrastrukturu dobíjecích stanic, problematickou výrobu baterií zatěžující životní prostředí a fakt, že elektřina často pochází z fosilních zdrojů, což relativizuje ekologický přínos.",
                "Cenová nedostupnost elektromobilů je stále významnou překážkou masového rozšíření. Průměrná cena elektromobilu v ČR je o 40% vyšší než u srovnatelného vozu se spalovacím motorem. Tato cenová propast znemožňuje většině domácností reálnou volbu elektromobility.",
                "Dalším problémem je životnost baterií a jejich recyklace. Současné lithium-iontové baterie mají omezenou životnost a jejich výroba je náročná na vzácné kovy jako kobalt a lithium, jejichž těžba má značné environmentální a sociální dopady v zemích třetího světa.",
                "Omezený dojezd a dlouhá doba nabíjení zůstávají praktickými překážkami, zejména v zemích jako je Česká republika s chladnějším klimatem, kde v zimě dojezd výrazně klesá. Pro mnoho uživatelů, zejména na delší cesty nebo pro profesionální využití, nejsou elektromobily praktickou alternativou."
            ],
            
            // Klimatické změny - pro aktivní řešení
            "klima_pro": [
                "Klimatické změny představují bez pochyby jednu z největších výzev naší doby. Vědecký konsenzus je jasný - globální oteplování je reálné a způsobené především lidskou činností, zejména spalováním fosilních paliv. Měření ukazují, že průměrná globální teplota vzrostla o více než 1°C od předindustriálního období.",
                "Argument ekonomických nákladů je krátkozraký. Podle studie ekonoma Nicholase Sterna budou náklady na nečinnost mnohonásobně vyšší než investice do snižování emisí. Každé euro investované do prevence ušetří 4-7 eur na budoucích škodách.",
                "Co se týče účinnosti opatření, máme již mnoho příkladů úspěšných strategií. Dánsko například dokázalo výrazně snížit své emise a zároveň udržet ekonomický růst. Podobné úspěchy vidíme i v jiných zemích, které investovaly do obnovitelných zdrojů a energetické účinnosti.",
                "Souhlasím, že adaptace je důležitá, ale bez mitigace – tedy snižování emisí – budou nutné adaptační opatření stále nákladnější a v určitém bodě již nebudou stačit. Je nezbytné kombinovat oba přístupy."
            ],
            
            // Klimatické změny - proti aktivnímu řešení
            "klima_proti": [
                "Ačkoli klimatické změny probíhají, míra naší schopnosti je ovlivnit je přeceňována. Klimatický systém Země je složitý a ovlivňovaný mnoha faktory včetně sluneční aktivity a přírodních cyklů. Historické údaje ukazují, že Země prošla mnoha obdobími oteplování a ochlazování ještě před průmyslovou revolucí.",
                "Ekonomické náklady na radikální transformaci energetiky jsou enormní. Například Green Deal EU bude podle odhadů stát biliony eur, zatímco jeho vliv na globální klima bude minimální, pokud se nezapojí největší emitenti jako Čína a Indie, které své emise nadále zvyšují.",
                "Namísto nákladných a potenciálně neúčinných opatření na snižování emisí bychom se měli více zaměřit na adaptaci na změny klimatu. Investice do protipovodňových opatření, odolnějšího zemědělství a lepšího hospodaření s vodou přinesou konkrétní a měřitelné výsledky.",
                "Je důležité zachovat ekonomickou prosperitu, abychom měli prostředky na řešení skutečných problémů. Příliš ambiciózní klimatické cíle mohou vést k energetické chudobě, ztrátě konkurenceschopnosti a pracovních míst, a nakonec i ke snížení životní úrovně."
            ]
        };
        
        const summaries = {
            "elektromobily": {
                mainPointsA: [
                    "Elektromobily mají vyšší energetickou účinnost než spalovací motory",
                    "Infrastruktura dobíjecích stanic se rychle rozvíjí",
                    "Celkové náklady na vlastnictví jsou již nyní konkurenceschopné",
                    "Dojezd moderních elektromobilů je dostačující pro běžné použití"
                ],
                mainPointsB: [
                    "Výroba baterií má významné environmentální dopady",
                    "Elektromobily jsou cenově nedostupné pro většinu populace",
                    "Omezená životnost baterií a problémy s recyklací",
                    "Praktické problémy s dojezdem a dobíjením v určitých podmínkách"
                ],
                agreements: [
                    "Elektromobilita je důležitou součástí budoucí dopravy",
                    "Technologie baterií se zlepšuje a bude se dále vyvíjet",
                    "Ceny elektromobilů budou v budoucnu klesat"
                ],
                disagreements: [
                    "Časový horizont, kdy se elektromobily stanou dominantní formou dopravy",
                    "Míra ekologického přínosu elektromobilů vs. jejich výrobní zátěž",
                    "Dostatečnost současné a plánované infrastruktury"
                ],
                overall: "Debata o elektromobilech jako budoucnosti dopravy ukázala, že ačkoli obě strany souhlasí s jejich rostoucím významem, existují legitimní obavy ohledně cenové dostupnosti, infrastruktury a environmentálních dopadů výroby baterií. Pro masové rozšíření elektromobility bude klíčové řešit tyto výzvy a zajistit, aby přechod byl ekonomicky udržitelný a environmentálně přínosný. Technologický pokrok, zejména v oblasti baterií, bude hrát zásadní roli."
            },
            "klima": {
                mainPointsA: [
                    "Existuje vědecký konsenzus o antropogenních příčinách klimatické změny",
                    "Náklady na nečinnost převýší náklady na preventivní opatření",
                    "Existují příklady úspěšných strategií snižování emisí",
                    "Mitigace i adaptace jsou nezbytné pro účinné řešení"
                ],
                mainPointsB: [
                    "Klimatický systém je komplexní a ovlivněný mnoha přirozenými faktory",
                    "Ekonomické náklady na radikální transformaci jsou příliš vysoké",
                    "Adaptace je účinnější strategie než nákladná mitigační opatření",
                    "Klimatické cíle mohou ohrozit ekonomickou prosperitu a životní úroveň"
                ],
                agreements: [
                    "Klimatické změny probíhají a představují výzvu",
                    "Adaptační opatření jsou důležitou součástí řešení",
                    "Ekonomické aspekty musí být brány v úvahu"
                ],
                disagreements: [
                    "Míra, jakou lidská činnost přispívá ke klimatickým změnám",
                    "Poměr nákladů a přínosů ambiciózních klimatických opatření",
                    "Prioritizace mitigace versus adaptace"
                ],
                overall: "Debata o přístupu ke klimatickým změnám odhalila základní shodu, že klimatické změny jsou reálné, ale zásadní rozdíly v pohledu na jejich příčiny, závažnost a optimální strategii řešení. Zatímco jedna strana zdůrazňuje vědecký konsenzus a potřebu ambiciózních mitigačních opatření, druhá upřednostňuje adaptaci a ekonomickou udržitelnost. Efektivní klimatická politika pravděpodobně vyžaduje vyvážený přístup, který bere v úvahu jak environmentální, tak ekonomické a sociální aspekty problému."
            }
        };
        
        // Hlavní proměnné pro simulaci
        let debateTopic = "";
        let debateFormat = "";
        let debateLength = "";
        let debatePaused = false;
        let currentExchange = 0;
        let totalExchanges = 5;
        let debateMessages = [];
        let typingSpeed = 20; // ms na znak
        let currentlyTyping = false;
        
        // DOM reference
        const startDebateBtn = document.getElementById('startDebate');
        const debateSection = document.getElementById('debateSection');
        const summarySection = document.getElementById('summarySection');
        const podcastSection = document.getElementById('podcastSection');
        const downloadSection = document.getElementById('downloadSection');
        const debateContent = document.getElementById('debateContent');
        const currentTyping = document.getElementById('currentTyping');
        const pauseDebateBtn = document.getElementById('pauseDebate');
        const skipToEndBtn = document.getElementById('skipToEnd');
        const progressBar = document.getElementById('progressBar');
        const debateProgressText = document.getElementById('debateProgress');
        const createPodcastBtn = document.getElementById('createPodcast');
        const podcastPreview = document.getElementById('podcastPreview');
        const previewTitle = document.getElementById('previewTitle');
        const previewDetails = document.getElementById('previewDetails');
        
        // Event listenery
        startDebateBtn.addEventListener('click', startDebate);
        pauseDebateBtn.addEventListener('click', togglePauseDebate);
        skipToEndBtn.addEventListener('click', skipToEnd);
        createPodcastBtn.addEventListener('click', createPodcast);
        
        // Funkce pro zahájení debaty
        function startDebate() {
            debateTopic = document.getElementById('topic').value || "Jsou elektromobily budoucností dopravy?";
            debateFormat = document.getElementById('format').value;
            debateLength = document.getElementById('length').value;
            
            // Určení délky debaty podle výběru
            if (debateLength === 'short') totalExchanges = 3;
            else if (debateLength === 'medium') totalExchanges = 5;
            else totalExchanges = 8;
            
            // Reset proměnných
            currentExchange = 0;
            debateMessages = [];
            debateContent.innerHTML = '';
            currentTyping.innerHTML = '';
            currentTyping.classList.add('hidden');
            progressBar.style.width = '0%';
            debateProgressText.textContent = '0%';
            
            // Zobrazení sekce debaty
            debateSection.classList.remove('hidden');
            summarySection.classList.add('hidden');
            podcastSection.classList.add('hidden');
            downloadSection.classList.add('hidden');
            
            // Simulace načítání
            setTimeout(() => {
                // Vybereme odpovídající odpovědi podle tématu
                let responses = [];
                if (debateTopic.toLowerCase().includes('elektromobil')) {
                    responses = [
                        debateResponses.elektromobily_pro,
                        debateResponses.elektromobily_proti
                    ];
                } else if (debateTopic.toLowerCase().includes('klima') || debateTopic.toLowerCase().includes('globální oteplování')) {
                    responses = [
                        debateResponses.klima_pro,
                        debateResponses.klima_proti
                    ];
                } else {
                    // Fallback na elektromobily, pokud téma nerozpoznáme
                    responses = [
                        debateResponses.elektromobily_pro,
                        debateResponses.elektromobily_proti
                    ];
                }
                
                // Vytvoříme zprávy pro debatu
                for (let i = 0; i < totalExchanges; i++) {
                    // Model A
                    if (i < responses[0].length) {
                        debateMessages.push({
                            model: 'A',
                            text: responses[0][i]
                        });
                    } else {
                        debateMessages.push({
                            model: 'A',
                            text: "Děkuji za tuto debatu. Myslím, že jsme pokryli mnoho důležitých aspektů tohoto tématu."
                        });
                    }
                    
                    // Model B
                    if (i < responses[1].length) {
                        debateMessages.push({
                            model: 'B',
                            text: responses[1][i]
                        });
                    } else {
                        debateMessages.push({
                            model: 'B',
                            text: "Souhlasím, že tato debata byla produktivní. Děkuji za sdílení vašich názorů."
                        });
                    }
                }
                
                // Zahájíme zobrazování zpráv
                displayNextMessage();
            }, 500);
        }
        
        // Funkce pro zobrazení další zprávy v debatě
        function displayNextMessage() {
            if (debatePaused) return;
            
            if (currentExchange < debateMessages.length) {
                const message = debateMessages[currentExchange];
                currentlyTyping = true;
                
                // Vytvoření bubliny pro zprávu
                const messageDiv = document.createElement('div');
                messageDiv.className = `debate-bubble model-${message.model.toLowerCase()}`;
                
                const modelLabel = document.createElement('div');
                modelLabel.className = 'text-sm font-medium mb-1';
                modelLabel.textContent = `Model ${message.model}`;
                
                const messageContent = document.createElement('div');
                messageContent.id = `message-${currentExchange}`;
                
                messageDiv.appendChild(modelLabel);
                messageDiv.appendChild(messageContent);
                debateContent.appendChild(messageDiv);
                
                // Animace psaní textu
                currentTyping.classList.remove('hidden');
                typeWriter(message.text, `message-${currentExchange}`, 0, () => {
                    currentlyTyping.classList.add('hidden');
                    currentlyTyping = false;
                    currentExchange++;
                    
                    // Aktualizace progress baru
                    const progress = Math.round((currentExchange / debateMessages.length) * 100);
                    progressBar.style.width = `${progress}%`;
                    debateProgressText.textContent = `${progress}%`;
                    
                    // Pokud jsme dokončili všechny výměny, zobrazíme shrnutí
                    if (currentExchange >= debateMessages.length) {
                        setTimeout(showSummary, 1000);
                    } else {
                        // Jinak pokračujeme další zprávou
                        setTimeout(displayNextMessage, 500);
                    }
                });
            }
        }
        
        // Funkce pro animaci psaní textu
        function typeWriter(text, elementId, index, callback) {
            if (index < text.length) {
                document.getElementById(elementId).textContent = text.substring(0, index + 1);
                setTimeout(() => {
                    typeWriter(text, elementId, index + 1, callback);
                }, typingSpeed);
            } else {
                callback();
            }
        }
        
        // Funkce pro přepínání pauzy debaty
        function togglePauseDebate() {
            debatePaused = !debatePaused;
            
            if (debatePaused) {
                pauseDebateBtn.innerHTML = '<i class="fas fa-play mr-2"></i>Pokračovat';
            } else {
                pauseDebateBtn.innerHTML = '<i class="fas fa-pause mr-2"></i>Pozastavit';
                displayNextMessage();
            }
        }
        
        // Funkce pro přeskočení na konec debaty
        function skipToEnd() {
            // Zastavíme aktuální psaní
            debatePaused = true;
            
            // Vypíšeme všechny zbývající zprávy najednou
            for (let i = currentExchange; i < debateMessages.length; i++) {
                const message = debateMessages[i];
                
                // Vytvoření bubliny pro zprávu
                const messageDiv = document.createElement('div');
                messageDiv.className = `debate-bubble model-${message.model.toLowerCase()}`;
                
                const modelLabel = document.createElement('div');
                modelLabel.className = 'text-sm font-medium mb-1';
                modelLabel.textContent = `Model ${message.model}`;
                
                const messageContent = document.createElement('div');
                messageContent.textContent = message.text;
                
                messageDiv.appendChild(modelLabel);
                messageDiv.appendChild(messageContent);
                debateContent.appendChild(messageDiv);
            }
            
            // Nastavíme progress bar na 100%
            progressBar.style.width = '100%';
            debateProgressText.textContent = '100%';
            
            // Ukážeme shrnutí
            currentExchange = debateMessages.length;
            setTimeout(showSummary, 500);
        }
        
        // Funkce pro zobrazení shrnutí
        function showSummary() {
            // Určení, které shrnutí použít
            let summary;
            if (debateTopic.toLowerCase().includes('elektromobil')) {
                summary = summaries.elektromobily;
            } else if (debateTopic.toLowerCase().includes('klima') || debateTopic.toLowerCase().includes('globální oteplování')) {
                summary = summaries.klima;
            } else {
                // Fallback na elektromobily
                summary = summaries.elektromobily;
            }
            
            // Naplnění hlavních bodů
            const mainPointsDiv = document.getElementById('mainPoints');
            mainPointsDiv.innerHTML = '';
            
            const modelADiv = document.createElement('div');
            modelADiv.className = 'bg-blue-50 rounded-xl p-4';
            modelADiv.innerHTML = '<h4 class="font-medium text-gray-800 mb-2">Model A</h4>';
            
            summary.mainPointsA.forEach(point => {
                const pointDiv = document.createElement('div');
                pointDiv.className = 'summary-point model-a-point mb-2';
                pointDiv.textContent = point;
                modelADiv.appendChild(pointDiv);
            });
            
            const modelBDiv = document.createElement('div');
            modelBDiv.className = 'bg-green-50 rounded-xl p-4';
            modelBDiv.innerHTML = '<h4 class="font-medium text-gray-800 mb-2">Model B</h4>';
            
            summary.mainPointsB.forEach(point => {
                const pointDiv = document.createElement('div');
                pointDiv.className = 'summary-point model-b-point mb-2';
                pointDiv.textContent = point;
                modelBDiv.appendChild(pointDiv);
            });
            
            mainPointsDiv.appendChild(modelADiv);
            mainPointsDiv.appendChild(modelBDiv);
            
            // Naplnění oblastí shody
            const agreementsDiv = document.getElementById('agreements');
            agreementsDiv.innerHTML = '';
            
            summary.agreements.forEach(point => {
                const pointDiv = document.createElement('div');
                pointDiv.className = 'mb-2';
                pointDiv.innerHTML = `<i class="fas fa-check-circle text-green-500 mr-2"></i>${point}`;
                agreementsDiv.appendChild(pointDiv);
            });
            
            // Naplnění oblastí neshody
            const disagreementsDiv = document.getElementById('disagreements');
            disagreementsDiv.innerHTML = '';
            
            summary.disagreements.forEach(point => {
                const pointDiv = document.createElement('div');
                pointDiv.className = 'mb-2';
                pointDiv.innerHTML = `<i class="fas fa-times-circle text-red-500 mr-2"></i>${point}`;
                disagreementsDiv.appendChild(pointDiv);
            });
            
            // Naplnění celkového shrnutí
            document.getElementById('overallSummary').textContent = summary.overall;
            
            // Zobrazení shrnutí a skrytí debaty
            debateSection.classList.add('hidden');
            summarySection.classList.remove('hidden');
            podcastSection.classList.remove('hidden');
            downloadSection.classList.remove('hidden');
        }
        
        // Funkce pro vytvoření podcastu
        function createPodcast() {
            const podcastName = document.getElementById('podcastName').value || `Debata o tématu: ${debateTopic}`;
            const podcastFormat = document.getElementById('podcastFormat').value;
            
            // Aktualizace náhledu podcastu
            previewTitle.textContent = podcastName;
            let formatText = "";
            if (podcastFormat === 'standard') formatText = "Standardní";
            else if (podcastFormat === 'narrative') formatText = "Narativní";
            else formatText = "Interview";
            
            previewDetails.textContent = `25:32 • Formát: ${formatText}`;
            
            // Zobrazení náhledu
            podcastPreview.classList.remove('hidden');
            
            // Simulace vytváření podcastu
            createPodcastBtn.disabled = true;
            createPodcastBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Vytvářím podcast...';
            
            setTimeout(() => {
                createPodcastBtn.disabled = false;
                createPodcastBtn.innerHTML = '<i class="fas fa-check mr-2"></i>Podcast vytvořen';
                
                // Po 2 sekundách vrátíme původní text
                setTimeout(() => {
                    createPodcastBtn.innerHTML = '<i class="fas fa-podcast mr-2"></i>Vytvořit podcast';
                }, 2000);
            }, 3000);
        }
    </script>
</body>
</html>
