import{a as F,s as N}from"./index-CEJd0imi.js";import{Z as A,aa as I,c as m,a as u,k as b,e as o,N as f,a1 as v,H as U,d as O,K as G,a_ as H,J as Z,bT as x,bU as g,l as q,h as i,z as S,f as t,b as n,x as r,W as K,g as W,v as J,a5 as j,m as Q,F as X,bV as c,C as Y}from"./index-jan7QZNA.js";import{s as _}from"./index-CnZDKEcR.js";var ee=({dt:s})=>`
.p-card {
    background: ${s("card.background")};
    color: ${s("card.color")};
    box-shadow: ${s("card.shadow")};
    border-radius: ${s("card.border.radius")};
    display: flex;
    flex-direction: column;
}

.p-card-caption {
    display: flex;
    flex-direction: column;
    gap: ${s("card.caption.gap")};
}

.p-card-body {
    padding: ${s("card.body.padding")};
    display: flex;
    flex-direction: column;
    gap: ${s("card.body.gap")};
}

.p-card-title {
    font-size: ${s("card.title.font.size")};
    font-weight: ${s("card.title.font.weight")};
}

.p-card-subtitle {
    color: ${s("card.subtitle.color")};
}
`,te={root:"p-card p-component",header:"p-card-header",body:"p-card-body",caption:"p-card-caption",title:"p-card-title",subtitle:"p-card-subtitle",content:"p-card-content",footer:"p-card-footer"},se=A.extend({name:"card",style:ee,classes:te}),oe={name:"BaseCard",extends:I,style:se,provide:function(){return{$pcCard:this,$parentInstance:this}}},T={name:"Card",extends:oe,inheritAttrs:!1};function le(s,a,p,y,k,w){return u(),m("div",f({class:s.cx("root")},s.ptmi("root")),[s.$slots.header?(u(),m("div",f({key:0,class:s.cx("header")},s.ptm("header")),[v(s.$slots,"header")],16)):b("",!0),o("div",f({class:s.cx("body")},s.ptm("body")),[s.$slots.title||s.$slots.subtitle?(u(),m("div",f({key:0,class:s.cx("caption")},s.ptm("caption")),[s.$slots.title?(u(),m("div",f({key:0,class:s.cx("title")},s.ptm("title")),[v(s.$slots,"title")],16)):b("",!0),s.$slots.subtitle?(u(),m("div",f({key:1,class:s.cx("subtitle")},s.ptm("subtitle")),[v(s.$slots,"subtitle")],16)):b("",!0)],16)):b("",!0),o("div",f({class:s.cx("content")},s.ptm("content")),[v(s.$slots,"content")],16),s.$slots.footer?(u(),m("div",f({key:1,class:s.cx("footer")},s.ptm("footer")),[v(s.$slots,"footer")],16)):b("",!0)],16)],16)}T.render=le;const ae={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"};function ne(s,a){return u(),m("svg",ae,a[0]||(a[0]=[o("path",{fill:"currentColor",d:"M7.03 13.92h4V5l2.01-.03v8.95h3.99l-5 5Z"},null,-1)]))}const ie=U({name:"mdi-arrow-down-thin",render:ne}),re={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"};function de(s,a){return u(),m("svg",re,a[0]||(a[0]=[o("path",{fill:"currentColor",d:"M7.03 9.97h4v8.92l2.01.03V9.97h3.99l-5-5Z"},null,-1)]))}const ue=U({name:"mdi-arrow-up-thin",render:de}),me={class:"flex flex-col gap-4 text-start dark:text-surface-300"},pe={class:"text-left"},fe={class:"border border-surface-400 rounded-sm p-4 space-y-4"},ce={class:"flex flex-row gap-2 space-y-2"},ye={class:"space-y-1"},be={class:"space-y-1"},ve={class:"border border-surface-400 rounded-sm p-4 space-y-4"},ge={class:"space-y-1"},Ve={class:"flex gap-4"},xe={class:"flex items-center"},ke={class:"flex items-center"},we={class:"space-y-1"},$e={class:"flex flex-row gap-5 items-center"},Ce=["for"],Se={class:"mr-2"},Ue={class:"border rounded-sm p-4 space-y-4"},Te={class:"space-y-2"},Be={class:"border rounded-sm p-4 space-y-4"},he=O({__name:"SettingsView",setup(s){const a=G(),p=H(),y=Z(),k=["UTC",Intl.DateTimeFormat().resolvedOptions().timeZone],w=[{value:x.showPill,text:"Show pill in icon"},{value:x.asTitle,text:"Show in title"},{value:x.noOpenTrades,text:"Don't show open trades in header"}],B=[{value:g.GREEN_UP,text:"Green Up/Red Down"},{value:g.RED_UP,text:"Green Down/Red Up"}],P=()=>{y.resetTradingLayout(),y.resetDashboardLayout(),Y("Layouts have been reset.")};return($,e)=>{const d=K,z=W,L=J,C=j,V=_,h=ue,D=ie,E=N,M=T;return u(),q(M,{class:"mx-auto mt-3 p-4 max-w-4xl"},{title:i(()=>e[16]||(e[16]=[r("FreqUI Settings")])),content:i(()=>[o("div",me,[o("p",pe,"UI Version: "+S(t(a).uiVersion),1),o("div",fe,[e[31]||(e[31]=o("h4",{class:"text-xl font-semibold"},"UI settings",-1)),n(d,{modelValue:t(y).layoutLocked,"onUpdate:modelValue":e[0]||(e[0]=l=>t(y).layoutLocked=l),class:"space-y-1"},{hint:i(()=>e[17]||(e[17]=[r(" Lock dynamic layouts, so they cannot move anymore. Can also be set from the navbar at the top. ")])),default:i(()=>[e[18]||(e[18]=r(" Lock dynamic layouts "))]),_:1},8,["modelValue"]),o("div",ce,[n(z,{severity:"secondary",size:"small",onClick:P},{default:i(()=>e[19]||(e[19]=[r("Reset layout")])),_:1}),e[20]||(e[20]=o("small",{class:"block text-surface-600 dark:text-surface-400"},"Reset dynamic layouts to how they were.",-1))]),n(L),o("div",ye,[e[21]||(e[21]=o("label",{class:"block text-sm"},"Show open trades in header",-1)),n(C,{modelValue:t(a).openTradesInTitle,"onUpdate:modelValue":e[1]||(e[1]=l=>t(a).openTradesInTitle=l),options:w,"option-label":"text","option-value":"value",size:"small",class:"w-full"},null,8,["modelValue"]),e[22]||(e[22]=o("small",{class:"text-surface-600 dark:text-surface-400"},"Decide if open trades should be visualized",-1))]),o("div",be,[e[23]||(e[23]=o("label",{class:"block text-sm"},"UTC Timezone",-1)),n(C,{modelValue:t(a).timezone,"onUpdate:modelValue":e[2]||(e[2]=l=>t(a).timezone=l),options:k,class:"w-full",size:"small"},null,8,["modelValue"]),e[24]||(e[24]=o("small",{class:"text-surface-600 dark:text-surface-400"},"Select timezone (UTC is recommended as exchanges usually work in UTC)",-1))]),n(d,{modelValue:t(a).backgroundSync,"onUpdate:modelValue":e[3]||(e[3]=l=>t(a).backgroundSync=l),class:"space-y-1"},{hint:i(()=>e[25]||(e[25]=[r(" Keep background sync running while other bots are selected. ")])),default:i(()=>[e[26]||(e[26]=r(" Background sync "))]),_:1},8,["modelValue"]),n(d,{modelValue:t(a).confirmDialog,"onUpdate:modelValue":e[4]||(e[4]=l=>t(a).confirmDialog=l),class:"space-y-1"},{hint:i(()=>e[27]||(e[27]=[r("Use confirmation dialogs when force-exiting a trade.")])),default:i(()=>[e[28]||(e[28]=r(" Show Confirm Dialog for Trade Exits "))]),_:1},8,["modelValue"]),n(d,{modelValue:t(a).multiPaneButtonsShowText,"onUpdate:modelValue":e[5]||(e[5]=l=>t(a).multiPaneButtonsShowText=l),class:"space-y-1"},{hint:i(()=>e[29]||(e[29]=[r("Show text on multi pane buttons. If disabled, only shows images.")])),default:i(()=>[e[30]||(e[30]=r(" Show Text on Multi Pane Buttons "))]),_:1},8,["modelValue"])]),o("div",ve,[e[41]||(e[41]=o("h4",{class:"text-lg font-semibold"},"Chart settings",-1)),o("div",ge,[e[34]||(e[34]=o("label",{class:"block text-sm"},"Chart scale Side",-1)),o("div",Ve,[o("div",xe,[n(V,{modelValue:t(a).chartLabelSide,"onUpdate:modelValue":e[6]||(e[6]=l=>t(a).chartLabelSide=l),value:"left",size:"small"},null,8,["modelValue"]),e[32]||(e[32]=o("label",{class:"ml-2"},"Left",-1))]),o("div",ke,[n(V,{modelValue:t(a).chartLabelSide,"onUpdate:modelValue":e[7]||(e[7]=l=>t(a).chartLabelSide=l),value:"right",size:"small"},null,8,["modelValue"]),e[33]||(e[33]=o("label",{class:"ml-2"},"Right",-1))])]),e[35]||(e[35]=o("small",{class:"text-surface-600 dark:text-surface-400"},"Should the scale be displayed on the right or left?",-1))]),n(d,{modelValue:t(a).useHeikinAshiCandles,"onUpdate:modelValue":e[8]||(e[8]=l=>t(a).useHeikinAshiCandles=l),class:"space-y-1"},{hint:i(()=>e[36]||(e[36]=[r("Use Heikin Ashi candles in your charts")])),default:i(()=>[e[37]||(e[37]=r(" Use Heikin Ashi candles "))]),_:1},8,["modelValue"]),n(d,{modelValue:t(a).useReducedPairCalls,"onUpdate:modelValue":e[9]||(e[9]=l=>t(a).useReducedPairCalls=l),class:"space-y-1"},{hint:i(()=>e[38]||(e[38]=[r("Can reduce the transfer size for large dataframes. May require additional calls if the plot config changes.")])),default:i(()=>[e[39]||(e[39]=r(" Only request necessary columns "))]),_:1},8,["modelValue"]),o("div",we,[e[40]||(e[40]=o("label",{class:"block text-sm"},"Candle Color Preference",-1)),o("div",$e,[(u(),m(X,null,Q(B,l=>o("div",{key:l.value,class:"flex items-center"},[n(V,{modelValue:t(p).colorPreference,"onUpdate:modelValue":e[10]||(e[10]=R=>t(p).colorPreference=R),value:l.value,"input-id":`input-id${l.value}`,size:"small",onChange:t(p).updateProfitLossColor},null,8,["modelValue","value","input-id","onChange"]),o("label",{for:`input-id${l.value}`,class:"ml-2 flex items-center"},[o("span",Se,S(l.text),1),n(h,{color:l.value===t(g).GREEN_UP?t(p).colorProfit:t(p).colorLoss,class:"-ml-2"},null,8,["color"]),n(D,{color:l.value===t(g).GREEN_UP?t(p).colorLoss:t(p).colorProfit,class:"-ml-2"},null,8,["color"])],8,Ce)])),64))])])]),o("div",Ue,[e[46]||(e[46]=o("h4",{class:"text-lg font-semibold"},"Notification Settings",-1)),o("div",Te,[n(d,{modelValue:t(a).notifications[t(c).entryFill],"onUpdate:modelValue":e[11]||(e[11]=l=>t(a).notifications[t(c).entryFill]=l)},{default:i(()=>e[42]||(e[42]=[r(" Entry notifications ")])),_:1},8,["modelValue"]),n(d,{modelValue:t(a).notifications[t(c).exitFill],"onUpdate:modelValue":e[12]||(e[12]=l=>t(a).notifications[t(c).exitFill]=l)},{default:i(()=>e[43]||(e[43]=[r(" Exit notifications ")])),_:1},8,["modelValue"]),n(d,{modelValue:t(a).notifications[t(c).entryCancel],"onUpdate:modelValue":e[13]||(e[13]=l=>t(a).notifications[t(c).entryCancel]=l)},{default:i(()=>e[44]||(e[44]=[r(" Entry Cancel notifications ")])),_:1},8,["modelValue"]),n(d,{modelValue:t(a).notifications[t(c).exitCancel],"onUpdate:modelValue":e[14]||(e[14]=l=>t(a).notifications[t(c).exitCancel]=l)},{default:i(()=>e[45]||(e[45]=[r(" Exit Cancel notifications ")])),_:1},8,["modelValue"])])]),o("div",Be,[e[49]||(e[49]=o("h4",{class:"text-lg font-semibold"},"Backtesting settings",-1)),o("div",null,[e[47]||(e[47]=o("label",{for:"backtestMetrics",class:"block text-sm"},"Backtesting metrics",-1)),n(E,{id:"backtestMetrics",modelValue:t(a).backtestAdditionalMetrics,"onUpdate:modelValue":e[15]||(e[15]=l=>t(a).backtestAdditionalMetrics=l),options:"availableBacktestMetrics"in $?$.availableBacktestMetrics:t(F),"option-label":"header","option-value":"field",class:"w-full",size:"small",display:"chip"},null,8,["modelValue","options"]),e[48]||(e[48]=o("small",{class:"text-surface-600 dark:text-surface-400"},"Select which metrics should be shown on a per pair / tag basis.",-1))])])])]),_:1})}}});export{he as default};
//# sourceMappingURL=SettingsView-41zYQ4Vi.js.map
