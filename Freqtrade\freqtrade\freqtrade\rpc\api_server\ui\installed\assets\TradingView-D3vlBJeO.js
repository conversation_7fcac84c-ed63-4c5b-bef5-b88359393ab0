import{_ as gt}from"./CandleChartContainer-kYq-Q-7a.js";import{_ as ht}from"./TradeDetail-KSLv3-F8.js";import{_ as Bt,a as bt,b as xt,c as kt}from"./TradeList.vue_vue_type_script_setup_true_lang-BoGONby_.js";import{_ as wt}from"./DraggableContainer.vue_vue_type_script_setup_true_lang-sbOSbagt.js";import{s as $t,a as St,b as Tt,c as Ct,d as Lt,_ as Pt}from"./index-31R9tXfD.js";import{d as V,u as O,c as d,a as i,e as s,b as o,g as j,f as e,h as r,y as Q,x as g,z as u,A as I,B as zt,C as Vt,r as q,j as b,D as Dt,o as At,l as p,k as x,i as X,F as Mt,m as Nt,E as G,G as z,v as Rt,H as T,I as Ft,J as Et,K as Ht,L as F,T as E,M as Z,N as H,O as Gt,P as Ot}from"./index-jan7QZNA.js";import{s as W,a as J}from"./index-BliD00yU.js";import qt from"./PairListLive-nSOC6ehl.js";import{_ as Ut}from"./PeriodBreakdown.vue_vue_type_script_setup_true_lang-BcNLHw4D.js";import{_ as It}from"./BotBalance.vue_vue_type_script_setup_true_lang-khW_mqWh.js";import{s as jt}from"./index-1hBUUM27.js";import{_ as Wt}from"./InfoBox.vue_vue_type_script_setup_true_lang-CKarcGN-.js";import"./index-baoyobTy.js";import"./index-CDX2XNv4.js";import"./check-Dry-GCBf.js";import"./plus-box-outline-BnlrPtQJ.js";import"./installCanvasRenderer-DkI93HUo.js";import"./chartZoom-Dvf7f-_l.js";import"./index-vUyST4l6.js";import"./index-F4gt2S59.js";import"./index-CnZDKEcR.js";import"./index-BGWAsqFH.js";import"./check-circle-D8u0q7gU.js";import"./install-BAjuFKaO.js";const Jt={class:"mb-2"},Kt=V({__name:"PairLockList",setup(v){const t=O();function l(a){console.log(a),a.id!==void 0?t.activeBot.deleteLock(a.id):Vt("This Freqtrade version does not support deleting locks.")}return(a,y)=>{const h=Q,k=j,w=J,B=zt,c=W;return i(),d("div",null,[s("div",Jt,[y[0]||(y[0]=s("label",{class:"me-auto text-xl"},"Pair Locks",-1)),o(k,{class:"float-end",severity:"secondary",onClick:e(t).activeBot.getLocks},{icon:r(()=>[o(h)]),_:1},8,["onClick"])]),s("div",null,[o(c,{size:"small",items:e(t).activeBot.activeLocks},{default:r(()=>[o(w,{field:"pair",header:"Pair"}),o(w,{field:"lock_end_timestamp",header:"Until"},{body:r(({data:n,field:m})=>[g(u(("timestampms"in a?a.timestampms:e(I))(n[m])),1)]),_:1}),o(w,{field:"reason",header:"Reason"}),o(w,{field:"actions",header:"Actions"},{body:r(({data:n})=>[o(k,{class:"btn-xs ms-1",size:"small",severity:"secondary",title:"Delete Lock",onClick:m=>l(n)},{default:r(()=>[o(B)]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["items"])])])}}}),Zt={class:"mb-2"},Qt=V({__name:"BotPerformance",setup(v){const t=O(),l=q("performance");function a(c,n){return c.length>n?c.substring(0,n)+"...":c}const y=b(()=>{var m;return[{performance:{key:"pair",label:"Pair"},entryStats:{key:"enter_tag",label:"Enter tag",formatter:_=>a(_,17)},exitStats:{key:"exit_reason",label:"Exit Reason",formatter:_=>a(_,17)},mixTagStats:{key:"mix_tag",label:"Mix Tag",formatter:_=>a(_,17)}}[l.value],{key:"profit",label:"Profit %"},{key:"profit_abs",label:`Profit ${(m=t.activeBot.botState)==null?void 0:m.stake_currency}`,formatter:_=>Dt(_,5)},{key:"count",label:"Count"}]}),h=b(()=>l.value==="performance"?t.activeBot.performanceStats:l.value==="entryStats"?t.activeBot.entryStats:l.value==="exitStats"?t.activeBot.exitStats:l.value==="mixTagStats"?t.activeBot.mixTagStats:[]),k=b(()=>t.activeBot.botApiVersion>=2.34),w=[{value:"performance",text:"Performance"},{value:"entryStats",text:"Entries"},{value:"exitStats",text:"Exits"},{value:"mixTagStats",text:"Mix Tag"}];function B(){l.value==="performance"&&t.activeBot.getPerformance(),l.value==="entryStats"&&t.activeBot.getEntryStats(),l.value==="exitStats"&&t.activeBot.getExitStats(),l.value==="mixTagStats"&&t.activeBot.getMixTagStats()}return At(()=>{B()}),(c,n)=>{const m=Q,_=j,S=jt,D=J,A=W;return i(),d("div",null,[s("div",Zt,[n[1]||(n[1]=s("h3",{class:"me-auto text-2xl inline"},"Performance",-1)),o(_,{class:"float-end",severity:"secondary",onClick:B},{icon:r(()=>[o(m)]),_:1})]),e(k)?(i(),p(S,{key:0,id:"order-direction",modelValue:e(l),"onUpdate:modelValue":n[0]||(n[0]=f=>X(l)?l.value=f:null),options:w,"allow-empty":!1,"option-label":"text","option-value":"value",size:"small",onChange:B},null,8,["modelValue"])):x("",!0),o(A,{size:"small",class:"text-center",value:e(h)},{default:r(()=>[(i(!0),d(Mt,null,Nt(e(y),f=>(i(),p(D,{key:f.key,field:f.key,header:f.label},{body:r(C=>[g(u(f.formatter?f.formatter(C.data[f.key]):C.data[f.key]),1)]),_:2},1032,["field","header"]))),128))]),_:1},8,["value"])])}}}),Xt=V({__name:"BotProfit",props:{profit:{required:!0,type:Object},stakeCurrency:{required:!0,type:String},stakeCurrencyDecimals:{required:!0,type:Number}},setup(v){const t=v,l=b(()=>t.profit?[{metric:"ROI closed trades",value:t.profit.profit_closed_coin?`${G(t.profit.profit_closed_coin,t.stakeCurrency,t.stakeCurrencyDecimals)} (${z(t.profit.profit_closed_ratio_mean,2)})`:"N/A"},{metric:"ROI all trades",value:t.profit.profit_all_coin?`${G(t.profit.profit_all_coin,t.stakeCurrency,t.stakeCurrencyDecimals)} (${z(t.profit.profit_all_ratio_mean,2)})`:"N/A"},{metric:"Total Trade count",value:`${t.profit.trade_count??0}`},{metric:"Bot started",value:t.profit.bot_start_timestamp,isTs:!0},{metric:"First Trade opened",value:t.profit.first_trade_timestamp,isTs:!0},{metric:"Latest Trade opened",value:t.profit.latest_trade_timestamp,isTs:!0},{metric:"Win / Loss",value:`${t.profit.winning_trades??0} / ${t.profit.losing_trades??0}`},{metric:"Winrate",value:`${t.profit.winrate?z(t.profit.winrate):"N/A"}`},{metric:"Expectancy (ratio)",value:`${t.profit.expectancy?t.profit.expectancy.toFixed(2):"N/A"} (${t.profit.expectancy_ratio?t.profit.expectancy_ratio.toFixed(2):"N/A"})`},{metric:"Avg. Duration",value:`${t.profit.avg_duration??"N/A"}`},{metric:"Best performing",value:t.profit.best_pair?`${t.profit.best_pair}: ${z(t.profit.best_pair_profit_ratio,2)}`:"N/A"},{metric:"Trading volume",value:`${G(t.profit.trading_volume??0,t.stakeCurrency,t.stakeCurrencyDecimals)}`},{metric:"Profit factor",value:`${t.profit.profit_factor?t.profit.profit_factor.toFixed(2):"N/A"}`},{metric:"Max Drawdown",value:`${t.profit.max_drawdown?z(t.profit.max_drawdown,2):"N/A"} (${t.profit.max_drawdown_abs?G(t.profit.max_drawdown_abs,t.stakeCurrency,t.stakeCurrencyDecimals):"N/A"}) ${t.profit.max_drawdown_start_timestamp&&t.profit.max_drawdown_end_timestamp?"from "+I(t.profit.max_drawdown_start_timestamp)+" to "+I(t.profit.max_drawdown_end_timestamp):""}`}]:[]);return(a,y)=>{const h=J,k=W;return i(),p(k,{class:"text-start",small:"",borderless:"",value:e(l)},{default:r(()=>[o(h,{field:"metric",header:"Metric"}),o(h,{field:"value",header:"Value"})]),_:1},8,["value"])}}}),Yt={key:0,class:"p-4"},te={class:"mb-4"},ee={class:"mb-4"},oe={key:0,class:"mb-4"},ae={class:"mb-4"},ne={class:"mb-4"},re={key:1,class:"mb-4"},ie={key:0,class:"block"},se={class:"block"},le={class:"block"},ce={key:0,class:"block"},de={key:1,class:"block mb-4"},_e=V({__name:"BotStatus",setup(v){const t=O();return(l,a)=>{const y=Rt,h=Wt,k=Xt;return e(t).activeBot.botState?(i(),d("div",Yt,[s("p",te,[a[0]||(a[0]=g(" Running Freqtrade ")),s("strong",null,u(e(t).activeBot.version),1)]),s("p",ee,[a[1]||(a[1]=g(" Running with ")),s("strong",null,u(e(t).activeBot.botState.max_open_trades)+"x"+u(e(t).activeBot.botState.stake_amount)+" "+u(e(t).activeBot.botState.stake_currency),1),a[2]||(a[2]=g(" on ")),s("strong",null,u(e(t).activeBot.botState.exchange),1),a[3]||(a[3]=g(" in ")),s("strong",null,u(e(t).activeBot.botState.trading_mode||"spot"),1),a[4]||(a[4]=g(" markets, with Strategy ")),s("strong",null,u(e(t).activeBot.botState.strategy),1),a[5]||(a[5]=g(". "))]),"stoploss_on_exchange"in e(t).activeBot.botState?(i(),d("p",oe,[a[6]||(a[6]=g(" Stoploss on exchange is ")),s("strong",null,u(e(t).activeBot.botState.stoploss_on_exchange?"enabled":"disabled"),1),a[7]||(a[7]=g(". "))])):x("",!0),s("p",ae,[a[8]||(a[8]=g(" Currently ")),s("strong",null,u(e(t).activeBot.botState.state),1),a[9]||(a[9]=g(", ")),s("strong",null,"force entry: "+u(e(t).activeBot.botState.force_entry_enable),1)]),s("p",null,[s("strong",null,u(e(t).activeBot.botState.dry_run?"Dry-Run":"Live"),1)]),o(y),s("p",ne," Avg Profit "+u(("formatPercent"in l?l.formatPercent:e(z))(e(t).activeBot.profit.profit_all_ratio_mean))+" (∑ "+u(("formatPercent"in l?l.formatPercent:e(z))(e(t).activeBot.profit.profit_all_ratio_sum))+") in "+u(e(t).activeBot.profit.trade_count)+" Trades, with an average duration of "+u(e(t).activeBot.profit.avg_duration)+". Best pair: "+u(e(t).activeBot.profit.best_pair)+". ",1),e(t).activeBot.profit.first_trade_timestamp?(i(),d("p",re,[e(t).activeBot.profit.bot_start_timestamp?(i(),d("span",ie,[a[10]||(a[10]=g(" Bot start date: ")),s("strong",null,[o(h,{date:e(t).activeBot.profit.bot_start_timestamp,"show-timezone":""},null,8,["date"])])])):x("",!0),s("span",se,[a[11]||(a[11]=g(" First trade opened: ")),s("strong",null,[o(h,{date:e(t).activeBot.profit.first_trade_timestamp,"show-timezone":""},null,8,["date"])])]),s("span",le,[a[12]||(a[12]=g(" Last trade opened: ")),s("strong",null,[o(h,{date:e(t).activeBot.profit.latest_trade_timestamp,"show-timezone":""},null,8,["date"])])])])):x("",!0),s("p",null,[e(t).activeBot.profit.profit_factor?(i(),d("span",ce," Profit factor: "+u(e(t).activeBot.profit.profit_factor.toFixed(2)),1)):x("",!0),e(t).activeBot.profit.trading_volume?(i(),d("span",de," Trading volume: "+u(("formatPriceCurrency"in l?l.formatPriceCurrency:e(G))(e(t).activeBot.profit.trading_volume,e(t).activeBot.botState.stake_currency,e(t).activeBot.botState.stake_currency_decimals??3)),1)):x("",!0)]),o(k,{class:"mx-1",profit:e(t).activeBot.profit,"stake-currency":e(t).activeBot.botState.stake_currency??"USDT","stake-currency-decimals":e(t).activeBot.botState.stake_currency_decimals??3},null,8,["profit","stake-currency","stake-currency-decimals"])])):x("",!0)}}}),ue={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"};function me(v,t){return i(),d("svg",ue,t[0]||(t[0]=[s("path",{fill:"currentColor",d:"M10 17c1.1 0 2-.9 2-2s-.9-2-2-2s-2 .9-2 2s.9 2 2 2m6-9c1.1 0 2 .9 2 2v10c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V10c0-1.1.9-2 2-2h1V6c0-2.8 2.2-5 5-5s5 2.2 5 5v2zm-6-5C8.3 3 7 4.3 7 6v2h6V6c0-1.7-1.3-3-3-3m12 10h-2V7h2zm0 4h-2v-2h2z"},null,-1)]))}const pe=T({name:"mdi-lock-alert",render:me}),fe={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"};function ve(v,t){return i(),d("svg",fe,t[0]||(t[0]=[s("path",{fill:"currentColor",d:"M5 5v14h2v2H3V3h4v2zm15 2H7v2h13zm0 4H7v2h13zm0 4H7v2h13z"},null,-1)]))}const ye=T({name:"mdi-format-list-group",render:ve}),ge={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"};function he(v,t){return i(),d("svg",ge,t[0]||(t[0]=[s("path",{fill:"currentColor",d:"M3 4c-1.11 0-2 .89-2 2v12a2 2 0 0 0 2 2h7.26c1.31 1.88 3.45 3 5.74 3a7 7 0 0 0 7-7c0-1.83-.72-3.58-2-4.89V8a2 2 0 0 0-2-2h-8L9 4zm13 7a5 5 0 0 1 5 5a5 5 0 0 1-5 5a5 5 0 0 1-5-5a5 5 0 0 1 5-5m-1 1v5l3.61 2.16l.75-1.22l-2.86-1.69V12z"},null,-1)]))}const Be=T({name:"mdi-folder-clock",render:he}),be={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"};function xe(v,t){return i(),d("svg",be,t[0]||(t[0]=[s("path",{fill:"currentColor",d:"m16 11.78l4.24-7.33l1.73 1l-5.23 9.05l-6.51-3.75L5.46 19H22v2H2V3h2v14.54L9.5 8z"},null,-1)]))}const ke=T({name:"mdi-chart-line",render:xe}),we={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"};function $e(v,t){return i(),d("svg",we,t[0]||(t[0]=[s("path",{fill:"currentColor",d:"M13 9h-2V7h2m0 10h-2v-6h2m-1-9A10 10 0 0 0 2 12a10 10 0 0 0 10 10a10 10 0 0 0 10-10A10 10 0 0 0 12 2"},null,-1)]))}const Se=T({name:"mdi-information",render:$e}),Te={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"};function Ce(v,t){return i(),d("svg",Te,t[0]||(t[0]=[s("path",{fill:"currentColor",d:"M2 12a9 9 0 0 0 9 9c2.39 0 4.68-.94 6.4-2.6l-1.5-1.5A6.7 6.7 0 0 1 11 19c-6.24 0-9.36-7.54-4.95-11.95S18 5.77 18 12h-3l4 4h.1l3.9-4h-3a9 9 0 0 0-18 0"},null,-1)]))}const Le=T({name:"mdi-reload",render:Ce}),Pe={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"};function ze(v,t){return i(),d("svg",Pe,t[0]||(t[0]=[s("path",{fill:"currentColor",d:"M14 19h4V5h-4M6 19h4V5H6z"},null,-1)]))}const Ve=T({name:"mdi-pause",render:ze}),De={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"};function Ae(v,t){return i(),d("svg",De,t[0]||(t[0]=[s("path",{fill:"currentColor",d:"M18 18H6V6h12z"},null,-1)]))}const Me=T({name:"mdi-stop",render:Ae}),Ne={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"};function Re(v,t){return i(),d("svg",Ne,t[0]||(t[0]=[s("path",{fill:"currentColor",d:"M8 5.14v14l11-7z"},null,-1)]))}const Fe=T({name:"mdi-play",render:Re}),Ee={class:"flex flex-row gap-1"},He=V({__name:"BotControls",setup(v){const t=O(),l=q(!1),a=q(),y=b(()=>{var c;return((c=t.activeBot.botState)==null?void 0:c.state)==="running"}),h=()=>{var n;const c={title:"Stop Bot",message:"Stop the bot loop from running?",accept:()=>{t.activeBot.stopBot()}};(n=a.value)==null||n.show(c)},k=()=>{var n;const c={title:"Pause - Stop Entering",message:"Freqtrade will continue to handle open trades, but will not enter new trades or increase position sizes.",accept:()=>{t.activeBot.stopBuy()}};(n=a.value)==null||n.show(c)},w=()=>{var n;const c={title:"Reload",message:"Reload configuration (including strategy)?",accept:()=>{console.log("reload..."),t.activeBot.reloadConfig()}};(n=a.value)==null||n.show(c)},B=()=>{var n;const c={title:"ForceExit all",message:"Really forceexit ALL trades?",accept:()=>{const m={tradeid:"all"};t.activeBot.forceexit(m)}};(n=a.value)==null||n.show(c)};return(c,n)=>{const m=Fe,_=j,S=Me,D=Ve,A=Le,f=Bt,C=bt,U=Ft;return i(),d("div",Ee,[o(_,{size:"large",severity:"secondary",disabled:!e(t).activeBot.isTrading||e(y),title:"Start Trading",onClick:n[0]||(n[0]=$=>e(t).activeBot.startBot())},{icon:r(()=>[o(m)]),_:1},8,["disabled"]),o(_,{size:"large",severity:"secondary",disabled:!e(t).activeBot.isTrading||!e(y),title:"Stop Trading - Also stops handling open trades.",onClick:n[1]||(n[1]=$=>h())},{icon:r(()=>[o(S)]),_:1},8,["disabled"]),o(_,{size:"large",severity:"secondary",disabled:!e(t).activeBot.isTrading||!e(y),title:"Pause (StopBuy) - Freqtrade will continue to handle open trades, but will not enter new trades or increase position sizes.",onClick:n[2]||(n[2]=$=>k())},{icon:r(()=>[o(D)]),_:1},8,["disabled"]),o(_,{size:"large",severity:"secondary",disabled:!e(t).activeBot.isTrading,title:"Reload Config - reloads configuration including strategy, resetting all settings changed on the fly.",onClick:n[3]||(n[3]=$=>w())},{icon:r(()=>[o(A)]),_:1},8,["disabled"]),o(_,{severity:"secondary",size:"large",disabled:!e(t).activeBot.isTrading,title:"Force exit all",onClick:n[4]||(n[4]=$=>B())},{icon:r(()=>[o(f)]),_:1},8,["disabled"]),e(t).activeBot.botState&&e(t).activeBot.botState.force_entry_enable?(i(),p(_,{key:0,size:"large",severity:"secondary",disabled:!e(t).activeBot.isTrading||!e(y),title:"Force enter - Immediately enter a trade at an optional price. Exits are then handled according to strategy rules.",onClick:n[5]||(n[5]=$=>l.value=!0)},{icon:r(()=>[o(C)]),_:1},8,["disabled"])):x("",!0),(e(t).activeBot.isWebserverMode,x("",!0)),o(xt,{modelValue:e(l),"onUpdate:modelValue":n[7]||(n[7]=$=>X(l)?l.value=$:null),pair:e(t).activeBot.selectedPair},null,8,["modelValue","pair"]),o(U,{ref_key:"msgBox",ref:a},null,512)])}}}),Ge={class:"mt-1 flex justify-center"},Oe={title:"Pairs combined"},qe={key:0,class:"ms-1"},Ue={title:"General"},Ie={key:0,class:"ms-1"},je={title:"Performance"},We={key:0,class:"ms-1"},Je={title:"Balance"},Ke={key:0,class:"ms-1"},Ze={title:"Time Breakdown"},Qe={key:0,class:"ms-1"},Xe={title:"Pairlist"},Ye={key:0,class:"ms-1"},to={title:"Pair Locks"},eo={key:0,class:"ms-1"},To=V({__name:"TradingView",setup(v){const t=O(),l=Et(),a=Ht(),y=q(""),h=f=>{y.value=f},k=b(()=>["","sm","md","lg","xl"].includes(y.value)),w=b(()=>l.layoutLocked||!k.value),B=b(()=>k.value?l.tradingLayout:[...l.getTradingLayoutSm]),c=b(()=>F(B.value,E.multiPane)),n=b(()=>F(B.value,E.openTrades)),m=b(()=>F(B.value,E.tradeHistory)),_=b(()=>F(B.value,E.tradeDetail)),S=b(()=>F(B.value,E.chartView)),D=b(()=>({sm:l.getTradingLayoutSm}));function A(f,C){t.activeBot.getPairCandles({pair:f,timeframe:t.activeBot.timeframe,columns:C})}return(f,C)=>{const U=He,$=Gt,L=Tt,Y=Se,tt=ke,et=Ot,ot=Be,at=ye,nt=pe,rt=St,it=Pt,P=Lt,st=_e,lt=Qt,ct=It,dt=Ut,_t=qt,ut=Kt,mt=Ct,pt=$t,M=wt,N=Z("GridItem"),K=kt,ft=ht,vt=gt,yt=Z("GridLayout");return i(),p(yt,{class:"h-full w-full",style:{padding:"1px"},"row-height":50,layout:e(B),"vertical-compact":!1,margin:[1,1],"responsive-layouts":e(D),"is-resizable":!e(w),"is-draggable":!e(w),responsive:!0,cols:{lg:12,md:12,sm:12,xs:4,xxs:2},"col-num":12,"onUpdate:breakpoint":h},{default:r(({gridItemProps:R})=>[e(c).h!=0?(i(),p(N,H({key:0},R,{i:e(c).i,x:e(c).x,y:e(c).y,w:e(c).w,h:e(c).h,"drag-allow-from":".drag-header"}),{default:r(()=>[o(M,{header:"Multi Pane"},{default:r(()=>[s("div",Ge,[o(U,{class:"mt-1 mb-2"})]),o(pt,{value:"0",scrollable:"",lazy:""},{default:r(()=>[o(rt,null,{default:r(()=>[o(L,{value:"0",severity:"secondary"},{default:r(()=>[s("div",Oe,[e(a).multiPaneButtonsShowText?(i(),d("span",qe,"Pairs combined")):(i(),p($,{key:1}))])]),_:1}),o(L,{value:"1",severity:"secondary"},{default:r(()=>[s("div",Ue,[e(a).multiPaneButtonsShowText?(i(),d("span",Ie,"General")):(i(),p(Y,{key:1}))])]),_:1}),o(L,{value:"2",severity:"secondary"},{default:r(()=>[s("div",je,[e(a).multiPaneButtonsShowText?(i(),d("span",We,"Performance")):(i(),p(tt,{key:1}))])]),_:1}),o(L,{value:"3",severity:"secondary"},{default:r(()=>[s("div",Je,[e(a).multiPaneButtonsShowText?(i(),d("span",Ke,"Balance")):(i(),p(et,{key:1}))])]),_:1}),o(L,{value:"4",severity:"secondary"},{default:r(()=>[s("div",Ze,[e(a).multiPaneButtonsShowText?(i(),d("span",Qe,"Time Breakdown")):(i(),p(ot,{key:1}))])]),_:1}),o(L,{value:"5",severity:"secondary"},{default:r(()=>[s("div",Xe,[e(a).multiPaneButtonsShowText?(i(),d("span",Ye,"Pairlist")):(i(),p(at,{key:1}))])]),_:1}),o(L,{value:"6",severity:"secondary"},{default:r(()=>[s("div",to,[e(a).multiPaneButtonsShowText?(i(),d("span",eo,"Pair Locks")):(i(),p(nt,{key:1}))])]),_:1})]),_:1}),o(mt,null,{default:r(()=>[o(P,{value:"0"},{default:r(()=>[o(it,{pairlist:e(t).activeBot.whitelist,"current-locks":e(t).activeBot.activeLocks,trades:e(t).activeBot.openTrades},null,8,["pairlist","current-locks","trades"])]),_:1}),o(P,{value:"1"},{default:r(()=>[o(st)]),_:1}),o(P,{value:"2",lazy:""},{default:r(()=>[o(lt)]),_:1}),o(P,{value:"3",lazy:""},{default:r(()=>[o(ct)]),_:1}),o(P,{value:"4",lazy:""},{default:r(()=>[o(dt)]),_:1}),o(P,{value:"5",lazy:""},{default:r(()=>[o(_t)]),_:1}),o(P,{value:"6",lazy:""},{default:r(()=>[o(ut)]),_:1})]),_:1})]),_:1})]),_:1})]),_:2},1040,["i","x","y","w","h"])):x("",!0),e(n).h!=0?(i(),p(N,H({key:1},R,{i:e(n).i,x:e(n).x,y:e(n).y,w:e(n).w,h:e(n).h,"drag-allow-from":".drag-header"}),{default:r(()=>[o(M,{header:"Open Trades"},{default:r(()=>[o(K,{class:"open-trades",trades:e(t).activeBot.openTrades,title:"Open trades","active-trades":!0,"empty-text":"Currently no open trades."},null,8,["trades"])]),_:1})]),_:2},1040,["i","x","y","w","h"])):x("",!0),e(m).h!=0?(i(),p(N,H({key:2},R,{i:e(m).i,x:e(m).x,y:e(m).y,w:e(m).w,h:e(m).h,"drag-allow-from":".drag-header"}),{default:r(()=>[o(M,{header:"Closed Trades"},{default:r(()=>[o(K,{class:"trade-history",trades:e(t).activeBot.closedTrades,title:"Trade history","show-filter":!0,"empty-text":"No closed trades so far."},null,8,["trades"])]),_:1})]),_:2},1040,["i","x","y","w","h"])):x("",!0),e(t).activeBot.detailTradeId&&e(t).activeBot.tradeDetail&&e(_).h!=0?(i(),p(N,H({key:3},R,{i:e(_).i,x:e(_).x,y:e(_).y,w:e(_).w,h:e(_).h,"min-h":4,"drag-allow-from":".drag-header"}),{default:r(()=>[o(M,{header:"Trade Detail"},{default:r(()=>[o(ft,{trade:e(t).activeBot.tradeDetail,"stake-currency":e(t).activeBot.stakeCurrency},null,8,["trade","stake-currency"])]),_:1})]),_:2},1040,["i","x","y","w","h"])):x("",!0),e(_).h!=0?(i(),p(N,H({key:4},R,{i:e(S).i,x:e(S).x,y:e(S).y,w:e(S).w,h:e(S).h,"min-h":6,"drag-allow-from":".drag-header"}),{default:r(()=>[o(M,{header:"Chart"},{default:r(()=>[o(vt,{"available-pairs":e(t).activeBot.whitelist,"historic-view":!1,timeframe:e(t).activeBot.timeframe,trades:e(t).activeBot.allTrades,onRefreshData:A},null,8,["available-pairs","timeframe","trades"])]),_:1})]),_:2},1040,["i","x","y","w","h"])):x("",!0)]),_:1},8,["layout","responsive-layouts","is-resizable","is-draggable"])}}});export{To as default};
//# sourceMappingURL=TradingView-D3vlBJeO.js.map
