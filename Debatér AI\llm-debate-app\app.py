# app.py
import threading
import os
import time
import asyncio
import json
import sqlite3
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime, timezone

from flask import Flask, render_template, request, jsonify, Response, stream_with_context, redirect, url_for
# Vlastní implementace SSE bez Redis
import queue
import threading
import json
import time
import google.generativeai as genai
from openai import AsyncOpenAI # Budeme používat pro OpenRouter
import tiktoken
from dotenv import load_dotenv

import database

# --- Konfigurace ---
load_dotenv()
database.init_db()

# Vlastní implementace SSE
class SSEManager:
    def __init__(self):
        self.listeners = {}  # channel -> list of queues
        self.lock = threading.Lock()

    def subscribe(self, channel):
        """Vytvoří novou frontu pro kanál a vrátí ji"""
        q = queue.Queue()
        with self.lock:
            if channel not in self.listeners:
                self.listeners[channel] = []
            self.listeners[channel].append(q)
        return q

    def unsubscribe(self, channel, q):
        """Odstraní frontu z kanálu"""
        with self.lock:
            if channel in self.listeners and q in self.listeners[channel]:
                self.listeners[channel].remove(q)
                if not self.listeners[channel]:
                    del self.listeners[channel]

    def publish(self, data, channel):
        """Publikuje data do všech front v kanálu"""
        with self.lock:
            if channel in self.listeners:
                for q in self.listeners[channel]:
                    q.put(data)

# Vytvoření instance SSE manageru
sse_manager = SSEManager()

app = Flask(__name__)
app.secret_key = os.getenv("FLASK_SECRET_KEY", os.urandom(24))

# --- API Klienti a Tokenizer ---
GEMINI_API_KEY = os.getenv("GOOGLE_API_KEY")
OPENROUTER_API_KEY = os.getenv("OPENROUTER_API_KEY")

gemini_client_initialized = False
openrouter_client = None

# Konfigurace Gemini (globální)
if GEMINI_API_KEY:
    try:
        genai.configure(api_key=GEMINI_API_KEY)
        gemini_client_initialized = True
        print("Google Gemini klient inicializován.")
    except Exception as e:
        print(f"Chyba při konfiguraci Google Gemini API: {e}")
else:
    print("Chyba: GOOGLE_API_KEY nebyl nalezen v .env")

# Asynchronní klient pro OpenRouter (používá OpenAI knihovnu)
if OPENROUTER_API_KEY:
    try:
        # Povolení nestandardních atributů pro AsyncOpenAI
        class PatchedAsyncOpenAI(AsyncOpenAI):
            model_config = {'arbitrary_types_allowed': True}

        openrouter_client = PatchedAsyncOpenAI(
            base_url="https://openrouter.ai/api/v1",
            api_key=OPENROUTER_API_KEY,
            default_headers={
                "HTTP-Referer": os.getenv("APP_URL", "http://localhost:5000"), # Přidejte APP_URL do .env pokud je třeba
                "X-Title": "LLM Debate App",
            }
        )
        print("OpenRouter klient inicializován.")
    except Exception as e:
        print(f"Kritická chyba při konfiguraci OpenRouter klienta: {e}")
else:
    print("Chyba: OPENROUTER_API_KEY nebyl nalezen v .env. OpenRouter nebude dostupný.")

# Tokenizer pro odhad (primárně pro OpenAI/OpenRouter formát)
try:
    tokenizer = tiktoken.get_encoding("cl100k_base")
except Exception:
    print("Varování: Nepodařilo se načíst tiktoken tokenizer 'cl100k_base'. Odhad tokenů nebude přesný.")
    tokenizer = None

# Executor pro synchronní Gemini API
executor = ThreadPoolExecutor(max_workers=os.cpu_count() or 4)

# --- Konstanty a Dostupné Modely ---
# Definujeme POUZE požadované modely
AVAILABLE_MODELS = {
    "gemini_exp": ["gemini-2.0-flash-thinking-exp-01-21"],
    "openrouter_free": ["deepseek/deepseek-r1:free"],
}

# Odhady limitů kontextu (ověřte si je, pokud možno!)
MAX_CONTEXT_TOKENS_APPROX = {
    "gemini-2.0-flash-thinking-exp-01-21": 32000, # ODHAD!
    "deepseek/deepseek-r1:free": 128000,        # Ověřeno na OpenRouter
}
DEFAULT_MAX_CONTEXT = 4000 # Fallback

# --- Pomocné funkce ---

def get_token_count(text, model_name):
    """Odhadne počet tokenů."""
    if not text: return 0
    # Pro modely volané přes OpenRouter (OpenAI kompatibilní API)
    if tokenizer and ("deepseek" in model_name or "openai" in model_name): # Rozšířit pokud přidáte další OpenAI-like modely
        try:
            return len(tokenizer.encode(text))
        except Exception:
            return len(text) // 3 # Hrubý odhad
    # Pro Gemini
    elif "gemini" in model_name:
        # Velmi hrubý odhad pro Gemini
        return len(text) // 4
    else:
        return len(text) // 3.5 # Obecný fallback

def prepare_openai_messages(history, model_name):
    """Připraví zprávy pro OpenAI/OpenRouter API a ořízne historii."""
    max_tokens_limit = MAX_CONTEXT_TOKENS_APPROX.get(model_name, DEFAULT_MAX_CONTEXT)
    available_tokens_for_history = max_tokens_limit - 1000 # Rezerva pro odpověď
    if available_tokens_for_history <= 0: available_tokens_for_history = max_tokens_limit // 2

    messages = []
    total_tokens = 0
    added_message_ids = set()

    system_message = next((msg for msg in history if msg['speaker'] == 'System'), None)
    if system_message:
        msg_text = system_message['message_text']
        msg_tokens = get_token_count(msg_text, model_name)
        if msg_tokens < available_tokens_for_history:
             messages.append({"role": "system", "content": msg_text})
             total_tokens += msg_tokens
             added_message_ids.add(system_message['id'])

    for msg in reversed(history):
        if msg['id'] in added_message_ids or msg['speaker'] == 'System': continue

        # Určení role: 'assistant' je model, jehož API voláme, 'user' je ten druhý
        # Předpoklad: Model B je DeepSeek/OpenRouter, Model A je Gemini
        role = "assistant" if msg['speaker'] == "ModelB" else "user"
        msg_text = msg['message_text']
        msg_tokens = get_token_count(msg_text, model_name)

        if total_tokens + msg_tokens < available_tokens_for_history:
            messages.insert(1 if system_message else 0, {"role": role, "content": msg_text})
            total_tokens += msg_tokens
            added_message_ids.add(msg['id'])
        else:
            break

    if not messages and system_message:
        messages.append({"role": "system", "content": system_message['message_text']}) # Alespoň systémová zpráva
    elif not messages:
        messages.append({"role": "user", "content": "Zahaj debatu."}) # Nouzovka

    #print(f"OpenRouter context ({model_name}): {len(messages)} messages, {total_tokens} tokens.")
    return messages

def prepare_gemini_history(history, model_name):
    """Připraví historii pro Gemini API a ořízne."""
    max_tokens_limit = MAX_CONTEXT_TOKENS_APPROX.get(model_name, DEFAULT_MAX_CONTEXT)
    available_tokens_for_history = max_tokens_limit - 1000
    if available_tokens_for_history <= 0: available_tokens_for_history = max_tokens_limit // 2

    gemini_history = []
    total_tokens = 0
    last_role = None

    for msg in reversed(history):
        if msg['speaker'] == 'System': continue

        # Role se musí střídat: 'model' (Gemini), 'user' (DeepSeek)
        # Předpoklad: Model A je Gemini, Model B je DeepSeek
        current_role = "model" if msg['speaker'] == "ModelA" else "user"
        if current_role == last_role:
            continue # Přeskakujeme pro zachování střídání

        msg_text = msg['message_text']
        msg_tokens = get_token_count(msg_text, model_name)

        if total_tokens + msg_tokens < available_tokens_for_history:
            gemini_history.insert(0, {"role": current_role, "parts": [msg_text]})
            total_tokens += msg_tokens
            last_role = current_role
        else:
            break

    if gemini_history and gemini_history[0]["role"] == "model":
        gemini_history.pop(0)

    #print(f"Gemini context ({model_name}): {len(gemini_history)} messages, {total_tokens} tokens.")
    return gemini_history

async def run_gemini_async(model_name, prompt_or_history, config):
    """Asynchronní wrapper pro synchronní Gemini API."""
    if not gemini_client_initialized:
        return "[Chyba: Gemini klient není inicializován]"

    loop = asyncio.get_event_loop()

    def generate():
        try:
            gemini_model = genai.GenerativeModel(model_name)
            generation_config = genai.types.GenerationConfig(
                temperature=config['temperature'],
                max_output_tokens=config.get('max_tokens') if config.get('max_tokens') else None
            )
            # Bezpečnostní filtry můžeme uvolnit, pokud je třeba (opatrně!)
            safety_settings = [
                {"category": category, "threshold": "BLOCK_NONE"}
                for category in ["HARM_CATEGORY_HARASSMENT", "HARM_CATEGORY_HATE_SPEECH",
                                 "HARM_CATEGORY_SEXUALLY_EXPLICIT", "HARM_CATEGORY_DANGEROUS_CONTENT"]
            ]

            # Rozlišení, zda voláme s historií nebo jen s promptem (např. pro shrnutí)
            if isinstance(prompt_or_history, str):
                # Jednoduché volání generate_content
                response = gemini_model.generate_content(
                    prompt_or_history,
                    generation_config=generation_config,
                    safety_settings=safety_settings
                )
            else: # Předpokládáme, že je to seznam historie zpráv
                 # Použití chatu s historií
                gemini_history_prepared = prepare_gemini_history(prompt_or_history, model_name)
                chat = gemini_model.start_chat(history=gemini_history_prepared)
                # Poslední zpráva z historie (od oponenta) by měla být v promptu
                last_opponent_message = next((msg['message_text'] for msg in reversed(prompt_or_history) if msg['speaker'] != 'ModelA'), "...")
                prompt_for_chat = f"Reaguj na: '{last_opponent_message}'" # Jednoduchý prompt pro chat
                response = chat.send_message(
                    prompt_for_chat,
                    generation_config=generation_config,
                    safety_settings=safety_settings
                )

            if not response.parts:
                 block_reason = response.prompt_feedback.block_reason if response.prompt_feedback else "Neznámý"
                 print(f"Varování: Odpověď Gemini ({model_name}) byla blokována, důvod: {block_reason}")
                 return f"[Odpověď blokována filtrem: {block_reason}]"

            return response.text
        except Exception as e:
            print(f"Chyba v Gemini API volání ({model_name}): {e}")
            return f"[Chyba Gemini API: {e}]"

    response_text = await loop.run_in_executor(executor, generate)
    return response_text

async def run_openrouter_async(model_name, history_or_prompt, config):
    """Volá OpenRouter API (OpenAI compatible)."""
    if not openrouter_client:
        return "[Chyba: OpenRouter klient není inicializován]"
    try:
        if isinstance(history_or_prompt, str):
            messages = [{"role": "user", "content": history_or_prompt}]
            #print(f"OpenRouter ({model_name}) calling with direct prompt.")
        else:
            messages = prepare_openai_messages(history_or_prompt, model_name)
            #print(f"OpenRouter ({model_name}) calling with message count: {len(messages)}")

        completion = await openrouter_client.chat.completions.create(
            model=model_name, # např. "deepseek/deepseek-r1:free"
            messages=messages,
            temperature=config['temperature'],
            max_tokens=config.get('max_tokens') if config.get('max_tokens') else None
        )
        return completion.choices[0].message.content
    except Exception as e:
        print(f"Chyba v OpenRouter API volání ({model_name}): {e}")
        return f"[Chyba OpenRouter API ({model_name}): {e}]"

def get_api_runner(model_name):
    """Vrátí správnou asynchronní funkci pro volání API daného modelu."""
    if model_name in AVAILABLE_MODELS.get("gemini_exp", []):
        return run_gemini_async
    elif model_name in AVAILABLE_MODELS.get("openrouter_free", []):
        return run_openrouter_async
    else:
        print(f"Varování: Není definován API runner pro model {model_name}")
        return None

# --- Funkce pro DB a SSE (zůstávají stejné jako v předchozí verzi) ---

def save_message(debate_id, speaker, text, model_name=None):
    conn = database.get_db()
    cursor = conn.cursor()
    timestamp_utc = datetime.now(timezone.utc)
    token_count = get_token_count(text, model_name) if model_name else None
    try:
        cursor.execute(
            "INSERT INTO messages (debate_id, speaker, message_text, token_count, model_used, timestamp) VALUES (?, ?, ?, ?, ?, ?)",
            (debate_id, speaker, text, token_count, model_name, timestamp_utc)
        )
        conn.commit()
        message_id = cursor.lastrowid
        cursor.execute("SELECT timestamp FROM messages WHERE id = ?", (message_id,))
        saved_timestamp_str = cursor.fetchone()['timestamp']
        saved_timestamp = datetime.fromisoformat(saved_timestamp_str.replace(" ", "T"))
    except sqlite3.Error as e:
        print(f"Chyba při ukládání zprávy do DB: {e}")
        conn.rollback()
        return None, None
    finally:
        conn.close()
    return message_id, saved_timestamp

def update_debate_status(debate_id, status, end_time_utc=None):
     conn = database.get_db()
     cursor = conn.cursor()
     try:
         if end_time_utc:
             cursor.execute("UPDATE debates SET status = ?, end_time = ? WHERE id = ?", (status, end_time_utc, debate_id))
         else:
             cursor.execute("UPDATE debates SET status = ? WHERE id = ?", (status, debate_id))
         conn.commit()
     except sqlite3.Error as e:
         print(f"Chyba při aktualizaci stavu debaty {debate_id}: {e}")
         conn.rollback()
     finally:
        conn.close()

def save_summary(debate_id, model_used, summary_text):
    conn = database.get_db()
    cursor = conn.cursor()
    timestamp_utc = datetime.now(timezone.utc)
    try:
        cursor.execute(
            "INSERT INTO summaries (debate_id, model_used, summary_text, timestamp) VALUES (?, ?, ?, ?)",
            (debate_id, model_used, summary_text, timestamp_utc)
        )
        conn.commit()
    except sqlite3.Error as e:
         print(f"Chyba při ukládání shrnutí ({model_used}) pro debatu {debate_id}: {e}")
         conn.rollback()
    finally:
        conn.close()

def stream_event(debate_id, event_type, data):
    """Odešle událost přes SSE na kanál specifický pro debatu."""
    channel = f"debate-{debate_id}"
    # ----> DEBUG: Logování pokusu o odeslání <----
    print(f"--- DEBUG SSE: Pokus o odeslání na kanál '{channel}', typ '{event_type}', data: {str(data)[:200]}...") # Logujeme jen část dat pro přehlednost
    try:
        # Publikujeme data do kanálu
        event_data = {"event": event_type, "data": data}
        sse_manager.publish(event_data, channel)
        # ----> DEBUG: Logování úspěšného odeslání <----
        print(f"--- DEBUG SSE: Odesláno OK na kanál '{channel}', typ '{event_type}'.")
    except Exception as e:
        # Logování chyby je důležité, ale nemusí běžet v kontextu
        # ----> DEBUG: Logování CHYBY při odesílání <----
        print(f"--- DEBUG SSE: CHYBA při odesílání na kanál '{channel}', typ '{event_type}': {e}")

# --- Hlavní smyčka debaty ---

async def run_debate_async(debate_id, topic, duration_minutes, model_a_name, model_b_name, config_a, config_b):
    start_time_mono = time.monotonic()
    end_time_mono = start_time_mono + duration_minutes * 60
    history = [] # Lokální historie pro tuto debatu

    # Získání správných API runnerů
    model_a_runner = get_api_runner(model_a_name)
    model_b_runner = get_api_runner(model_b_name)

    if not model_a_runner or not model_b_runner:
         error_msg = "Chyba: Nepodařilo se najít API runner pro jeden nebo oba vybrané modely."
         print(error_msg)
         update_debate_status(debate_id, "error", datetime.now(timezone.utc))
         stream_event(debate_id, "error", {"message": error_msg})
         stream_event(debate_id, "end", {"message": "Debata selhala při inicializaci."})
         return

    # Mapování pro snazší přístup
    model_map = {"ModelA": model_a_name, "ModelB": model_b_name}
    config_map = {"ModelA": config_a, "ModelB": config_b}
    runner_map = {"ModelA": model_a_runner, "ModelB": model_b_runner}

    try:
        print(f"Debata {debate_id}: Začíná ({topic}). A={model_a_name}, B={model_b_name}")
        update_debate_status(debate_id, "running")
        stream_event(debate_id, "status", {"message": "Debata začíná..."})

        # Systémová zpráva
        system_prompt_text = f"Probíhá debata na téma '{topic}'. Model A ({model_a_name}) a Model B ({model_b_name}) si vyměňují argumenty. Model A začíná."
        msg_id, ts = save_message(debate_id, "System", system_prompt_text)
        if msg_id:
            history.append({"id": msg_id, "speaker": "System", "message_text": system_prompt_text, "timestamp": ts, "model_used": None})
            stream_event(debate_id, "system_message", {"message": system_prompt_text})
        else:
            raise Exception("Nepodařilo se uložit systémovou zprávu.")

        # První tah - Model A
        current_speaker = "ModelA"
        prompt_a = f"Jsi Model A ({model_map[current_speaker]}). Zahaj debatu na téma '{topic}'. Představ svůj úvodní postoj (max 2-3 věty)."
        stream_event(debate_id, "status", {"speaker": current_speaker, "message": f"({model_map[current_speaker]}) přemýšlí..."})

        # Voláme runner pro Model A
        # Gemini runner může potřebovat historii pro `start_chat`
        # OpenRouter runner také může vzít historii
        response_text = await runner_map[current_speaker](model_map[current_speaker], history + [{"id": -1, "speaker":"System", "message_text": prompt_a, "timestamp": datetime.now(timezone.utc), "model_used": None}], config_map[current_speaker]) # Posíláme historii i s úvodním promptem

        msg_id, ts = save_message(debate_id, current_speaker, response_text, model_map[current_speaker])
        if msg_id:
            history.append({"id": msg_id, "speaker": current_speaker, "message_text": response_text, "timestamp": ts, "model_used": model_map[current_speaker]})
            stream_event(debate_id, "message", {"speaker": current_speaker, "model": model_map[current_speaker], "message": response_text})

        # Hlavní smyčka
        while time.monotonic() < end_time_mono:
            last_speaker = current_speaker
            current_speaker = "ModelB" if last_speaker == "ModelA" else "ModelA"
            await asyncio.sleep(1.5)

            stream_event(debate_id, "status", {"speaker": current_speaker, "message": f"({model_map[current_speaker]}) přemýšlí..."})

            # Volání API pro aktuálního řečníka - předáváme aktuální historii
            response_text = await runner_map[current_speaker](model_map[current_speaker], history, config_map[current_speaker])

            msg_id, ts = save_message(debate_id, current_speaker, response_text, model_map[current_speaker])
            if msg_id:
                history.append({"id": msg_id, "speaker": current_speaker, "message_text": response_text, "timestamp": ts, "model_used": model_map[current_speaker]})
                stream_event(debate_id, "message", {"speaker": current_speaker, "model": model_map[current_speaker], "message": response_text})

        # Konec debaty - generování shrnutí
        print(f"Debata {debate_id}: Čas vypršel. Generuji shrnutí.")
        stream_event(debate_id, "status", {"speaker": "System", "message": "Generuji závěrečná shrnutí..."})

        # Získání finální historie z DB
        conn = database.get_db()
        cursor = conn.cursor()
        cursor.execute("SELECT speaker, model_used, message_text FROM messages WHERE debate_id = ? AND speaker != 'System' ORDER BY timestamp ASC", (debate_id,))
        final_history_rows = cursor.fetchall()
        conn.close()
        debate_log_text = "\n".join([f"{row['speaker']} ({row['model_used'] or 'N/A'}): {row['message_text']}" for row in final_history_rows])

        summary_prompt_template = "Stručně (max 100 slov) shrň hlavní body debaty na téma '{topic}'.\n\nDebata:\n{debate_log}"
        summary_prompt_filled = summary_prompt_template.format(topic=topic, debate_log=debate_log_text)
        summary_config = {'temperature': 0.4}

        summary_tasks = []
        # Použijeme runnery pro generování shrnutí - předáváme jen prompt string
        summary_tasks.append(runner_map["ModelA"](model_map["ModelA"], summary_prompt_filled, summary_config))
        summary_tasks.append(runner_map["ModelB"](model_map["ModelB"], summary_prompt_filled, summary_config))

        try:
            summaries = await asyncio.gather(*summary_tasks, return_exceptions=True)
            summary_a = summaries[0] if not isinstance(summaries[0], Exception) else f"[Chyba shrnutí ({model_map['ModelA']}): {summaries[0]}]"
            summary_b = summaries[1] if not isinstance(summaries[1], Exception) else f"[Chyba shrnutí ({model_map['ModelB']}): {summaries[1]}]"

            save_summary(debate_id, model_map["ModelA"], summary_a)
            save_summary(debate_id, model_map["ModelB"], summary_b)

            stream_event(debate_id, "summary", {"model": model_map["ModelA"], "summary": summary_a})
            stream_event(debate_id, "summary", {"model": model_map["ModelB"], "summary": summary_b})

        except Exception as e:
            print(f"Chyba při generování shrnutí pro debatu {debate_id}: {e}")
            stream_event(debate_id, "error", {"message": f"Nepodařilo se vygenerovat shrnutí: {e}"})

        final_end_time_utc = datetime.now(timezone.utc)
        update_debate_status(debate_id, "completed", final_end_time_utc)
        stream_event(debate_id, "end", {"message": "Debata byla dokončena."})
        print(f"Debata {debate_id}: Dokončena.")

    except asyncio.CancelledError:
         print(f"Debata {debate_id}: Zrušena.")
         update_debate_status(debate_id, "cancelled", datetime.now(timezone.utc))
         stream_event(debate_id, "error", {"message": "Debata byla zrušena."})
         stream_event(debate_id, "end", {"message": "Debata byla zrušena."})
    except Exception as e:
        print(f"Neočekávaná chyba během debaty {debate_id}: {e}")
        import traceback
        traceback.print_exc()
        error_message = f"Kritická chyba v debatě: {e}"
        update_debate_status(debate_id, "error", datetime.now(timezone.utc))
        try:
            stream_event(debate_id, "error", {"message": error_message})
            stream_event(debate_id, "end", {"message": "Debata přerušena chybou."})
        except Exception as send_err:
            print(f"Nepodařilo se odeslat chybovou zprávu: {send_err}")

# --- Flask Routes ---

@app.route('/stream')
def stream():
    """SSE endpoint pro streamování událostí"""
    channel = request.args.get('channel', 'default')

    def event_stream():
        """Generátor pro SSE stream"""
        # Vytvoříme frontu pro tento kanál
        q = sse_manager.subscribe(channel)

        # Odešleme inicializační zprávu
        yield "data: {\"event\": \"connected\", \"data\": {\"channel\": \"" + channel + "\"}}\n\n"

        try:
            while True:
                # Čekáme na data z fronty
                try:
                    data = q.get(timeout=30)  # 30 sekund timeout
                    # Formátujeme data pro SSE - bez typu události (používáme výchozí 'message')
                    yield f"data: {json.dumps(data)}\n\n"
                except queue.Empty:
                    # Posíláme keep-alive zprávu
                    yield ": keep-alive\n\n"
        finally:
            # Odhlásíme frontu při ukončení spojení
            sse_manager.unsubscribe(channel, q)

    # Nastavíme hlavičky pro SSE
    return Response(
        event_stream(),
        mimetype='text/event-stream',
        headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'X-Accel-Buffering': 'no'  # Pro Nginx
        }
    )

@app.route('/', methods=['GET'])
def index():
    conn = database.get_db()
    cursor = conn.cursor()
    try:
        cursor.execute("SELECT id, topic, start_time, status FROM debates ORDER BY start_time DESC LIMIT 20")
    except sqlite3.OperationalError:
        # Fallback if status column doesn't exist
        cursor.execute("SELECT id, topic, start_time FROM debates ORDER BY start_time DESC LIMIT 20")
    past_debates_rows = cursor.fetchall()
    conn.close()
    past_debates = []
    for row in past_debates_rows:
        debate_data = dict(row) # Převedeme řádek na slovník
        # Add status if it doesn't exist in the database
        if 'status' not in debate_data:
            debate_data['status'] = 'unknown'
        # ----> Převod start_time ze stringu na datetime <----
        if debate_data['start_time']:
            try:
                # SQLite vrací TIMESTAMP jako string, převedeme ho
                # Formát může být s 'T' nebo bez, zkusíme oba běžné ISO formáty
                try:
                    debate_data['start_time'] = datetime.fromisoformat(debate_data['start_time'].replace(" ", "T"))
                except ValueError:
                     # Pokud první formát selže, zkusíme starší formát bez T (méně časté u nových SQLite)
                     debate_data['start_time'] = datetime.strptime(debate_data['start_time'], '%Y-%m-%d %H:%M:%S')
            except (ValueError, TypeError) as e:
                 print(f"Varování: Nepodařilo se převést start_time '{debate_data['start_time']}' na datetime: {e}")
                 debate_data['start_time'] = None # Nastavíme na None, pokud převod selže
        else:
             debate_data['start_time'] = None # Pokud je v DB NULL

        past_debates.append(debate_data)
    # ----> Konec úpravy <----

    return render_template('index.html', models=AVAILABLE_MODELS, past_debates=past_debates)

@app.route('/start-debate', methods=['POST'])
def start_debate():
    try:
        topic = request.form.get('topic', '').strip()
        duration_minutes = int(request.form.get('duration', 5))
        model_a = request.form.get('model_a')
        model_b = request.form.get('model_b')
        temp_a = float(request.form.get('temperature_a', 0.7))
        temp_b = float(request.form.get('temperature_b', 0.7))
        max_tokens_a_str = request.form.get('max_tokens_a', '').strip()
        max_tokens_b_str = request.form.get('max_tokens_b', '').strip()
        max_tokens_a = int(max_tokens_a_str) if max_tokens_a_str else None
        max_tokens_b = int(max_tokens_b_str) if max_tokens_b_str else None

        # Validace - zkontrolujeme, zda vybrané modely jsou v našem povoleném seznamu
        allowed_models = AVAILABLE_MODELS["gemini_exp"] + AVAILABLE_MODELS["openrouter_free"]
        if not topic or model_a not in allowed_models or model_b not in allowed_models or \
           duration_minutes < 1 or duration_minutes > 120 or \
           temp_a < 0 or temp_a > 2 or temp_b < 0 or temp_b > 2 or \
           (max_tokens_a is not None and max_tokens_a < 10) or \
           (max_tokens_b is not None and max_tokens_b < 10):
             return jsonify({"error": "Neplatné vstupní parametry nebo nepovolený model."}), 400

        config_a = {'temperature': temp_a}
        if max_tokens_a: config_a['max_tokens'] = max_tokens_a
        config_b = {'temperature': temp_b}
        if max_tokens_b: config_b['max_tokens'] = max_tokens_b

        conn = database.get_db()
        cursor = conn.cursor()
        start_time_utc = datetime.now(timezone.utc)
        cursor.execute(
            """INSERT INTO debates (topic, start_time, model_a, model_b, temperature_a, temperature_b, max_tokens_a, max_tokens_b, status)
               VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)""",
            (topic, start_time_utc, model_a, model_b, temp_a, temp_b, max_tokens_a, max_tokens_b, 'starting')
        )
        debate_id = cursor.lastrowid
        conn.commit()
        conn.close()

        # Spuštění debaty na pozadí v samostatném vlákně
        import threading
        debate_args = (debate_id, topic, duration_minutes, model_a, model_b, config_a, config_b)
        thread = threading.Thread(target=lambda: asyncio.run(run_debate_async(*debate_args)), daemon=True)
        thread.start()

        print(f"Spouštím debatu ID: {debate_id} na téma: '{topic}'")
        return jsonify({"message": "Debata spuštěna", "debate_id": debate_id})

    except ValueError as e:
         print(f"Chyba zpracování formuláře: {e}")
         return jsonify({"error": f"Neplatná hodnota ve formuláři: {e}"}), 400
    except sqlite3.Error as e:
         print(f"Chyba databáze při startu debaty: {e}")
         return jsonify({"error": "Chyba při vytváření záznamu debaty v databázi."}), 500
    except Exception as e:
        print(f"Neočekávaná chyba při startu debaty: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({"error": "Nastala neočekávaná chyba serveru."}), 500

@app.route('/debate/<int:debate_id>')
def debate_page(debate_id):
    conn = database.get_db()
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM debates WHERE id = ?", (debate_id,))
    debate_info_row = cursor.fetchone()

    if not debate_info_row:
        conn.close(); return "Debata nenalezena", 404

    cursor.execute("SELECT * FROM messages WHERE debate_id = ? ORDER BY timestamp ASC", (debate_id,))
    messages_rows = cursor.fetchall()
    cursor.execute("SELECT * FROM summaries WHERE debate_id = ? ORDER BY timestamp ASC", (debate_id,))
    summaries_rows = cursor.fetchall()
    conn.close()

    debate_info = dict(debate_info_row)
    initial_messages = [dict(row) for row in messages_rows]
    initial_summaries = [dict(row) for row in summaries_rows]

    return render_template('debate_view.html',
                           debate=debate_info,
                           initial_messages=initial_messages,
                           initial_summaries=initial_summaries)

@app.route('/delete-debate/<int:debate_id>', methods=['POST'])
def delete_debate(debate_id):
    """Endpoint pro odstranění debaty z databáze."""
    try:
        conn = database.get_db()
        cursor = conn.cursor()

        # Nejprve zkontrolujeme, zda debata existuje
        cursor.execute("SELECT id, status FROM debates WHERE id = ?", (debate_id,))
        debate = cursor.fetchone()

        if not debate:
            conn.close()
            return jsonify({"error": "Debata nenalezena"}), 404

        # Odstraníme debatu a všechny související záznamy (zprávy, shrnutí)
        # Díky ON DELETE CASCADE v databázovém schématu se automaticky odstraní i související záznamy
        cursor.execute("DELETE FROM debates WHERE id = ?", (debate_id,))
        conn.commit()
        conn.close()

        return jsonify({"success": True, "message": f"Debata {debate_id} byla odstraněna"})

    except sqlite3.Error as e:
        print(f"Chyba databáze při odstraňování debaty {debate_id}: {e}")
        if conn:
            conn.rollback()
            conn.close()
        return jsonify({"error": f"Chyba při odstraňování debaty: {e}"}), 500
    except Exception as e:
        print(f"Neočekávaná chyba při odstraňování debaty {debate_id}: {e}")
        import traceback
        traceback.print_exc()
        if conn:
            conn.close()
        return jsonify({"error": "Nastala neočekávaná chyba serveru."}), 500

# --- Spuštění aplikace ---
if __name__ == '__main__':
    print("Spouštím Flask aplikaci...")
    # Potřebuje běžící Redis server
    app.run(host='0.0.0.0', port=5000, debug=True, threaded=True) # Debug=True pro vývoj
    # Pro produkci použijte ASGI server (např. hypercorn app:app)
