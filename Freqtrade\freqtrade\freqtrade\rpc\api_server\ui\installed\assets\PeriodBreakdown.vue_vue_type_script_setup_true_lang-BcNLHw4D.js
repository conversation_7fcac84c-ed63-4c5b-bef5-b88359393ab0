import{d as ul,j as oe,K as vl,a_ as Rv,r as cl,l as $r,k as $e,a as Te,f as it,t as Vv,u as Nv,b4 as we,o as Gv,c as Da,e as Ca,b as ne,z as Er,g as kv,h as er,y as zv,x as La,D as Ei,G as Ov}from"./index-jan7QZNA.js";import{s as Bv,a as Fv}from"./index-BliD00yU.js";import{w as Ri,_ as G,f as Hv,m as hn,g as fl,h as gr,j as P,B as dt,k as hl,l as pl,n as O,o as Pt,p as Wv,q as Y,P as Jt,r as ke,s as Yt,G as Z,t as Re,v as ct,x as j,y as Mt,Z as ge,z as H,A as Zt,C as Bt,D as Et,F as ft,H as gt,I as Je,J as dl,K as Ve,L as lt,M as ha,N as Uv,O as Ft,S as xt,Q as Qt,R as et,T as st,U as Rt,V as ye,W as me,X as Ne,Y as Tr,$ as gl,a0 as kt,a1 as ze,a2 as z,a3 as Vi,a4 as Xt,u as $,a5 as Tt,a6 as It,a7 as mt,a8 as yl,a9 as $v,aa as Yv,ab as se,ac as Zv,ad as ml,ae as Xv,af as On,ag as Sl,ah as yr,ai as Ir,aj as K,ak as Zr,al as qv,am as re,an as xl,ao as mr,ap as qe,aq as Bn,ar as ht,as as jv,at as Kv,au as Dr,av as bl,aw as St,ax as wl,ay as sr,az as Jv,aA as wt,aB as ce,aC as Qe,aD as Fn,aE as zt,aF as Se,aG as Hn,aH as Qv,aI as tc,aJ as ec,aK as pa,aL as Xr,aM as rc,aN as qr,aO as ac,aP as Vt,aQ as da,aR as Wn,aS as te,aT as xe,aU as je,aV as pn,aW as Ot,aX as nc,aY as ic,aZ as qt,a_ as $t,a$ as Ke,b0 as _l,b1 as Ni,b2 as Rr,b3 as Gi,b4 as Al,b5 as oc,b6 as Tl,b7 as dn,b8 as ki,b9 as Cr,ba as fe,bb as Un,bc as sc,bd as lc,be as lr,bf as zi,bg as gn,bh as uc,bi as Gt,bj as vc,bk as Il,bl as cc,bm as Dl,bn as fc,bo as hc,bp as Ge,bq as Cl,br as Oi,bs as pc,bt as dc,bu as gc,bv as le,bw as yc,bx as mc,by as ga,bz as Ll,bA as Sc,bB as $n,bC as jr,bD as Pl,bE as Kr,bF as Yn,bG as Ml,bH as El,bI as yn,bJ as Pa,bK as Rl,bL as ot,bM as Vl,bN as xc,bO as ur,bP as bc,bQ as Bi,bR as wc,bS as _c,bT as Fi,bU as Nl,bV as Ac,bW as Tc,bX as Ic,bY as Dc,bZ as Cc,b_ as Hi,b$ as Lc,c0 as Vr,c1 as Pc,c2 as Mc,c3 as Ec,c4 as Wi,c5 as Rc,c6 as Vc,c7 as Nc,c8 as Gc,c9 as Gl,ca as kc,cb as kl,cc as Ui,cd as zl,ce as zc,cf as $i,cg as Lr,ch as Oc,e as Zn,ci as Bc,cj as Fc,ck as Hc,cl as Wc,cm as Nr,cn as Ol,co as Yi,cp as Uc,cq as Bl,cr as $c,cs as Yc,ct as Zc,cu as Xc,cv as ue,cw as Sr,cx as qc,cy as Zi,cz as jc,cA as Kc,cB as Jc,cC as Fl,cD as Hl,cE as Wl,cF as Qc,cG as ya,cH as tf,cI as ef,cJ as rf,cK as af,cL as nf,cM as of,cN as sf,cO as lf,cP as uf,cQ as vf,cR as cf,cS as ff,cT as hf,cU as Ul,cV as Xn,cW as pf,cX as df,cY as gf,cZ as Xi,c_ as yf,c$ as Jr,d0 as qn,d1 as mf,d2 as jn,d3 as Ma,d4 as Sf,i as $l,d as Yl,c as Zl,b as Xl,a as ql,d5 as xf,E as bf}from"./installCanvasRenderer-DkI93HUo.js";import{A as ma,p as wf,q as ae,r as _f,R as Sa,s as Kn,S as jl,V as Kl,t as Jn,L as Jl,u as Pr,v as qi,w as Af,B as Ql,x as Tf,y as If,z as Df,C as Qr,W as Cf,D as tu,E as eu,F as Qn,G as ti,H as mn,I as ji,J as Lf,K as ei,M as Ki,N as Pf,O as Mf,P as Ef,Q as Rf,T as Vf,U as Nf,X as Gf,Y as kf,Z as zf,_ as Of,$ as Bf,a0 as Ff,a1 as Hf,a2 as xa,a3 as ru,a4 as Sn,a5 as xn,a6 as Wf,a7 as au,a8 as Uf,i as nu,m as iu,n as $f,l as Yf,b as ou,a as Zf,o as Xf,h as qf,g as jf,k as Kf,c as Jf,a9 as Qf,aa as th,f as su,ab as eh,j as rh}from"./chartZoom-Dvf7f-_l.js";import{L as ba,d as wa,s as ah,g as nh,i as ih,a as oh}from"./install-BAjuFKaO.js";import{s as sh}from"./index-1hBUUM27.js";var lh=1e-8;function Ji(r,e){return Math.abs(r-e)<lh}function Me(r,e,t){var a=0,n=r[0];if(!n)return!1;for(var i=1;i<r.length;i++){var o=r[i];a+=Ri(n[0],n[1],o[0],o[1],e,t),n=o}var s=r[0];return(!Ji(n[0],s[0])||!Ji(n[1],s[1]))&&(a+=Ri(n[0],n[1],s[0],s[1],e,t)),a!==0}var uh=[];function Ea(r,e){for(var t=0;t<r.length;t++)gr(r[t],r[t],e)}function Qi(r,e,t,a){for(var n=0;n<r.length;n++){var i=r[n];a&&(i=a.project(i)),i&&isFinite(i[0])&&isFinite(i[1])&&(hl(e,e,i),pl(t,t,i))}}function vh(r){for(var e=0,t=0,a=0,n=r.length,i=r[n-1][0],o=r[n-1][1],s=0;s<n;s++){var l=r[s][0],u=r[s][1],v=i*u-l*o;e+=v,t+=(i+l)*v,a+=(o+u)*v,i=l,o=u}return e?[t/e/3,a/e/3,e]:[r[0][0]||0,r[0][1]||0]}var lu=function(){function r(e){this.name=e}return r.prototype.setCenter=function(e){this._center=e},r.prototype.getCenter=function(){var e=this._center;return e||(e=this._center=this.calcCenter()),e},r}(),to=function(){function r(e,t){this.type="polygon",this.exterior=e,this.interiors=t}return r}(),eo=function(){function r(e){this.type="linestring",this.points=e}return r}(),uu=function(r){G(e,r);function e(t,a,n){var i=r.call(this,t)||this;return i.type="geoJSON",i.geometries=a,i._center=n&&[n[0],n[1]],i}return e.prototype.calcCenter=function(){for(var t=this.geometries,a,n=0,i=0;i<t.length;i++){var o=t[i],s=o.exterior,l=s&&s.length;l>n&&(a=o,n=l)}if(a)return vh(a.exterior);var u=this.getBoundingRect();return[u.x+u.width/2,u.y+u.height/2]},e.prototype.getBoundingRect=function(t){var a=this._rect;if(a&&!t)return a;var n=[1/0,1/0],i=[-1/0,-1/0],o=this.geometries;return P(o,function(s){s.type==="polygon"?Qi(s.exterior,n,i,t):P(s.points,function(l){Qi(l,n,i,t)})}),isFinite(n[0])&&isFinite(n[1])&&isFinite(i[0])&&isFinite(i[1])||(n[0]=n[1]=i[0]=i[1]=0),a=new dt(n[0],n[1],i[0]-n[0],i[1]-n[1]),t||(this._rect=a),a},e.prototype.contain=function(t){var a=this.getBoundingRect(),n=this.geometries;if(!a.contain(t[0],t[1]))return!1;t:for(var i=0,o=n.length;i<o;i++){var s=n[i];if(s.type==="polygon"){var l=s.exterior,u=s.interiors;if(Me(l,t[0],t[1])){for(var v=0;v<(u?u.length:0);v++)if(Me(u[v],t[0],t[1]))continue t;return!0}}}return!1},e.prototype.transformTo=function(t,a,n,i){var o=this.getBoundingRect(),s=o.width/o.height;n?i||(i=n/s):n=s*i;for(var l=new dt(t,a,n,i),u=o.calculateTransform(l),v=this.geometries,c=0;c<v.length;c++){var f=v[c];f.type==="polygon"?(Ea(f.exterior,u),P(f.interiors,function(h){Ea(h,u)})):P(f.points,function(h){Ea(h,u)})}o=this._rect,o.copy(l),this._center=[o.x+o.width/2,o.y+o.height/2]},e.prototype.cloneShallow=function(t){t==null&&(t=this.name);var a=new e(t,this.geometries,this._center);return a._rect=this._rect,a.transformTo=null,a},e}(lu),ch=function(r){G(e,r);function e(t,a){var n=r.call(this,t)||this;return n.type="geoSVG",n._elOnlyForCalculate=a,n}return e.prototype.calcCenter=function(){for(var t=this._elOnlyForCalculate,a=t.getBoundingRect(),n=[a.x+a.width/2,a.y+a.height/2],i=Hv(uh),o=t;o&&!o.isGeoSVGGraphicRoot;)hn(i,o.getLocalTransform(),i),o=o.parent;return fl(i,i),gr(n,n,i),n},e}(lu);function fh(r){if(!r.UTF8Encoding)return r;var e=r,t=e.UTF8Scale;t==null&&(t=1024);var a=e.features;return P(a,function(n){var i=n.geometry,o=i.encodeOffsets,s=i.coordinates;if(o)switch(i.type){case"LineString":i.coordinates=vu(s,o,t);break;case"Polygon":Ra(s,o,t);break;case"MultiLineString":Ra(s,o,t);break;case"MultiPolygon":P(s,function(l,u){return Ra(l,o[u],t)})}}),e.UTF8Encoding=!1,e}function Ra(r,e,t){for(var a=0;a<r.length;a++)r[a]=vu(r[a],e[a],t)}function vu(r,e,t){for(var a=[],n=e[0],i=e[1],o=0;o<r.length;o+=2){var s=r.charCodeAt(o)-64,l=r.charCodeAt(o+1)-64;s=s>>1^-(s&1),l=l>>1^-(l&1),s+=n,l+=i,n=s,i=l,a.push([s/t,l/t])}return a}function hh(r,e){return r=fh(r),O(Pt(r.features,function(t){return t.geometry&&t.properties&&t.geometry.coordinates.length>0}),function(t){var a=t.properties,n=t.geometry,i=[];switch(n.type){case"Polygon":var o=n.coordinates;i.push(new to(o[0],o.slice(1)));break;case"MultiPolygon":P(n.coordinates,function(l){l[0]&&i.push(new to(l[0],l.slice(1)))});break;case"LineString":i.push(new eo([n.coordinates]));break;case"MultiLineString":i.push(new eo(n.coordinates))}var s=new uu(a[e||"name"],i,a.cp);return s.properties=a,s})}function ph(r){r.eachSeriesByType("radar",function(e){var t=e.getData(),a=[],n=e.coordinateSystem;if(n){var i=n.getIndicatorAxes();P(i,function(o,s){t.each(t.mapDimension(i[s].dim),function(l,u){a[u]=a[u]||[];var v=n.dataToPoint(l,s);a[u][s]=ro(v)?v:ao(n)})}),t.each(function(o){var s=Wv(a[o],function(l){return ro(l)})||ao(n);a[o].push(s.slice()),t.setItemLayout(o,a[o])})}})}function ro(r){return!isNaN(r[0])&&!isNaN(r[1])}function ao(r){return[r.cx,r.cy]}function dh(r){var e=r.polar;if(e){Y(e)||(e=[e]);var t=[];P(e,function(a,n){a.indicator?(a.type&&!a.shape&&(a.shape=a.type),r.radar=r.radar||[],Y(r.radar)||(r.radar=[r.radar]),r.radar.push(a)):t.push(a)}),r.polar=t}P(r.series,function(a){a&&a.type==="radar"&&a.polarIndex&&(a.radarIndex=a.polarIndex)})}var gh=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,a,n){var i=t.coordinateSystem,o=this.group,s=t.getData(),l=this._data;function u(f,h){var p=f.getItemVisual(h,"symbol")||"circle";if(p!=="none"){var d=dl(f.getItemVisual(h,"symbolSize")),g=Ve(p,-1,-1,2,2),S=f.getItemVisual(h,"symbolRotate")||0;return g.attr({style:{strokeNoScale:!0},z2:100,scaleX:d[0]/2,scaleY:d[1]/2,rotation:S*Math.PI/180||0}),g}}function v(f,h,p,d,g,S){p.removeAll();for(var m=0;m<h.length-1;m++){var y=u(d,g);y&&(y.__dimIdx=m,f[m]?(y.setPosition(f[m]),Je[S?"initProps":"updateProps"](y,{x:h[m][0],y:h[m][1]},t,g)):y.setPosition(h[m]),p.add(y))}}function c(f){return O(f,function(h){return[i.cx,i.cy]})}s.diff(l).add(function(f){var h=s.getItemLayout(f);if(h){var p=new Jt,d=new ke,g={shape:{points:h}};p.shape.points=c(h),d.shape.points=c(h),Yt(p,g,t,f),Yt(d,g,t,f);var S=new Z,m=new Z;S.add(d),S.add(p),S.add(m),v(d.shape.points,h,m,s,f,!0),s.setItemGraphicEl(f,S)}}).update(function(f,h){var p=l.getItemGraphicEl(h),d=p.childAt(0),g=p.childAt(1),S=p.childAt(2),m={shape:{points:s.getItemLayout(f)}};m.shape.points&&(v(d.shape.points,m.shape.points,S,s,f,!1),Re(g),Re(d),ct(d,m,t),ct(g,m,t),s.setItemGraphicEl(f,p))}).remove(function(f){o.remove(l.getItemGraphicEl(f))}).execute(),s.eachItemGraphicEl(function(f,h){var p=s.getItemModel(h),d=f.childAt(0),g=f.childAt(1),S=f.childAt(2),m=s.getItemVisual(h,"style"),y=m.fill;o.add(f),d.useStyle(j(p.getModel("lineStyle").getLineStyle(),{fill:"none",stroke:y})),Mt(d,p,"lineStyle"),Mt(g,p,"areaStyle");var w=p.getModel("areaStyle"),x=w.isEmpty()&&w.parentModel.isEmpty();g.ignore=x,P(["emphasis","select","blur"],function(T){var I=p.getModel([T,"areaStyle"]),A=I.isEmpty()&&I.parentModel.isEmpty();g.ensureState(T).ignore=A&&x}),g.useStyle(j(w.getAreaStyle(),{fill:y,opacity:.7,decal:m.decal}));var b=p.getModel("emphasis"),_=b.getModel("itemStyle").getItemStyle();S.eachChild(function(T){if(T instanceof ge){var I=T.style;T.useStyle(H({image:I.image,x:I.x,y:I.y,width:I.width,height:I.height},m))}else T.useStyle(m),T.setColor(y),T.style.strokeNoScale=!0;var A=T.ensureState("emphasis");A.style=Zt(_);var D=s.getStore().get(s.getDimensionIndex(T.__dimIdx),h);(D==null||isNaN(D))&&(D=""),Bt(T,Et(p),{labelFetcher:s.hostModel,labelDataIndex:h,labelDimIndex:T.__dimIdx,defaultText:D,inheritColor:y,defaultOpacity:m.opacity})}),ft(f,b.get("focus"),b.get("blurScope"),b.get("disabled"))}),this._data=s},e.prototype.remove=function(){this.group.removeAll(),this._data=null},e.type="radar",e}(gt),yh=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t.hasSymbolVisual=!0,t}return e.prototype.init=function(t){r.prototype.init.apply(this,arguments),this.legendVisualProvider=new ba(lt(this.getData,this),lt(this.getRawData,this))},e.prototype.getInitialData=function(t,a){return ha(this,{generateCoord:"indicator_",generateCoordCount:1/0})},e.prototype.formatTooltip=function(t,a,n){var i=this.getData(),o=this.coordinateSystem,s=o.getIndicatorAxes(),l=this.getData().getName(t),u=l===""?this.name:l,v=Uv(this,t);return Ft("section",{header:u,sortBlocks:!0,blocks:O(s,function(c){var f=i.get(i.mapDimension(c.dim),t);return Ft("nameValue",{markerType:"subItem",markerColor:v,name:c.name,value:f,sortParam:f})})})},e.prototype.getTooltipPosition=function(t){if(t!=null){for(var a=this.getData(),n=this.coordinateSystem,i=a.getValues(O(n.dimensions,function(u){return a.mapDimension(u)}),t),o=0,s=i.length;o<s;o++)if(!isNaN(i[o])){var l=n.getIndicatorAxes();return n.coordToPoint(l[o].dataToCoord(i[o]),o)}}},e.type="series.radar",e.dependencies=["radar"],e.defaultOption={z:2,colorBy:"data",coordinateSystem:"radar",legendHoverLink:!0,radarIndex:0,lineStyle:{width:2,type:"solid",join:"round"},label:{position:"top"},symbolSize:8},e}(xt),rr=wf.value;function Gr(r,e){return j({show:e},r)}var mh=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.optionUpdated=function(){var t=this.get("boundaryGap"),a=this.get("splitNumber"),n=this.get("scale"),i=this.get("axisLine"),o=this.get("axisTick"),s=this.get("axisLabel"),l=this.get("axisName"),u=this.get(["axisName","show"]),v=this.get(["axisName","formatter"]),c=this.get("axisNameGap"),f=this.get("triggerEvent"),h=O(this.get("indicator")||[],function(p){p.max!=null&&p.max>0&&!p.min?p.min=0:p.min!=null&&p.min<0&&!p.max&&(p.max=0);var d=l;p.color!=null&&(d=j({color:p.color},l));var g=Qt(Zt(p),{boundaryGap:t,splitNumber:a,scale:n,axisLine:i,axisTick:o,axisLabel:s,name:p.text,showName:u,nameLocation:"end",nameGap:c,nameTextStyle:d,triggerEvent:f},!1);if(et(v)){var S=g.name;g.name=v.replace("{value}",S??"")}else st(v)&&(g.name=v(g.name,g));var m=new Rt(g,null,this.ecModel);return ye(m,ma.prototype),m.mainType="radar",m.componentIndex=this.componentIndex,m},this);this._indicatorModels=h},e.prototype.getIndicatorModels=function(){return this._indicatorModels},e.type="radar",e.defaultOption={z:0,center:["50%","50%"],radius:"75%",startAngle:90,axisName:{show:!0},boundaryGap:[0,0],splitNumber:5,axisNameGap:15,scale:!1,shape:"polygon",axisLine:Qt({lineStyle:{color:"#bbb"}},rr.axisLine),axisLabel:Gr(rr.axisLabel,!1),axisTick:Gr(rr.axisTick,!1),splitLine:Gr(rr.splitLine,!0),splitArea:Gr(rr.splitArea,!0),indicator:[]},e}(me),Sh=["axisLine","axisTickLabel","axisName"],xh=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,a,n){var i=this.group;i.removeAll(),this._buildAxes(t),this._buildSplitLineAndArea(t)},e.prototype._buildAxes=function(t){var a=t.coordinateSystem,n=a.getIndicatorAxes(),i=O(n,function(o){var s=o.model.get("showName")?o.name:"",l=new Ne(o.model,{axisName:s,position:[a.cx,a.cy],rotation:o.angle,labelDirection:-1,tickDirection:-1,nameDirection:1});return l});P(i,function(o){P(Sh,o.add,o),this.group.add(o.getGroup())},this)},e.prototype._buildSplitLineAndArea=function(t){var a=t.coordinateSystem,n=a.getIndicatorAxes();if(!n.length)return;var i=t.get("shape"),o=t.getModel("splitLine"),s=t.getModel("splitArea"),l=o.getModel("lineStyle"),u=s.getModel("areaStyle"),v=o.get("show"),c=s.get("show"),f=l.get("color"),h=u.get("color"),p=Y(f)?f:[f],d=Y(h)?h:[h],g=[],S=[];function m(L,R,V){var N=V%R.length;return L[N]=L[N]||[],N}if(i==="circle")for(var y=n[0].getTicksCoords(),w=a.cx,x=a.cy,b=0;b<y.length;b++){if(v){var _=m(g,p,b);g[_].push(new Tr({shape:{cx:w,cy:x,r:y[b].coord}}))}if(c&&b<y.length-1){var _=m(S,d,b);S[_].push(new gl({shape:{cx:w,cy:x,r0:y[b].coord,r:y[b+1].coord}}))}}else for(var T,I=O(n,function(L,R){var V=L.getTicksCoords();return T=T==null?V.length-1:Math.min(V.length-1,T),O(V,function(N){return a.coordToPoint(N.coord,R)})}),A=[],b=0;b<=T;b++){for(var D=[],E=0;E<n.length;E++)D.push(I[E][b]);if(D[0]&&D.push(D[0].slice()),v){var _=m(g,p,b);g[_].push(new ke({shape:{points:D}}))}if(c&&A){var _=m(S,d,b-1);S[_].push(new Jt({shape:{points:D.concat(A)}}))}A=D.slice().reverse()}var M=l.getLineStyle(),C=u.getAreaStyle();P(S,function(L,R){this.group.add(kt(L,{style:j({stroke:"none",fill:d[R%d.length]},C),silent:!0}))},this),P(g,function(L,R){this.group.add(kt(L,{style:j({fill:"none",stroke:p[R%p.length]},M),silent:!0}))},this)},e.type="radar",e}(ze),bh=function(r){G(e,r);function e(t,a,n){var i=r.call(this,t,a,n)||this;return i.type="value",i.angle=0,i.name="",i}return e}(ae),wh=function(){function r(e,t,a){this.dimensions=[],this._model=e,this._indicatorAxes=O(e.getIndicatorModels(),function(n,i){var o="indicator_"+i,s=new bh(o,new Vi);return s.name=n.get("name"),s.model=n,n.axis=s,this.dimensions.push(o),s},this),this.resize(e,a)}return r.prototype.getIndicatorAxes=function(){return this._indicatorAxes},r.prototype.dataToPoint=function(e,t){var a=this._indicatorAxes[t];return this.coordToPoint(a.dataToCoord(e),t)},r.prototype.coordToPoint=function(e,t){var a=this._indicatorAxes[t],n=a.angle,i=this.cx+e*Math.cos(n),o=this.cy-e*Math.sin(n);return[i,o]},r.prototype.pointToData=function(e){var t=e[0]-this.cx,a=e[1]-this.cy,n=Math.sqrt(t*t+a*a);t/=n,a/=n;for(var i=Math.atan2(-a,t),o=1/0,s,l=-1,u=0;u<this._indicatorAxes.length;u++){var v=this._indicatorAxes[u],c=Math.abs(i-v.angle);c<o&&(s=v,l=u,o=c)}return[l,+(s&&s.coordToData(n))]},r.prototype.resize=function(e,t){var a=e.get("center"),n=t.getWidth(),i=t.getHeight(),o=Math.min(n,i)/2;this.cx=z(a[0],n),this.cy=z(a[1],i),this.startAngle=e.get("startAngle")*Math.PI/180;var s=e.get("radius");(et(s)||Xt(s))&&(s=[0,s]),this.r0=z(s[0],o),this.r=z(s[1],o),P(this._indicatorAxes,function(l,u){l.setExtent(this.r0,this.r);var v=this.startAngle+u*Math.PI*2/this._indicatorAxes.length;v=Math.atan2(Math.sin(v),Math.cos(v)),l.angle=v},this)},r.prototype.update=function(e,t){var a=this._indicatorAxes,n=this._model;P(a,function(s){s.scale.setExtent(1/0,-1/0)}),e.eachSeriesByType("radar",function(s,l){if(!(s.get("coordinateSystem")!=="radar"||e.getComponent("radar",s.get("radarIndex"))!==n)){var u=s.getData();P(a,function(v){v.scale.unionExtentFromData(u,u.mapDimension(v.dim))})}},this);var i=n.get("splitNumber"),o=new Vi;o.setExtent(0,i),o.setInterval(1),P(a,function(s,l){_f(s.scale,s.model,o)})},r.prototype.convertToPixel=function(e,t,a){return console.warn("Not implemented."),null},r.prototype.convertFromPixel=function(e,t,a){return console.warn("Not implemented."),null},r.prototype.containPoint=function(e){return console.warn("Not implemented."),!1},r.create=function(e,t){var a=[];return e.eachComponent("radar",function(n){var i=new r(n,e,t);a.push(i),n.coordinateSystem=i}),e.eachSeriesByType("radar",function(n){n.get("coordinateSystem")==="radar"&&(n.coordinateSystem=a[n.get("radarIndex")||0])}),a},r.dimensions=[],r}();function _h(r){r.registerCoordinateSystem("radar",wh),r.registerComponentModel(mh),r.registerComponentView(xh),r.registerVisual({seriesType:"radar",reset:function(e){var t=e.getData();t.each(function(a){t.setItemVisual(a,"legendIcon","roundRect")}),t.setVisual("legendIcon","roundRect")}})}function Ah(r){$(_h),r.registerChartView(gh),r.registerSeriesModel(yh),r.registerLayout(ph),r.registerProcessor(wa("radar")),r.registerPreprocessor(dh)}function ri(r,e,t){var a=r.target;a.x+=e,a.y+=t,a.dirty()}function ai(r,e,t,a){var n=r.target,i=r.zoomLimit,o=r.zoom=r.zoom||1;if(o*=e,i){var s=i.min||0,l=i.max||1/0;o=Math.max(Math.min(l,o),s)}var u=o/r.zoom;r.zoom=o,n.x-=(t-n.x)*(u-1),n.y-=(a-n.y)*(u-1),n.scaleX*=u,n.scaleY*=u,n.dirty()}function cu(r){if(et(r)){var e=new DOMParser;r=e.parseFromString(r,"text/xml")}var t=r;for(t.nodeType===9&&(t=t.firstChild);t.nodeName.toLowerCase()!=="svg"||t.nodeType!==1;)t=t.nextSibling;return t}var Va,ta={fill:"fill",stroke:"stroke","stroke-width":"lineWidth",opacity:"opacity","fill-opacity":"fillOpacity","stroke-opacity":"strokeOpacity","stroke-dasharray":"lineDash","stroke-dashoffset":"lineDashOffset","stroke-linecap":"lineCap","stroke-linejoin":"lineJoin","stroke-miterlimit":"miterLimit","font-family":"fontFamily","font-size":"fontSize","font-style":"fontStyle","font-weight":"fontWeight","text-anchor":"textAlign",visibility:"visibility",display:"display"},no=Tt(ta),ea={"alignment-baseline":"textBaseline","stop-color":"stopColor"},io=Tt(ea),Th=function(){function r(){this._defs={},this._root=null}return r.prototype.parse=function(e,t){t=t||{};var a=cu(e);this._defsUsePending=[];var n=new Z;this._root=n;var i=[],o=a.getAttribute("viewBox")||"",s=parseFloat(a.getAttribute("width")||t.width),l=parseFloat(a.getAttribute("height")||t.height);isNaN(s)&&(s=null),isNaN(l)&&(l=null),At(a,n,null,!0,!1);for(var u=a.firstChild;u;)this._parseNode(u,n,i,null,!1,!1),u=u.nextSibling;Ch(this._defs,this._defsUsePending),this._defsUsePending=[];var v,c;if(o){var f=_a(o);f.length>=4&&(v={x:parseFloat(f[0]||0),y:parseFloat(f[1]||0),width:parseFloat(f[2]),height:parseFloat(f[3])})}if(v&&s!=null&&l!=null&&(c=hu(v,{x:0,y:0,width:s,height:l}),!t.ignoreViewBox)){var h=n;n=new Z,n.add(h),h.scaleX=h.scaleY=c.scale,h.x=c.x,h.y=c.y}return!t.ignoreRootClip&&s!=null&&l!=null&&n.setClipPath(new It({shape:{x:0,y:0,width:s,height:l}})),{root:n,width:s,height:l,viewBoxRect:v,viewBoxTransform:c,named:i}},r.prototype._parseNode=function(e,t,a,n,i,o){var s=e.nodeName.toLowerCase(),l,u=n;if(s==="defs"&&(i=!0),s==="text"&&(o=!0),s==="defs"||s==="switch")l=t;else{if(!i){var v=Va[s];if(v&&mt(Va,s)){l=v.call(this,e,t);var c=e.getAttribute("name");if(c){var f={name:c,namedFrom:null,svgNodeTagLower:s,el:l};a.push(f),s==="g"&&(u=f)}else n&&a.push({name:n.name,namedFrom:n,svgNodeTagLower:s,el:l});t.add(l)}}var h=oo[s];if(h&&mt(oo,s)){var p=h.call(this,e),d=e.getAttribute("id");d&&(this._defs[d]=p)}}if(l&&l.isGroup)for(var g=e.firstChild;g;)g.nodeType===1?this._parseNode(g,l,a,u,i,o):g.nodeType===3&&o&&this._parseText(g,l),g=g.nextSibling},r.prototype._parseText=function(e,t){var a=new yl({style:{text:e.textContent},silent:!0,x:this._textX||0,y:this._textY||0});Dt(t,a),At(e,a,this._defsUsePending,!1,!1),Ih(a,t);var n=a.style,i=n.fontSize;i&&i<9&&(n.fontSize=9,a.scaleX*=i/9,a.scaleY*=i/9);var o=(n.fontSize||n.fontFamily)&&[n.fontStyle,n.fontWeight,(n.fontSize||12)+"px",n.fontFamily||"sans-serif"].join(" ");n.font=o;var s=a.getBoundingRect();return this._textX+=s.width,t.add(a),a},r.internalField=function(){Va={g:function(e,t){var a=new Z;return Dt(t,a),At(e,a,this._defsUsePending,!1,!1),a},rect:function(e,t){var a=new It;return Dt(t,a),At(e,a,this._defsUsePending,!1,!1),a.setShape({x:parseFloat(e.getAttribute("x")||"0"),y:parseFloat(e.getAttribute("y")||"0"),width:parseFloat(e.getAttribute("width")||"0"),height:parseFloat(e.getAttribute("height")||"0")}),a.silent=!0,a},circle:function(e,t){var a=new Tr;return Dt(t,a),At(e,a,this._defsUsePending,!1,!1),a.setShape({cx:parseFloat(e.getAttribute("cx")||"0"),cy:parseFloat(e.getAttribute("cy")||"0"),r:parseFloat(e.getAttribute("r")||"0")}),a.silent=!0,a},line:function(e,t){var a=new se;return Dt(t,a),At(e,a,this._defsUsePending,!1,!1),a.setShape({x1:parseFloat(e.getAttribute("x1")||"0"),y1:parseFloat(e.getAttribute("y1")||"0"),x2:parseFloat(e.getAttribute("x2")||"0"),y2:parseFloat(e.getAttribute("y2")||"0")}),a.silent=!0,a},ellipse:function(e,t){var a=new Yv;return Dt(t,a),At(e,a,this._defsUsePending,!1,!1),a.setShape({cx:parseFloat(e.getAttribute("cx")||"0"),cy:parseFloat(e.getAttribute("cy")||"0"),rx:parseFloat(e.getAttribute("rx")||"0"),ry:parseFloat(e.getAttribute("ry")||"0")}),a.silent=!0,a},polygon:function(e,t){var a=e.getAttribute("points"),n;a&&(n=uo(a));var i=new Jt({shape:{points:n||[]},silent:!0});return Dt(t,i),At(e,i,this._defsUsePending,!1,!1),i},polyline:function(e,t){var a=e.getAttribute("points"),n;a&&(n=uo(a));var i=new ke({shape:{points:n||[]},silent:!0});return Dt(t,i),At(e,i,this._defsUsePending,!1,!1),i},image:function(e,t){var a=new ge;return Dt(t,a),At(e,a,this._defsUsePending,!1,!1),a.setStyle({image:e.getAttribute("xlink:href")||e.getAttribute("href"),x:+e.getAttribute("x"),y:+e.getAttribute("y"),width:+e.getAttribute("width"),height:+e.getAttribute("height")}),a.silent=!0,a},text:function(e,t){var a=e.getAttribute("x")||"0",n=e.getAttribute("y")||"0",i=e.getAttribute("dx")||"0",o=e.getAttribute("dy")||"0";this._textX=parseFloat(a)+parseFloat(i),this._textY=parseFloat(n)+parseFloat(o);var s=new Z;return Dt(t,s),At(e,s,this._defsUsePending,!1,!0),s},tspan:function(e,t){var a=e.getAttribute("x"),n=e.getAttribute("y");a!=null&&(this._textX=parseFloat(a)),n!=null&&(this._textY=parseFloat(n));var i=e.getAttribute("dx")||"0",o=e.getAttribute("dy")||"0",s=new Z;return Dt(t,s),At(e,s,this._defsUsePending,!1,!0),this._textX+=parseFloat(i),this._textY+=parseFloat(o),s},path:function(e,t){var a=e.getAttribute("d")||"",n=$v(a);return Dt(t,n),At(e,n,this._defsUsePending,!1,!1),n.silent=!0,n}}}(),r}(),oo={lineargradient:function(r){var e=parseInt(r.getAttribute("x1")||"0",10),t=parseInt(r.getAttribute("y1")||"0",10),a=parseInt(r.getAttribute("x2")||"10",10),n=parseInt(r.getAttribute("y2")||"0",10),i=new ml(e,t,a,n);return so(r,i),lo(r,i),i},radialgradient:function(r){var e=parseInt(r.getAttribute("cx")||"0",10),t=parseInt(r.getAttribute("cy")||"0",10),a=parseInt(r.getAttribute("r")||"0",10),n=new Zv(e,t,a);return so(r,n),lo(r,n),n}};function so(r,e){var t=r.getAttribute("gradientUnits");t==="userSpaceOnUse"&&(e.global=!0)}function lo(r,e){for(var t=r.firstChild;t;){if(t.nodeType===1&&t.nodeName.toLocaleLowerCase()==="stop"){var a=t.getAttribute("offset"),n=void 0;a&&a.indexOf("%")>0?n=parseInt(a,10)/100:a?n=parseFloat(a):n=0;var i={};fu(t,i,i);var o=i.stopColor||t.getAttribute("stop-color")||"#000000";e.colorStops.push({offset:n,color:o})}t=t.nextSibling}}function Dt(r,e){r&&r.__inheritedStyle&&(e.__inheritedStyle||(e.__inheritedStyle={}),j(e.__inheritedStyle,r.__inheritedStyle))}function uo(r){for(var e=_a(r),t=[],a=0;a<e.length;a+=2){var n=parseFloat(e[a]),i=parseFloat(e[a+1]);t.push([n,i])}return t}function At(r,e,t,a,n){var i=e,o=i.__inheritedStyle=i.__inheritedStyle||{},s={};r.nodeType===1&&(Mh(r,e),fu(r,o,s),a||Eh(r,o,s)),i.style=i.style||{},o.fill!=null&&(i.style.fill=vo(i,"fill",o.fill,t)),o.stroke!=null&&(i.style.stroke=vo(i,"stroke",o.stroke,t)),P(["lineWidth","opacity","fillOpacity","strokeOpacity","miterLimit","fontSize"],function(l){o[l]!=null&&(i.style[l]=parseFloat(o[l]))}),P(["lineDashOffset","lineCap","lineJoin","fontWeight","fontFamily","fontStyle","textAlign"],function(l){o[l]!=null&&(i.style[l]=o[l])}),n&&(i.__selfStyle=s),o.lineDash&&(i.style.lineDash=O(_a(o.lineDash),function(l){return parseFloat(l)})),(o.visibility==="hidden"||o.visibility==="collapse")&&(i.invisible=!0),o.display==="none"&&(i.ignore=!0)}function Ih(r,e){var t=e.__selfStyle;if(t){var a=t.textBaseline,n=a;!a||a==="auto"||a==="baseline"?n="alphabetic":a==="before-edge"||a==="text-before-edge"?n="top":a==="after-edge"||a==="text-after-edge"?n="bottom":(a==="central"||a==="mathematical")&&(n="middle"),r.style.textBaseline=n}var i=e.__inheritedStyle;if(i){var o=i.textAlign,s=o;o&&(o==="middle"&&(s="center"),r.style.textAlign=s)}}var Dh=/^url\(\s*#(.*?)\)/;function vo(r,e,t,a){var n=t&&t.match(Dh);if(n){var i=Xv(n[1]);a.push([r,e,i]);return}return t==="none"&&(t=null),t}function Ch(r,e){for(var t=0;t<e.length;t++){var a=e[t];a[0].style[a[1]]=r[a[2]]}}var Lh=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;function _a(r){return r.match(Lh)||[]}var Ph=/(translate|scale|rotate|skewX|skewY|matrix)\(([\-\s0-9\.eE,]*)\)/g,Na=Math.PI/180;function Mh(r,e){var t=r.getAttribute("transform");if(t){t=t.replace(/,/g," ");var a=[],n=null;t.replace(Ph,function(c,f,h){return a.push(f,h),""});for(var i=a.length-1;i>0;i-=2){var o=a[i],s=a[i-1],l=_a(o);switch(n=n||Ir(),s){case"translate":yr(n,n,[parseFloat(l[0]),parseFloat(l[1]||"0")]);break;case"scale":Sl(n,n,[parseFloat(l[0]),parseFloat(l[1]||l[0])]);break;case"rotate":On(n,n,-parseFloat(l[0])*Na,[parseFloat(l[1]||"0"),parseFloat(l[2]||"0")]);break;case"skewX":var u=Math.tan(parseFloat(l[0])*Na);hn(n,[1,0,u,1,0,0],n);break;case"skewY":var v=Math.tan(parseFloat(l[0])*Na);hn(n,[1,v,0,1,0,0],n);break;case"matrix":n[0]=parseFloat(l[0]),n[1]=parseFloat(l[1]),n[2]=parseFloat(l[2]),n[3]=parseFloat(l[3]),n[4]=parseFloat(l[4]),n[5]=parseFloat(l[5]);break}}e.setLocalTransform(n)}}var co=/([^\s:;]+)\s*:\s*([^:;]+)/g;function fu(r,e,t){var a=r.getAttribute("style");if(a){co.lastIndex=0;for(var n;(n=co.exec(a))!=null;){var i=n[1],o=mt(ta,i)?ta[i]:null;o&&(e[o]=n[2]);var s=mt(ea,i)?ea[i]:null;s&&(t[s]=n[2])}}}function Eh(r,e,t){for(var a=0;a<no.length;a++){var n=no[a],i=r.getAttribute(n);i!=null&&(e[ta[n]]=i)}for(var a=0;a<io.length;a++){var n=io[a],i=r.getAttribute(n);i!=null&&(t[ea[n]]=i)}}function hu(r,e){var t=e.width/r.width,a=e.height/r.height,n=Math.min(t,a);return{scale:n,x:-(r.x+r.width/2)*n+(e.x+e.width/2),y:-(r.y+r.height/2)*n+(e.y+e.height/2)}}function Rh(r,e){var t=new Th;return t.parse(r,e)}var Vh=K(["rect","circle","line","ellipse","polygon","polyline","path","text","tspan","g"]),Nh=function(){function r(e,t){this.type="geoSVG",this._usedGraphicMap=K(),this._freedGraphics=[],this._mapName=e,this._parsedXML=cu(t)}return r.prototype.load=function(){var e=this._firstGraphic;if(!e){e=this._firstGraphic=this._buildGraphic(this._parsedXML),this._freedGraphics.push(e),this._boundingRect=this._firstGraphic.boundingRect.clone();var t=kh(e.named),a=t.regions,n=t.regionsMap;this._regions=a,this._regionsMap=n}return{boundingRect:this._boundingRect,regions:this._regions,regionsMap:this._regionsMap}},r.prototype._buildGraphic=function(e){var t,a;try{t=e&&Rh(e,{ignoreViewBox:!0,ignoreRootClip:!0})||{},a=t.root,Zr(a!=null)}catch(g){throw new Error(`Invalid svg format
`+g.message)}var n=new Z;n.add(a),n.isGeoSVGGraphicRoot=!0;var i=t.width,o=t.height,s=t.viewBoxRect,l=this._boundingRect;if(!l){var u=void 0,v=void 0,c=void 0,f=void 0;if(i!=null?(u=0,c=i):s&&(u=s.x,c=s.width),o!=null?(v=0,f=o):s&&(v=s.y,f=s.height),u==null||v==null){var h=a.getBoundingRect();u==null&&(u=h.x,c=h.width),v==null&&(v=h.y,f=h.height)}l=this._boundingRect=new dt(u,v,c,f)}if(s){var p=hu(s,l);a.scaleX=a.scaleY=p.scale,a.x=p.x,a.y=p.y}n.setClipPath(new It({shape:l.plain()}));var d=[];return P(t.named,function(g){Vh.get(g.svgNodeTagLower)!=null&&(d.push(g),Gh(g.el))}),{root:n,boundingRect:l,named:d}},r.prototype.useGraphic=function(e){var t=this._usedGraphicMap,a=t.get(e);return a||(a=this._freedGraphics.pop()||this._buildGraphic(this._parsedXML),t.set(e,a),a)},r.prototype.freeGraphic=function(e){var t=this._usedGraphicMap,a=t.get(e);a&&(t.removeKey(e),this._freedGraphics.push(a))},r}();function Gh(r){r.silent=!1,r.isGroup&&r.traverse(function(e){e.silent=!1})}function kh(r){var e=[],t=K();return P(r,function(a){if(a.namedFrom==null){var n=new ch(a.name,a.el);e.push(n),t.set(a.name,n)}}),{regions:e,regionsMap:t}}var bn=[126,25],fo="南海诸岛",Ie=[[[0,3.5],[7,11.2],[15,11.9],[30,7],[42,.7],[52,.7],[56,7.7],[59,.7],[64,.7],[64,0],[5,0],[0,3.5]],[[13,16.1],[19,14.7],[16,21.7],[11,23.1],[13,16.1]],[[12,32.2],[14,38.5],[15,38.5],[13,32.2],[12,32.2]],[[16,47.6],[12,53.2],[13,53.2],[18,47.6],[16,47.6]],[[6,64.4],[8,70],[9,70],[8,64.4],[6,64.4]],[[23,82.6],[29,79.8],[30,79.8],[25,82.6],[23,82.6]],[[37,70.7],[43,62.3],[44,62.3],[39,70.7],[37,70.7]],[[48,51.1],[51,45.5],[53,45.5],[50,51.1],[48,51.1]],[[51,35],[51,28.7],[53,28.7],[53,35],[51,35]],[[52,22.4],[55,17.5],[56,17.5],[53,22.4],[52,22.4]],[[58,12.6],[62,7],[63,7],[60,12.6],[58,12.6]],[[0,3.5],[0,93.1],[64,93.1],[64,0],[63,0],[63,92.4],[1,92.4],[1,3.5],[0,3.5]]];for(var _e=0;_e<Ie.length;_e++)for(var Oe=0;Oe<Ie[_e].length;Oe++)Ie[_e][Oe][0]/=10.5,Ie[_e][Oe][1]/=-10.5/.75,Ie[_e][Oe][0]+=bn[0],Ie[_e][Oe][1]+=bn[1];function zh(r,e){if(r==="china"){for(var t=0;t<e.length;t++)if(e[t].name===fo)return;e.push(new uu(fo,O(Ie,function(a){return{type:"polygon",exterior:a}}),bn))}}var Oh={南海诸岛:[32,80],广东:[0,-10],香港:[10,5],澳门:[-10,10],天津:[5,5]};function Bh(r,e){if(r==="china"){var t=Oh[e.name];if(t){var a=e.getCenter();a[0]+=t[0]/10.5,a[1]+=-t[1]/(10.5/.75),e.setCenter(a)}}}var Fh=[[[123.45165252685547,25.73527164402261],[123.49731445312499,25.73527164402261],[123.49731445312499,25.750734064600884],[123.45165252685547,25.750734064600884],[123.45165252685547,25.73527164402261]]];function Hh(r,e){r==="china"&&e.name==="台湾"&&e.geometries.push({type:"polygon",exterior:Fh[0]})}var Wh="name",Uh=function(){function r(e,t,a){this.type="geoJSON",this._parsedMap=K(),this._mapName=e,this._specialAreas=a,this._geoJSON=Yh(t)}return r.prototype.load=function(e,t){t=t||Wh;var a=this._parsedMap.get(t);if(!a){var n=this._parseToRegions(t);a=this._parsedMap.set(t,{regions:n,boundingRect:$h(n)})}var i=K(),o=[];return P(a.regions,function(s){var l=s.name;e&&mt(e,l)&&(s=s.cloneShallow(l=e[l])),o.push(s),i.set(l,s)}),{regions:o,boundingRect:a.boundingRect||new dt(0,0,0,0),regionsMap:i}},r.prototype._parseToRegions=function(e){var t=this._mapName,a=this._geoJSON,n;try{n=a?hh(a,e):[]}catch(i){throw new Error(`Invalid geoJson format
`+i.message)}return zh(t,n),P(n,function(i){var o=i.name;Bh(t,i),Hh(t,i);var s=this._specialAreas&&this._specialAreas[o];s&&i.transformTo(s.left,s.top,s.width,s.height)},this),n},r.prototype.getMapForUser=function(){return{geoJson:this._geoJSON,geoJSON:this._geoJSON,specialAreas:this._specialAreas}},r}();function $h(r){for(var e,t=0;t<r.length;t++){var a=r[t].getBoundingRect();e=e||a.clone(),e.union(a)}return e}function Yh(r){return et(r)?typeof JSON<"u"&&JSON.parse?JSON.parse(r):new Function("return ("+r+");")():r}var ar=K();const ee={registerMap:function(r,e,t){if(e.svg){var a=new Nh(r,e.svg);ar.set(r,a)}else{var n=e.geoJson||e.geoJSON;n&&!e.features?t=e.specialAreas:n=e;var a=new Uh(r,n,t);ar.set(r,a)}},getGeoResource:function(r){return ar.get(r)},getMapForUser:function(r){var e=ar.get(r);return e&&e.type==="geoJSON"&&e.getMapForUser()},load:function(r,e,t){var a=ar.get(r);if(a)return a.load(e,t)}};var ni=["rect","circle","line","ellipse","polygon","polyline","path"],Zh=K(ni),Xh=K(ni.concat(["g"])),qh=K(ni.concat(["g"])),pu=re();function kr(r){var e=r.getItemStyle(),t=r.get("areaColor");return t!=null&&(e.fill=t),e}function ho(r){var e=r.style;e&&(e.stroke=e.stroke||e.fill,e.fill=null)}var du=function(){function r(e){var t=new Z;this.uid=qv("ec_map_draw"),this._controller=new Sa(e.getZr()),this._controllerHost={target:t},this.group=t,t.add(this._regionsGroup=new Z),t.add(this._svgGroup=new Z)}return r.prototype.draw=function(e,t,a,n,i){var o=e.mainType==="geo",s=e.getData&&e.getData();o&&t.eachComponent({mainType:"series",subType:"map"},function(S){!s&&S.getHostGeoModel()===e&&(s=S.getData())});var l=e.coordinateSystem,u=this._regionsGroup,v=this.group,c=l.getTransformInfo(),f=c.raw,h=c.roam,p=!u.childAt(0)||i;p?(v.x=h.x,v.y=h.y,v.scaleX=h.scaleX,v.scaleY=h.scaleY,v.dirty()):ct(v,h,e);var d=s&&s.getVisual("visualMeta")&&s.getVisual("visualMeta").length>0,g={api:a,geo:l,mapOrGeoModel:e,data:s,isVisualEncodedByVisualMap:d,isGeo:o,transformInfoRaw:f};l.resourceType==="geoJSON"?this._buildGeoJSON(g):l.resourceType==="geoSVG"&&this._buildSVG(g),this._updateController(e,t,a),this._updateMapSelectHandler(e,u,a,n)},r.prototype._buildGeoJSON=function(e){var t=this._regionsGroupByName=K(),a=K(),n=this._regionsGroup,i=e.transformInfoRaw,o=e.mapOrGeoModel,s=e.data,l=e.geo.projection,u=l&&l.stream;function v(h,p){return p&&(h=p(h)),h&&[h[0]*i.scaleX+i.x,h[1]*i.scaleY+i.y]}function c(h){for(var p=[],d=!u&&l&&l.project,g=0;g<h.length;++g){var S=v(h[g],d);S&&p.push(S)}return p}function f(h){return{shape:{points:c(h)}}}n.removeAll(),P(e.geo.regions,function(h){var p=h.name,d=t.get(p),g=a.get(p)||{},S=g.dataIdx,m=g.regionModel;if(!d){d=t.set(p,new Z),n.add(d),S=s?s.indexOfName(p):null,m=e.isGeo?o.getRegionModel(p):s?s.getItemModel(S):null;var y=m.get("silent",!0);y!=null&&(d.silent=y),a.set(p,{dataIdx:S,regionModel:m})}var w=[],x=[];P(h.geometries,function(T){if(T.type==="polygon"){var I=[T.exterior].concat(T.interiors||[]);u&&(I=xo(I,u)),P(I,function(D){w.push(new Jt(f(D)))})}else{var A=T.points;u&&(A=xo(A,u,!0)),P(A,function(D){x.push(new ke(f(D)))})}});var b=v(h.getCenter(),l&&l.project);function _(T,I){if(T.length){var A=new xl({culling:!0,segmentIgnoreThreshold:1,shape:{paths:T}});d.add(A),po(e,A,S,m),go(e,A,p,m,o,S,b),I&&(ho(A),P(A.states,ho))}}_(w),_(x,!0)}),t.each(function(h,p){var d=a.get(p),g=d.dataIdx,S=d.regionModel;yo(e,h,p,S,o,g),mo(e,h,p,S,o),So(e,h,p,S,o)},this)},r.prototype._buildSVG=function(e){var t=e.geo.map,a=e.transformInfoRaw;this._svgGroup.x=a.x,this._svgGroup.y=a.y,this._svgGroup.scaleX=a.scaleX,this._svgGroup.scaleY=a.scaleY,this._svgResourceChanged(t)&&(this._freeSVG(),this._useSVG(t));var n=this._svgDispatcherMap=K(),i=!1;P(this._svgGraphicRecord.named,function(o){var s=o.name,l=e.mapOrGeoModel,u=e.data,v=o.svgNodeTagLower,c=o.el,f=u?u.indexOfName(s):null,h=l.getRegionModel(s);Zh.get(v)!=null&&c instanceof mr&&po(e,c,f,h),c instanceof mr&&(c.culling=!0);var p=h.get("silent",!0);if(p!=null&&(c.silent=p),c.z2EmphasisLift=0,!o.namedFrom&&(qh.get(v)!=null&&go(e,c,s,h,l,f,null),yo(e,c,s,h,l,f),mo(e,c,s,h,l),Xh.get(v)!=null)){var d=So(e,c,s,h,l);d==="self"&&(i=!0);var g=n.get(s)||n.set(s,[]);g.push(c)}},this),this._enableBlurEntireSVG(i,e)},r.prototype._enableBlurEntireSVG=function(e,t){if(e&&t.isGeo){var a=t.mapOrGeoModel.getModel(["blur","itemStyle"]).getItemStyle(),n=a.opacity;this._svgGraphicRecord.root.traverse(function(i){if(!i.isGroup){qe(i);var o=i.ensureState("blur").style||{};o.opacity==null&&n!=null&&(o.opacity=n),i.ensureState("emphasis")}})}},r.prototype.remove=function(){this._regionsGroup.removeAll(),this._regionsGroupByName=null,this._svgGroup.removeAll(),this._freeSVG(),this._controller.dispose(),this._controllerHost=null},r.prototype.findHighDownDispatchers=function(e,t){if(e==null)return[];var a=t.coordinateSystem;if(a.resourceType==="geoJSON"){var n=this._regionsGroupByName;if(n){var i=n.get(e);return i?[i]:[]}}else if(a.resourceType==="geoSVG")return this._svgDispatcherMap&&this._svgDispatcherMap.get(e)||[]},r.prototype._svgResourceChanged=function(e){return this._svgMapName!==e},r.prototype._useSVG=function(e){var t=ee.getGeoResource(e);if(t&&t.type==="geoSVG"){var a=t.useGraphic(this.uid);this._svgGroup.add(a.root),this._svgGraphicRecord=a,this._svgMapName=e}},r.prototype._freeSVG=function(){var e=this._svgMapName;if(e!=null){var t=ee.getGeoResource(e);t&&t.type==="geoSVG"&&t.freeGraphic(this.uid),this._svgGraphicRecord=null,this._svgDispatcherMap=null,this._svgGroup.removeAll(),this._svgMapName=null}},r.prototype._updateController=function(e,t,a){var n=e.coordinateSystem,i=this._controller,o=this._controllerHost;o.zoomLimit=e.get("scaleLimit"),o.zoom=n.getZoom(),i.enable(e.get("roam")||!1);var s=e.mainType;function l(){var u={type:"geoRoam",componentType:s};return u[s+"Id"]=e.id,u}i.off("pan").on("pan",function(u){this._mouseDownFlag=!1,ri(o,u.dx,u.dy),a.dispatchAction(H(l(),{dx:u.dx,dy:u.dy,animation:{duration:0}}))},this),i.off("zoom").on("zoom",function(u){this._mouseDownFlag=!1,ai(o,u.scale,u.originX,u.originY),a.dispatchAction(H(l(),{totalZoom:o.zoom,zoom:u.scale,originX:u.originX,originY:u.originY,animation:{duration:0}}))},this),i.setPointerChecker(function(u,v,c){return n.containPoint([v,c])&&!Kn(u,a,e)})},r.prototype.resetForLabelLayout=function(){this.group.traverse(function(e){var t=e.getTextContent();t&&(t.ignore=pu(t).ignore)})},r.prototype._updateMapSelectHandler=function(e,t,a,n){var i=this;t.off("mousedown"),t.off("click"),e.get("selectedMode")&&(t.on("mousedown",function(){i._mouseDownFlag=!0}),t.on("click",function(o){i._mouseDownFlag&&(i._mouseDownFlag=!1)}))},r}();function po(r,e,t,a){var n=a.getModel("itemStyle"),i=a.getModel(["emphasis","itemStyle"]),o=a.getModel(["blur","itemStyle"]),s=a.getModel(["select","itemStyle"]),l=kr(n),u=kr(i),v=kr(s),c=kr(o),f=r.data;if(f){var h=f.getItemVisual(t,"style"),p=f.getItemVisual(t,"decal");r.isVisualEncodedByVisualMap&&h.fill&&(l.fill=h.fill),p&&(l.decal=Bn(p,r.api))}e.setStyle(l),e.style.strokeNoScale=!0,e.ensureState("emphasis").style=u,e.ensureState("select").style=v,e.ensureState("blur").style=c,qe(e)}function go(r,e,t,a,n,i,o){var s=r.data,l=r.isGeo,u=s&&isNaN(s.get(s.mapDimension("value"),i)),v=s&&s.getItemLayout(i);if(l||u||v&&v.showLabel){var c=l?t:i,f=void 0;(!s||i>=0)&&(f=n);var h=o?{normal:{align:"center",verticalAlign:"middle"}}:null;Bt(e,Et(a),{labelFetcher:f,labelDataIndex:c,defaultText:t},h);var p=e.getTextContent();if(p&&(pu(p).ignore=p.ignore,e.textConfig&&o)){var d=e.getBoundingRect().clone();e.textConfig.layoutRect=d,e.textConfig.position=[(o[0]-d.x)/d.width*100+"%",(o[1]-d.y)/d.height*100+"%"]}e.disableLabelAnimation=!0}else e.removeTextContent(),e.removeTextConfig(),e.disableLabelAnimation=null}function yo(r,e,t,a,n,i){r.data?r.data.setItemGraphicEl(i,e):ht(e).eventData={componentType:"geo",componentIndex:n.componentIndex,geoIndex:n.componentIndex,name:t,region:a&&a.option||{}}}function mo(r,e,t,a,n){r.data||jv({el:e,componentModel:n,itemName:t,itemTooltipOption:a.get("tooltip")})}function So(r,e,t,a,n){e.highDownSilentOnTouch=!!n.get("selectedMode");var i=a.getModel("emphasis"),o=i.get("focus");return ft(e,o,i.get("blurScope"),i.get("disabled")),r.isGeo&&Kv(e,n,t),o}function xo(r,e,t){var a=[],n;function i(){n=[]}function o(){n.length&&(a.push(n),n=[])}var s=e({polygonStart:i,polygonEnd:o,lineStart:i,lineEnd:o,point:function(l,u){isFinite(l)&&isFinite(u)&&n.push([l,u])},sphere:function(){}});return!t&&s.polygonStart(),P(r,function(l){s.lineStart();for(var u=0;u<l.length;u++)s.point(l[u][0],l[u][1]);s.lineEnd()}),!t&&s.polygonEnd(),a}var jh=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,a,n,i){if(!(i&&i.type==="mapToggleSelect"&&i.from===this.uid)){var o=this.group;if(o.removeAll(),!t.getHostGeoModel()){if(this._mapDraw&&i&&i.type==="geoRoam"&&this._mapDraw.resetForLabelLayout(),i&&i.type==="geoRoam"&&i.componentType==="series"&&i.seriesId===t.id){var s=this._mapDraw;s&&o.add(s.group)}else if(t.needsDrawMap){var s=this._mapDraw||new du(n);o.add(s.group),s.draw(t,a,n,this,i),this._mapDraw=s}else this._mapDraw&&this._mapDraw.remove(),this._mapDraw=null;t.get("showLegendSymbol")&&a.getComponent("legend")&&this._renderSymbols(t,a,n)}}},e.prototype.remove=function(){this._mapDraw&&this._mapDraw.remove(),this._mapDraw=null,this.group.removeAll()},e.prototype.dispose=function(){this._mapDraw&&this._mapDraw.remove(),this._mapDraw=null},e.prototype._renderSymbols=function(t,a,n){var i=t.originalData,o=this.group;i.each(i.mapDimension("value"),function(s,l){if(!isNaN(s)){var u=i.getItemLayout(l);if(!(!u||!u.point)){var v=u.point,c=u.offset,f=new Tr({style:{fill:t.getData().getVisual("style").fill},shape:{cx:v[0]+c*9,cy:v[1],r:3},silent:!0,z2:8+(c?0:Dr+1)});if(!c){var h=t.mainSeries.getData(),p=i.getName(l),d=h.indexOfName(p),g=i.getItemModel(l),S=g.getModel("label"),m=h.getItemGraphicEl(d);Bt(f,Et(g),{labelFetcher:{getFormattedLabel:function(y,w){return t.getFormattedLabel(d,w)}},defaultText:p}),f.disableLabelAnimation=!0,S.get("position")||f.setTextConfig({position:"bottom"}),m.onHoverStateChange=function(y){bl(f,y)}}o.add(f)}}})},e.type="map",e}(gt),Kh=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t.needsDrawMap=!1,t.seriesGroup=[],t.getTooltipPosition=function(a){if(a!=null){var n=this.getData().getName(a),i=this.coordinateSystem,o=i.getRegion(n);return o&&i.dataToPoint(o.getCenter())}},t}return e.prototype.getInitialData=function(t){for(var a=ha(this,{coordDimensions:["value"],encodeDefaulter:St(wl,this)}),n=K(),i=[],o=0,s=a.count();o<s;o++){var l=a.getName(o);n.set(l,o)}var u=ee.load(this.getMapType(),this.option.nameMap,this.option.nameProperty);return P(u.regions,function(v){var c=v.name,f=n.get(c),h=v.properties&&v.properties.echartsStyle,p;f==null?(p={name:c},i.push(p)):p=a.getRawDataItem(f),h&&Qt(p,h)}),a.appendData(i),a},e.prototype.getHostGeoModel=function(){var t=this.option.geoIndex;return t!=null?this.ecModel.getComponent("geo",t):null},e.prototype.getMapType=function(){return(this.getHostGeoModel()||this).option.map},e.prototype.getRawValue=function(t){var a=this.getData();return a.get(a.mapDimension("value"),t)},e.prototype.getRegionModel=function(t){var a=this.getData();return a.getItemModel(a.indexOfName(t))},e.prototype.formatTooltip=function(t,a,n){for(var i=this.getData(),o=this.getRawValue(t),s=i.getName(t),l=this.seriesGroup,u=[],v=0;v<l.length;v++){var c=l[v].originalData.indexOfName(s),f=i.mapDimension("value");isNaN(l[v].originalData.get(f,c))||u.push(l[v].name)}return Ft("section",{header:u.join(", "),noHeader:!u.length,blocks:[Ft("nameValue",{name:s,value:o})]})},e.prototype.setZoom=function(t){this.option.zoom=t},e.prototype.setCenter=function(t){this.option.center=t},e.prototype.getLegendIcon=function(t){var a=t.icon||"roundRect",n=Ve(a,0,0,t.itemWidth,t.itemHeight,t.itemStyle.fill);return n.setStyle(t.itemStyle),n.style.stroke="none",a.indexOf("empty")>-1&&(n.style.stroke=n.style.fill,n.style.fill="#fff",n.style.lineWidth=2),n},e.type="series.map",e.dependencies=["geo"],e.layoutMode="box",e.defaultOption={z:2,coordinateSystem:"geo",map:"",left:"center",top:"center",aspectScale:null,showLegendSymbol:!0,boundingCoords:null,center:null,zoom:1,scaleLimit:null,selectedMode:!0,label:{show:!1,color:"#000"},itemStyle:{borderWidth:.5,borderColor:"#444",areaColor:"#eee"},emphasis:{label:{show:!0,color:"rgb(100,0,0)"},itemStyle:{areaColor:"rgba(255,215,0,0.8)"}},select:{label:{show:!0,color:"rgb(100,0,0)"},itemStyle:{color:"rgba(255,215,0,0.8)"}},nameProperty:"name"},e}(xt);function Jh(r,e){var t={};return P(r,function(a){a.each(a.mapDimension("value"),function(n,i){var o="ec-"+a.getName(i);t[o]=t[o]||[],isNaN(n)||t[o].push(n)})}),r[0].map(r[0].mapDimension("value"),function(a,n){for(var i="ec-"+r[0].getName(n),o=0,s=1/0,l=-1/0,u=t[i].length,v=0;v<u;v++)s=Math.min(s,t[i][v]),l=Math.max(l,t[i][v]),o+=t[i][v];var c;return e==="min"?c=s:e==="max"?c=l:e==="average"?c=o/u:c=o,u===0?NaN:c})}function Qh(r){var e={};r.eachSeriesByType("map",function(t){var a=t.getHostGeoModel(),n=a?"o"+a.id:"i"+t.getMapType();(e[n]=e[n]||[]).push(t)}),P(e,function(t,a){for(var n=Jh(O(t,function(o){return o.getData()}),t[0].get("mapValueCalculation")),i=0;i<t.length;i++)t[i].originalData=t[i].getData();for(var i=0;i<t.length;i++)t[i].seriesGroup=t,t[i].needsDrawMap=i===0&&!t[i].getHostGeoModel(),t[i].setData(n.cloneShallow()),t[i].mainSeries=t[0]})}function tp(r){var e={};r.eachSeriesByType("map",function(t){var a=t.getMapType();if(!(t.getHostGeoModel()||e[a])){var n={};P(t.seriesGroup,function(o){var s=o.coordinateSystem,l=o.originalData;o.get("showLegendSymbol")&&r.getComponent("legend")&&l.each(l.mapDimension("value"),function(u,v){var c=l.getName(v),f=s.getRegion(c);if(!(!f||isNaN(u))){var h=n[c]||0,p=s.dataToPoint(f.getCenter());n[c]=h+1,l.setItemLayout(v,{point:p,offset:h})}})});var i=t.getData();i.each(function(o){var s=i.getName(o),l=i.getItemLayout(o)||{};l.showLabel=!n[s],i.setItemLayout(o,l)}),e[a]=!0}})}var bo=gr,Mr=function(r){G(e,r);function e(t){var a=r.call(this)||this;return a.type="view",a.dimensions=["x","y"],a._roamTransformable=new sr,a._rawTransformable=new sr,a.name=t,a}return e.prototype.setBoundingRect=function(t,a,n,i){return this._rect=new dt(t,a,n,i),this._rect},e.prototype.getBoundingRect=function(){return this._rect},e.prototype.setViewRect=function(t,a,n,i){this._transformTo(t,a,n,i),this._viewRect=new dt(t,a,n,i)},e.prototype._transformTo=function(t,a,n,i){var o=this.getBoundingRect(),s=this._rawTransformable;s.transform=o.calculateTransform(new dt(t,a,n,i));var l=s.parent;s.parent=null,s.decomposeTransform(),s.parent=l,this._updateTransform()},e.prototype.setCenter=function(t,a){t&&(this._center=[z(t[0],a.getWidth()),z(t[1],a.getHeight())],this._updateCenterAndZoom())},e.prototype.setZoom=function(t){t=t||1;var a=this.zoomLimit;a&&(a.max!=null&&(t=Math.min(a.max,t)),a.min!=null&&(t=Math.max(a.min,t))),this._zoom=t,this._updateCenterAndZoom()},e.prototype.getDefaultCenter=function(){var t=this.getBoundingRect(),a=t.x+t.width/2,n=t.y+t.height/2;return[a,n]},e.prototype.getCenter=function(){return this._center||this.getDefaultCenter()},e.prototype.getZoom=function(){return this._zoom||1},e.prototype.getRoamTransform=function(){return this._roamTransformable.getLocalTransform()},e.prototype._updateCenterAndZoom=function(){var t=this._rawTransformable.getLocalTransform(),a=this._roamTransformable,n=this.getDefaultCenter(),i=this.getCenter(),o=this.getZoom();i=gr([],i,t),n=gr([],n,t),a.originX=i[0],a.originY=i[1],a.x=n[0]-i[0],a.y=n[1]-i[1],a.scaleX=a.scaleY=o,this._updateTransform()},e.prototype._updateTransform=function(){var t=this._roamTransformable,a=this._rawTransformable;a.parent=t,t.updateTransform(),a.updateTransform(),Jv(this.transform||(this.transform=[]),a.transform||Ir()),this._rawTransform=a.getLocalTransform(),this.invTransform=this.invTransform||[],fl(this.invTransform,this.transform),this.decomposeTransform()},e.prototype.getTransformInfo=function(){var t=this._rawTransformable,a=this._roamTransformable,n=new sr;return n.transform=a.transform,n.decomposeTransform(),{roam:{x:n.x,y:n.y,scaleX:n.scaleX,scaleY:n.scaleY},raw:{x:t.x,y:t.y,scaleX:t.scaleX,scaleY:t.scaleY}}},e.prototype.getViewRect=function(){return this._viewRect},e.prototype.getViewRectAfterRoam=function(){var t=this.getBoundingRect().clone();return t.applyTransform(this.transform),t},e.prototype.dataToPoint=function(t,a,n){var i=a?this._rawTransform:this.transform;return n=n||[],i?bo(n,t,i):wt(n,t)},e.prototype.pointToData=function(t){var a=this.invTransform;return a?bo([],t,a):[t[0],t[1]]},e.prototype.convertToPixel=function(t,a,n){var i=wo(a);return i===this?i.dataToPoint(n):null},e.prototype.convertFromPixel=function(t,a,n){var i=wo(a);return i===this?i.pointToData(n):null},e.prototype.containPoint=function(t){return this.getViewRectAfterRoam().contain(t[0],t[1])},e.dimensions=["x","y"],e}(sr);function wo(r){var e=r.seriesModel;return e?e.coordinateSystem:null}var ep={geoJSON:{aspectScale:.75,invertLongitute:!0},geoSVG:{aspectScale:1,invertLongitute:!1}},gu=["lng","lat"],wn=function(r){G(e,r);function e(t,a,n){var i=r.call(this,t)||this;i.dimensions=gu,i.type="geo",i._nameCoordMap=K(),i.map=a;var o=n.projection,s=ee.load(a,n.nameMap,n.nameProperty),l=ee.getGeoResource(a);i.resourceType=l?l.type:null;var u=i.regions=s.regions,v=ep[l.type];i._regionsMap=s.regionsMap,i.regions=s.regions,i.projection=o;var c;if(o)for(var f=0;f<u.length;f++){var h=u[f].getBoundingRect(o);c=c||h.clone(),c.union(h)}else c=s.boundingRect;return i.setBoundingRect(c.x,c.y,c.width,c.height),i.aspectScale=o?1:ce(n.aspectScale,v.aspectScale),i._invertLongitute=o?!1:v.invertLongitute,i}return e.prototype._transformTo=function(t,a,n,i){var o=this.getBoundingRect(),s=this._invertLongitute;o=o.clone(),s&&(o.y=-o.y-o.height);var l=this._rawTransformable;l.transform=o.calculateTransform(new dt(t,a,n,i));var u=l.parent;l.parent=null,l.decomposeTransform(),l.parent=u,s&&(l.scaleY=-l.scaleY),this._updateTransform()},e.prototype.getRegion=function(t){return this._regionsMap.get(t)},e.prototype.getRegionByCoord=function(t){for(var a=this.regions,n=0;n<a.length;n++){var i=a[n];if(i.type==="geoJSON"&&i.contain(t))return a[n]}},e.prototype.addGeoCoord=function(t,a){this._nameCoordMap.set(t,a)},e.prototype.getGeoCoord=function(t){var a=this._regionsMap.get(t);return this._nameCoordMap.get(t)||a&&a.getCenter()},e.prototype.dataToPoint=function(t,a,n){if(et(t)&&(t=this.getGeoCoord(t)),t){var i=this.projection;return i&&(t=i.project(t)),t&&this.projectedToPoint(t,a,n)}},e.prototype.pointToData=function(t){var a=this.projection;return a&&(t=a.unproject(t)),t&&this.pointToProjected(t)},e.prototype.pointToProjected=function(t){return r.prototype.pointToData.call(this,t)},e.prototype.projectedToPoint=function(t,a,n){return r.prototype.dataToPoint.call(this,t,a,n)},e.prototype.convertToPixel=function(t,a,n){var i=_o(a);return i===this?i.dataToPoint(n):null},e.prototype.convertFromPixel=function(t,a,n){var i=_o(a);return i===this?i.pointToData(n):null},e}(Mr);ye(wn,Mr);function _o(r){var e=r.geoModel,t=r.seriesModel;return e?e.coordinateSystem:t?t.coordinateSystem||(t.getReferringComponents("geo",Qe).models[0]||{}).coordinateSystem:null}function Ao(r,e){var t=r.get("boundingCoords");if(t!=null){var a=t[0],n=t[1];if(isFinite(a[0])&&isFinite(a[1])&&isFinite(n[0])&&isFinite(n[1])){var i=this.projection;if(i){var o=a[0],s=a[1],l=n[0],u=n[1];a=[1/0,1/0],n=[-1/0,-1/0];var v=function(b,_,T,I){for(var A=T-b,D=I-_,E=0;E<=100;E++){var M=E/100,C=i.project([b+A*M,_+D*M]);hl(a,a,C),pl(n,n,C)}};v(o,s,l,s),v(l,s,l,u),v(l,u,o,u),v(o,u,l,s)}this.setBoundingRect(a[0],a[1],n[0]-a[0],n[1]-a[1])}}var c=this.getBoundingRect(),f=r.get("layoutCenter"),h=r.get("layoutSize"),p=e.getWidth(),d=e.getHeight(),g=c.width/c.height*this.aspectScale,S=!1,m,y;f&&h&&(m=[z(f[0],p),z(f[1],d)],y=z(h,Math.min(p,d)),!isNaN(m[0])&&!isNaN(m[1])&&!isNaN(y)&&(S=!0));var w;if(S)w={},g>1?(w.width=y,w.height=y/g):(w.height=y,w.width=y*g),w.y=m[1]-w.height/2,w.x=m[0]-w.width/2;else{var x=r.getBoxLayoutParams();x.aspect=g,w=Se(x,{width:p,height:d})}this.setViewRect(w.x,w.y,w.width,w.height),this.setCenter(r.get("center"),e),this.setZoom(r.get("zoom"))}function rp(r,e){P(e.get("geoCoord"),function(t,a){r.addGeoCoord(a,t)})}var ap=function(){function r(){this.dimensions=gu}return r.prototype.create=function(e,t){var a=[];function n(o){return{nameProperty:o.get("nameProperty"),aspectScale:o.get("aspectScale"),projection:o.get("projection")}}e.eachComponent("geo",function(o,s){var l=o.get("map"),u=new wn(l+s,l,H({nameMap:o.get("nameMap")},n(o)));u.zoomLimit=o.get("scaleLimit"),a.push(u),o.coordinateSystem=u,u.model=o,u.resize=Ao,u.resize(o,t)}),e.eachSeries(function(o){var s=o.get("coordinateSystem");if(s==="geo"){var l=o.get("geoIndex")||0;o.coordinateSystem=a[l]}});var i={};return e.eachSeriesByType("map",function(o){if(!o.getHostGeoModel()){var s=o.getMapType();i[s]=i[s]||[],i[s].push(o)}}),P(i,function(o,s){var l=O(o,function(v){return v.get("nameMap")}),u=new wn(s,s,H({nameMap:Fn(l)},n(o[0])));u.zoomLimit=zt.apply(null,O(o,function(v){return v.get("scaleLimit")})),a.push(u),u.resize=Ao,u.resize(o[0],t),P(o,function(v){v.coordinateSystem=u,rp(u,v)})}),a},r.prototype.getFilledRegions=function(e,t,a,n){for(var i=(e||[]).slice(),o=K(),s=0;s<i.length;s++)o.set(i[s].name,i[s]);var l=ee.load(t,a,n);return P(l.regions,function(u){var v=u.name,c=o.get(v),f=u.properties&&u.properties.echartsStyle;c||(c={name:v},i.push(c)),f&&Qt(c,f)}),i},r}(),yu=new ap,np=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t,a,n){var i=ee.getGeoResource(t.map);if(i&&i.type==="geoJSON"){var o=t.itemStyle=t.itemStyle||{};"color"in o||(o.color="#eee")}this.mergeDefaultAndTheme(t,n),Hn(t,"label",["show"])},e.prototype.optionUpdated=function(){var t=this,a=this.option;a.regions=yu.getFilledRegions(a.regions,a.map,a.nameMap,a.nameProperty);var n={};this._optionModelMap=Qv(a.regions||[],function(i,o){var s=o.name;return s&&(i.set(s,new Rt(o,t,t.ecModel)),o.selected&&(n[s]=!0)),i},K()),a.selectedMap||(a.selectedMap=n)},e.prototype.getRegionModel=function(t){return this._optionModelMap.get(t)||new Rt(null,this,this.ecModel)},e.prototype.getFormattedLabel=function(t,a){var n=this.getRegionModel(t),i=a==="normal"?n.get(["label","formatter"]):n.get(["emphasis","label","formatter"]),o={name:t};if(st(i))return o.status=a,i(o);if(et(i))return i.replace("{a}",t??"")},e.prototype.setZoom=function(t){this.option.zoom=t},e.prototype.setCenter=function(t){this.option.center=t},e.prototype.select=function(t){var a=this.option,n=a.selectedMode;if(n){n!=="multiple"&&(a.selectedMap=null);var i=a.selectedMap||(a.selectedMap={});i[t]=!0}},e.prototype.unSelect=function(t){var a=this.option.selectedMap;a&&(a[t]=!1)},e.prototype.toggleSelected=function(t){this[this.isSelected(t)?"unSelect":"select"](t)},e.prototype.isSelected=function(t){var a=this.option.selectedMap;return!!(a&&a[t])},e.type="geo",e.layoutMode="box",e.defaultOption={z:0,show:!0,left:"center",top:"center",aspectScale:null,silent:!1,map:"",boundingCoords:null,center:null,zoom:1,scaleLimit:null,label:{show:!1,color:"#000"},itemStyle:{borderWidth:.5,borderColor:"#444"},emphasis:{label:{show:!0,color:"rgb(100,0,0)"},itemStyle:{color:"rgba(255,215,0,0.8)"}},select:{label:{show:!0,color:"rgb(100,0,0)"},itemStyle:{color:"rgba(255,215,0,0.8)"}},regions:[]},e}(me);function To(r,e){return r.pointToProjected?r.pointToProjected(e):r.pointToData(e)}function ii(r,e,t,a){var n=r.getZoom(),i=r.getCenter(),o=e.zoom,s=r.projectedToPoint?r.projectedToPoint(i):r.dataToPoint(i);if(e.dx!=null&&e.dy!=null&&(s[0]-=e.dx,s[1]-=e.dy,r.setCenter(To(r,s),a)),o!=null){if(t){var l=t.min||0,u=t.max||1/0;o=Math.max(Math.min(n*o,u),l)/n}r.scaleX*=o,r.scaleY*=o;var v=(e.originX-r.x)*(o-1),c=(e.originY-r.y)*(o-1);r.x-=v,r.y-=c,r.updateTransform(),r.setCenter(To(r,s),a),r.setZoom(o*n)}return{center:r.getCenter(),zoom:r.getZoom()}}var ip=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t.focusBlurEnabled=!0,t}return e.prototype.init=function(t,a){this._api=a},e.prototype.render=function(t,a,n,i){if(this._model=t,!t.get("show")){this._mapDraw&&this._mapDraw.remove(),this._mapDraw=null;return}this._mapDraw||(this._mapDraw=new du(n));var o=this._mapDraw;o.draw(t,a,n,this,i),o.group.on("click",this._handleRegionClick,this),o.group.silent=t.get("silent"),this.group.add(o.group),this.updateSelectStatus(t,a,n)},e.prototype._handleRegionClick=function(t){var a;tc(t.target,function(n){return(a=ht(n).eventData)!=null},!0),a&&this._api.dispatchAction({type:"geoToggleSelect",geoId:this._model.id,name:a.name})},e.prototype.updateSelectStatus=function(t,a,n){var i=this;this._mapDraw.group.traverse(function(o){var s=ht(o).eventData;if(s)return i._model.isSelected(s.name)?n.enterSelect(o):n.leaveSelect(o),!0})},e.prototype.findHighDownDispatchers=function(t){return this._mapDraw&&this._mapDraw.findHighDownDispatchers(t,this._model)},e.prototype.dispose=function(){this._mapDraw&&this._mapDraw.remove()},e.type="geo",e}(ze);function op(r,e,t){ee.registerMap(r,e,t)}function mu(r){r.registerCoordinateSystem("geo",yu),r.registerComponentModel(np),r.registerComponentView(ip),r.registerImpl("registerMap",op),r.registerImpl("getMap",function(t){return ee.getMapForUser(t)});function e(t,a){a.update="geo:updateSelectStatus",r.registerAction(a,function(n,i){var o={},s=[];return i.eachComponent({mainType:"geo",query:n},function(l){l[t](n.name);var u=l.coordinateSystem;P(u.regions,function(c){o[c.name]=l.isSelected(c.name)||!1});var v=[];P(o,function(c,f){o[f]&&v.push(f)}),s.push({geoIndex:l.componentIndex,name:v})}),{selected:o,allSelected:s,name:n.name}})}e("toggleSelected",{type:"geoToggleSelect",event:"geoselectchanged"}),e("select",{type:"geoSelect",event:"geoselected"}),e("unSelect",{type:"geoUnSelect",event:"geounselected"}),r.registerAction({type:"geoRoam",event:"geoRoam",update:"updateTransform"},function(t,a,n){var i=t.componentType||"series";a.eachComponent({mainType:i,query:t},function(o){var s=o.coordinateSystem;if(s.type==="geo"){var l=ii(s,t,o.get("scaleLimit"),n);o.setCenter&&o.setCenter(l.center),o.setZoom&&o.setZoom(l.zoom),i==="series"&&P(o.seriesGroup,function(u){u.setCenter(l.center),u.setZoom(l.zoom)})}})})}function sp(r){$(mu),r.registerChartView(jh),r.registerSeriesModel(Kh),r.registerLayout(tp),r.registerProcessor(r.PRIORITY.PROCESSOR.STATISTIC,Qh),ec("map",r.registerAction)}function lp(r){var e=r;e.hierNode={defaultAncestor:null,ancestor:e,prelim:0,modifier:0,change:0,shift:0,i:0,thread:null};for(var t=[e],a,n;a=t.pop();)if(n=a.children,a.isExpand&&n.length)for(var i=n.length,o=i-1;o>=0;o--){var s=n[o];s.hierNode={defaultAncestor:null,ancestor:s,prelim:0,modifier:0,change:0,shift:0,i:o,thread:null},t.push(s)}}function up(r,e){var t=r.isExpand?r.children:[],a=r.parentNode.children,n=r.hierNode.i?a[r.hierNode.i-1]:null;if(t.length){fp(r);var i=(t[0].hierNode.prelim+t[t.length-1].hierNode.prelim)/2;n?(r.hierNode.prelim=n.hierNode.prelim+e(r,n),r.hierNode.modifier=r.hierNode.prelim-i):r.hierNode.prelim=i}else n&&(r.hierNode.prelim=n.hierNode.prelim+e(r,n));r.parentNode.hierNode.defaultAncestor=hp(r,n,r.parentNode.hierNode.defaultAncestor||a[0],e)}function vp(r){var e=r.hierNode.prelim+r.parentNode.hierNode.modifier;r.setLayout({x:e},!0),r.hierNode.modifier+=r.parentNode.hierNode.modifier}function Io(r){return arguments.length?r:gp}function vr(r,e){return r-=Math.PI/2,{x:e*Math.cos(r),y:e*Math.sin(r)}}function cp(r,e){return Se(r.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()})}function fp(r){for(var e=r.children,t=e.length,a=0,n=0;--t>=0;){var i=e[t];i.hierNode.prelim+=a,i.hierNode.modifier+=a,n+=i.hierNode.change,a+=i.hierNode.shift+n}}function hp(r,e,t,a){if(e){for(var n=r,i=r,o=i.parentNode.children[0],s=e,l=n.hierNode.modifier,u=i.hierNode.modifier,v=o.hierNode.modifier,c=s.hierNode.modifier;s=Ga(s),i=ka(i),s&&i;){n=Ga(n),o=ka(o),n.hierNode.ancestor=r;var f=s.hierNode.prelim+c-i.hierNode.prelim-u+a(s,i);f>0&&(dp(pp(s,r,t),r,f),u+=f,l+=f),c+=s.hierNode.modifier,u+=i.hierNode.modifier,l+=n.hierNode.modifier,v+=o.hierNode.modifier}s&&!Ga(n)&&(n.hierNode.thread=s,n.hierNode.modifier+=c-l),i&&!ka(o)&&(o.hierNode.thread=i,o.hierNode.modifier+=u-v,t=r)}return t}function Ga(r){var e=r.children;return e.length&&r.isExpand?e[e.length-1]:r.hierNode.thread}function ka(r){var e=r.children;return e.length&&r.isExpand?e[0]:r.hierNode.thread}function pp(r,e,t){return r.hierNode.ancestor.parentNode===e.parentNode?r.hierNode.ancestor:t}function dp(r,e,t){var a=t/(e.hierNode.i-r.hierNode.i);e.hierNode.change-=a,e.hierNode.shift+=t,e.hierNode.modifier+=t,e.hierNode.prelim+=t,r.hierNode.change+=a}function gp(r,e){return r.parentNode===e.parentNode?1:2}var yp=function(){function r(){this.parentPoint=[],this.childPoints=[]}return r}(),mp=function(r){G(e,r);function e(t){return r.call(this,t)||this}return e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new yp},e.prototype.buildPath=function(t,a){var n=a.childPoints,i=n.length,o=a.parentPoint,s=n[0],l=n[i-1];if(i===1){t.moveTo(o[0],o[1]),t.lineTo(s[0],s[1]);return}var u=a.orient,v=u==="TB"||u==="BT"?0:1,c=1-v,f=z(a.forkPosition,1),h=[];h[v]=o[v],h[c]=o[c]+(l[c]-o[c])*f,t.moveTo(o[0],o[1]),t.lineTo(h[0],h[1]),t.moveTo(s[0],s[1]),h[v]=s[v],t.lineTo(h[0],h[1]),h[v]=l[v],t.lineTo(h[0],h[1]),t.lineTo(l[0],l[1]);for(var p=1;p<i-1;p++){var d=n[p];t.moveTo(d[0],d[1]),h[v]=d[v],t.lineTo(h[0],h[1])}},e}(Vt),Sp=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t._mainGroup=new Z,t}return e.prototype.init=function(t,a){this._controller=new Sa(a.getZr()),this._controllerHost={target:this.group},this.group.add(this._mainGroup)},e.prototype.render=function(t,a,n){var i=t.getData(),o=t.layoutInfo,s=this._mainGroup,l=t.get("layout");l==="radial"?(s.x=o.x+o.width/2,s.y=o.y+o.height/2):(s.x=o.x,s.y=o.y),this._updateViewCoordSys(t,n),this._updateController(t,a,n);var u=this._data;i.diff(u).add(function(v){Do(i,v)&&Co(i,v,null,s,t)}).update(function(v,c){var f=u.getItemGraphicEl(c);if(!Do(i,v)){f&&Po(u,c,f,s,t);return}Co(i,v,f,s,t)}).remove(function(v){var c=u.getItemGraphicEl(v);c&&Po(u,v,c,s,t)}).execute(),this._nodeScaleRatio=t.get("nodeScaleRatio"),this._updateNodeAndLinkScale(t),t.get("expandAndCollapse")===!0&&i.eachItemGraphicEl(function(v,c){v.off("click").on("click",function(){n.dispatchAction({type:"treeExpandAndCollapse",seriesId:t.id,dataIndex:c})})}),this._data=i},e.prototype._updateViewCoordSys=function(t,a){var n=t.getData(),i=[];n.each(function(c){var f=n.getItemLayout(c);f&&!isNaN(f.x)&&!isNaN(f.y)&&i.push([+f.x,+f.y])});var o=[],s=[];pa(i,o,s);var l=this._min,u=this._max;s[0]-o[0]===0&&(o[0]=l?l[0]:o[0]-1,s[0]=u?u[0]:s[0]+1),s[1]-o[1]===0&&(o[1]=l?l[1]:o[1]-1,s[1]=u?u[1]:s[1]+1);var v=t.coordinateSystem=new Mr;v.zoomLimit=t.get("scaleLimit"),v.setBoundingRect(o[0],o[1],s[0]-o[0],s[1]-o[1]),v.setCenter(t.get("center"),a),v.setZoom(t.get("zoom")),this.group.attr({x:v.x,y:v.y,scaleX:v.scaleX,scaleY:v.scaleY}),this._min=o,this._max=s},e.prototype._updateController=function(t,a,n){var i=this,o=this._controller,s=this._controllerHost,l=this.group;o.setPointerChecker(function(u,v,c){var f=l.getBoundingRect();return f.applyTransform(l.transform),f.contain(v,c)&&!Kn(u,n,t)}),o.enable(t.get("roam")),s.zoomLimit=t.get("scaleLimit"),s.zoom=t.coordinateSystem.getZoom(),o.off("pan").off("zoom").on("pan",function(u){ri(s,u.dx,u.dy),n.dispatchAction({seriesId:t.id,type:"treeRoam",dx:u.dx,dy:u.dy})}).on("zoom",function(u){ai(s,u.scale,u.originX,u.originY),n.dispatchAction({seriesId:t.id,type:"treeRoam",zoom:u.scale,originX:u.originX,originY:u.originY}),i._updateNodeAndLinkScale(t),n.updateLabelLayout()})},e.prototype._updateNodeAndLinkScale=function(t){var a=t.getData(),n=this._getNodeGlobalScale(t);a.eachItemGraphicEl(function(i,o){i.setSymbolScale(n)})},e.prototype._getNodeGlobalScale=function(t){var a=t.coordinateSystem;if(a.type!=="view")return 1;var n=this._nodeScaleRatio,i=a.scaleX||1,o=a.getZoom(),s=(o-1)*n+1;return s/i},e.prototype.dispose=function(){this._controller&&this._controller.dispose(),this._controllerHost=null},e.prototype.remove=function(){this._mainGroup.removeAll(),this._data=null},e.type="tree",e}(gt);function Do(r,e){var t=r.getItemLayout(e);return t&&!isNaN(t.x)&&!isNaN(t.y)}function Co(r,e,t,a,n){var i=!t,o=r.tree.getNodeByDataIndex(e),s=o.getModel(),l=o.getVisual("style").fill,u=o.isExpand===!1&&o.children.length!==0?l:"#fff",v=r.tree.root,c=o.parentNode===v?o:o.parentNode||o,f=r.getItemGraphicEl(c.dataIndex),h=c.getLayout(),p=f?{x:f.__oldX,y:f.__oldY,rawX:f.__radialOldRawX,rawY:f.__radialOldRawY}:h,d=o.getLayout();i?(t=new jl(r,e,null,{symbolInnerColor:u,useNameLabel:!0}),t.x=p.x,t.y=p.y):t.updateData(r,e,null,{symbolInnerColor:u,useNameLabel:!0}),t.__radialOldRawX=t.__radialRawX,t.__radialOldRawY=t.__radialRawY,t.__radialRawX=d.rawX,t.__radialRawY=d.rawY,a.add(t),r.setItemGraphicEl(e,t),t.__oldX=t.x,t.__oldY=t.y,ct(t,{x:d.x,y:d.y},n);var g=t.getSymbolPath();if(n.get("layout")==="radial"){var S=v.children[0],m=S.getLayout(),y=S.children.length,w=void 0,x=void 0;if(d.x===m.x&&o.isExpand===!0&&S.children.length){var b={x:(S.children[0].getLayout().x+S.children[y-1].getLayout().x)/2,y:(S.children[0].getLayout().y+S.children[y-1].getLayout().y)/2};w=Math.atan2(b.y-m.y,b.x-m.x),w<0&&(w=Math.PI*2+w),x=b.x<m.x,x&&(w=w-Math.PI)}else w=Math.atan2(d.y-m.y,d.x-m.x),w<0&&(w=Math.PI*2+w),o.children.length===0||o.children.length!==0&&o.isExpand===!1?(x=d.x<m.x,x&&(w=w-Math.PI)):(x=d.x>m.x,x||(w=w-Math.PI));var _=x?"left":"right",T=s.getModel("label"),I=T.get("rotate"),A=I*(Math.PI/180),D=g.getTextContent();D&&(g.setTextConfig({position:T.get("position")||_,rotation:I==null?-w:A,origin:"center"}),D.setStyle("verticalAlign","middle"))}var E=s.get(["emphasis","focus"]),M=E==="relative"?Xr(o.getAncestorsIndices(),o.getDescendantIndices()):E==="ancestor"?o.getAncestorsIndices():E==="descendant"?o.getDescendantIndices():null;M&&(ht(t).focus=M),xp(n,o,v,t,p,h,d,a),t.__edge&&(t.onHoverStateChange=function(C){if(C!=="blur"){var L=o.parentNode&&r.getItemGraphicEl(o.parentNode.dataIndex);L&&L.hoverState===rc||bl(t.__edge,C)}})}function xp(r,e,t,a,n,i,o,s){var l=e.getModel(),u=r.get("edgeShape"),v=r.get("layout"),c=r.getOrient(),f=r.get(["lineStyle","curveness"]),h=r.get("edgeForkPosition"),p=l.getModel("lineStyle").getLineStyle(),d=a.__edge;if(u==="curve")e.parentNode&&e.parentNode!==t&&(d||(d=a.__edge=new ac({shape:_n(v,c,f,n,n)})),ct(d,{shape:_n(v,c,f,i,o)},r));else if(u==="polyline"&&v==="orthogonal"&&e!==t&&e.children&&e.children.length!==0&&e.isExpand===!0){for(var g=e.children,S=[],m=0;m<g.length;m++){var y=g[m].getLayout();S.push([y.x,y.y])}d||(d=a.__edge=new mp({shape:{parentPoint:[o.x,o.y],childPoints:[[o.x,o.y]],orient:c,forkPosition:h}})),ct(d,{shape:{parentPoint:[o.x,o.y],childPoints:S}},r)}d&&!(u==="polyline"&&!e.isExpand)&&(d.useStyle(j({strokeNoScale:!0,fill:null},p)),Mt(d,l,"lineStyle"),qe(d),s.add(d))}function Lo(r,e,t,a,n){var i=e.tree.root,o=Su(i,r),s=o.source,l=o.sourceLayout,u=e.getItemGraphicEl(r.dataIndex);if(u){var v=e.getItemGraphicEl(s.dataIndex),c=v.__edge,f=u.__edge||(s.isExpand===!1||s.children.length===1?c:void 0),h=a.get("edgeShape"),p=a.get("layout"),d=a.get("orient"),g=a.get(["lineStyle","curveness"]);f&&(h==="curve"?qr(f,{shape:_n(p,d,g,l,l),style:{opacity:0}},a,{cb:function(){t.remove(f)},removeOpt:n}):h==="polyline"&&a.get("layout")==="orthogonal"&&qr(f,{shape:{parentPoint:[l.x,l.y],childPoints:[[l.x,l.y]]},style:{opacity:0}},a,{cb:function(){t.remove(f)},removeOpt:n}))}}function Su(r,e){for(var t=e.parentNode===r?e:e.parentNode||e,a;a=t.getLayout(),a==null;)t=t.parentNode===r?t:t.parentNode||t;return{source:t,sourceLayout:a}}function Po(r,e,t,a,n){var i=r.tree.getNodeByDataIndex(e),o=r.tree.root,s=Su(o,i).sourceLayout,l={duration:n.get("animationDurationUpdate"),easing:n.get("animationEasingUpdate")};qr(t,{x:s.x+1,y:s.y+1},n,{cb:function(){a.remove(t),r.setItemGraphicEl(e,null)},removeOpt:l}),t.fadeOut(null,r.hostModel,{fadeLabel:!0,animation:l}),i.children.forEach(function(u){Lo(u,r,a,n,l)}),Lo(i,r,a,n,l)}function _n(r,e,t,a,n){var i,o,s,l,u,v,c,f;if(r==="radial"){u=a.rawX,c=a.rawY,v=n.rawX,f=n.rawY;var h=vr(u,c),p=vr(u,c+(f-c)*t),d=vr(v,f+(c-f)*t),g=vr(v,f);return{x1:h.x||0,y1:h.y||0,x2:g.x||0,y2:g.y||0,cpx1:p.x||0,cpy1:p.y||0,cpx2:d.x||0,cpy2:d.y||0}}else u=a.x,c=a.y,v=n.x,f=n.y,(e==="LR"||e==="RL")&&(i=u+(v-u)*t,o=c,s=v+(u-v)*t,l=f),(e==="TB"||e==="BT")&&(i=u,o=c+(f-c)*t,s=v,l=f+(c-f)*t);return{x1:u,y1:c,x2:v,y2:f,cpx1:i,cpy1:o,cpx2:s,cpy2:l}}var Lt=re();function xu(r){var e=r.mainData,t=r.datas;t||(t={main:e},r.datasAttr={main:"data"}),r.datas=r.mainData=null,bu(e,t,r),P(t,function(a){P(e.TRANSFERABLE_METHODS,function(n){a.wrapMethod(n,St(bp,r))})}),e.wrapMethod("cloneShallow",St(_p,r)),P(e.CHANGABLE_METHODS,function(a){e.wrapMethod(a,St(wp,r))}),Zr(t[e.dataType]===e)}function bp(r,e){if(Ip(this)){var t=H({},Lt(this).datas);t[this.dataType]=e,bu(e,t,r)}else oi(e,this.dataType,Lt(this).mainData,r);return e}function wp(r,e){return r.struct&&r.struct.update(),e}function _p(r,e){return P(Lt(e).datas,function(t,a){t!==e&&oi(t.cloneShallow(),a,e,r)}),e}function Ap(r){var e=Lt(this).mainData;return r==null||e==null?e:Lt(e).datas[r]}function Tp(){var r=Lt(this).mainData;return r==null?[{data:r}]:O(Tt(Lt(r).datas),function(e){return{type:e,data:Lt(r).datas[e]}})}function Ip(r){return Lt(r).mainData===r}function bu(r,e,t){Lt(r).datas={},P(e,function(a,n){oi(a,n,r,t)})}function oi(r,e,t,a){Lt(t).datas[e]=r,Lt(r).mainData=t,r.dataType=e,a.struct&&(r[a.structAttr]=a.struct,a.struct[a.datasAttr[e]]=r),r.getLinkedData=Ap,r.getLinkedDataAll=Tp}var Dp=function(){function r(e,t){this.depth=0,this.height=0,this.dataIndex=-1,this.children=[],this.viewChildren=[],this.isExpand=!1,this.name=e||"",this.hostTree=t}return r.prototype.isRemoved=function(){return this.dataIndex<0},r.prototype.eachNode=function(e,t,a){st(e)&&(a=t,t=e,e=null),e=e||{},et(e)&&(e={order:e});var n=e.order||"preorder",i=this[e.attr||"children"],o;n==="preorder"&&(o=t.call(a,this));for(var s=0;!o&&s<i.length;s++)i[s].eachNode(e,t,a);n==="postorder"&&t.call(a,this)},r.prototype.updateDepthAndHeight=function(e){var t=0;this.depth=e;for(var a=0;a<this.children.length;a++){var n=this.children[a];n.updateDepthAndHeight(e+1),n.height>t&&(t=n.height)}this.height=t+1},r.prototype.getNodeById=function(e){if(this.getId()===e)return this;for(var t=0,a=this.children,n=a.length;t<n;t++){var i=a[t].getNodeById(e);if(i)return i}},r.prototype.contains=function(e){if(e===this)return!0;for(var t=0,a=this.children,n=a.length;t<n;t++){var i=a[t].contains(e);if(i)return i}},r.prototype.getAncestors=function(e){for(var t=[],a=e?this:this.parentNode;a;)t.push(a),a=a.parentNode;return t.reverse(),t},r.prototype.getAncestorsIndices=function(){for(var e=[],t=this;t;)e.push(t.dataIndex),t=t.parentNode;return e.reverse(),e},r.prototype.getDescendantIndices=function(){var e=[];return this.eachNode(function(t){e.push(t.dataIndex)}),e},r.prototype.getValue=function(e){var t=this.hostTree.data;return t.getStore().get(t.getDimensionIndex(e||"value"),this.dataIndex)},r.prototype.setLayout=function(e,t){this.dataIndex>=0&&this.hostTree.data.setItemLayout(this.dataIndex,e,t)},r.prototype.getLayout=function(){return this.hostTree.data.getItemLayout(this.dataIndex)},r.prototype.getModel=function(e){if(!(this.dataIndex<0)){var t=this.hostTree,a=t.data.getItemModel(this.dataIndex);return a.getModel(e)}},r.prototype.getLevelModel=function(){return(this.hostTree.levelModels||[])[this.depth]},r.prototype.setVisual=function(e,t){this.dataIndex>=0&&this.hostTree.data.setItemVisual(this.dataIndex,e,t)},r.prototype.getVisual=function(e){return this.hostTree.data.getItemVisual(this.dataIndex,e)},r.prototype.getRawIndex=function(){return this.hostTree.data.getRawIndex(this.dataIndex)},r.prototype.getId=function(){return this.hostTree.data.getId(this.dataIndex)},r.prototype.getChildIndex=function(){if(this.parentNode){for(var e=this.parentNode.children,t=0;t<e.length;++t)if(e[t]===this)return t;return-1}return-1},r.prototype.isAncestorOf=function(e){for(var t=e.parentNode;t;){if(t===this)return!0;t=t.parentNode}return!1},r.prototype.isDescendantOf=function(e){return e!==this&&e.isAncestorOf(this)},r}(),si=function(){function r(e){this.type="tree",this._nodes=[],this.hostModel=e}return r.prototype.eachNode=function(e,t,a){this.root.eachNode(e,t,a)},r.prototype.getNodeByDataIndex=function(e){var t=this.data.getRawIndex(e);return this._nodes[t]},r.prototype.getNodeById=function(e){return this.root.getNodeById(e)},r.prototype.update=function(){for(var e=this.data,t=this._nodes,a=0,n=t.length;a<n;a++)t[a].dataIndex=-1;for(var a=0,n=e.count();a<n;a++)t[e.getRawIndex(a)].dataIndex=a},r.prototype.clearLayouts=function(){this.data.clearItemLayouts()},r.createTree=function(e,t,a){var n=new r(t),i=[],o=1;s(e);function s(v,c){var f=v.value;o=Math.max(o,Y(f)?f.length:1),i.push(v);var h=new Dp(da(v.name,""),n);c?Cp(h,c):n.root=h,n._nodes.push(h);var p=v.children;if(p)for(var d=0;d<p.length;d++)s(p[d],h)}n.root.updateDepthAndHeight(0);var l=Wn(i,{coordDimensions:["value"],dimensionsCount:o}).dimensions,u=new te(l,t);return u.initData(i),a&&a(u),xu({mainData:u,struct:n,structAttr:"tree"}),n.update(),n},r}();function Cp(r,e){var t=e.children;r.parentNode!==e&&(t.push(r),r.parentNode=e)}function xr(r,e,t){if(r&&xe(e,r.type)>=0){var a=t.getData().tree.root,n=r.targetNode;if(et(n)&&(n=a.getNodeById(n)),n&&a.contains(n))return{node:n};var i=r.targetNodeId;if(i!=null&&(n=a.getNodeById(i)))return{node:n}}}function wu(r){for(var e=[];r;)r=r.parentNode,r&&e.push(r);return e.reverse()}function li(r,e){var t=wu(r);return xe(t,e)>=0}function Aa(r,e){for(var t=[];r;){var a=r.dataIndex;t.push({name:r.name,dataIndex:a,value:e.getRawValue(a)}),r=r.parentNode}return t.reverse(),t}var Lp=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.hasSymbolVisual=!0,t.ignoreStyleOnData=!0,t}return e.prototype.getInitialData=function(t){var a={name:t.name,children:t.data},n=t.leaves||{},i=new Rt(n,this,this.ecModel),o=si.createTree(a,this,s);function s(c){c.wrapMethod("getItemModel",function(f,h){var p=o.getNodeByDataIndex(h);return p&&p.children.length&&p.isExpand||(f.parentModel=i),f})}var l=0;o.eachNode("preorder",function(c){c.depth>l&&(l=c.depth)});var u=t.expandAndCollapse,v=u&&t.initialTreeDepth>=0?t.initialTreeDepth:l;return o.root.eachNode("preorder",function(c){var f=c.hostTree.data.getRawDataItem(c.dataIndex);c.isExpand=f&&f.collapsed!=null?!f.collapsed:c.depth<=v}),o.data},e.prototype.getOrient=function(){var t=this.get("orient");return t==="horizontal"?t="LR":t==="vertical"&&(t="TB"),t},e.prototype.setZoom=function(t){this.option.zoom=t},e.prototype.setCenter=function(t){this.option.center=t},e.prototype.formatTooltip=function(t,a,n){for(var i=this.getData().tree,o=i.root.children[0],s=i.getNodeByDataIndex(t),l=s.getValue(),u=s.name;s&&s!==o;)u=s.parentNode.name+"."+u,s=s.parentNode;return Ft("nameValue",{name:u,value:l,noValue:isNaN(l)||l==null})},e.prototype.getDataParams=function(t){var a=r.prototype.getDataParams.apply(this,arguments),n=this.getData().tree.getNodeByDataIndex(t);return a.treeAncestors=Aa(n,this),a.collapsed=!n.isExpand,a},e.type="series.tree",e.layoutMode="box",e.defaultOption={z:2,coordinateSystem:"view",left:"12%",top:"12%",right:"12%",bottom:"12%",layout:"orthogonal",edgeShape:"curve",edgeForkPosition:"50%",roam:!1,nodeScaleRatio:.4,center:null,zoom:1,orient:"LR",symbol:"emptyCircle",symbolSize:7,expandAndCollapse:!0,initialTreeDepth:2,lineStyle:{color:"#ccc",width:1.5,curveness:.5},itemStyle:{color:"lightsteelblue",borderWidth:1.5},label:{show:!0},animationEasing:"linear",animationDuration:700,animationDurationUpdate:500},e}(xt);function Pp(r,e,t){for(var a=[r],n=[],i;i=a.pop();)if(n.push(i),i.isExpand){var o=i.children;if(o.length)for(var s=0;s<o.length;s++)a.push(o[s])}for(;i=n.pop();)e(i,t)}function nr(r,e){for(var t=[r],a;a=t.pop();)if(e(a),a.isExpand){var n=a.children;if(n.length)for(var i=n.length-1;i>=0;i--)t.push(n[i])}}function Mp(r,e){r.eachSeriesByType("tree",function(t){Ep(t,e)})}function Ep(r,e){var t=cp(r,e);r.layoutInfo=t;var a=r.get("layout"),n=0,i=0,o=null;a==="radial"?(n=2*Math.PI,i=Math.min(t.height,t.width)/2,o=Io(function(y,w){return(y.parentNode===w.parentNode?1:2)/y.depth})):(n=t.width,i=t.height,o=Io());var s=r.getData().tree.root,l=s.children[0];if(l){lp(s),Pp(l,up,o),s.hierNode.modifier=-l.hierNode.prelim,nr(l,vp);var u=l,v=l,c=l;nr(l,function(y){var w=y.getLayout().x;w<u.getLayout().x&&(u=y),w>v.getLayout().x&&(v=y),y.depth>c.depth&&(c=y)});var f=u===v?1:o(u,v)/2,h=f-u.getLayout().x,p=0,d=0,g=0,S=0;if(a==="radial")p=n/(v.getLayout().x+f+h),d=i/(c.depth-1||1),nr(l,function(y){g=(y.getLayout().x+h)*p,S=(y.depth-1)*d;var w=vr(g,S);y.setLayout({x:w.x,y:w.y,rawX:g,rawY:S},!0)});else{var m=r.getOrient();m==="RL"||m==="LR"?(d=i/(v.getLayout().x+f+h),p=n/(c.depth-1||1),nr(l,function(y){S=(y.getLayout().x+h)*d,g=m==="LR"?(y.depth-1)*p:n-(y.depth-1)*p,y.setLayout({x:g,y:S},!0)})):(m==="TB"||m==="BT")&&(p=n/(v.getLayout().x+f+h),d=i/(c.depth-1||1),nr(l,function(y){g=(y.getLayout().x+h)*p,S=m==="TB"?(y.depth-1)*d:i-(y.depth-1)*d,y.setLayout({x:g,y:S},!0)}))}}}function Rp(r){r.eachSeriesByType("tree",function(e){var t=e.getData(),a=t.tree;a.eachNode(function(n){var i=n.getModel(),o=i.getModel("itemStyle").getItemStyle(),s=t.ensureUniqueItemVisual(n.dataIndex,"style");H(s,o)})})}function Vp(r){r.registerAction({type:"treeExpandAndCollapse",event:"treeExpandAndCollapse",update:"update"},function(e,t){t.eachComponent({mainType:"series",subType:"tree",query:e},function(a){var n=e.dataIndex,i=a.getData().tree,o=i.getNodeByDataIndex(n);o.isExpand=!o.isExpand})}),r.registerAction({type:"treeRoam",event:"treeRoam",update:"none"},function(e,t,a){t.eachComponent({mainType:"series",subType:"tree",query:e},function(n){var i=n.coordinateSystem,o=ii(i,e,void 0,a);n.setCenter&&n.setCenter(o.center),n.setZoom&&n.setZoom(o.zoom)})})}function Np(r){r.registerChartView(Sp),r.registerSeriesModel(Lp),r.registerLayout(Mp),r.registerVisual(Rp),Vp(r)}var Mo=["treemapZoomToNode","treemapRender","treemapMove"];function Gp(r){for(var e=0;e<Mo.length;e++)r.registerAction({type:Mo[e],update:"updateView"},je);r.registerAction({type:"treemapRootToNode",update:"updateView"},function(t,a){a.eachComponent({mainType:"series",subType:"treemap",query:t},n);function n(i,o){var s=["treemapZoomToNode","treemapRootToNode"],l=xr(t,s,i);if(l){var u=i.getViewRoot();u&&(t.direction=li(u,l.node)?"rollUp":"drillDown"),i.resetViewRoot(l.node)}}})}function _u(r){var e=r.getData(),t=e.tree,a={};t.eachNode(function(n){for(var i=n;i&&i.depth>1;)i=i.parentNode;var o=pn(r.ecModel,i.name||i.dataIndex+"",a);n.setVisual("decal",o)})}var kp=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t.preventUsingHoverLayer=!0,t}return e.prototype.getInitialData=function(t,a){var n={name:t.name,children:t.data};Au(n);var i=t.levels||[],o=this.designatedVisualItemStyle={},s=new Rt({itemStyle:o},this,a);i=t.levels=zp(i,a);var l=O(i||[],function(c){return new Rt(c,s,a)},this),u=si.createTree(n,this,v);function v(c){c.wrapMethod("getItemModel",function(f,h){var p=u.getNodeByDataIndex(h),d=p?l[p.depth]:null;return f.parentModel=d||s,f})}return u.data},e.prototype.optionUpdated=function(){this.resetViewRoot()},e.prototype.formatTooltip=function(t,a,n){var i=this.getData(),o=this.getRawValue(t),s=i.getName(t);return Ft("nameValue",{name:s,value:o})},e.prototype.getDataParams=function(t){var a=r.prototype.getDataParams.apply(this,arguments),n=this.getData().tree.getNodeByDataIndex(t);return a.treeAncestors=Aa(n,this),a.treePathInfo=a.treeAncestors,a},e.prototype.setLayoutInfo=function(t){this.layoutInfo=this.layoutInfo||{},H(this.layoutInfo,t)},e.prototype.mapIdToIndex=function(t){var a=this._idIndexMap;a||(a=this._idIndexMap=K(),this._idIndexMapCount=0);var n=a.get(t);return n==null&&a.set(t,n=this._idIndexMapCount++),n},e.prototype.getViewRoot=function(){return this._viewRoot},e.prototype.resetViewRoot=function(t){t?this._viewRoot=t:t=this._viewRoot;var a=this.getRawData().tree.root;(!t||t!==a&&!a.contains(t))&&(this._viewRoot=a)},e.prototype.enableAriaDecal=function(){_u(this)},e.type="series.treemap",e.layoutMode="box",e.defaultOption={progressive:0,left:"center",top:"middle",width:"80%",height:"80%",sort:!0,clipWindow:"origin",squareRatio:.5*(1+Math.sqrt(5)),leafDepth:null,drillDownIcon:"▶",zoomToNodeRatio:.32*.32,scaleLimit:null,roam:!0,nodeClick:"zoomToNode",animation:!0,animationDurationUpdate:900,animationEasing:"quinticInOut",breadcrumb:{show:!0,height:22,left:"center",top:"bottom",emptyItemWidth:25,itemStyle:{color:"rgba(0,0,0,0.7)",textStyle:{color:"#fff"}},emphasis:{itemStyle:{color:"rgba(0,0,0,0.9)"}}},label:{show:!0,distance:0,padding:5,position:"inside",color:"#fff",overflow:"truncate"},upperLabel:{show:!1,position:[0,"50%"],height:20,overflow:"truncate",verticalAlign:"middle"},itemStyle:{color:null,colorAlpha:null,colorSaturation:null,borderWidth:0,gapWidth:0,borderColor:"#fff",borderColorSaturation:null},emphasis:{upperLabel:{show:!0,position:[0,"50%"],overflow:"truncate",verticalAlign:"middle"}},visualDimension:0,visualMin:null,visualMax:null,color:[],colorAlpha:null,colorSaturation:null,colorMappingBy:"index",visibleMin:10,childrenVisibleMin:null,levels:[]},e}(xt);function Au(r){var e=0;P(r.children,function(a){Au(a);var n=a.value;Y(n)&&(n=n[0]),e+=n});var t=r.value;Y(t)&&(t=t[0]),(t==null||isNaN(t))&&(t=e),t<0&&(t=0),Y(r.value)?r.value[0]=t:r.value=t}function zp(r,e){var t=Ot(e.get("color")),a=Ot(e.get(["aria","decal","decals"]));if(t){r=r||[];var n,i;P(r,function(s){var l=new Rt(s),u=l.get("color"),v=l.get("decal");(l.get(["itemStyle","color"])||u&&u!=="none")&&(n=!0),(l.get(["itemStyle","decal"])||v&&v!=="none")&&(i=!0)});var o=r[0]||(r[0]={});return n||(o.color=t.slice()),!i&&a&&(o.decal=a.slice()),r}}var Op=8,Eo=8,za=5,Bp=function(){function r(e){this.group=new Z,e.add(this.group)}return r.prototype.render=function(e,t,a,n){var i=e.getModel("breadcrumb"),o=this.group;if(o.removeAll(),!(!i.get("show")||!a)){var s=i.getModel("itemStyle"),l=i.getModel("emphasis"),u=s.getModel("textStyle"),v=l.getModel(["itemStyle","textStyle"]),c={pos:{left:i.get("left"),right:i.get("right"),top:i.get("top"),bottom:i.get("bottom")},box:{width:t.getWidth(),height:t.getHeight()},emptyItemWidth:i.get("emptyItemWidth"),totalWidth:0,renderList:[]};this._prepare(a,c,u),this._renderContent(e,c,s,l,u,v,n),nc(o,c.pos,c.box)}},r.prototype._prepare=function(e,t,a){for(var n=e;n;n=n.parentNode){var i=da(n.getModel().get("name"),""),o=a.getTextRect(i),s=Math.max(o.width+Op*2,t.emptyItemWidth);t.totalWidth+=s+Eo,t.renderList.push({node:n,text:i,width:s})}},r.prototype._renderContent=function(e,t,a,n,i,o,s){for(var l=0,u=t.emptyItemWidth,v=e.get(["breadcrumb","height"]),c=ic(t.pos,t.box),f=t.totalWidth,h=t.renderList,p=n.getModel("itemStyle").getItemStyle(),d=h.length-1;d>=0;d--){var g=h[d],S=g.node,m=g.width,y=g.text;f>c.width&&(f-=m-u,m=u,y=null);var w=new Jt({shape:{points:Fp(l,0,m,v,d===h.length-1,d===0)},style:j(a.getItemStyle(),{lineJoin:"bevel"}),textContent:new qt({style:$t(i,{text:y})}),textConfig:{position:"inside"},z2:Dr*1e4,onclick:St(s,S)});w.disableLabelAnimation=!0,w.getTextContent().ensureState("emphasis").style=$t(o,{text:y}),w.ensureState("emphasis").style=p,ft(w,n.get("focus"),n.get("blurScope"),n.get("disabled")),this.group.add(w),Hp(w,e,S),l+=m+Eo}},r.prototype.remove=function(){this.group.removeAll()},r}();function Fp(r,e,t,a,n,i){var o=[[n?r:r-za,e],[r+t,e],[r+t,e+a],[n?r:r-za,e+a]];return!i&&o.splice(2,0,[r+t+za,e+a/2]),!n&&o.push([r,e+a/2]),o}function Hp(r,e,t){ht(r).eventData={componentType:"series",componentSubType:"treemap",componentIndex:e.componentIndex,seriesIndex:e.seriesIndex,seriesName:e.name,seriesType:"treemap",selfType:"breadcrumb",nodeData:{dataIndex:t&&t.dataIndex,name:t&&t.name},treePathInfo:t&&Aa(t,e)}}var Wp=function(){function r(){this._storage=[],this._elExistsMap={}}return r.prototype.add=function(e,t,a,n,i){return this._elExistsMap[e.id]?!1:(this._elExistsMap[e.id]=!0,this._storage.push({el:e,target:t,duration:a,delay:n,easing:i}),!0)},r.prototype.finished=function(e){return this._finishedCallback=e,this},r.prototype.start=function(){for(var e=this,t=this._storage.length,a=function(){t--,t<=0&&(e._storage.length=0,e._elExistsMap={},e._finishedCallback&&e._finishedCallback())},n=0,i=this._storage.length;n<i;n++){var o=this._storage[n];o.el.animateTo(o.target,{duration:o.duration,delay:o.delay,easing:o.easing,setToFinal:!0,done:a,aborted:a})}return this},r}();function Up(){return new Wp}var An=Z,Ro=It,Vo=3,No="label",Go="upperLabel",$p=Dr*10,Yp=Dr*2,Zp=Dr*3,De=Al([["fill","color"],["stroke","strokeColor"],["lineWidth","strokeWidth"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"]]),ko=function(r){var e=De(r);return e.stroke=e.fill=e.lineWidth=null,e},ra=re(),Xp=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t._state="ready",t._storage=ir(),t}return e.prototype.render=function(t,a,n,i){var o=a.findComponents({mainType:"series",subType:"treemap",query:i});if(!(xe(o,t)<0)){this.seriesModel=t,this.api=n,this.ecModel=a;var s=["treemapZoomToNode","treemapRootToNode"],l=xr(i,s,t),u=i&&i.type,v=t.layoutInfo,c=!this._oldTree,f=this._storage,h=u==="treemapRootToNode"&&l&&f?{rootNodeGroup:f.nodeGroup[l.node.getRawIndex()],direction:i.direction}:null,p=this._giveContainerGroup(v),d=t.get("animation"),g=this._doRender(p,t,h);d&&!c&&(!u||u==="treemapZoomToNode"||u==="treemapRootToNode")?this._doAnimation(p,g,t,h):g.renderFinally(),this._resetController(n),this._renderBreadcrumb(t,n,l)}},e.prototype._giveContainerGroup=function(t){var a=this._containerGroup;return a||(a=this._containerGroup=new An,this._initEvents(a),this.group.add(a)),a.x=t.x,a.y=t.y,a},e.prototype._doRender=function(t,a,n){var i=a.getData().tree,o=this._oldTree,s=ir(),l=ir(),u=this._storage,v=[];function c(m,y,w,x){return qp(a,l,u,n,s,v,m,y,w,x)}d(i.root?[i.root]:[],o&&o.root?[o.root]:[],t,i===o||!o,0);var f=g(u);if(this._oldTree=i,this._storage=l,this._controllerHost){var h=this.seriesModel.layoutInfo,p=i.root.getLayout();p.width===h.width&&p.height===h.height&&(this._controllerHost.zoom=1)}return{lastsForAnimation:s,willDeleteEls:f,renderFinally:S};function d(m,y,w,x,b){x?(y=m,P(m,function(I,A){!I.isRemoved()&&T(A,A)})):new Ke(y,m,_,_).add(T).update(T).remove(St(T,null)).execute();function _(I){return I.getId()}function T(I,A){var D=I!=null?m[I]:null,E=A!=null?y[A]:null,M=c(D,E,w,b);M&&d(D&&D.viewChildren||[],E&&E.viewChildren||[],M,x,b+1)}}function g(m){var y=ir();return m&&P(m,function(w,x){var b=y[x];P(w,function(_){_&&(b.push(_),ra(_).willDelete=!0)})}),y}function S(){P(f,function(m){P(m,function(y){y.parent&&y.parent.remove(y)})}),P(v,function(m){m.invisible=!0,m.dirty()})}},e.prototype._doAnimation=function(t,a,n,i){var o=n.get("animationDurationUpdate"),s=n.get("animationEasing"),l=(st(o)?0:o)||0,u=(st(s)?null:s)||"cubicOut",v=Up();P(a.willDeleteEls,function(c,f){P(c,function(h,p){if(!h.invisible){var d=h.parent,g,S=ra(d);if(i&&i.direction==="drillDown")g=d===i.rootNodeGroup?{shape:{x:0,y:0,width:S.nodeWidth,height:S.nodeHeight},style:{opacity:0}}:{style:{opacity:0}};else{var m=0,y=0;S.willDelete||(m=S.nodeWidth/2,y=S.nodeHeight/2),g=f==="nodeGroup"?{x:m,y,style:{opacity:0}}:{shape:{x:m,y,width:0,height:0},style:{opacity:0}}}g&&v.add(h,g,l,0,u)}})}),P(this._storage,function(c,f){P(c,function(h,p){var d=a.lastsForAnimation[f][p],g={};d&&(h instanceof Z?d.oldX!=null&&(g.x=h.x,g.y=h.y,h.x=d.oldX,h.y=d.oldY):(d.oldShape&&(g.shape=H({},h.shape),h.setShape(d.oldShape)),d.fadein?(h.setStyle("opacity",0),g.style={opacity:1}):h.style.opacity!==1&&(g.style={opacity:1})),v.add(h,g,l,0,u))})},this),this._state="animating",v.finished(lt(function(){this._state="ready",a.renderFinally()},this)).start()},e.prototype._resetController=function(t){var a=this._controller,n=this._controllerHost;n||(this._controllerHost={target:this.group},n=this._controllerHost),a||(a=this._controller=new Sa(t.getZr()),a.enable(this.seriesModel.get("roam")),n.zoomLimit=this.seriesModel.get("scaleLimit"),n.zoom=this.seriesModel.get("zoom"),a.on("pan",lt(this._onPan,this)),a.on("zoom",lt(this._onZoom,this)));var i=new dt(0,0,t.getWidth(),t.getHeight());a.setPointerChecker(function(o,s,l){return i.contain(s,l)})},e.prototype._clearController=function(){var t=this._controller;this._controllerHost=null,t&&(t.dispose(),t=null)},e.prototype._onPan=function(t){if(this._state!=="animating"&&(Math.abs(t.dx)>Vo||Math.abs(t.dy)>Vo)){var a=this.seriesModel.getData().tree.root;if(!a)return;var n=a.getLayout();if(!n)return;this.api.dispatchAction({type:"treemapMove",from:this.uid,seriesId:this.seriesModel.id,rootRect:{x:n.x+t.dx,y:n.y+t.dy,width:n.width,height:n.height}})}},e.prototype._onZoom=function(t){var a=t.originX,n=t.originY,i=t.scale;if(this._state!=="animating"){var o=this.seriesModel.getData().tree.root;if(!o)return;var s=o.getLayout();if(!s)return;var l=new dt(s.x,s.y,s.width,s.height),u=null,v=this._controllerHost;u=v.zoomLimit;var c=v.zoom=v.zoom||1;if(c*=i,u){var f=u.min||0,h=u.max||1/0;c=Math.max(Math.min(h,c),f)}var p=c/v.zoom;v.zoom=c;var d=this.seriesModel.layoutInfo;a-=d.x,n-=d.y;var g=Ir();yr(g,g,[-a,-n]),Sl(g,g,[p,p]),yr(g,g,[a,n]),l.applyTransform(g),this.api.dispatchAction({type:"treemapRender",from:this.uid,seriesId:this.seriesModel.id,rootRect:{x:l.x,y:l.y,width:l.width,height:l.height}})}},e.prototype._initEvents=function(t){var a=this;t.on("click",function(n){if(a._state==="ready"){var i=a.seriesModel.get("nodeClick",!0);if(i){var o=a.findTarget(n.offsetX,n.offsetY);if(o){var s=o.node;if(s.getLayout().isLeafRoot)a._rootToNode(o);else if(i==="zoomToNode")a._zoomToNode(o);else if(i==="link"){var l=s.hostTree.data.getItemModel(s.dataIndex),u=l.get("link",!0),v=l.get("target",!0)||"blank";u&&_l(u,v)}}}}},this)},e.prototype._renderBreadcrumb=function(t,a,n){var i=this;n||(n=t.get("leafDepth",!0)!=null?{node:t.getViewRoot()}:this.findTarget(a.getWidth()/2,a.getHeight()/2),n||(n={node:t.getData().tree.root})),(this._breadcrumb||(this._breadcrumb=new Bp(this.group))).render(t,a,n.node,function(o){i._state!=="animating"&&(li(t.getViewRoot(),o)?i._rootToNode({node:o}):i._zoomToNode({node:o}))})},e.prototype.remove=function(){this._clearController(),this._containerGroup&&this._containerGroup.removeAll(),this._storage=ir(),this._state="ready",this._breadcrumb&&this._breadcrumb.remove()},e.prototype.dispose=function(){this._clearController()},e.prototype._zoomToNode=function(t){this.api.dispatchAction({type:"treemapZoomToNode",from:this.uid,seriesId:this.seriesModel.id,targetNode:t.node})},e.prototype._rootToNode=function(t){this.api.dispatchAction({type:"treemapRootToNode",from:this.uid,seriesId:this.seriesModel.id,targetNode:t.node})},e.prototype.findTarget=function(t,a){var n,i=this.seriesModel.getViewRoot();return i.eachNode({attr:"viewChildren",order:"preorder"},function(o){var s=this._storage.background[o.getRawIndex()];if(s){var l=s.transformCoordToLocal(t,a),u=s.shape;if(u.x<=l[0]&&l[0]<=u.x+u.width&&u.y<=l[1]&&l[1]<=u.y+u.height)n={node:o,offsetX:l[0],offsetY:l[1]};else return!1}},this),n},e.type="treemap",e}(gt);function ir(){return{nodeGroup:[],background:[],content:[]}}function qp(r,e,t,a,n,i,o,s,l,u){if(!o)return;var v=o.getLayout(),c=r.getData(),f=o.getModel();if(c.setItemGraphicEl(o.dataIndex,null),!v||!v.isInView)return;var h=v.width,p=v.height,d=v.borderWidth,g=v.invisible,S=o.getRawIndex(),m=s&&s.getRawIndex(),y=o.viewChildren,w=v.upperHeight,x=y&&y.length,b=f.getModel("itemStyle"),_=f.getModel(["emphasis","itemStyle"]),T=f.getModel(["blur","itemStyle"]),I=f.getModel(["select","itemStyle"]),A=b.get("borderRadius")||0,D=rt("nodeGroup",An);if(!D)return;if(l.add(D),D.x=v.x||0,D.y=v.y||0,D.markRedraw(),ra(D).nodeWidth=h,ra(D).nodeHeight=p,v.isAboveViewRoot)return D;var E=rt("background",Ro,u,Yp);E&&B(D,E,x&&v.upperLabelHeight);var M=f.getModel("emphasis"),C=M.get("focus"),L=M.get("blurScope"),R=M.get("disabled"),V=C==="ancestor"?o.getAncestorsIndices():C==="descendant"?o.getDescendantIndices():C;if(x)Ni(D)&&Rr(D,!1),E&&(Rr(E,!R),c.setItemGraphicEl(o.dataIndex,E),Gi(E,V,L));else{var N=rt("content",Ro,u,Zp);N&&W(D,N),E.disableMorphing=!0,E&&Ni(E)&&Rr(E,!1),Rr(D,!R),c.setItemGraphicEl(o.dataIndex,D);var k=f.getShallow("cursor");k&&N.attr("cursor",k),Gi(D,V,L)}return D;function B(U,F,Q){var q=ht(F);if(q.dataIndex=o.dataIndex,q.seriesIndex=r.seriesIndex,F.setShape({x:0,y:0,width:h,height:p,r:A}),g)X(F);else{F.invisible=!1;var at=o.getVisual("style"),vt=at.stroke,bt=ko(b);bt.fill=vt;var nt=De(_);nt.fill=_.get("borderColor");var yt=De(T);yt.fill=T.get("borderColor");var _t=De(I);if(_t.fill=I.get("borderColor"),Q){var Nt=h-2*d;tt(F,vt,at.opacity,{x:d,y:0,width:Nt,height:w})}else F.removeTextContent();F.setStyle(bt),F.ensureState("emphasis").style=nt,F.ensureState("blur").style=yt,F.ensureState("select").style=_t,qe(F)}U.add(F)}function W(U,F){var Q=ht(F);Q.dataIndex=o.dataIndex,Q.seriesIndex=r.seriesIndex;var q=Math.max(h-2*d,0),at=Math.max(p-2*d,0);if(F.culling=!0,F.setShape({x:d,y:d,width:q,height:at,r:A}),g)X(F);else{F.invisible=!1;var vt=o.getVisual("style"),bt=vt.fill,nt=ko(b);nt.fill=bt,nt.decal=vt.decal;var yt=De(_),_t=De(T),Nt=De(I);tt(F,bt,vt.opacity,null),F.setStyle(nt),F.ensureState("emphasis").style=yt,F.ensureState("blur").style=_t,F.ensureState("select").style=Nt,qe(F)}U.add(F)}function X(U){!U.invisible&&i.push(U)}function tt(U,F,Q,q){var at=f.getModel(q?Go:No),vt=da(f.get("name"),null),bt=at.getShallow("show");Bt(U,Et(f,q?Go:No),{defaultText:bt?vt:null,inheritColor:F,defaultOpacity:Q,labelFetcher:r,labelDataIndex:o.dataIndex});var nt=U.getTextContent();if(nt){var yt=nt.style,_t=oc(yt.padding||0);q&&(U.setTextConfig({layoutRect:q}),nt.disableLabelLayout=!0),nt.beforeUpdate=function(){var be=Math.max((q?q.width:U.shape.width)-_t[1]-_t[3],0),tr=Math.max((q?q.height:U.shape.height)-_t[0]-_t[2],0);(yt.width!==be||yt.height!==tr)&&nt.setStyle({width:be,height:tr})},yt.truncateMinChar=2,yt.lineOverflow="truncate",J(yt,q,v);var Nt=nt.getState("emphasis");J(Nt?Nt.style:null,q,v)}}function J(U,F,Q){var q=U?U.text:null;if(!F&&Q.isLeafRoot&&q!=null){var at=r.get("drillDownIcon",!0);U.text=at?at+" "+q:q}}function rt(U,F,Q,q){var at=m!=null&&t[U][m],vt=n[U];return at?(t[U][m]=null,pt(vt,at)):g||(at=new F,at instanceof mr&&(at.z2=jp(Q,q)),Ht(vt,at)),e[U][S]=at}function pt(U,F){var Q=U[S]={};F instanceof An?(Q.oldX=F.x,Q.oldY=F.y):Q.oldShape=H({},F.shape)}function Ht(U,F){var Q=U[S]={},q=o.parentNode,at=F instanceof Z;if(q&&(!a||a.direction==="drillDown")){var vt=0,bt=0,nt=n.background[q.getRawIndex()];!a&&nt&&nt.oldShape&&(vt=nt.oldShape.width,bt=nt.oldShape.height),at?(Q.oldX=0,Q.oldY=bt):Q.oldShape={x:vt,y:bt,width:0,height:0}}Q.fadein=!at}}function jp(r,e){return r*$p+e}var Kp="itemStyle",Tu=re();const Jp={seriesType:"treemap",reset:function(r){var e=r.getData().tree,t=e.root;t.isRemoved()||Iu(t,{},r.getViewRoot().getAncestors(),r)}};function Iu(r,e,t,a){var n=r.getModel(),i=r.getLayout(),o=r.hostTree.data;if(!(!i||i.invisible||!i.isInView)){var s=n.getModel(Kp),l=Qp(s,e,a),u=o.ensureUniqueItemVisual(r.dataIndex,"style"),v=s.get("borderColor"),c=s.get("borderColorSaturation"),f;c!=null&&(f=zo(l),v=td(c,f)),u.stroke=v;var h=r.viewChildren;if(!h||!h.length)f=zo(l),u.fill=f;else{var p=ed(r,n,i,s,l,h);P(h,function(d,g){if(d.depth>=t.length||d===t[d.depth]){var S=rd(n,l,d,g,p,a);Iu(d,S,t,a)}})}}}function Qp(r,e,t){var a=H({},e),n=t.designatedVisualItemStyle;return P(["color","colorAlpha","colorSaturation"],function(i){n[i]=e[i];var o=r.get(i);n[i]=null,o!=null&&(a[i]=o)}),a}function zo(r){var e=Oa(r,"color");if(e){var t=Oa(r,"colorAlpha"),a=Oa(r,"colorSaturation");return a&&(e=Tl(e,null,null,a)),t&&(e=dn(e,t)),e}}function td(r,e){return e!=null?Tl(e,null,null,r):null}function Oa(r,e){var t=r[e];if(t!=null&&t!=="none")return t}function ed(r,e,t,a,n,i){if(!(!i||!i.length)){var o=Ba(e,"color")||n.color!=null&&n.color!=="none"&&(Ba(e,"colorAlpha")||Ba(e,"colorSaturation"));if(o){var s=e.get("visualMin"),l=e.get("visualMax"),u=t.dataExtent.slice();s!=null&&s<u[0]&&(u[0]=s),l!=null&&l>u[1]&&(u[1]=l);var v=e.get("colorMappingBy"),c={type:o.name,dataExtent:u,visual:o.range};c.type==="color"&&(v==="index"||v==="id")?(c.mappingMethod="category",c.loop=!0):c.mappingMethod="linear";var f=new Kl(c);return Tu(f).drColorMappingBy=v,f}}}function Ba(r,e){var t=r.get(e);return Y(t)&&t.length?{name:e,range:t}:null}function rd(r,e,t,a,n,i){var o=H({},e);if(n){var s=n.type,l=s==="color"&&Tu(n).drColorMappingBy,u=l==="index"?a:l==="id"?i.mapIdToIndex(t.getId()):t.getValue(r.get("visualDimension"));o[s]=n.mapValueToVisual(u)}return o}var br=Math.max,aa=Math.min,Oo=zt,ui=P,Du=["itemStyle","borderWidth"],ad=["itemStyle","gapWidth"],nd=["upperLabel","show"],id=["upperLabel","height"];const od={seriesType:"treemap",reset:function(r,e,t,a){var n=t.getWidth(),i=t.getHeight(),o=r.option,s=Se(r.getBoxLayoutParams(),{width:t.getWidth(),height:t.getHeight()}),l=o.size||[],u=z(Oo(s.width,l[0]),n),v=z(Oo(s.height,l[1]),i),c=a&&a.type,f=["treemapZoomToNode","treemapRootToNode"],h=xr(a,f,r),p=c==="treemapRender"||c==="treemapMove"?a.rootRect:null,d=r.getViewRoot(),g=wu(d);if(c!=="treemapMove"){var S=c==="treemapZoomToNode"?fd(r,h,d,u,v):p?[p.width,p.height]:[u,v],m=o.sort;m&&m!=="asc"&&m!=="desc"&&(m="desc");var y={squareRatio:o.squareRatio,sort:m,leafDepth:o.leafDepth};d.hostTree.clearLayouts();var w={x:0,y:0,width:S[0],height:S[1],area:S[0]*S[1]};d.setLayout(w),Cu(d,y,!1,0),w=d.getLayout(),ui(g,function(b,_){var T=(g[_+1]||d).getValue();b.setLayout(H({dataExtent:[T,T],borderWidth:0,upperHeight:0},w))})}var x=r.getData().tree.root;x.setLayout(hd(s,p,h),!0),r.setLayoutInfo(s),Lu(x,new dt(-s.x,-s.y,n,i),g,d,0)}};function Cu(r,e,t,a){var n,i;if(!r.isRemoved()){var o=r.getLayout();n=o.width,i=o.height;var s=r.getModel(),l=s.get(Du),u=s.get(ad)/2,v=Pu(s),c=Math.max(l,v),f=l-u,h=c-u;r.setLayout({borderWidth:l,upperHeight:c,upperLabelHeight:v},!0),n=br(n-2*f,0),i=br(i-f-h,0);var p=n*i,d=sd(r,s,p,e,t,a);if(d.length){var g={x:f,y:h,width:n,height:i},S=aa(n,i),m=1/0,y=[];y.area=0;for(var w=0,x=d.length;w<x;){var b=d[w];y.push(b),y.area+=b.getLayout().area;var _=cd(y,S,e.squareRatio);_<=m?(w++,m=_):(y.area-=y.pop().getLayout().area,Bo(y,S,g,u,!1),S=aa(g.width,g.height),y.length=y.area=0,m=1/0)}if(y.length&&Bo(y,S,g,u,!0),!t){var T=s.get("childrenVisibleMin");T!=null&&p<T&&(t=!0)}for(var w=0,x=d.length;w<x;w++)Cu(d[w],e,t,a+1)}}}function sd(r,e,t,a,n,i){var o=r.children||[],s=a.sort;s!=="asc"&&s!=="desc"&&(s=null);var l=a.leafDepth!=null&&a.leafDepth<=i;if(n&&!l)return r.viewChildren=[];o=Pt(o,function(h){return!h.isRemoved()}),ud(o,s);var u=vd(e,o,s);if(u.sum===0)return r.viewChildren=[];if(u.sum=ld(e,t,u.sum,s,o),u.sum===0)return r.viewChildren=[];for(var v=0,c=o.length;v<c;v++){var f=o[v].getValue()/u.sum*t;o[v].setLayout({area:f})}return l&&(o.length&&r.setLayout({isLeafRoot:!0},!0),o.length=0),r.viewChildren=o,r.setLayout({dataExtent:u.dataExtent},!0),o}function ld(r,e,t,a,n){if(!a)return t;for(var i=r.get("visibleMin"),o=n.length,s=o,l=o-1;l>=0;l--){var u=n[a==="asc"?o-l-1:l].getValue();u/t*e<i&&(s=l,t-=u)}return a==="asc"?n.splice(0,o-s):n.splice(s,o-s),t}function ud(r,e){return e&&r.sort(function(t,a){var n=e==="asc"?t.getValue()-a.getValue():a.getValue()-t.getValue();return n===0?e==="asc"?t.dataIndex-a.dataIndex:a.dataIndex-t.dataIndex:n}),r}function vd(r,e,t){for(var a=0,n=0,i=e.length;n<i;n++)a+=e[n].getValue();var o=r.get("visualDimension"),s;return!e||!e.length?s=[NaN,NaN]:o==="value"&&t?(s=[e[e.length-1].getValue(),e[0].getValue()],t==="asc"&&s.reverse()):(s=[1/0,-1/0],ui(e,function(l){var u=l.getValue(o);u<s[0]&&(s[0]=u),u>s[1]&&(s[1]=u)})),{sum:a,dataExtent:s}}function cd(r,e,t){for(var a=0,n=1/0,i=0,o=void 0,s=r.length;i<s;i++)o=r[i].getLayout().area,o&&(o<n&&(n=o),o>a&&(a=o));var l=r.area*r.area,u=e*e*t;return l?br(u*a/l,l/(u*n)):1/0}function Bo(r,e,t,a,n){var i=e===t.width?0:1,o=1-i,s=["x","y"],l=["width","height"],u=t[s[i]],v=e?r.area/e:0;(n||v>t[l[o]])&&(v=t[l[o]]);for(var c=0,f=r.length;c<f;c++){var h=r[c],p={},d=v?h.getLayout().area/v:0,g=p[l[o]]=br(v-2*a,0),S=t[s[i]]+t[l[i]]-u,m=c===f-1||S<d?S:d,y=p[l[i]]=br(m-2*a,0);p[s[o]]=t[s[o]]+aa(a,g/2),p[s[i]]=u+aa(a,y/2),u+=m,h.setLayout(p,!0)}t[s[o]]+=v,t[l[o]]-=v}function fd(r,e,t,a,n){var i=(e||{}).node,o=[a,n];if(!i||i===t)return o;for(var s,l=a*n,u=l*r.option.zoomToNodeRatio;s=i.parentNode;){for(var v=0,c=s.children,f=0,h=c.length;f<h;f++)v+=c[f].getValue();var p=i.getValue();if(p===0)return o;u*=v/p;var d=s.getModel(),g=d.get(Du),S=Math.max(g,Pu(d));u+=4*g*g+(3*g+S)*Math.pow(u,.5),u>ki&&(u=ki),i=s}u<l&&(u=l);var m=Math.pow(u/l,.5);return[a*m,n*m]}function hd(r,e,t){if(e)return{x:e.x,y:e.y};var a={x:0,y:0};if(!t)return a;var n=t.node,i=n.getLayout();if(!i)return a;for(var o=[i.width/2,i.height/2],s=n;s;){var l=s.getLayout();o[0]+=l.x,o[1]+=l.y,s=s.parentNode}return{x:r.width/2-o[0],y:r.height/2-o[1]}}function Lu(r,e,t,a,n){var i=r.getLayout(),o=t[n],s=o&&o===r;if(!(o&&!s||n===t.length&&r!==a)){r.setLayout({isInView:!0,invisible:!s&&!e.intersect(i),isAboveViewRoot:s},!0);var l=new dt(e.x-i.x,e.y-i.y,e.width,e.height);ui(r.viewChildren||[],function(u){Lu(u,l,t,a,n+1)})}}function Pu(r){return r.get(nd)?r.get(id):0}function pd(r){r.registerSeriesModel(kp),r.registerChartView(Xp),r.registerVisual(Jp),r.registerLayout(od),Gp(r)}function dd(r){var e=r.findComponents({mainType:"legend"});!e||!e.length||r.eachSeriesByType("graph",function(t){var a=t.getCategoriesData(),n=t.getGraph(),i=n.data,o=a.mapArray(a.getName);i.filterSelf(function(s){var l=i.getItemModel(s),u=l.getShallow("category");if(u!=null){Xt(u)&&(u=o[u]);for(var v=0;v<e.length;v++)if(!e[v].isSelected(u))return!1}return!0})})}function gd(r){var e={};r.eachSeriesByType("graph",function(t){var a=t.getCategoriesData(),n=t.getData(),i={};a.each(function(o){var s=a.getName(o);i["ec-"+s]=o;var l=a.getItemModel(o),u=l.getModel("itemStyle").getItemStyle();u.fill||(u.fill=t.getColorFromPalette(s,e)),a.setItemVisual(o,"style",u);for(var v=["symbol","symbolSize","symbolKeepAspect"],c=0;c<v.length;c++){var f=l.getShallow(v[c],!0);f!=null&&a.setItemVisual(o,v[c],f)}}),a.count()&&n.each(function(o){var s=n.getItemModel(o),l=s.getShallow("category");if(l!=null){et(l)&&(l=i["ec-"+l]);var u=a.getItemVisual(l,"style"),v=n.ensureUniqueItemVisual(o,"style");H(v,u);for(var c=["symbol","symbolSize","symbolKeepAspect"],f=0;f<c.length;f++)n.setItemVisual(o,c[f],a.getItemVisual(l,c[f]))}})})}function zr(r){return r instanceof Array||(r=[r,r]),r}function yd(r){r.eachSeriesByType("graph",function(e){var t=e.getGraph(),a=e.getEdgeData(),n=zr(e.get("edgeSymbol")),i=zr(e.get("edgeSymbolSize"));a.setVisual("fromSymbol",n&&n[0]),a.setVisual("toSymbol",n&&n[1]),a.setVisual("fromSymbolSize",i&&i[0]),a.setVisual("toSymbolSize",i&&i[1]),a.setVisual("style",e.getModel("lineStyle").getLineStyle()),a.each(function(o){var s=a.getItemModel(o),l=t.getEdgeByIndex(o),u=zr(s.getShallow("symbol",!0)),v=zr(s.getShallow("symbolSize",!0)),c=s.getModel("lineStyle").getLineStyle(),f=a.ensureUniqueItemVisual(o,"style");switch(H(f,c),f.stroke){case"source":{var h=l.node1.getVisual("style");f.stroke=h&&h.fill;break}case"target":{var h=l.node2.getVisual("style");f.stroke=h&&h.fill;break}}u[0]&&l.setVisual("fromSymbol",u[0]),u[1]&&l.setVisual("toSymbol",u[1]),v[0]&&l.setVisual("fromSymbolSize",v[0]),v[1]&&l.setVisual("toSymbolSize",v[1])})})}var Tn="-->",Ta=function(r){return r.get("autoCurveness")||null},Mu=function(r,e){var t=Ta(r),a=20,n=[];if(Xt(t))a=t;else if(Y(t)){r.__curvenessList=t;return}e>a&&(a=e);var i=a%2?a+2:a+3;n=[];for(var o=0;o<i;o++)n.push((o%2?o+1:o)/10*(o%2?-1:1));r.__curvenessList=n},wr=function(r,e,t){var a=[r.id,r.dataIndex].join("."),n=[e.id,e.dataIndex].join(".");return[t.uid,a,n].join(Tn)},Eu=function(r){var e=r.split(Tn);return[e[0],e[2],e[1]].join(Tn)},md=function(r,e){var t=wr(r.node1,r.node2,e);return e.__edgeMap[t]},Sd=function(r,e){var t=In(wr(r.node1,r.node2,e),e),a=In(wr(r.node2,r.node1,e),e);return t+a},In=function(r,e){var t=e.__edgeMap;return t[r]?t[r].length:0};function xd(r){Ta(r)&&(r.__curvenessList=[],r.__edgeMap={},Mu(r))}function bd(r,e,t,a){if(Ta(t)){var n=wr(r,e,t),i=t.__edgeMap,o=i[Eu(n)];i[n]&&!o?i[n].isForward=!0:o&&i[n]&&(o.isForward=!0,i[n].isForward=!1),i[n]=i[n]||[],i[n].push(a)}}function vi(r,e,t,a){var n=Ta(e),i=Y(n);if(!n)return null;var o=md(r,e);if(!o)return null;for(var s=-1,l=0;l<o.length;l++)if(o[l]===t){s=l;break}var u=Sd(r,e);Mu(e,u),r.lineStyle=r.lineStyle||{};var v=wr(r.node1,r.node2,e),c=e.__curvenessList,f=i||u%2?0:1;if(o.isForward)return c[f+s];var h=Eu(v),p=In(h,e),d=c[s+p+f];return a?i?n&&n[0]===0?(p+f)%2?d:-d:((p%2?0:1)+f)%2?d:-d:(p+f)%2?d:-d:c[s+p+f]}function Ru(r){var e=r.coordinateSystem;if(!(e&&e.type!=="view")){var t=r.getGraph();t.eachNode(function(a){var n=a.getModel();a.setLayout([+n.get("x"),+n.get("y")])}),ci(t,r)}}function ci(r,e){r.eachEdge(function(t,a){var n=Cr(t.getModel().get(["lineStyle","curveness"]),-vi(t,e,a,!0),0),i=fe(t.node1.getLayout()),o=fe(t.node2.getLayout()),s=[i,o];+n&&s.push([(i[0]+o[0])/2-(i[1]-o[1])*n,(i[1]+o[1])/2-(o[0]-i[0])*n]),t.setLayout(s)})}function wd(r,e){r.eachSeriesByType("graph",function(t){var a=t.get("layout"),n=t.coordinateSystem;if(n&&n.type!=="view"){var i=t.getData(),o=[];P(n.dimensions,function(f){o=o.concat(i.mapDimensionsAll(f))});for(var s=0;s<i.count();s++){for(var l=[],u=!1,v=0;v<o.length;v++){var c=i.get(o[v],s);isNaN(c)||(u=!0),l.push(c)}u?i.setItemLayout(s,n.dataToPoint(l)):i.setItemLayout(s,[NaN,NaN])}ci(i.graph,t)}else(!a||a==="none")&&Ru(t)})}function cr(r){var e=r.coordinateSystem;if(e.type!=="view")return 1;var t=r.option.nodeScaleRatio,a=e.scaleX,n=e.getZoom(),i=(n-1)*t+1;return i/a}function fr(r){var e=r.getVisual("symbolSize");return e instanceof Array&&(e=(e[0]+e[1])/2),+e}var Fo=Math.PI,Fa=[];function fi(r,e,t,a){var n=r.coordinateSystem;if(!(n&&n.type!=="view")){var i=n.getBoundingRect(),o=r.getData(),s=o.graph,l=i.width/2+i.x,u=i.height/2+i.y,v=Math.min(i.width,i.height)/2,c=o.count();if(o.setLayout({cx:l,cy:u}),!!c){if(t){var f=n.pointToData(a),h=f[0],p=f[1],d=[h-l,p-u];Un(d,d),sc(d,d,v),t.setLayout([l+d[0],u+d[1]],!0);var g=r.get(["circular","rotateLabel"]);Vu(t,g,l,u)}_d[e](r,s,o,v,l,u,c),s.eachEdge(function(S,m){var y=Cr(S.getModel().get(["lineStyle","curveness"]),vi(S,r,m),0),w=fe(S.node1.getLayout()),x=fe(S.node2.getLayout()),b,_=(w[0]+x[0])/2,T=(w[1]+x[1])/2;+y&&(y*=3,b=[l*y+_*(1-y),u*y+T*(1-y)]),S.setLayout([w,x,b])})}}}var _d={value:function(r,e,t,a,n,i,o){var s=0,l=t.getSum("value"),u=Math.PI*2/(l||o);e.eachNode(function(v){var c=v.getValue("value"),f=u*(l?c:1)/2;s+=f,v.setLayout([a*Math.cos(s)+n,a*Math.sin(s)+i]),s+=f})},symbolSize:function(r,e,t,a,n,i,o){var s=0;Fa.length=o;var l=cr(r);e.eachNode(function(c){var f=fr(c);isNaN(f)&&(f=2),f<0&&(f=0),f*=l;var h=Math.asin(f/2/a);isNaN(h)&&(h=Fo/2),Fa[c.dataIndex]=h,s+=h*2});var u=(2*Fo-s)/o/2,v=0;e.eachNode(function(c){var f=u+Fa[c.dataIndex];v+=f,(!c.getLayout()||!c.getLayout().fixed)&&c.setLayout([a*Math.cos(v)+n,a*Math.sin(v)+i]),v+=f})}};function Vu(r,e,t,a){var n=r.getGraphicEl();if(n){var i=r.getModel(),o=i.get(["label","rotate"])||0,s=n.getSymbolPath();if(e){var l=r.getLayout(),u=Math.atan2(l[1]-a,l[0]-t);u<0&&(u=Math.PI*2+u);var v=l[0]<t;v&&(u=u-Math.PI);var c=v?"left":"right";s.setTextConfig({rotation:-u,position:c,origin:"center"});var f=s.ensureState("emphasis");H(f.textConfig||(f.textConfig={}),{position:c})}else s.setTextConfig({rotation:o*=Math.PI/180})}}function Ad(r){r.eachSeriesByType("graph",function(e){e.get("layout")==="circular"&&fi(e,"symbolSize")})}var Be=gn;function Td(r,e,t){for(var a=r,n=e,i=t.rect,o=i.width,s=i.height,l=[i.x+o/2,i.y+s/2],u=t.gravity==null?.1:t.gravity,v=0;v<a.length;v++){var c=a[v];c.p||(c.p=lc(o*(Math.random()-.5)+l[0],s*(Math.random()-.5)+l[1])),c.pp=fe(c.p),c.edges=null}var f=t.friction==null?.6:t.friction,h=f,p,d;return{warmUp:function(){h=f*.8},setFixed:function(g){a[g].fixed=!0},setUnfixed:function(g){a[g].fixed=!1},beforeStep:function(g){p=g},afterStep:function(g){d=g},step:function(g){p&&p(a,n);for(var S=[],m=a.length,y=0;y<n.length;y++){var w=n[y];if(!w.ignoreForceLayout){var x=w.n1,b=w.n2;lr(S,b.p,x.p);var _=zi(S)-w.d,T=b.w/(x.w+b.w);isNaN(T)&&(T=0),Un(S,S),!x.fixed&&Be(x.p,x.p,S,T*_*h),!b.fixed&&Be(b.p,b.p,S,-(1-T)*_*h)}}for(var y=0;y<m;y++){var I=a[y];I.fixed||(lr(S,l,I.p),Be(I.p,I.p,S,u*h))}for(var y=0;y<m;y++)for(var x=a[y],A=y+1;A<m;A++){var b=a[A];lr(S,b.p,x.p);var _=zi(S);_===0&&(uc(S,Math.random()-.5,Math.random()-.5),_=1);var D=(x.rep+b.rep)/_/_;!x.fixed&&Be(x.pp,x.pp,S,D),!b.fixed&&Be(b.pp,b.pp,S,-D)}for(var E=[],y=0;y<m;y++){var I=a[y];I.fixed||(lr(E,I.p,I.pp),Be(I.p,I.p,E,h),wt(I.pp,I.p))}h=h*.992;var M=h<.01;d&&d(a,n,M),g&&g(M)}}}function Id(r){r.eachSeriesByType("graph",function(e){var t=e.coordinateSystem;if(!(t&&t.type!=="view"))if(e.get("layout")==="force"){var a=e.preservedPoints||{},n=e.getGraph(),i=n.data,o=n.edgeData,s=e.getModel("force"),l=s.get("initLayout");e.preservedPoints?i.each(function(y){var w=i.getId(y);i.setItemLayout(y,a[w]||[NaN,NaN])}):!l||l==="none"?Ru(e):l==="circular"&&fi(e,"value");var u=i.getDataExtent("value"),v=o.getDataExtent("value"),c=s.get("repulsion"),f=s.get("edgeLength"),h=Y(c)?c:[c,c],p=Y(f)?f:[f,f];p=[p[1],p[0]];var d=i.mapArray("value",function(y,w){var x=i.getItemLayout(w),b=Gt(y,u,h);return isNaN(b)&&(b=(h[0]+h[1])/2),{w:b,rep:b,fixed:i.getItemModel(w).get("fixed"),p:!x||isNaN(x[0])||isNaN(x[1])?null:x}}),g=o.mapArray("value",function(y,w){var x=n.getEdgeByIndex(w),b=Gt(y,v,p);isNaN(b)&&(b=(p[0]+p[1])/2);var _=x.getModel(),T=Cr(x.getModel().get(["lineStyle","curveness"]),-vi(x,e,w,!0),0);return{n1:d[x.node1.dataIndex],n2:d[x.node2.dataIndex],d:b,curveness:T,ignoreForceLayout:_.get("ignoreForceLayout")}}),S=t.getBoundingRect(),m=Td(d,g,{rect:S,gravity:s.get("gravity"),friction:s.get("friction")});m.beforeStep(function(y,w){for(var x=0,b=y.length;x<b;x++)y[x].fixed&&wt(y[x].p,n.getNodeByIndex(x).getLayout())}),m.afterStep(function(y,w,x){for(var b=0,_=y.length;b<_;b++)y[b].fixed||n.getNodeByIndex(b).setLayout(y[b].p),a[i.getId(b)]=y[b].p;for(var b=0,_=w.length;b<_;b++){var T=w[b],I=n.getEdgeByIndex(b),A=T.n1.p,D=T.n2.p,E=I.getLayout();E=E?E.slice():[],E[0]=E[0]||[],E[1]=E[1]||[],wt(E[0],A),wt(E[1],D),+T.curveness&&(E[2]=[(A[0]+D[0])/2-(A[1]-D[1])*T.curveness,(A[1]+D[1])/2-(D[0]-A[0])*T.curveness]),I.setLayout(E)}}),e.forceLayout=m,e.preservedPoints=a,m.step()}else e.forceLayout=null})}function Dd(r,e,t){var a=H(r.getBoxLayoutParams(),{aspect:t});return Se(a,{width:e.getWidth(),height:e.getHeight()})}function Cd(r,e){var t=[];return r.eachSeriesByType("graph",function(a){var n=a.get("coordinateSystem");if(!n||n==="view"){var i=a.getData(),o=i.mapArray(function(g){var S=i.getItemModel(g);return[+S.get("x"),+S.get("y")]}),s=[],l=[];pa(o,s,l),l[0]-s[0]===0&&(l[0]+=1,s[0]-=1),l[1]-s[1]===0&&(l[1]+=1,s[1]-=1);var u=(l[0]-s[0])/(l[1]-s[1]),v=Dd(a,e,u);isNaN(u)&&(s=[v.x,v.y],l=[v.x+v.width,v.y+v.height]);var c=l[0]-s[0],f=l[1]-s[1],h=v.width,p=v.height,d=a.coordinateSystem=new Mr;d.zoomLimit=a.get("scaleLimit"),d.setBoundingRect(s[0],s[1],c,f),d.setViewRect(v.x,v.y,h,p),d.setCenter(a.get("center"),e),d.setZoom(a.get("zoom")),t.push(d)}}),t}var Ha=[],Wa=[],Ua=[],Fe=Il,$a=cc,Ho=Math.abs;function Wo(r,e,t){for(var a=r[0],n=r[1],i=r[2],o=1/0,s,l=t*t,u=.1,v=.1;v<=.9;v+=.1){Ha[0]=Fe(a[0],n[0],i[0],v),Ha[1]=Fe(a[1],n[1],i[1],v);var c=Ho($a(Ha,e)-l);c<o&&(o=c,s=v)}for(var f=0;f<32;f++){var h=s+u;Wa[0]=Fe(a[0],n[0],i[0],s),Wa[1]=Fe(a[1],n[1],i[1],s),Ua[0]=Fe(a[0],n[0],i[0],h),Ua[1]=Fe(a[1],n[1],i[1],h);var c=$a(Wa,e)-l;if(Ho(c)<.01)break;var p=$a(Ua,e)-l;u/=2,c<0?p>=0?s=s+u:s=s-u:p>=0?s=s-u:s=s+u}return s}function Ya(r,e){var t=[],a=vc,n=[[],[],[]],i=[[],[]],o=[];e/=2,r.eachEdge(function(s,l){var u=s.getLayout(),v=s.getVisual("fromSymbol"),c=s.getVisual("toSymbol");u.__original||(u.__original=[fe(u[0]),fe(u[1])],u[2]&&u.__original.push(fe(u[2])));var f=u.__original;if(u[2]!=null){if(wt(n[0],f[0]),wt(n[1],f[2]),wt(n[2],f[1]),v&&v!=="none"){var h=fr(s.node1),p=Wo(n,f[0],h*e);a(n[0][0],n[1][0],n[2][0],p,t),n[0][0]=t[3],n[1][0]=t[4],a(n[0][1],n[1][1],n[2][1],p,t),n[0][1]=t[3],n[1][1]=t[4]}if(c&&c!=="none"){var h=fr(s.node2),p=Wo(n,f[1],h*e);a(n[0][0],n[1][0],n[2][0],p,t),n[1][0]=t[1],n[2][0]=t[2],a(n[0][1],n[1][1],n[2][1],p,t),n[1][1]=t[1],n[2][1]=t[2]}wt(u[0],n[0]),wt(u[1],n[2]),wt(u[2],n[1])}else{if(wt(i[0],f[0]),wt(i[1],f[1]),lr(o,i[1],i[0]),Un(o,o),v&&v!=="none"){var h=fr(s.node1);gn(i[0],i[0],o,h*e)}if(c&&c!=="none"){var h=fr(s.node2);gn(i[1],i[1],o,-h*e)}wt(u[0],i[0]),wt(u[1],i[1])}})}function Uo(r){return r.type==="view"}var Ld=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t,a){var n=new Jn,i=new Jl,o=this.group;this._controller=new Sa(a.getZr()),this._controllerHost={target:o},o.add(n.group),o.add(i.group),this._symbolDraw=n,this._lineDraw=i,this._firstRender=!0},e.prototype.render=function(t,a,n){var i=this,o=t.coordinateSystem;this._model=t;var s=this._symbolDraw,l=this._lineDraw,u=this.group;if(Uo(o)){var v={x:o.x,y:o.y,scaleX:o.scaleX,scaleY:o.scaleY};this._firstRender?u.attr(v):ct(u,v,t)}Ya(t.getGraph(),cr(t));var c=t.getData();s.updateData(c);var f=t.getEdgeData();l.updateData(f),this._updateNodeAndLinkScale(),this._updateController(t,a,n),clearTimeout(this._layoutTimeout);var h=t.forceLayout,p=t.get(["force","layoutAnimation"]);h&&this._startForceLayoutIteration(h,p);var d=t.get("layout");c.graph.eachNode(function(y){var w=y.dataIndex,x=y.getGraphicEl(),b=y.getModel();if(x){x.off("drag").off("dragend");var _=b.get("draggable");_&&x.on("drag",function(I){switch(d){case"force":h.warmUp(),!i._layouting&&i._startForceLayoutIteration(h,p),h.setFixed(w),c.setItemLayout(w,[x.x,x.y]);break;case"circular":c.setItemLayout(w,[x.x,x.y]),y.setLayout({fixed:!0},!0),fi(t,"symbolSize",y,[I.offsetX,I.offsetY]),i.updateLayout(t);break;case"none":default:c.setItemLayout(w,[x.x,x.y]),ci(t.getGraph(),t),i.updateLayout(t);break}}).on("dragend",function(){h&&h.setUnfixed(w)}),x.setDraggable(_,!!b.get("cursor"));var T=b.get(["emphasis","focus"]);T==="adjacency"&&(ht(x).focus=y.getAdjacentDataIndices())}}),c.graph.eachEdge(function(y){var w=y.getGraphicEl(),x=y.getModel().get(["emphasis","focus"]);w&&x==="adjacency"&&(ht(w).focus={edge:[y.dataIndex],node:[y.node1.dataIndex,y.node2.dataIndex]})});var g=t.get("layout")==="circular"&&t.get(["circular","rotateLabel"]),S=c.getLayout("cx"),m=c.getLayout("cy");c.graph.eachNode(function(y){Vu(y,g,S,m)}),this._firstRender=!1},e.prototype.dispose=function(){this.remove(),this._controller&&this._controller.dispose(),this._controllerHost=null},e.prototype._startForceLayoutIteration=function(t,a){var n=this;(function i(){t.step(function(o){n.updateLayout(n._model),(n._layouting=!o)&&(a?n._layoutTimeout=setTimeout(i,16):i())})})()},e.prototype._updateController=function(t,a,n){var i=this,o=this._controller,s=this._controllerHost,l=this.group;if(o.setPointerChecker(function(u,v,c){var f=l.getBoundingRect();return f.applyTransform(l.transform),f.contain(v,c)&&!Kn(u,n,t)}),!Uo(t.coordinateSystem)){o.disable();return}o.enable(t.get("roam")),s.zoomLimit=t.get("scaleLimit"),s.zoom=t.coordinateSystem.getZoom(),o.off("pan").off("zoom").on("pan",function(u){ri(s,u.dx,u.dy),n.dispatchAction({seriesId:t.id,type:"graphRoam",dx:u.dx,dy:u.dy})}).on("zoom",function(u){ai(s,u.scale,u.originX,u.originY),n.dispatchAction({seriesId:t.id,type:"graphRoam",zoom:u.scale,originX:u.originX,originY:u.originY}),i._updateNodeAndLinkScale(),Ya(t.getGraph(),cr(t)),i._lineDraw.updateLayout(),n.updateLabelLayout()})},e.prototype._updateNodeAndLinkScale=function(){var t=this._model,a=t.getData(),n=cr(t);a.eachItemGraphicEl(function(i,o){i&&i.setSymbolScale(n)})},e.prototype.updateLayout=function(t){Ya(t.getGraph(),cr(t)),this._symbolDraw.updateLayout(),this._lineDraw.updateLayout()},e.prototype.remove=function(){clearTimeout(this._layoutTimeout),this._layouting=!1,this._layoutTimeout=null,this._symbolDraw&&this._symbolDraw.remove(),this._lineDraw&&this._lineDraw.remove()},e.type="graph",e}(gt);function He(r){return"_EC_"+r}var Pd=function(){function r(e){this.type="graph",this.nodes=[],this.edges=[],this._nodesMap={},this._edgesMap={},this._directed=e||!1}return r.prototype.isDirected=function(){return this._directed},r.prototype.addNode=function(e,t){e=e==null?""+t:""+e;var a=this._nodesMap;if(!a[He(e)]){var n=new Ce(e,t);return n.hostGraph=this,this.nodes.push(n),a[He(e)]=n,n}},r.prototype.getNodeByIndex=function(e){var t=this.data.getRawIndex(e);return this.nodes[t]},r.prototype.getNodeById=function(e){return this._nodesMap[He(e)]},r.prototype.addEdge=function(e,t,a){var n=this._nodesMap,i=this._edgesMap;if(Xt(e)&&(e=this.nodes[e]),Xt(t)&&(t=this.nodes[t]),e instanceof Ce||(e=n[He(e)]),t instanceof Ce||(t=n[He(t)]),!(!e||!t)){var o=e.id+"-"+t.id,s=new Nu(e,t,a);return s.hostGraph=this,this._directed&&(e.outEdges.push(s),t.inEdges.push(s)),e.edges.push(s),e!==t&&t.edges.push(s),this.edges.push(s),i[o]=s,s}},r.prototype.getEdgeByIndex=function(e){var t=this.edgeData.getRawIndex(e);return this.edges[t]},r.prototype.getEdge=function(e,t){e instanceof Ce&&(e=e.id),t instanceof Ce&&(t=t.id);var a=this._edgesMap;return this._directed?a[e+"-"+t]:a[e+"-"+t]||a[t+"-"+e]},r.prototype.eachNode=function(e,t){for(var a=this.nodes,n=a.length,i=0;i<n;i++)a[i].dataIndex>=0&&e.call(t,a[i],i)},r.prototype.eachEdge=function(e,t){for(var a=this.edges,n=a.length,i=0;i<n;i++)a[i].dataIndex>=0&&a[i].node1.dataIndex>=0&&a[i].node2.dataIndex>=0&&e.call(t,a[i],i)},r.prototype.breadthFirstTraverse=function(e,t,a,n){if(t instanceof Ce||(t=this._nodesMap[He(t)]),!!t){for(var i=a==="out"?"outEdges":a==="in"?"inEdges":"edges",o=0;o<this.nodes.length;o++)this.nodes[o].__visited=!1;if(!e.call(n,t,null))for(var s=[t];s.length;)for(var l=s.shift(),u=l[i],o=0;o<u.length;o++){var v=u[o],c=v.node1===l?v.node2:v.node1;if(!c.__visited){if(e.call(n,c,l))return;s.push(c),c.__visited=!0}}}},r.prototype.update=function(){for(var e=this.data,t=this.edgeData,a=this.nodes,n=this.edges,i=0,o=a.length;i<o;i++)a[i].dataIndex=-1;for(var i=0,o=e.count();i<o;i++)a[e.getRawIndex(i)].dataIndex=i;t.filterSelf(function(s){var l=n[t.getRawIndex(s)];return l.node1.dataIndex>=0&&l.node2.dataIndex>=0});for(var i=0,o=n.length;i<o;i++)n[i].dataIndex=-1;for(var i=0,o=t.count();i<o;i++)n[t.getRawIndex(i)].dataIndex=i},r.prototype.clone=function(){for(var e=new r(this._directed),t=this.nodes,a=this.edges,n=0;n<t.length;n++)e.addNode(t[n].id,t[n].dataIndex);for(var n=0;n<a.length;n++){var i=a[n];e.addEdge(i.node1.id,i.node2.id,i.dataIndex)}return e},r}(),Ce=function(){function r(e,t){this.inEdges=[],this.outEdges=[],this.edges=[],this.dataIndex=-1,this.id=e??"",this.dataIndex=t??-1}return r.prototype.degree=function(){return this.edges.length},r.prototype.inDegree=function(){return this.inEdges.length},r.prototype.outDegree=function(){return this.outEdges.length},r.prototype.getModel=function(e){if(!(this.dataIndex<0)){var t=this.hostGraph,a=t.data.getItemModel(this.dataIndex);return a.getModel(e)}},r.prototype.getAdjacentDataIndices=function(){for(var e={edge:[],node:[]},t=0;t<this.edges.length;t++){var a=this.edges[t];a.dataIndex<0||(e.edge.push(a.dataIndex),e.node.push(a.node1.dataIndex,a.node2.dataIndex))}return e},r.prototype.getTrajectoryDataIndices=function(){for(var e=K(),t=K(),a=0;a<this.edges.length;a++){var n=this.edges[a];if(!(n.dataIndex<0)){e.set(n.dataIndex,!0);for(var i=[n.node1],o=[n.node2],s=0;s<i.length;){var l=i[s];s++,t.set(l.dataIndex,!0);for(var u=0;u<l.inEdges.length;u++)e.set(l.inEdges[u].dataIndex,!0),i.push(l.inEdges[u].node1)}for(s=0;s<o.length;){var v=o[s];s++,t.set(v.dataIndex,!0);for(var u=0;u<v.outEdges.length;u++)e.set(v.outEdges[u].dataIndex,!0),o.push(v.outEdges[u].node2)}}}return{edge:e.keys(),node:t.keys()}},r}(),Nu=function(){function r(e,t,a){this.dataIndex=-1,this.node1=e,this.node2=t,this.dataIndex=a??-1}return r.prototype.getModel=function(e){if(!(this.dataIndex<0)){var t=this.hostGraph,a=t.edgeData.getItemModel(this.dataIndex);return a.getModel(e)}},r.prototype.getAdjacentDataIndices=function(){return{edge:[this.dataIndex],node:[this.node1.dataIndex,this.node2.dataIndex]}},r.prototype.getTrajectoryDataIndices=function(){var e=K(),t=K();e.set(this.dataIndex,!0);for(var a=[this.node1],n=[this.node2],i=0;i<a.length;){var o=a[i];i++,t.set(o.dataIndex,!0);for(var s=0;s<o.inEdges.length;s++)e.set(o.inEdges[s].dataIndex,!0),a.push(o.inEdges[s].node1)}for(i=0;i<n.length;){var l=n[i];i++,t.set(l.dataIndex,!0);for(var s=0;s<l.outEdges.length;s++)e.set(l.outEdges[s].dataIndex,!0),n.push(l.outEdges[s].node2)}return{edge:e.keys(),node:t.keys()}},r}();function Gu(r,e){return{getValue:function(t){var a=this[r][e];return a.getStore().get(a.getDimensionIndex(t||"value"),this.dataIndex)},setVisual:function(t,a){this.dataIndex>=0&&this[r][e].setItemVisual(this.dataIndex,t,a)},getVisual:function(t){return this[r][e].getItemVisual(this.dataIndex,t)},setLayout:function(t,a){this.dataIndex>=0&&this[r][e].setItemLayout(this.dataIndex,t,a)},getLayout:function(){return this[r][e].getItemLayout(this.dataIndex)},getGraphicEl:function(){return this[r][e].getItemGraphicEl(this.dataIndex)},getRawIndex:function(){return this[r][e].getRawIndex(this.dataIndex)}}}ye(Ce,Gu("hostGraph","data"));ye(Nu,Gu("hostGraph","edgeData"));function ku(r,e,t,a,n){for(var i=new Pd(a),o=0;o<r.length;o++)i.addNode(zt(r[o].id,r[o].name,o),o);for(var s=[],l=[],u=0,o=0;o<e.length;o++){var v=e[o],c=v.source,f=v.target;i.addEdge(c,f,u)&&(l.push(v),s.push(zt(da(v.id,null),c+" > "+f)),u++)}var h=t.get("coordinateSystem"),p;if(h==="cartesian2d"||h==="polar")p=Pr(r,t);else{var d=Dl.get(h),g=d?d.dimensions||[]:[];xe(g,"value")<0&&g.concat(["value"]);var S=Wn(r,{coordDimensions:g,encodeDefine:t.getEncode()}).dimensions;p=new te(S,t),p.initData(r)}var m=new te(["value"],t);return m.initData(l,s),n&&n(p,m),xu({mainData:p,struct:i,structAttr:"graph",datas:{node:p,edge:m},datasAttr:{node:"data",edge:"edgeData"}}),i.update(),i}var Md=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t.hasSymbolVisual=!0,t}return e.prototype.init=function(t){r.prototype.init.apply(this,arguments);var a=this;function n(){return a._categoriesData}this.legendVisualProvider=new ba(n,n),this.fillDataTextStyle(t.edges||t.links),this._updateCategoriesData()},e.prototype.mergeOption=function(t){r.prototype.mergeOption.apply(this,arguments),this.fillDataTextStyle(t.edges||t.links),this._updateCategoriesData()},e.prototype.mergeDefaultAndTheme=function(t){r.prototype.mergeDefaultAndTheme.apply(this,arguments),Hn(t,"edgeLabel",["show"])},e.prototype.getInitialData=function(t,a){var n=t.edges||t.links||[],i=t.data||t.nodes||[],o=this;if(i&&n){xd(this);var s=ku(i,n,this,!0,l);return P(s.edges,function(u){bd(u.node1,u.node2,this,u.dataIndex)},this),s.data}function l(u,v){u.wrapMethod("getItemModel",function(p){var d=o._categoriesModels,g=p.getShallow("category"),S=d[g];return S&&(S.parentModel=p.parentModel,p.parentModel=S),p});var c=Rt.prototype.getModel;function f(p,d){var g=c.call(this,p,d);return g.resolveParentPath=h,g}v.wrapMethod("getItemModel",function(p){return p.resolveParentPath=h,p.getModel=f,p});function h(p){if(p&&(p[0]==="label"||p[1]==="label")){var d=p.slice();return p[0]==="label"?d[0]="edgeLabel":p[1]==="label"&&(d[1]="edgeLabel"),d}return p}}},e.prototype.getGraph=function(){return this.getData().graph},e.prototype.getEdgeData=function(){return this.getGraph().edgeData},e.prototype.getCategoriesData=function(){return this._categoriesData},e.prototype.formatTooltip=function(t,a,n){if(n==="edge"){var i=this.getData(),o=this.getDataParams(t,n),s=i.graph.getEdgeByIndex(t),l=i.getName(s.node1.dataIndex),u=i.getName(s.node2.dataIndex),v=[];return l!=null&&v.push(l),u!=null&&v.push(u),Ft("nameValue",{name:v.join(" > "),value:o.value,noValue:o.value==null})}var c=fc({series:this,dataIndex:t,multipleSeries:a});return c},e.prototype._updateCategoriesData=function(){var t=O(this.option.categories||[],function(n){return n.value!=null?n:H({value:0},n)}),a=new te(["value"],this);a.initData(t),this._categoriesData=a,this._categoriesModels=a.mapArray(function(n){return a.getItemModel(n)})},e.prototype.setZoom=function(t){this.option.zoom=t},e.prototype.setCenter=function(t){this.option.center=t},e.prototype.isAnimationEnabled=function(){return r.prototype.isAnimationEnabled.call(this)&&!(this.get("layout")==="force"&&this.get(["force","layoutAnimation"]))},e.type="series.graph",e.dependencies=["grid","polar","geo","singleAxis","calendar"],e.defaultOption={z:2,coordinateSystem:"view",legendHoverLink:!0,layout:null,circular:{rotateLabel:!1},force:{initLayout:null,repulsion:[0,50],gravity:.1,friction:.6,edgeLength:30,layoutAnimation:!0},left:"center",top:"center",symbol:"circle",symbolSize:10,edgeSymbol:["none","none"],edgeSymbolSize:10,edgeLabel:{position:"middle",distance:5},draggable:!1,roam:!1,center:null,zoom:1,nodeScaleRatio:.6,label:{show:!1,formatter:"{b}"},itemStyle:{},lineStyle:{color:"#aaa",width:1,opacity:.5},emphasis:{scale:!0,label:{show:!0}},select:{itemStyle:{borderColor:"#212121"}}},e}(xt),Ed={type:"graphRoam",event:"graphRoam",update:"none"};function Rd(r){r.registerChartView(Ld),r.registerSeriesModel(Md),r.registerProcessor(dd),r.registerVisual(gd),r.registerVisual(yd),r.registerLayout(wd),r.registerLayout(r.PRIORITY.VISUAL.POST_CHART_LAYOUT,Ad),r.registerLayout(Id),r.registerCoordinateSystem("graphView",{dimensions:Mr.dimensions,create:Cd}),r.registerAction({type:"focusNodeAdjacency",event:"focusNodeAdjacency",update:"series:focusNodeAdjacency"},je),r.registerAction({type:"unfocusNodeAdjacency",event:"unfocusNodeAdjacency",update:"series:unfocusNodeAdjacency"},je),r.registerAction(Ed,function(e,t,a){t.eachComponent({mainType:"series",query:e},function(n){var i=n.coordinateSystem,o=ii(i,e,void 0,a);n.setCenter&&n.setCenter(o.center),n.setZoom&&n.setZoom(o.zoom)})})}var Vd=function(){function r(){this.angle=0,this.width=10,this.r=10,this.x=0,this.y=0}return r}(),Nd=function(r){G(e,r);function e(t){var a=r.call(this,t)||this;return a.type="pointer",a}return e.prototype.getDefaultShape=function(){return new Vd},e.prototype.buildPath=function(t,a){var n=Math.cos,i=Math.sin,o=a.r,s=a.width,l=a.angle,u=a.x-n(l)*s*(s>=o/3?1:2),v=a.y-i(l)*s*(s>=o/3?1:2);l=a.angle-Math.PI/2,t.moveTo(u,v),t.lineTo(a.x+n(l)*s,a.y+i(l)*s),t.lineTo(a.x+n(a.angle)*o,a.y+i(a.angle)*o),t.lineTo(a.x-n(l)*s,a.y-i(l)*s),t.lineTo(u,v)},e}(Vt);function Gd(r,e){var t=r.get("center"),a=e.getWidth(),n=e.getHeight(),i=Math.min(a,n),o=z(t[0],e.getWidth()),s=z(t[1],e.getHeight()),l=z(r.get("radius"),i/2);return{cx:o,cy:s,r:l}}function Or(r,e){var t=r==null?"":r+"";return e&&(et(e)?t=e.replace("{value}",t):st(e)&&(t=e(r))),t}var kd=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,a,n){this.group.removeAll();var i=t.get(["axisLine","lineStyle","color"]),o=Gd(t,n);this._renderMain(t,a,n,i,o),this._data=t.getData()},e.prototype.dispose=function(){},e.prototype._renderMain=function(t,a,n,i,o){var s=this.group,l=t.get("clockwise"),u=-t.get("startAngle")/180*Math.PI,v=-t.get("endAngle")/180*Math.PI,c=t.getModel("axisLine"),f=c.get("roundCap"),h=f?qi:Ge,p=c.get("show"),d=c.getModel("lineStyle"),g=d.get("width"),S=[u,v];hc(S,!l),u=S[0],v=S[1];for(var m=v-u,y=u,w=[],x=0;p&&x<i.length;x++){var b=Math.min(Math.max(i[x][0],0),1);v=u+m*b;var _=new h({shape:{startAngle:y,endAngle:v,cx:o.cx,cy:o.cy,clockwise:l,r0:o.r-g,r:o.r},silent:!0});_.setStyle({fill:i[x][1]}),_.setStyle(d.getLineStyle(["color","width"])),w.push(_),y=v}w.reverse(),P(w,function(I){return s.add(I)});var T=function(I){if(I<=0)return i[0][1];var A;for(A=0;A<i.length;A++)if(i[A][0]>=I&&(A===0?0:i[A-1][0])<I)return i[A][1];return i[A-1][1]};this._renderTicks(t,a,n,T,o,u,v,l,g),this._renderTitleAndDetail(t,a,n,T,o),this._renderAnchor(t,o),this._renderPointer(t,a,n,T,o,u,v,l,g)},e.prototype._renderTicks=function(t,a,n,i,o,s,l,u,v){for(var c=this.group,f=o.cx,h=o.cy,p=o.r,d=+t.get("min"),g=+t.get("max"),S=t.getModel("splitLine"),m=t.getModel("axisTick"),y=t.getModel("axisLabel"),w=t.get("splitNumber"),x=m.get("splitNumber"),b=z(S.get("length"),p),_=z(m.get("length"),p),T=s,I=(l-s)/w,A=I/x,D=S.getModel("lineStyle").getLineStyle(),E=m.getModel("lineStyle").getLineStyle(),M=S.get("distance"),C,L,R=0;R<=w;R++){if(C=Math.cos(T),L=Math.sin(T),S.get("show")){var V=M?M+v:v,N=new se({shape:{x1:C*(p-V)+f,y1:L*(p-V)+h,x2:C*(p-b-V)+f,y2:L*(p-b-V)+h},style:D,silent:!0});D.stroke==="auto"&&N.setStyle({stroke:i(R/w)}),c.add(N)}if(y.get("show")){var V=y.get("distance")+M,k=Or(Cl(R/w*(g-d)+d),y.get("formatter")),B=i(R/w),W=C*(p-b-V)+f,X=L*(p-b-V)+h,tt=y.get("rotate"),J=0;tt==="radial"?(J=-T+2*Math.PI,J>Math.PI/2&&(J+=Math.PI)):tt==="tangential"?J=-T-Math.PI/2:Xt(tt)&&(J=tt*Math.PI/180),J===0?c.add(new qt({style:$t(y,{text:k,x:W,y:X,verticalAlign:L<-.8?"top":L>.8?"bottom":"middle",align:C<-.4?"left":C>.4?"right":"center"},{inheritColor:B}),silent:!0})):c.add(new qt({style:$t(y,{text:k,x:W,y:X,verticalAlign:"middle",align:"center"},{inheritColor:B}),silent:!0,originX:W,originY:X,rotation:J}))}if(m.get("show")&&R!==w){var V=m.get("distance");V=V?V+v:v;for(var rt=0;rt<=x;rt++){C=Math.cos(T),L=Math.sin(T);var pt=new se({shape:{x1:C*(p-V)+f,y1:L*(p-V)+h,x2:C*(p-_-V)+f,y2:L*(p-_-V)+h},silent:!0,style:E});E.stroke==="auto"&&pt.setStyle({stroke:i((R+rt/x)/w)}),c.add(pt),T+=A}T-=A}else T+=I}},e.prototype._renderPointer=function(t,a,n,i,o,s,l,u,v){var c=this.group,f=this._data,h=this._progressEls,p=[],d=t.get(["pointer","show"]),g=t.getModel("progress"),S=g.get("show"),m=t.getData(),y=m.mapDimension("value"),w=+t.get("min"),x=+t.get("max"),b=[w,x],_=[s,l];function T(A,D){var E=m.getItemModel(A),M=E.getModel("pointer"),C=z(M.get("width"),o.r),L=z(M.get("length"),o.r),R=t.get(["pointer","icon"]),V=M.get("offsetCenter"),N=z(V[0],o.r),k=z(V[1],o.r),B=M.get("keepAspect"),W;return R?W=Ve(R,N-C/2,k-L,C,L,null,B):W=new Nd({shape:{angle:-Math.PI/2,width:C,r:L,x:N,y:k}}),W.rotation=-(D+Math.PI/2),W.x=o.cx,W.y=o.cy,W}function I(A,D){var E=g.get("roundCap"),M=E?qi:Ge,C=g.get("overlap"),L=C?g.get("width"):v/m.count(),R=C?o.r-L:o.r-(A+1)*L,V=C?o.r:o.r-A*L,N=new M({shape:{startAngle:s,endAngle:D,cx:o.cx,cy:o.cy,clockwise:u,r0:R,r:V}});return C&&(N.z2=Gt(m.get(y,A),[w,x],[100,0],!0)),N}(S||d)&&(m.diff(f).add(function(A){var D=m.get(y,A);if(d){var E=T(A,s);Yt(E,{rotation:-((isNaN(+D)?_[0]:Gt(D,b,_,!0))+Math.PI/2)},t),c.add(E),m.setItemGraphicEl(A,E)}if(S){var M=I(A,s),C=g.get("clip");Yt(M,{shape:{endAngle:Gt(D,b,_,C)}},t),c.add(M),Oi(t.seriesIndex,m.dataType,A,M),p[A]=M}}).update(function(A,D){var E=m.get(y,A);if(d){var M=f.getItemGraphicEl(D),C=M?M.rotation:s,L=T(A,C);L.rotation=C,ct(L,{rotation:-((isNaN(+E)?_[0]:Gt(E,b,_,!0))+Math.PI/2)},t),c.add(L),m.setItemGraphicEl(A,L)}if(S){var R=h[D],V=R?R.shape.endAngle:s,N=I(A,V),k=g.get("clip");ct(N,{shape:{endAngle:Gt(E,b,_,k)}},t),c.add(N),Oi(t.seriesIndex,m.dataType,A,N),p[A]=N}}).execute(),m.each(function(A){var D=m.getItemModel(A),E=D.getModel("emphasis"),M=E.get("focus"),C=E.get("blurScope"),L=E.get("disabled");if(d){var R=m.getItemGraphicEl(A),V=m.getItemVisual(A,"style"),N=V.fill;if(R instanceof ge){var k=R.style;R.useStyle(H({image:k.image,x:k.x,y:k.y,width:k.width,height:k.height},V))}else R.useStyle(V),R.type!=="pointer"&&R.setColor(N);R.setStyle(D.getModel(["pointer","itemStyle"]).getItemStyle()),R.style.fill==="auto"&&R.setStyle("fill",i(Gt(m.get(y,A),b,[0,1],!0))),R.z2EmphasisLift=0,Mt(R,D),ft(R,M,C,L)}if(S){var B=p[A];B.useStyle(m.getItemVisual(A,"style")),B.setStyle(D.getModel(["progress","itemStyle"]).getItemStyle()),B.z2EmphasisLift=0,Mt(B,D),ft(B,M,C,L)}}),this._progressEls=p)},e.prototype._renderAnchor=function(t,a){var n=t.getModel("anchor"),i=n.get("show");if(i){var o=n.get("size"),s=n.get("icon"),l=n.get("offsetCenter"),u=n.get("keepAspect"),v=Ve(s,a.cx-o/2+z(l[0],a.r),a.cy-o/2+z(l[1],a.r),o,o,null,u);v.z2=n.get("showAbove")?1:0,v.setStyle(n.getModel("itemStyle").getItemStyle()),this.group.add(v)}},e.prototype._renderTitleAndDetail=function(t,a,n,i,o){var s=this,l=t.getData(),u=l.mapDimension("value"),v=+t.get("min"),c=+t.get("max"),f=new Z,h=[],p=[],d=t.isAnimationEnabled(),g=t.get(["pointer","showAbove"]);l.diff(this._data).add(function(S){h[S]=new qt({silent:!0}),p[S]=new qt({silent:!0})}).update(function(S,m){h[S]=s._titleEls[m],p[S]=s._detailEls[m]}).execute(),l.each(function(S){var m=l.getItemModel(S),y=l.get(u,S),w=new Z,x=i(Gt(y,[v,c],[0,1],!0)),b=m.getModel("title");if(b.get("show")){var _=b.get("offsetCenter"),T=o.cx+z(_[0],o.r),I=o.cy+z(_[1],o.r),A=h[S];A.attr({z2:g?0:2,style:$t(b,{x:T,y:I,text:l.getName(S),align:"center",verticalAlign:"middle"},{inheritColor:x})}),w.add(A)}var D=m.getModel("detail");if(D.get("show")){var E=D.get("offsetCenter"),M=o.cx+z(E[0],o.r),C=o.cy+z(E[1],o.r),L=z(D.get("width"),o.r),R=z(D.get("height"),o.r),V=t.get(["progress","show"])?l.getItemVisual(S,"style").fill:x,A=p[S],N=D.get("formatter");A.attr({z2:g?0:2,style:$t(D,{x:M,y:C,text:Or(y,N),width:isNaN(L)?null:L,height:isNaN(R)?null:R,align:"center",verticalAlign:"middle"},{inheritColor:V})}),pc(A,{normal:D},y,function(B){return Or(B,N)}),d&&dc(A,S,l,t,{getFormattedLabel:function(B,W,X,tt,J,rt){return Or(rt?rt.interpolatedValue:y,N)}}),w.add(A)}f.add(w)}),this.group.add(f),this._titleEls=h,this._detailEls=p},e.type="gauge",e}(gt),zd=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t.visualStyleAccessPath="itemStyle",t}return e.prototype.getInitialData=function(t,a){return ha(this,["value"])},e.type="series.gauge",e.defaultOption={z:2,colorBy:"data",center:["50%","50%"],legendHoverLink:!0,radius:"75%",startAngle:225,endAngle:-45,clockwise:!0,min:0,max:100,splitNumber:10,axisLine:{show:!0,roundCap:!1,lineStyle:{color:[[1,"#E6EBF8"]],width:10}},progress:{show:!1,overlap:!0,width:10,roundCap:!1,clip:!0},splitLine:{show:!0,length:10,distance:10,lineStyle:{color:"#63677A",width:3,type:"solid"}},axisTick:{show:!0,splitNumber:5,length:6,distance:10,lineStyle:{color:"#63677A",width:1,type:"solid"}},axisLabel:{show:!0,distance:15,color:"#464646",fontSize:12,rotate:0},pointer:{icon:null,offsetCenter:[0,0],show:!0,showAbove:!0,length:"60%",width:6,keepAspect:!1},anchor:{show:!1,showAbove:!1,size:6,icon:"circle",offsetCenter:[0,0],keepAspect:!1,itemStyle:{color:"#fff",borderWidth:0,borderColor:"#5470c6"}},title:{show:!0,offsetCenter:[0,"20%"],color:"#464646",fontSize:16,valueAnimation:!1},detail:{show:!0,backgroundColor:"rgba(0,0,0,0)",borderWidth:0,borderColor:"#ccc",width:100,height:null,padding:[5,10],offsetCenter:[0,"40%"],color:"#464646",fontSize:30,fontWeight:"bold",lineHeight:30,valueAnimation:!1}},e}(xt);function Od(r){r.registerChartView(kd),r.registerSeriesModel(zd)}var Bd=["itemStyle","opacity"],Fd=function(r){G(e,r);function e(t,a){var n=r.call(this)||this,i=n,o=new ke,s=new qt;return i.setTextContent(s),n.setTextGuideLine(o),n.updateData(t,a,!0),n}return e.prototype.updateData=function(t,a,n){var i=this,o=t.hostModel,s=t.getItemModel(a),l=t.getItemLayout(a),u=s.getModel("emphasis"),v=s.get(Bd);v=v??1,n||Re(i),i.useStyle(t.getItemVisual(a,"style")),i.style.lineJoin="round",n?(i.setShape({points:l.points}),i.style.opacity=0,Yt(i,{style:{opacity:v}},o,a)):ct(i,{style:{opacity:v},shape:{points:l.points}},o,a),Mt(i,s),this._updateLabel(t,a),ft(this,u.get("focus"),u.get("blurScope"),u.get("disabled"))},e.prototype._updateLabel=function(t,a){var n=this,i=this.getTextGuideLine(),o=n.getTextContent(),s=t.hostModel,l=t.getItemModel(a),u=t.getItemLayout(a),v=u.label,c=t.getItemVisual(a,"style"),f=c.fill;Bt(o,Et(l),{labelFetcher:t.hostModel,labelDataIndex:a,defaultOpacity:c.opacity,defaultText:t.getName(a)},{normal:{align:v.textAlign,verticalAlign:v.verticalAlign}}),n.setTextConfig({local:!0,inside:!!v.inside,insideStroke:f,outsideFill:f});var h=v.linePoints;i.setShape({points:h}),n.textGuideLineConfig={anchor:h?new le(h[0][0],h[0][1]):null},ct(o,{style:{x:v.x,y:v.y}},s,a),o.attr({rotation:v.rotation,originX:v.x,originY:v.y,z2:10}),ah(n,nh(l),{stroke:f})},e}(Jt),Hd=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t.ignoreLabelLineUpdate=!0,t}return e.prototype.render=function(t,a,n){var i=t.getData(),o=this._data,s=this.group;i.diff(o).add(function(l){var u=new Fd(i,l);i.setItemGraphicEl(l,u),s.add(u)}).update(function(l,u){var v=o.getItemGraphicEl(u);v.updateData(i,l),s.add(v),i.setItemGraphicEl(l,v)}).remove(function(l){var u=o.getItemGraphicEl(l);gc(u,t,l)}).execute(),this._data=i},e.prototype.remove=function(){this.group.removeAll(),this._data=null},e.prototype.dispose=function(){},e.type="funnel",e}(gt),Wd=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t){r.prototype.init.apply(this,arguments),this.legendVisualProvider=new ba(lt(this.getData,this),lt(this.getRawData,this)),this._defaultLabelLine(t)},e.prototype.getInitialData=function(t,a){return ha(this,{coordDimensions:["value"],encodeDefaulter:St(wl,this)})},e.prototype._defaultLabelLine=function(t){Hn(t,"labelLine",["show"]);var a=t.labelLine,n=t.emphasis.labelLine;a.show=a.show&&t.label.show,n.show=n.show&&t.emphasis.label.show},e.prototype.getDataParams=function(t){var a=this.getData(),n=r.prototype.getDataParams.call(this,t),i=a.mapDimension("value"),o=a.getSum(i);return n.percent=o?+(a.get(i,t)/o*100).toFixed(2):0,n.$vars.push("percent"),n},e.type="series.funnel",e.defaultOption={z:2,legendHoverLink:!0,colorBy:"data",left:80,top:60,right:80,bottom:60,minSize:"0%",maxSize:"100%",sort:"descending",orient:"vertical",gap:0,funnelAlign:"center",label:{show:!0,position:"outer"},labelLine:{show:!0,length:20,lineStyle:{width:1}},itemStyle:{borderColor:"#fff",borderWidth:1},emphasis:{label:{show:!0}},select:{itemStyle:{borderColor:"#212121"}}},e}(xt);function Ud(r,e){return Se(r.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()})}function $d(r,e){for(var t=r.mapDimension("value"),a=r.mapArray(t,function(l){return l}),n=[],i=e==="ascending",o=0,s=r.count();o<s;o++)n[o]=o;return st(e)?n.sort(e):e!=="none"&&n.sort(function(l,u){return i?a[l]-a[u]:a[u]-a[l]}),n}function Yd(r){var e=r.hostModel,t=e.get("orient");r.each(function(a){var n=r.getItemModel(a),i=n.getModel("label"),o=i.get("position"),s=n.getModel("labelLine"),l=r.getItemLayout(a),u=l.points,v=o==="inner"||o==="inside"||o==="center"||o==="insideLeft"||o==="insideRight",c,f,h,p;if(v)o==="insideLeft"?(f=(u[0][0]+u[3][0])/2+5,h=(u[0][1]+u[3][1])/2,c="left"):o==="insideRight"?(f=(u[1][0]+u[2][0])/2-5,h=(u[1][1]+u[2][1])/2,c="right"):(f=(u[0][0]+u[1][0]+u[2][0]+u[3][0])/4,h=(u[0][1]+u[1][1]+u[2][1]+u[3][1])/4,c="center"),p=[[f,h],[f,h]];else{var d=void 0,g=void 0,S=void 0,m=void 0,y=s.get("length");o==="left"?(d=(u[3][0]+u[0][0])/2,g=(u[3][1]+u[0][1])/2,S=d-y,f=S-5,c="right"):o==="right"?(d=(u[1][0]+u[2][0])/2,g=(u[1][1]+u[2][1])/2,S=d+y,f=S+5,c="left"):o==="top"?(d=(u[3][0]+u[0][0])/2,g=(u[3][1]+u[0][1])/2,m=g-y,h=m-5,c="center"):o==="bottom"?(d=(u[1][0]+u[2][0])/2,g=(u[1][1]+u[2][1])/2,m=g+y,h=m+5,c="center"):o==="rightTop"?(d=t==="horizontal"?u[3][0]:u[1][0],g=t==="horizontal"?u[3][1]:u[1][1],t==="horizontal"?(m=g-y,h=m-5,c="center"):(S=d+y,f=S+5,c="top")):o==="rightBottom"?(d=u[2][0],g=u[2][1],t==="horizontal"?(m=g+y,h=m+5,c="center"):(S=d+y,f=S+5,c="bottom")):o==="leftTop"?(d=u[0][0],g=t==="horizontal"?u[0][1]:u[1][1],t==="horizontal"?(m=g-y,h=m-5,c="center"):(S=d-y,f=S-5,c="right")):o==="leftBottom"?(d=t==="horizontal"?u[1][0]:u[3][0],g=t==="horizontal"?u[1][1]:u[2][1],t==="horizontal"?(m=g+y,h=m+5,c="center"):(S=d-y,f=S-5,c="right")):(d=(u[1][0]+u[2][0])/2,g=(u[1][1]+u[2][1])/2,t==="horizontal"?(m=g+y,h=m+5,c="center"):(S=d+y,f=S+5,c="left")),t==="horizontal"?(S=d,f=S):(m=g,h=m),p=[[d,g],[S,m]]}l.label={linePoints:p,x:f,y:h,verticalAlign:"middle",textAlign:c,inside:v}})}function Zd(r,e){r.eachSeriesByType("funnel",function(t){var a=t.getData(),n=a.mapDimension("value"),i=t.get("sort"),o=Ud(t,e),s=t.get("orient"),l=o.width,u=o.height,v=$d(a,i),c=o.x,f=o.y,h=s==="horizontal"?[z(t.get("minSize"),u),z(t.get("maxSize"),u)]:[z(t.get("minSize"),l),z(t.get("maxSize"),l)],p=a.getDataExtent(n),d=t.get("min"),g=t.get("max");d==null&&(d=Math.min(p[0],0)),g==null&&(g=p[1]);var S=t.get("funnelAlign"),m=t.get("gap"),y=s==="horizontal"?l:u,w=(y-m*(a.count()-1))/a.count(),x=function(C,L){if(s==="horizontal"){var R=a.get(n,C)||0,V=Gt(R,[d,g],h,!0),N=void 0;switch(S){case"top":N=f;break;case"center":N=f+(u-V)/2;break;case"bottom":N=f+(u-V);break}return[[L,N],[L,N+V]]}var k=a.get(n,C)||0,B=Gt(k,[d,g],h,!0),W;switch(S){case"left":W=c;break;case"center":W=c+(l-B)/2;break;case"right":W=c+l-B;break}return[[W,L],[W+B,L]]};i==="ascending"&&(w=-w,m=-m,s==="horizontal"?c+=l:f+=u,v=v.reverse());for(var b=0;b<v.length;b++){var _=v[b],T=v[b+1],I=a.getItemModel(_);if(s==="horizontal"){var A=I.get(["itemStyle","width"]);A==null?A=w:(A=z(A,l),i==="ascending"&&(A=-A));var D=x(_,c),E=x(T,c+A);c+=A+m,a.setItemLayout(_,{points:D.concat(E.slice().reverse())})}else{var M=I.get(["itemStyle","height"]);M==null?M=w:(M=z(M,u),i==="ascending"&&(M=-M));var D=x(_,f),E=x(T,f+M);f+=M+m,a.setItemLayout(_,{points:D.concat(E.slice().reverse())})}}Yd(a)})}function Xd(r){r.registerChartView(Hd),r.registerSeriesModel(Wd),r.registerLayout(Zd),r.registerProcessor(wa("funnel"))}var qd=.3,jd=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t._dataGroup=new Z,t._initialized=!1,t}return e.prototype.init=function(){this.group.add(this._dataGroup)},e.prototype.render=function(t,a,n,i){this._progressiveEls=null;var o=this._dataGroup,s=t.getData(),l=this._data,u=t.coordinateSystem,v=u.dimensions,c=Yo(t);s.diff(l).add(f).update(h).remove(p).execute();function f(g){var S=$o(s,o,g,v,u);Za(S,s,g,c)}function h(g,S){var m=l.getItemGraphicEl(S),y=zu(s,g,v,u);s.setItemGraphicEl(g,m),ct(m,{shape:{points:y}},t,g),Re(m),Za(m,s,g,c)}function p(g){var S=l.getItemGraphicEl(g);o.remove(S)}if(!this._initialized){this._initialized=!0;var d=Kd(u,t,function(){setTimeout(function(){o.removeClipPath()})});o.setClipPath(d)}this._data=s},e.prototype.incrementalPrepareRender=function(t,a,n){this._initialized=!0,this._data=null,this._dataGroup.removeAll()},e.prototype.incrementalRender=function(t,a,n){for(var i=a.getData(),o=a.coordinateSystem,s=o.dimensions,l=Yo(a),u=this._progressiveEls=[],v=t.start;v<t.end;v++){var c=$o(i,this._dataGroup,v,s,o);c.incremental=!0,Za(c,i,v,l),u.push(c)}},e.prototype.remove=function(){this._dataGroup&&this._dataGroup.removeAll(),this._data=null},e.type="parallel",e}(gt);function Kd(r,e,t){var a=r.model,n=r.getRect(),i=new It({shape:{x:n.x,y:n.y,width:n.width,height:n.height}}),o=a.get("layout")==="horizontal"?"width":"height";return i.setShape(o,0),Yt(i,{shape:{width:n.width,height:n.height}},e,t),i}function zu(r,e,t,a){for(var n=[],i=0;i<t.length;i++){var o=t[i],s=r.get(r.mapDimension(o),e);Jd(s,a.getAxis(o).type)||n.push(a.dataToPoint(s,o))}return n}function $o(r,e,t,a,n){var i=zu(r,t,a,n),o=new ke({shape:{points:i},z2:10});return e.add(o),r.setItemGraphicEl(t,o),o}function Yo(r){var e=r.get("smooth",!0);return e===!0&&(e=qd),e=yc(e),mc(e)&&(e=0),{smooth:e}}function Za(r,e,t,a){r.useStyle(e.getItemVisual(t,"style")),r.style.fill=null,r.setShape("smooth",a.smooth);var n=e.getItemModel(t),i=n.getModel("emphasis");Mt(r,n,"lineStyle"),ft(r,i.get("focus"),i.get("blurScope"),i.get("disabled"))}function Jd(r,e){return e==="category"?r==null:r==null||isNaN(r)}var Qd=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t.visualStyleAccessPath="lineStyle",t.visualDrawType="stroke",t}return e.prototype.getInitialData=function(t,a){return Pr(null,this,{useEncodeDefaulter:lt(tg,null,this)})},e.prototype.getRawIndicesByActiveState=function(t){var a=this.coordinateSystem,n=this.getData(),i=[];return a.eachActiveState(n,function(o,s){t===o&&i.push(n.getRawIndex(s))}),i},e.type="series.parallel",e.dependencies=["parallel"],e.defaultOption={z:2,coordinateSystem:"parallel",parallelIndex:0,label:{show:!1},inactiveOpacity:.05,activeOpacity:1,lineStyle:{width:1,opacity:.45,type:"solid"},emphasis:{label:{show:!1}},progressive:500,smooth:!1,animationEasing:"linear"},e}(xt);function tg(r){var e=r.ecModel.getComponent("parallel",r.get("parallelIndex"));if(e){var t={};return P(e.dimensions,function(a){var n=eg(a);t[a]=n}),t}}function eg(r){return+r.replace("dim","")}var rg=["lineStyle","opacity"],ag={seriesType:"parallel",reset:function(r,e){var t=r.coordinateSystem,a={normal:r.get(["lineStyle","opacity"]),active:r.get("activeOpacity"),inactive:r.get("inactiveOpacity")};return{progress:function(n,i){t.eachActiveState(i,function(o,s){var l=a[o];if(o==="normal"&&i.hasItemOption){var u=i.getItemModel(s).get(rg,!0);u!=null&&(l=u)}var v=i.ensureUniqueItemVisual(s,"style");v.opacity=l},n.start,n.end)}}}};function ng(r){ig(r),og(r)}function ig(r){if(!r.parallel){var e=!1;P(r.series,function(t){t&&t.type==="parallel"&&(e=!0)}),e&&(r.parallel=[{}])}}function og(r){var e=Ot(r.parallelAxis);P(e,function(t){if(ga(t)){var a=t.parallelIndex||0,n=Ot(r.parallel)[a];n&&n.parallelAxisDefault&&Qt(t,n.parallelAxisDefault,!1)}})}var sg=5,lg=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,a,n){this._model=t,this._api=n,this._handlers||(this._handlers={},P(ug,function(i,o){n.getZr().on(o,this._handlers[o]=lt(i,this))},this)),Ll(this,"_throttledDispatchExpand",t.get("axisExpandRate"),"fixRate")},e.prototype.dispose=function(t,a){Sc(this,"_throttledDispatchExpand"),P(this._handlers,function(n,i){a.getZr().off(i,n)}),this._handlers=null},e.prototype._throttledDispatchExpand=function(t){this._dispatchExpand(t)},e.prototype._dispatchExpand=function(t){t&&this._api.dispatchAction(H({type:"parallelAxisExpand"},t))},e.type="parallel",e}(ze),ug={mousedown:function(r){Xa(this,"click")&&(this._mouseDownPoint=[r.offsetX,r.offsetY])},mouseup:function(r){var e=this._mouseDownPoint;if(Xa(this,"click")&&e){var t=[r.offsetX,r.offsetY],a=Math.pow(e[0]-t[0],2)+Math.pow(e[1]-t[1],2);if(a>sg)return;var n=this._model.coordinateSystem.getSlidedAxisExpandWindow([r.offsetX,r.offsetY]);n.behavior!=="none"&&this._dispatchExpand({axisExpandWindow:n.axisExpandWindow})}this._mouseDownPoint=null},mousemove:function(r){if(!(this._mouseDownPoint||!Xa(this,"mousemove"))){var e=this._model,t=e.coordinateSystem.getSlidedAxisExpandWindow([r.offsetX,r.offsetY]),a=t.behavior;a==="jump"&&this._throttledDispatchExpand.debounceNextCall(e.get("axisExpandDebounce")),this._throttledDispatchExpand(a==="none"?null:{axisExpandWindow:t.axisExpandWindow,animation:a==="jump"?null:{duration:0}})}}};function Xa(r,e){var t=r._model;return t.get("axisExpandable")&&t.get("axisExpandTriggerOn")===e}var vg=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(){r.prototype.init.apply(this,arguments),this.mergeOption({})},e.prototype.mergeOption=function(t){var a=this.option;t&&Qt(a,t,!0),this._initDimensions()},e.prototype.contains=function(t,a){var n=t.get("parallelIndex");return n!=null&&a.getComponent("parallel",n)===this},e.prototype.setAxisExpand=function(t){P(["axisExpandable","axisExpandCenter","axisExpandCount","axisExpandWidth","axisExpandWindow"],function(a){t.hasOwnProperty(a)&&(this.option[a]=t[a])},this)},e.prototype._initDimensions=function(){var t=this.dimensions=[],a=this.parallelAxisIndex=[],n=Pt(this.ecModel.queryComponents({mainType:"parallelAxis"}),function(i){return(i.get("parallelIndex")||0)===this.componentIndex},this);P(n,function(i){t.push("dim"+i.get("dim")),a.push(i.componentIndex)})},e.type="parallel",e.dependencies=["parallelAxis"],e.layoutMode="box",e.defaultOption={z:0,left:80,top:60,right:80,bottom:60,layout:"horizontal",axisExpandable:!1,axisExpandCenter:null,axisExpandCount:0,axisExpandWidth:50,axisExpandRate:17,axisExpandDebounce:50,axisExpandSlideTriggerArea:[-.15,.05,.4],axisExpandTriggerOn:"click",parallelAxisDefault:null},e}(me),cg=function(r){G(e,r);function e(t,a,n,i,o){var s=r.call(this,t,a,n)||this;return s.type=i||"value",s.axisIndex=o,s}return e.prototype.isHorizontal=function(){return this.coordinateSystem.getModel().get("layout")!=="horizontal"},e}(ae),qa=P,Ou=Math.min,Bu=Math.max,Zo=Math.floor,fg=Math.ceil,Xo=Cl,hg=Math.PI,pg=function(){function r(e,t,a){this.type="parallel",this._axesMap=K(),this._axesLayout={},this.dimensions=e.dimensions,this._model=e,this._init(e,t,a)}return r.prototype._init=function(e,t,a){var n=e.dimensions,i=e.parallelAxisIndex;qa(n,function(o,s){var l=i[s],u=t.getComponent("parallelAxis",l),v=this._axesMap.set(o,new cg(o,$n(u),[0,0],u.get("type"),l)),c=v.type==="category";v.onBand=c&&u.get("boundaryGap"),v.inverse=u.get("inverse"),u.axis=v,v.model=u,v.coordinateSystem=u.coordinateSystem=this},this)},r.prototype.update=function(e,t){this._updateAxesFromSeries(this._model,e)},r.prototype.containPoint=function(e){var t=this._makeLayoutInfo(),a=t.axisBase,n=t.layoutBase,i=t.pixelDimIndex,o=e[1-i],s=e[i];return o>=a&&o<=a+t.axisLength&&s>=n&&s<=n+t.layoutLength},r.prototype.getModel=function(){return this._model},r.prototype._updateAxesFromSeries=function(e,t){t.eachSeries(function(a){if(e.contains(a,t)){var n=a.getData();qa(this.dimensions,function(i){var o=this._axesMap.get(i);o.scale.unionExtentFromData(n,n.mapDimension(i)),jr(o.scale,o.model)},this)}},this)},r.prototype.resize=function(e,t){this._rect=Se(e.getBoxLayoutParams(),{width:t.getWidth(),height:t.getHeight()}),this._layoutAxes()},r.prototype.getRect=function(){return this._rect},r.prototype._makeLayoutInfo=function(){var e=this._model,t=this._rect,a=["x","y"],n=["width","height"],i=e.get("layout"),o=i==="horizontal"?0:1,s=t[n[o]],l=[0,s],u=this.dimensions.length,v=Br(e.get("axisExpandWidth"),l),c=Br(e.get("axisExpandCount")||0,[0,u]),f=e.get("axisExpandable")&&u>3&&u>c&&c>1&&v>0&&s>0,h=e.get("axisExpandWindow"),p;if(h)p=Br(h[1]-h[0],l),h[1]=h[0]+p;else{p=Br(v*(c-1),l);var d=e.get("axisExpandCenter")||Zo(u/2);h=[v*d-p/2],h[1]=h[0]+p}var g=(s-p)/(u-c);g<3&&(g=0);var S=[Zo(Xo(h[0]/v,1))+1,fg(Xo(h[1]/v,1))-1],m=g/v*h[0];return{layout:i,pixelDimIndex:o,layoutBase:t[a[o]],layoutLength:s,axisBase:t[a[1-o]],axisLength:t[n[1-o]],axisExpandable:f,axisExpandWidth:v,axisCollapseWidth:g,axisExpandWindow:h,axisCount:u,winInnerIndices:S,axisExpandWindow0Pos:m}},r.prototype._layoutAxes=function(){var e=this._rect,t=this._axesMap,a=this.dimensions,n=this._makeLayoutInfo(),i=n.layout;t.each(function(o){var s=[0,n.axisLength],l=o.inverse?1:0;o.setExtent(s[l],s[1-l])}),qa(a,function(o,s){var l=(n.axisExpandable?gg:dg)(s,n),u={horizontal:{x:l.position,y:n.axisLength},vertical:{x:0,y:l.position}},v={horizontal:hg/2,vertical:0},c=[u[i].x+e.x,u[i].y+e.y],f=v[i],h=Ir();On(h,h,f),yr(h,h,c),this._axesLayout[o]={position:c,rotation:f,transform:h,axisNameAvailableWidth:l.axisNameAvailableWidth,axisLabelShow:l.axisLabelShow,nameTruncateMaxWidth:l.nameTruncateMaxWidth,tickDirection:1,labelDirection:1}},this)},r.prototype.getAxis=function(e){return this._axesMap.get(e)},r.prototype.dataToPoint=function(e,t){return this.axisCoordToPoint(this._axesMap.get(t).dataToCoord(e),t)},r.prototype.eachActiveState=function(e,t,a,n){a==null&&(a=0),n==null&&(n=e.count());var i=this._axesMap,o=this.dimensions,s=[],l=[];P(o,function(g){s.push(e.mapDimension(g)),l.push(i.get(g).model)});for(var u=this.hasAxisBrushed(),v=a;v<n;v++){var c=void 0;if(!u)c="normal";else{c="active";for(var f=e.getValues(s,v),h=0,p=o.length;h<p;h++){var d=l[h].getActiveState(f[h]);if(d==="inactive"){c="inactive";break}}}t(c,v)}},r.prototype.hasAxisBrushed=function(){for(var e=this.dimensions,t=this._axesMap,a=!1,n=0,i=e.length;n<i;n++)t.get(e[n]).model.getActiveState()!=="normal"&&(a=!0);return a},r.prototype.axisCoordToPoint=function(e,t){var a=this._axesLayout[t];return Pl([e,0],a.transform)},r.prototype.getAxisLayout=function(e){return Zt(this._axesLayout[e])},r.prototype.getSlidedAxisExpandWindow=function(e){var t=this._makeLayoutInfo(),a=t.pixelDimIndex,n=t.axisExpandWindow.slice(),i=n[1]-n[0],o=[0,t.axisExpandWidth*(t.axisCount-1)];if(!this.containPoint(e))return{behavior:"none",axisExpandWindow:n};var s=e[a]-t.layoutBase-t.axisExpandWindow0Pos,l,u="slide",v=t.axisCollapseWidth,c=this._model.get("axisExpandSlideTriggerArea"),f=c[0]!=null;if(v)f&&v&&s<i*c[0]?(u="jump",l=s-i*c[2]):f&&v&&s>i*(1-c[0])?(u="jump",l=s-i*(1-c[2])):(l=s-i*c[1])>=0&&(l=s-i*(1-c[1]))<=0&&(l=0),l*=t.axisExpandWidth/v,l?Af(l,n,o,"all"):u="none";else{var h=n[1]-n[0],p=o[1]*s/h;n=[Bu(0,p-h/2)],n[1]=Ou(o[1],n[0]+h),n[0]=n[1]-h}return{axisExpandWindow:n,behavior:u}},r}();function Br(r,e){return Ou(Bu(r,e[0]),e[1])}function dg(r,e){var t=e.layoutLength/(e.axisCount-1);return{position:t*r,axisNameAvailableWidth:t,axisLabelShow:!0}}function gg(r,e){var t=e.layoutLength,a=e.axisExpandWidth,n=e.axisCount,i=e.axisCollapseWidth,o=e.winInnerIndices,s,l=i,u=!1,v;return r<o[0]?(s=r*i,v=i):r<=o[1]?(s=e.axisExpandWindow0Pos+r*a-e.axisExpandWindow[0],l=a,u=!0):(s=t-(n-1-r)*i,v=i),{position:s,axisNameAvailableWidth:l,axisLabelShow:u,nameTruncateMaxWidth:v}}function yg(r,e){var t=[];return r.eachComponent("parallel",function(a,n){var i=new pg(a,r,e);i.name="parallel_"+n,i.resize(a,e),a.coordinateSystem=i,i.model=a,t.push(i)}),r.eachSeries(function(a){if(a.get("coordinateSystem")==="parallel"){var n=a.getReferringComponents("parallel",Qe).models[0];a.coordinateSystem=n.coordinateSystem}}),t}var mg={create:yg},Dn=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t.activeIntervals=[],t}return e.prototype.getAreaSelectStyle=function(){return Al([["fill","color"],["lineWidth","borderWidth"],["stroke","borderColor"],["width","width"],["opacity","opacity"]])(this.getModel("areaSelectStyle"))},e.prototype.setActiveIntervals=function(t){var a=this.activeIntervals=Zt(t);if(a)for(var n=a.length-1;n>=0;n--)Kr(a[n])},e.prototype.getActiveState=function(t){var a=this.activeIntervals;if(!a.length)return"normal";if(t==null||isNaN(+t))return"inactive";if(a.length===1){var n=a[0];if(n[0]<=t&&t<=n[1])return"active"}else for(var i=0,o=a.length;i<o;i++)if(a[i][0]<=t&&t<=a[i][1])return"active";return"inactive"},e}(me);ye(Dn,ma);var Sg=["axisLine","axisTickLabel","axisName"],xg=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t,a){r.prototype.init.apply(this,arguments),(this._brushController=new Ql(a.getZr())).on("brush",lt(this._onBrush,this))},e.prototype.render=function(t,a,n,i){if(!bg(t,a,i)){this.axisModel=t,this.api=n,this.group.removeAll();var o=this._axisGroup;if(this._axisGroup=new Z,this.group.add(this._axisGroup),!!t.get("show")){var s=_g(t,a),l=s.coordinateSystem,u=t.getAreaSelectStyle(),v=u.width,c=t.axis.dim,f=l.getAxisLayout(c),h=H({strokeContainThreshold:v},f),p=new Ne(t,h);P(Sg,p.add,p),this._axisGroup.add(p.getGroup()),this._refreshBrushController(h,u,t,s,v,n),Yn(o,this._axisGroup,t)}}},e.prototype._refreshBrushController=function(t,a,n,i,o,s){var l=n.axis.getExtent(),u=l[1]-l[0],v=Math.min(30,Math.abs(u)*.1),c=dt.create({x:l[0],y:-o/2,width:u,height:o});c.x-=v,c.width+=2*v,this._brushController.mount({enableGlobalPan:!0,rotation:t.rotation,x:t.position[0],y:t.position[1]}).setPanels([{panelId:"pl",clipPath:Df(c),isTargetByCursor:If(c,s,i),getLinearBrushOtherExtent:Tf(c,0)}]).enableBrush({brushType:"lineX",brushStyle:a,removeOnClick:!0}).updateCovers(wg(n))},e.prototype._onBrush=function(t){var a=t.areas,n=this.axisModel,i=n.axis,o=O(a,function(s){return[i.coordToData(s.range[0],!0),i.coordToData(s.range[1],!0)]});(!n.option.realtime===t.isEnd||t.removeOnClick)&&this.api.dispatchAction({type:"axisAreaSelect",parallelAxisId:n.id,intervals:o})},e.prototype.dispose=function(){this._brushController.dispose()},e.type="parallelAxis",e}(ze);function bg(r,e,t){return t&&t.type==="axisAreaSelect"&&e.findComponents({mainType:"parallelAxis",query:t})[0]===r}function wg(r){var e=r.axis;return O(r.activeIntervals,function(t){return{brushType:"lineX",panelId:"pl",range:[e.dataToCoord(t[0],!0),e.dataToCoord(t[1],!0)]}})}function _g(r,e){return e.getComponent("parallel",r.get("parallelIndex"))}var Ag={type:"axisAreaSelect",event:"axisAreaSelected"};function Tg(r){r.registerAction(Ag,function(e,t){t.eachComponent({mainType:"parallelAxis",query:e},function(a){a.axis.model.setActiveIntervals(e.intervals)})}),r.registerAction("parallelAxisExpand",function(e,t){t.eachComponent({mainType:"parallel",query:e},function(a){a.setAxisExpand(e)})})}var Ig={type:"value",areaSelectStyle:{width:20,borderWidth:1,borderColor:"rgba(160,197,232)",color:"rgba(160,197,232)",opacity:.3},realtime:!0,z:10};function Fu(r){r.registerComponentView(lg),r.registerComponentModel(vg),r.registerCoordinateSystem("parallel",mg),r.registerPreprocessor(ng),r.registerComponentModel(Dn),r.registerComponentView(xg),Qr(r,"parallel",Dn,Ig),Tg(r)}function Dg(r){$(Fu),r.registerChartView(jd),r.registerSeriesModel(Qd),r.registerVisual(r.PRIORITY.VISUAL.BRUSH,ag)}var Cg=function(){function r(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.cpx1=0,this.cpy1=0,this.cpx2=0,this.cpy2=0,this.extent=0}return r}(),Lg=function(r){G(e,r);function e(t){return r.call(this,t)||this}return e.prototype.getDefaultShape=function(){return new Cg},e.prototype.buildPath=function(t,a){var n=a.extent;t.moveTo(a.x1,a.y1),t.bezierCurveTo(a.cpx1,a.cpy1,a.cpx2,a.cpy2,a.x2,a.y2),a.orient==="vertical"?(t.lineTo(a.x2+n,a.y2),t.bezierCurveTo(a.cpx2+n,a.cpy2,a.cpx1+n,a.cpy1,a.x1+n,a.y1)):(t.lineTo(a.x2,a.y2+n),t.bezierCurveTo(a.cpx2,a.cpy2+n,a.cpx1,a.cpy1+n,a.x1,a.y1+n)),t.closePath()},e.prototype.highlight=function(){Ml(this)},e.prototype.downplay=function(){El(this)},e}(Vt),Pg=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t._focusAdjacencyDisabled=!1,t}return e.prototype.render=function(t,a,n){var i=this,o=t.getGraph(),s=this.group,l=t.layoutInfo,u=l.width,v=l.height,c=t.getData(),f=t.getData("edge"),h=t.get("orient");this._model=t,s.removeAll(),s.x=l.x,s.y=l.y,o.eachEdge(function(p){var d=new Lg,g=ht(d);g.dataIndex=p.dataIndex,g.seriesIndex=t.seriesIndex,g.dataType="edge";var S=p.getModel(),m=S.getModel("lineStyle"),y=m.get("curveness"),w=p.node1.getLayout(),x=p.node1.getModel(),b=x.get("localX"),_=x.get("localY"),T=p.node2.getLayout(),I=p.node2.getModel(),A=I.get("localX"),D=I.get("localY"),E=p.getLayout(),M,C,L,R,V,N,k,B;d.shape.extent=Math.max(1,E.dy),d.shape.orient=h,h==="vertical"?(M=(b!=null?b*u:w.x)+E.sy,C=(_!=null?_*v:w.y)+w.dy,L=(A!=null?A*u:T.x)+E.ty,R=D!=null?D*v:T.y,V=M,N=C*(1-y)+R*y,k=L,B=C*y+R*(1-y)):(M=(b!=null?b*u:w.x)+w.dx,C=(_!=null?_*v:w.y)+E.sy,L=A!=null?A*u:T.x,R=(D!=null?D*v:T.y)+E.ty,V=M*(1-y)+L*y,N=C,k=M*y+L*(1-y),B=R),d.setShape({x1:M,y1:C,x2:L,y2:R,cpx1:V,cpy1:N,cpx2:k,cpy2:B}),d.useStyle(m.getItemStyle()),qo(d.style,h,p);var W=""+S.get("value"),X=Et(S,"edgeLabel");Bt(d,X,{labelFetcher:{getFormattedLabel:function(rt,pt,Ht,U,F,Q){return t.getFormattedLabel(rt,pt,"edge",U,Cr(F,X.normal&&X.normal.get("formatter"),W),Q)}},labelDataIndex:p.dataIndex,defaultText:W}),d.setTextConfig({position:"inside"});var tt=S.getModel("emphasis");Mt(d,S,"lineStyle",function(rt){var pt=rt.getItemStyle();return qo(pt,h,p),pt}),s.add(d),f.setItemGraphicEl(p.dataIndex,d);var J=tt.get("focus");ft(d,J==="adjacency"?p.getAdjacentDataIndices():J==="trajectory"?p.getTrajectoryDataIndices():J,tt.get("blurScope"),tt.get("disabled"))}),o.eachNode(function(p){var d=p.getLayout(),g=p.getModel(),S=g.get("localX"),m=g.get("localY"),y=g.getModel("emphasis"),w=g.get(["itemStyle","borderRadius"])||0,x=new It({shape:{x:S!=null?S*u:d.x,y:m!=null?m*v:d.y,width:d.dx,height:d.dy,r:w},style:g.getModel("itemStyle").getItemStyle(),z2:10});Bt(x,Et(g),{labelFetcher:{getFormattedLabel:function(_,T){return t.getFormattedLabel(_,T,"node")}},labelDataIndex:p.dataIndex,defaultText:p.id}),x.disableLabelAnimation=!0,x.setStyle("fill",p.getVisual("color")),x.setStyle("decal",p.getVisual("style").decal),Mt(x,g),s.add(x),c.setItemGraphicEl(p.dataIndex,x),ht(x).dataType="node";var b=y.get("focus");ft(x,b==="adjacency"?p.getAdjacentDataIndices():b==="trajectory"?p.getTrajectoryDataIndices():b,y.get("blurScope"),y.get("disabled"))}),c.eachItemGraphicEl(function(p,d){var g=c.getItemModel(d);g.get("draggable")&&(p.drift=function(S,m){i._focusAdjacencyDisabled=!0,this.shape.x+=S,this.shape.y+=m,this.dirty(),n.dispatchAction({type:"dragNode",seriesId:t.id,dataIndex:c.getRawIndex(d),localX:this.shape.x/u,localY:this.shape.y/v})},p.ondragend=function(){i._focusAdjacencyDisabled=!1},p.draggable=!0,p.cursor="move")}),!this._data&&t.isAnimationEnabled()&&s.setClipPath(Mg(s.getBoundingRect(),t,function(){s.removeClipPath()})),this._data=t.getData()},e.prototype.dispose=function(){},e.type="sankey",e}(gt);function qo(r,e,t){switch(r.fill){case"source":r.fill=t.node1.getVisual("color"),r.decal=t.node1.getVisual("style").decal;break;case"target":r.fill=t.node2.getVisual("color"),r.decal=t.node2.getVisual("style").decal;break;case"gradient":var a=t.node1.getVisual("color"),n=t.node2.getVisual("color");et(a)&&et(n)&&(r.fill=new ml(0,0,+(e==="horizontal"),+(e==="vertical"),[{color:a,offset:0},{color:n,offset:1}]))}}function Mg(r,e,t){var a=new It({shape:{x:r.x-10,y:r.y-10,width:0,height:r.height+20}});return Yt(a,{shape:{width:r.width+20}},e,t),a}var Eg=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.getInitialData=function(t,a){var n=t.edges||t.links||[],i=t.data||t.nodes||[],o=t.levels||[];this.levelModels=[];for(var s=this.levelModels,l=0;l<o.length;l++)o[l].depth!=null&&o[l].depth>=0&&(s[o[l].depth]=new Rt(o[l],this,a));var u=ku(i,n,this,!0,v);return u.data;function v(c,f){c.wrapMethod("getItemModel",function(h,p){var d=h.parentModel,g=d.getData().getItemLayout(p);if(g){var S=g.depth,m=d.levelModels[S];m&&(h.parentModel=m)}return h}),f.wrapMethod("getItemModel",function(h,p){var d=h.parentModel,g=d.getGraph().getEdgeByIndex(p),S=g.node1.getLayout();if(S){var m=S.depth,y=d.levelModels[m];y&&(h.parentModel=y)}return h})}},e.prototype.setNodePosition=function(t,a){var n=this.option.data||this.option.nodes,i=n[t];i.localX=a[0],i.localY=a[1]},e.prototype.getGraph=function(){return this.getData().graph},e.prototype.getEdgeData=function(){return this.getGraph().edgeData},e.prototype.formatTooltip=function(t,a,n){function i(h){return isNaN(h)||h==null}if(n==="edge"){var o=this.getDataParams(t,n),s=o.data,l=o.value,u=s.source+" -- "+s.target;return Ft("nameValue",{name:u,value:l,noValue:i(l)})}else{var v=this.getGraph().getNodeByIndex(t),c=v.getLayout().value,f=this.getDataParams(t,n).data.name;return Ft("nameValue",{name:f!=null?f+"":null,value:c,noValue:i(c)})}},e.prototype.optionUpdated=function(){},e.prototype.getDataParams=function(t,a){var n=r.prototype.getDataParams.call(this,t,a);if(n.value==null&&a==="node"){var i=this.getGraph().getNodeByIndex(t),o=i.getLayout().value;n.value=o}return n},e.type="series.sankey",e.defaultOption={z:2,coordinateSystem:"view",left:"5%",top:"5%",right:"20%",bottom:"5%",orient:"horizontal",nodeWidth:20,nodeGap:8,draggable:!0,layoutIterations:32,label:{show:!0,position:"right",fontSize:12},edgeLabel:{show:!1,fontSize:12},levels:[],nodeAlign:"justify",lineStyle:{color:"#314656",opacity:.2,curveness:.5},emphasis:{label:{show:!0},lineStyle:{opacity:.5}},select:{itemStyle:{borderColor:"#212121"}},animationEasing:"linear",animationDuration:1e3},e}(xt);function Rg(r,e){r.eachSeriesByType("sankey",function(t){var a=t.get("nodeWidth"),n=t.get("nodeGap"),i=Vg(t,e);t.layoutInfo=i;var o=i.width,s=i.height,l=t.getGraph(),u=l.nodes,v=l.edges;Gg(u);var c=Pt(u,function(d){return d.getLayout().value===0}),f=c.length!==0?0:t.get("layoutIterations"),h=t.get("orient"),p=t.get("nodeAlign");Ng(u,v,a,n,o,s,f,h,p)})}function Vg(r,e){return Se(r.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()})}function Ng(r,e,t,a,n,i,o,s,l){kg(r,e,t,n,i,s,l),Fg(r,e,i,n,a,o,s),jg(r,s)}function Gg(r){P(r,function(e){var t=he(e.outEdges,na),a=he(e.inEdges,na),n=e.getValue()||0,i=Math.max(t,a,n);e.setLayout({value:i},!0)})}function kg(r,e,t,a,n,i,o){for(var s=[],l=[],u=[],v=[],c=0,f=0;f<e.length;f++)s[f]=1;for(var f=0;f<r.length;f++)l[f]=r[f].inEdges.length,l[f]===0&&u.push(r[f]);for(var h=-1;u.length;){for(var p=0;p<u.length;p++){var d=u[p],g=d.hostGraph.data.getRawDataItem(d.dataIndex),S=g.depth!=null&&g.depth>=0;S&&g.depth>h&&(h=g.depth),d.setLayout({depth:S?g.depth:c},!0),i==="vertical"?d.setLayout({dy:t},!0):d.setLayout({dx:t},!0);for(var m=0;m<d.outEdges.length;m++){var y=d.outEdges[m],w=e.indexOf(y);s[w]=0;var x=y.node2,b=r.indexOf(x);--l[b]===0&&v.indexOf(x)<0&&v.push(x)}}++c,u=v,v=[]}for(var f=0;f<s.length;f++)if(s[f]===1)throw new Error("Sankey is a DAG, the original data has cycle!");var _=h>c-1?h:c-1;o&&o!=="left"&&zg(r,o,i,_);var T=i==="vertical"?(n-t)/_:(a-t)/_;Bg(r,T,i)}function Hu(r){var e=r.hostGraph.data.getRawDataItem(r.dataIndex);return e.depth!=null&&e.depth>=0}function zg(r,e,t,a){if(e==="right"){for(var n=[],i=r,o=0;i.length;){for(var s=0;s<i.length;s++){var l=i[s];l.setLayout({skNodeHeight:o},!0);for(var u=0;u<l.inEdges.length;u++){var v=l.inEdges[u];n.indexOf(v.node1)<0&&n.push(v.node1)}}i=n,n=[],++o}P(r,function(c){Hu(c)||c.setLayout({depth:Math.max(0,a-c.getLayout().skNodeHeight)},!0)})}else e==="justify"&&Og(r,a)}function Og(r,e){P(r,function(t){!Hu(t)&&!t.outEdges.length&&t.setLayout({depth:e},!0)})}function Bg(r,e,t){P(r,function(a){var n=a.getLayout().depth*e;t==="vertical"?a.setLayout({y:n},!0):a.setLayout({x:n},!0)})}function Fg(r,e,t,a,n,i,o){var s=Hg(r,o);Wg(s,e,t,a,n,o),ja(s,n,t,a,o);for(var l=1;i>0;i--)l*=.99,Ug(s,l,o),ja(s,n,t,a,o),qg(s,l,o),ja(s,n,t,a,o)}function Hg(r,e){var t=[],a=e==="vertical"?"y":"x",n=yn(r,function(i){return i.getLayout()[a]});return n.keys.sort(function(i,o){return i-o}),P(n.keys,function(i){t.push(n.buckets.get(i))}),t}function Wg(r,e,t,a,n,i){var o=1/0;P(r,function(s){var l=s.length,u=0;P(s,function(c){u+=c.getLayout().value});var v=i==="vertical"?(a-(l-1)*n)/u:(t-(l-1)*n)/u;v<o&&(o=v)}),P(r,function(s){P(s,function(l,u){var v=l.getLayout().value*o;i==="vertical"?(l.setLayout({x:u},!0),l.setLayout({dx:v},!0)):(l.setLayout({y:u},!0),l.setLayout({dy:v},!0))})}),P(e,function(s){var l=+s.getValue()*o;s.setLayout({dy:l},!0)})}function ja(r,e,t,a,n){var i=n==="vertical"?"x":"y";P(r,function(o){o.sort(function(d,g){return d.getLayout()[i]-g.getLayout()[i]});for(var s,l,u,v=0,c=o.length,f=n==="vertical"?"dx":"dy",h=0;h<c;h++)l=o[h],u=v-l.getLayout()[i],u>0&&(s=l.getLayout()[i]+u,n==="vertical"?l.setLayout({x:s},!0):l.setLayout({y:s},!0)),v=l.getLayout()[i]+l.getLayout()[f]+e;var p=n==="vertical"?a:t;if(u=v-e-p,u>0){s=l.getLayout()[i]-u,n==="vertical"?l.setLayout({x:s},!0):l.setLayout({y:s},!0),v=s;for(var h=c-2;h>=0;--h)l=o[h],u=l.getLayout()[i]+l.getLayout()[f]+e-v,u>0&&(s=l.getLayout()[i]-u,n==="vertical"?l.setLayout({x:s},!0):l.setLayout({y:s},!0)),v=l.getLayout()[i]}})}function Ug(r,e,t){P(r.slice().reverse(),function(a){P(a,function(n){if(n.outEdges.length){var i=he(n.outEdges,$g,t)/he(n.outEdges,na);if(isNaN(i)){var o=n.outEdges.length;i=o?he(n.outEdges,Yg,t)/o:0}if(t==="vertical"){var s=n.getLayout().x+(i-pe(n,t))*e;n.setLayout({x:s},!0)}else{var l=n.getLayout().y+(i-pe(n,t))*e;n.setLayout({y:l},!0)}}})})}function $g(r,e){return pe(r.node2,e)*r.getValue()}function Yg(r,e){return pe(r.node2,e)}function Zg(r,e){return pe(r.node1,e)*r.getValue()}function Xg(r,e){return pe(r.node1,e)}function pe(r,e){return e==="vertical"?r.getLayout().x+r.getLayout().dx/2:r.getLayout().y+r.getLayout().dy/2}function na(r){return r.getValue()}function he(r,e,t){for(var a=0,n=r.length,i=-1;++i<n;){var o=+e(r[i],t);isNaN(o)||(a+=o)}return a}function qg(r,e,t){P(r,function(a){P(a,function(n){if(n.inEdges.length){var i=he(n.inEdges,Zg,t)/he(n.inEdges,na);if(isNaN(i)){var o=n.inEdges.length;i=o?he(n.inEdges,Xg,t)/o:0}if(t==="vertical"){var s=n.getLayout().x+(i-pe(n,t))*e;n.setLayout({x:s},!0)}else{var l=n.getLayout().y+(i-pe(n,t))*e;n.setLayout({y:l},!0)}}})})}function jg(r,e){var t=e==="vertical"?"x":"y";P(r,function(a){a.outEdges.sort(function(n,i){return n.node2.getLayout()[t]-i.node2.getLayout()[t]}),a.inEdges.sort(function(n,i){return n.node1.getLayout()[t]-i.node1.getLayout()[t]})}),P(r,function(a){var n=0,i=0;P(a.outEdges,function(o){o.setLayout({sy:n},!0),n+=o.getLayout().dy}),P(a.inEdges,function(o){o.setLayout({ty:i},!0),i+=o.getLayout().dy})})}function Kg(r){r.eachSeriesByType("sankey",function(e){var t=e.getGraph(),a=t.nodes,n=t.edges;if(a.length){var i=1/0,o=-1/0;P(a,function(s){var l=s.getLayout().value;l<i&&(i=l),l>o&&(o=l)}),P(a,function(s){var l=new Kl({type:"color",mappingMethod:"linear",dataExtent:[i,o],visual:e.get("color")}),u=l.mapValueToVisual(s.getLayout().value),v=s.getModel().get(["itemStyle","color"]);v!=null?(s.setVisual("color",v),s.setVisual("style",{fill:v})):(s.setVisual("color",u),s.setVisual("style",{fill:u}))})}n.length&&P(n,function(s){var l=s.getModel().get("lineStyle");s.setVisual("style",l)})})}function Jg(r){r.registerChartView(Pg),r.registerSeriesModel(Eg),r.registerLayout(Rg),r.registerVisual(Kg),r.registerAction({type:"dragNode",event:"dragnode",update:"update"},function(e,t){t.eachComponent({mainType:"series",subType:"sankey",query:e},function(a){a.setNodePosition(e.dataIndex,[e.localX,e.localY])})})}var Wu=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t.defaultValueDimensions=[{name:"min",defaultTooltip:!0},{name:"Q1",defaultTooltip:!0},{name:"median",defaultTooltip:!0},{name:"Q3",defaultTooltip:!0},{name:"max",defaultTooltip:!0}],t.visualDrawType="stroke",t}return e.type="series.boxplot",e.dependencies=["xAxis","yAxis","grid"],e.defaultOption={z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,layout:null,boxWidth:[7,50],itemStyle:{color:"#fff",borderWidth:1},emphasis:{scale:!0,itemStyle:{borderWidth:2,shadowBlur:5,shadowOffsetX:1,shadowOffsetY:1,shadowColor:"rgba(0,0,0,0.2)"}},animationDuration:800},e}(xt);ye(Wu,Cf,!0);var Qg=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,a,n){var i=t.getData(),o=this.group,s=this._data;this._data||o.removeAll();var l=t.get("layout")==="horizontal"?1:0;i.diff(s).add(function(u){if(i.hasValue(u)){var v=i.getItemLayout(u),c=jo(v,i,u,l,!0);i.setItemGraphicEl(u,c),o.add(c)}}).update(function(u,v){var c=s.getItemGraphicEl(v);if(!i.hasValue(u)){o.remove(c);return}var f=i.getItemLayout(u);c?(Re(c),Uu(f,c,i,u)):c=jo(f,i,u,l),o.add(c),i.setItemGraphicEl(u,c)}).remove(function(u){var v=s.getItemGraphicEl(u);v&&o.remove(v)}).execute(),this._data=i},e.prototype.remove=function(t){var a=this.group,n=this._data;this._data=null,n&&n.eachItemGraphicEl(function(i){i&&a.remove(i)})},e.type="boxplot",e}(gt),ty=function(){function r(){}return r}(),ey=function(r){G(e,r);function e(t){var a=r.call(this,t)||this;return a.type="boxplotBoxPath",a}return e.prototype.getDefaultShape=function(){return new ty},e.prototype.buildPath=function(t,a){var n=a.points,i=0;for(t.moveTo(n[i][0],n[i][1]),i++;i<4;i++)t.lineTo(n[i][0],n[i][1]);for(t.closePath();i<n.length;i++)t.moveTo(n[i][0],n[i][1]),i++,t.lineTo(n[i][0],n[i][1])},e}(Vt);function jo(r,e,t,a,n){var i=r.ends,o=new ey({shape:{points:n?ry(i,a,r):i}});return Uu(r,o,e,t,n),o}function Uu(r,e,t,a,n){var i=t.hostModel,o=Je[n?"initProps":"updateProps"];o(e,{shape:{points:r.ends}},i,a),e.useStyle(t.getItemVisual(a,"style")),e.style.strokeNoScale=!0,e.z2=100;var s=t.getItemModel(a),l=s.getModel("emphasis");Mt(e,s),ft(e,l.get("focus"),l.get("blurScope"),l.get("disabled"))}function ry(r,e,t){return O(r,function(a){return a=a.slice(),a[e]=t.initBaseline,a})}var pr=P;function ay(r){var e=ny(r);pr(e,function(t){var a=t.seriesModels;a.length&&(iy(t),pr(a,function(n,i){oy(n,t.boxOffsetList[i],t.boxWidthList[i])}))})}function ny(r){var e=[],t=[];return r.eachSeriesByType("boxplot",function(a){var n=a.getBaseAxis(),i=xe(t,n);i<0&&(i=t.length,t[i]=n,e[i]={axis:n,seriesModels:[]}),e[i].seriesModels.push(a)}),e}function iy(r){var e=r.axis,t=r.seriesModels,a=t.length,n=r.boxWidthList=[],i=r.boxOffsetList=[],o=[],s;if(e.type==="category")s=e.getBandWidth();else{var l=0;pr(t,function(p){l=Math.max(l,p.getData().count())});var u=e.getExtent();s=Math.abs(u[1]-u[0])/l}pr(t,function(p){var d=p.get("boxWidth");Y(d)||(d=[d,d]),o.push([z(d[0],s)||0,z(d[1],s)||0])});var v=s*.8-2,c=v/a*.3,f=(v-c*(a-1))/a,h=f/2-v/2;pr(t,function(p,d){i.push(h),h+=c+f,n.push(Math.min(Math.max(f,o[d][0]),o[d][1]))})}function oy(r,e,t){var a=r.coordinateSystem,n=r.getData(),i=t/2,o=r.get("layout")==="horizontal"?0:1,s=1-o,l=["x","y"],u=n.mapDimension(l[o]),v=n.mapDimensionsAll(l[s]);if(u==null||v.length<5)return;for(var c=0;c<n.count();c++){var f=n.get(u,c),h=y(f,v[2],c),p=y(f,v[0],c),d=y(f,v[1],c),g=y(f,v[3],c),S=y(f,v[4],c),m=[];w(m,d,!1),w(m,g,!0),m.push(p,d,S,g),x(m,p),x(m,S),x(m,h),n.setItemLayout(c,{initBaseline:h[s],ends:m})}function y(b,_,T){var I=n.get(_,T),A=[];A[o]=b,A[s]=I;var D;return isNaN(b)||isNaN(I)?D=[NaN,NaN]:(D=a.dataToPoint(A),D[o]+=e),D}function w(b,_,T){var I=_.slice(),A=_.slice();I[o]+=i,A[o]-=i,T?b.push(I,A):b.push(A,I)}function x(b,_){var T=_.slice(),I=_.slice();T[o]-=i,I[o]+=i,b.push(T,I)}}function sy(r,e){e=e||{};for(var t=[],a=[],n=e.boundIQR,i=n==="none"||n===0,o=0;o<r.length;o++){var s=Kr(r[o].slice()),l=Pa(s,.25),u=Pa(s,.5),v=Pa(s,.75),c=s[0],f=s[s.length-1],h=(n??1.5)*(v-l),p=i?c:Math.max(c,l-h),d=i?f:Math.min(f,v+h),g=e.itemNameFormatter,S=st(g)?g({value:o}):et(g)?g.replace("{value}",o+""):o+"";t.push([S,p,l,u,v,d]);for(var m=0;m<s.length;m++){var y=s[m];if(y<p||y>d){var w=[S,y];a.push(w)}}}return{boxData:t,outliers:a}}var ly={type:"echarts:boxplot",transform:function(e){var t=e.upstream;if(t.sourceFormat!==Rl){var a="";ot(a)}var n=sy(t.getRawData(),e.config);return[{dimensions:["ItemName","Low","Q1","Q2","Q3","High"],data:n.boxData},{data:n.outliers}]}};function uy(r){r.registerSeriesModel(Wu),r.registerChartView(Qg),r.registerLayout(ay),r.registerTransform(ly)}function Ko(r,e){var t=e.rippleEffectColor||e.color;r.eachChild(function(a){a.attr({z:e.z,zlevel:e.zlevel,style:{stroke:e.brushType==="stroke"?t:null,fill:e.brushType==="fill"?t:null}})})}var vy=function(r){G(e,r);function e(t,a){var n=r.call(this)||this,i=new jl(t,a),o=new Z;return n.add(i),n.add(o),n.updateData(t,a),n}return e.prototype.stopEffectAnimation=function(){this.childAt(1).removeAll()},e.prototype.startEffectAnimation=function(t){for(var a=t.symbolType,n=t.color,i=t.rippleNumber,o=this.childAt(1),s=0;s<i;s++){var l=Ve(a,-1,-1,2,2,n);l.attr({style:{strokeNoScale:!0},z2:99,silent:!0,scaleX:.5,scaleY:.5});var u=-s/i*t.period+t.effectOffset;l.animate("",!0).when(t.period,{scaleX:t.rippleScale/2,scaleY:t.rippleScale/2}).delay(u).start(),l.animateStyle(!0).when(t.period,{opacity:0}).delay(u).start(),o.add(l)}Ko(o,t)},e.prototype.updateEffectAnimation=function(t){for(var a=this._effectCfg,n=this.childAt(1),i=["symbolType","period","rippleScale","rippleNumber"],o=0;o<i.length;o++){var s=i[o];if(a[s]!==t[s]){this.stopEffectAnimation(),this.startEffectAnimation(t);return}}Ko(n,t)},e.prototype.highlight=function(){Ml(this)},e.prototype.downplay=function(){El(this)},e.prototype.getSymbolType=function(){var t=this.childAt(0);return t&&t.getSymbolType()},e.prototype.updateData=function(t,a){var n=this,i=t.hostModel;this.childAt(0).updateData(t,a);var o=this.childAt(1),s=t.getItemModel(a),l=t.getItemVisual(a,"symbol"),u=dl(t.getItemVisual(a,"symbolSize")),v=t.getItemVisual(a,"style"),c=v&&v.fill,f=s.getModel("emphasis");o.setScale(u),o.traverse(function(g){g.setStyle("fill",c)});var h=Vl(t.getItemVisual(a,"symbolOffset"),u);h&&(o.x=h[0],o.y=h[1]);var p=t.getItemVisual(a,"symbolRotate");o.rotation=(p||0)*Math.PI/180||0;var d={};d.showEffectOn=i.get("showEffectOn"),d.rippleScale=s.get(["rippleEffect","scale"]),d.brushType=s.get(["rippleEffect","brushType"]),d.period=s.get(["rippleEffect","period"])*1e3,d.effectOffset=a/t.count(),d.z=i.getShallow("z")||0,d.zlevel=i.getShallow("zlevel")||0,d.symbolType=l,d.color=c,d.rippleEffectColor=s.get(["rippleEffect","color"]),d.rippleNumber=s.get(["rippleEffect","number"]),d.showEffectOn==="render"?(this._effectCfg?this.updateEffectAnimation(d):this.startEffectAnimation(d),this._effectCfg=d):(this._effectCfg=null,this.stopEffectAnimation(),this.onHoverStateChange=function(g){g==="emphasis"?d.showEffectOn!=="render"&&n.startEffectAnimation(d):g==="normal"&&d.showEffectOn!=="render"&&n.stopEffectAnimation()}),this._effectCfg=d,ft(this,f.get("focus"),f.get("blurScope"),f.get("disabled"))},e.prototype.fadeOut=function(t){t&&t()},e}(Z),cy=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(){this._symbolDraw=new Jn(vy)},e.prototype.render=function(t,a,n){var i=t.getData(),o=this._symbolDraw;o.updateData(i,{clipShape:this._getClipShape(t)}),this.group.add(o.group)},e.prototype._getClipShape=function(t){var a=t.coordinateSystem,n=a&&a.getArea&&a.getArea();return t.get("clip",!0)?n:null},e.prototype.updateTransform=function(t,a,n){var i=t.getData();this.group.dirty();var o=tu("").reset(t,a,n);o.progress&&o.progress({start:0,end:i.count(),count:i.count()},i),this._symbolDraw.updateLayout()},e.prototype._updateGroupTransform=function(t){var a=t.coordinateSystem;a&&a.getRoamTransform&&(this.group.transform=xc(a.getRoamTransform()),this.group.decomposeTransform())},e.prototype.remove=function(t,a){this._symbolDraw&&this._symbolDraw.remove(!0)},e.type="effectScatter",e}(gt),fy=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t.hasSymbolVisual=!0,t}return e.prototype.getInitialData=function(t,a){return Pr(null,this,{useEncodeDefaulter:!0})},e.prototype.brushSelector=function(t,a,n){return n.point(a.getItemLayout(t))},e.type="series.effectScatter",e.dependencies=["grid","polar"],e.defaultOption={coordinateSystem:"cartesian2d",z:2,legendHoverLink:!0,effectType:"ripple",progressive:0,showEffectOn:"render",clip:!0,rippleEffect:{period:4,scale:2.5,brushType:"fill",number:3},universalTransition:{divideShape:"clone"},symbolSize:10},e}(xt);function hy(r){r.registerChartView(cy),r.registerSeriesModel(fy),r.registerLayout(tu("effectScatter"))}var $u=function(r){G(e,r);function e(t,a,n){var i=r.call(this)||this;return i.add(i.createLine(t,a,n)),i._updateEffectSymbol(t,a),i}return e.prototype.createLine=function(t,a,n){return new eu(t,a,n)},e.prototype._updateEffectSymbol=function(t,a){var n=t.getItemModel(a),i=n.getModel("effect"),o=i.get("symbolSize"),s=i.get("symbol");Y(o)||(o=[o,o]);var l=t.getItemVisual(a,"style"),u=i.get("color")||l&&l.stroke,v=this.childAt(1);this._symbolType!==s&&(this.remove(v),v=Ve(s,-.5,-.5,1,1,u),v.z2=100,v.culling=!0,this.add(v)),v&&(v.setStyle("shadowColor",u),v.setStyle(i.getItemStyle(["color"])),v.scaleX=o[0],v.scaleY=o[1],v.setColor(u),this._symbolType=s,this._symbolScale=o,this._updateEffectAnimation(t,i,a))},e.prototype._updateEffectAnimation=function(t,a,n){var i=this.childAt(1);if(i){var o=t.getItemLayout(n),s=a.get("period")*1e3,l=a.get("loop"),u=a.get("roundTrip"),v=a.get("constantSpeed"),c=zt(a.get("delay"),function(h){return h/t.count()*s/3});if(i.ignore=!0,this._updateAnimationPoints(i,o),v>0&&(s=this._getLineLength(i)/v*1e3),s!==this._period||l!==this._loop||u!==this._roundTrip){i.stopAnimation();var f=void 0;st(c)?f=c(n):f=c,i.__t>0&&(f=-s*i.__t),this._animateSymbol(i,s,f,l,u)}this._period=s,this._loop=l,this._roundTrip=u}},e.prototype._animateSymbol=function(t,a,n,i,o){if(a>0){t.__t=0;var s=this,l=t.animate("",i).when(o?a*2:a,{__t:o?2:1}).delay(n).during(function(){s._updateSymbolPosition(t)});i||l.done(function(){s.remove(t)}),l.start()}},e.prototype._getLineLength=function(t){return ur(t.__p1,t.__cp1)+ur(t.__cp1,t.__p2)},e.prototype._updateAnimationPoints=function(t,a){t.__p1=a[0],t.__p2=a[1],t.__cp1=a[2]||[(a[0][0]+a[1][0])/2,(a[0][1]+a[1][1])/2]},e.prototype.updateData=function(t,a,n){this.childAt(0).updateData(t,a,n),this._updateEffectSymbol(t,a)},e.prototype._updateSymbolPosition=function(t){var a=t.__p1,n=t.__p2,i=t.__cp1,o=t.__t<1?t.__t:2-t.__t,s=[t.x,t.y],l=s.slice(),u=Il,v=bc;s[0]=u(a[0],i[0],n[0],o),s[1]=u(a[1],i[1],n[1],o);var c=t.__t<1?v(a[0],i[0],n[0],o):v(n[0],i[0],a[0],1-o),f=t.__t<1?v(a[1],i[1],n[1],o):v(n[1],i[1],a[1],1-o);t.rotation=-Math.atan2(f,c)-Math.PI/2,(this._symbolType==="line"||this._symbolType==="rect"||this._symbolType==="roundRect")&&(t.__lastT!==void 0&&t.__lastT<t.__t?(t.scaleY=ur(l,s)*1.05,o===1&&(s[0]=l[0]+(s[0]-l[0])/2,s[1]=l[1]+(s[1]-l[1])/2)):t.__lastT===1?t.scaleY=2*ur(a,s):t.scaleY=this._symbolScale[1]),t.__lastT=t.__t,t.ignore=!1,t.x=s[0],t.y=s[1]},e.prototype.updateLayout=function(t,a){this.childAt(0).updateLayout(t,a);var n=t.getItemModel(a).getModel("effect");this._updateEffectAnimation(t,n,a)},e}(Z),Yu=function(r){G(e,r);function e(t,a,n){var i=r.call(this)||this;return i._createPolyline(t,a,n),i}return e.prototype._createPolyline=function(t,a,n){var i=t.getItemLayout(a),o=new ke({shape:{points:i}});this.add(o),this._updateCommonStl(t,a,n)},e.prototype.updateData=function(t,a,n){var i=t.hostModel,o=this.childAt(0),s={shape:{points:t.getItemLayout(a)}};ct(o,s,i,a),this._updateCommonStl(t,a,n)},e.prototype._updateCommonStl=function(t,a,n){var i=this.childAt(0),o=t.getItemModel(a),s=n&&n.emphasisLineStyle,l=n&&n.focus,u=n&&n.blurScope,v=n&&n.emphasisDisabled;if(!n||t.hasItemOption){var c=o.getModel("emphasis");s=c.getModel("lineStyle").getLineStyle(),v=c.get("disabled"),l=c.get("focus"),u=c.get("blurScope")}i.useStyle(t.getItemVisual(a,"style")),i.style.fill=null,i.style.strokeNoScale=!0;var f=i.ensureState("emphasis");f.style=s,ft(this,l,u,v)},e.prototype.updateLayout=function(t,a){var n=this.childAt(0);n.setShape("points",t.getItemLayout(a))},e}(Z),py=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t._lastFrame=0,t._lastFramePercent=0,t}return e.prototype.createLine=function(t,a,n){return new Yu(t,a,n)},e.prototype._updateAnimationPoints=function(t,a){this._points=a;for(var n=[0],i=0,o=1;o<a.length;o++){var s=a[o-1],l=a[o];i+=ur(s,l),n.push(i)}if(i===0){this._length=0;return}for(var o=0;o<n.length;o++)n[o]/=i;this._offsets=n,this._length=i},e.prototype._getLineLength=function(){return this._length},e.prototype._updateSymbolPosition=function(t){var a=t.__t<1?t.__t:2-t.__t,n=this._points,i=this._offsets,o=n.length;if(i){var s=this._lastFrame,l;if(a<this._lastFramePercent){var u=Math.min(s+1,o-1);for(l=u;l>=0&&!(i[l]<=a);l--);l=Math.min(l,o-2)}else{for(l=s;l<o&&!(i[l]>a);l++);l=Math.min(l-1,o-2)}var v=(a-i[l])/(i[l+1]-i[l]),c=n[l],f=n[l+1];t.x=c[0]*(1-v)+v*f[0],t.y=c[1]*(1-v)+v*f[1];var h=t.__t<1?f[0]-c[0]:c[0]-f[0],p=t.__t<1?f[1]-c[1]:c[1]-f[1];t.rotation=-Math.atan2(p,h)-Math.PI/2,this._lastFrame=l,this._lastFramePercent=a,t.ignore=!1}},e}($u),dy=function(){function r(){this.polyline=!1,this.curveness=0,this.segs=[]}return r}(),gy=function(r){G(e,r);function e(t){var a=r.call(this,t)||this;return a._off=0,a.hoverDataIdx=-1,a}return e.prototype.reset=function(){this.notClear=!1,this._off=0},e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new dy},e.prototype.buildPath=function(t,a){var n=a.segs,i=a.curveness,o;if(a.polyline)for(o=this._off;o<n.length;){var s=n[o++];if(s>0){t.moveTo(n[o++],n[o++]);for(var l=1;l<s;l++)t.lineTo(n[o++],n[o++])}}else for(o=this._off;o<n.length;){var u=n[o++],v=n[o++],c=n[o++],f=n[o++];if(t.moveTo(u,v),i>0){var h=(u+c)/2-(v-f)*i,p=(v+f)/2-(c-u)*i;t.quadraticCurveTo(h,p,c,f)}else t.lineTo(c,f)}this.incremental&&(this._off=o,this.notClear=!0)},e.prototype.findDataIndex=function(t,a){var n=this.shape,i=n.segs,o=n.curveness,s=this.style.lineWidth;if(n.polyline)for(var l=0,u=0;u<i.length;){var v=i[u++];if(v>0)for(var c=i[u++],f=i[u++],h=1;h<v;h++){var p=i[u++],d=i[u++];if(Bi(c,f,p,d,s,t,a))return l}l++}else for(var l=0,u=0;u<i.length;){var c=i[u++],f=i[u++],p=i[u++],d=i[u++];if(o>0){var g=(c+p)/2-(f-d)*o,S=(f+d)/2-(p-c)*o;if(wc(c,f,g,S,p,d,s,t,a))return l}else if(Bi(c,f,p,d,s,t,a))return l;l++}return-1},e.prototype.contain=function(t,a){var n=this.transformCoordToLocal(t,a),i=this.getBoundingRect();if(t=n[0],a=n[1],i.contain(t,a)){var o=this.hoverDataIdx=this.findDataIndex(t,a);return o>=0}return this.hoverDataIdx=-1,!1},e.prototype.getBoundingRect=function(){var t=this._rect;if(!t){for(var a=this.shape,n=a.segs,i=1/0,o=1/0,s=-1/0,l=-1/0,u=0;u<n.length;){var v=n[u++],c=n[u++];i=Math.min(v,i),s=Math.max(v,s),o=Math.min(c,o),l=Math.max(c,l)}t=this._rect=new dt(i,o,s,l)}return t},e}(Vt),yy=function(){function r(){this.group=new Z}return r.prototype.updateData=function(e){this._clear();var t=this._create();t.setShape({segs:e.getLayout("linesPoints")}),this._setCommon(t,e)},r.prototype.incrementalPrepareUpdate=function(e){this.group.removeAll(),this._clear()},r.prototype.incrementalUpdate=function(e,t){var a=this._newAdded[0],n=t.getLayout("linesPoints"),i=a&&a.shape.segs;if(i&&i.length<2e4){var o=i.length,s=new Float32Array(o+n.length);s.set(i),s.set(n,o),a.setShape({segs:s})}else{this._newAdded=[];var l=this._create();l.incremental=!0,l.setShape({segs:n}),this._setCommon(l,t),l.__startIndex=e.start}},r.prototype.remove=function(){this._clear()},r.prototype.eachRendered=function(e){this._newAdded[0]&&e(this._newAdded[0])},r.prototype._create=function(){var e=new gy({cursor:"default",ignoreCoarsePointer:!0});return this._newAdded.push(e),this.group.add(e),e},r.prototype._setCommon=function(e,t,a){var n=t.hostModel;e.setShape({polyline:n.get("polyline"),curveness:n.get(["lineStyle","curveness"])}),e.useStyle(n.getModel("lineStyle").getLineStyle()),e.style.strokeNoScale=!0;var i=t.getVisual("style");i&&i.stroke&&e.setStyle("stroke",i.stroke),e.setStyle("fill",null);var o=ht(e);o.seriesIndex=n.seriesIndex,e.on("mousemove",function(s){o.dataIndex=null;var l=e.hoverDataIdx;l>0&&(o.dataIndex=l+e.__startIndex)})},r.prototype._clear=function(){this._newAdded=[],this.group.removeAll()},r}(),Zu={seriesType:"lines",plan:_c(),reset:function(r){var e=r.coordinateSystem;if(e){var t=r.get("polyline"),a=r.pipelineContext.large;return{progress:function(n,i){var o=[];if(a){var s=void 0,l=n.end-n.start;if(t){for(var u=0,v=n.start;v<n.end;v++)u+=r.getLineCoordsCount(v);s=new Float32Array(l+u*2)}else s=new Float32Array(l*4);for(var c=0,f=[],v=n.start;v<n.end;v++){var h=r.getLineCoords(v,o);t&&(s[c++]=h);for(var p=0;p<h;p++)f=e.dataToPoint(o[p],!1,f),s[c++]=f[0],s[c++]=f[1]}i.setLayout("linesPoints",s)}else for(var v=n.start;v<n.end;v++){var d=i.getItemModel(v),h=r.getLineCoords(v,o),g=[];if(t)for(var S=0;S<h;S++)g.push(e.dataToPoint(o[S]));else{g[0]=e.dataToPoint(o[0]),g[1]=e.dataToPoint(o[1]);var m=d.get(["lineStyle","curveness"]);+m&&(g[2]=[(g[0][0]+g[1][0])/2-(g[0][1]-g[1][1])*m,(g[0][1]+g[1][1])/2-(g[1][0]-g[0][0])*m])}i.setItemLayout(v,g)}}}}}},my=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,a,n){var i=t.getData(),o=this._updateLineDraw(i,t),s=t.get("zlevel"),l=t.get(["effect","trailLength"]),u=n.getZr(),v=u.painter.getType()==="svg";v||u.painter.getLayer(s).clear(!0),this._lastZlevel!=null&&!v&&u.configLayer(this._lastZlevel,{motionBlur:!1}),this._showEffect(t)&&l>0&&(v||u.configLayer(s,{motionBlur:!0,lastFrameAlpha:Math.max(Math.min(l/10+.9,1),0)})),o.updateData(i);var c=t.get("clip",!0)&&Qn(t.coordinateSystem,!1,t);c?this.group.setClipPath(c):this.group.removeClipPath(),this._lastZlevel=s,this._finished=!0},e.prototype.incrementalPrepareRender=function(t,a,n){var i=t.getData(),o=this._updateLineDraw(i,t);o.incrementalPrepareUpdate(i),this._clearLayer(n),this._finished=!1},e.prototype.incrementalRender=function(t,a,n){this._lineDraw.incrementalUpdate(t,a.getData()),this._finished=t.end===a.getData().count()},e.prototype.eachRendered=function(t){this._lineDraw&&this._lineDraw.eachRendered(t)},e.prototype.updateTransform=function(t,a,n){var i=t.getData(),o=t.pipelineContext;if(!this._finished||o.large||o.progressiveRender)return{update:!0};var s=Zu.reset(t,a,n);s.progress&&s.progress({start:0,end:i.count(),count:i.count()},i),this._lineDraw.updateLayout(),this._clearLayer(n)},e.prototype._updateLineDraw=function(t,a){var n=this._lineDraw,i=this._showEffect(a),o=!!a.get("polyline"),s=a.pipelineContext,l=s.large;return(!n||i!==this._hasEffet||o!==this._isPolyline||l!==this._isLargeDraw)&&(n&&n.remove(),n=this._lineDraw=l?new yy:new Jl(o?i?py:Yu:i?$u:eu),this._hasEffet=i,this._isPolyline=o,this._isLargeDraw=l),this.group.add(n.group),n},e.prototype._showEffect=function(t){return!!t.get(["effect","show"])},e.prototype._clearLayer=function(t){var a=t.getZr(),n=a.painter.getType()==="svg";!n&&this._lastZlevel!=null&&a.painter.getLayer(this._lastZlevel).clear(!0)},e.prototype.remove=function(t,a){this._lineDraw&&this._lineDraw.remove(),this._lineDraw=null,this._clearLayer(a)},e.prototype.dispose=function(t,a){this.remove(t,a)},e.type="lines",e}(gt),Sy=typeof Uint32Array>"u"?Array:Uint32Array,xy=typeof Float64Array>"u"?Array:Float64Array;function Jo(r){var e=r.data;e&&e[0]&&e[0][0]&&e[0][0].coord&&(r.data=O(e,function(t){var a=[t[0].coord,t[1].coord],n={coords:a};return t[0].name&&(n.fromName=t[0].name),t[1].name&&(n.toName=t[1].name),Fn([n,t[0],t[1]])}))}var by=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t.visualStyleAccessPath="lineStyle",t.visualDrawType="stroke",t}return e.prototype.init=function(t){t.data=t.data||[],Jo(t);var a=this._processFlatCoordsArray(t.data);this._flatCoords=a.flatCoords,this._flatCoordsOffset=a.flatCoordsOffset,a.flatCoords&&(t.data=new Float32Array(a.count)),r.prototype.init.apply(this,arguments)},e.prototype.mergeOption=function(t){if(Jo(t),t.data){var a=this._processFlatCoordsArray(t.data);this._flatCoords=a.flatCoords,this._flatCoordsOffset=a.flatCoordsOffset,a.flatCoords&&(t.data=new Float32Array(a.count))}r.prototype.mergeOption.apply(this,arguments)},e.prototype.appendData=function(t){var a=this._processFlatCoordsArray(t.data);a.flatCoords&&(this._flatCoords?(this._flatCoords=Xr(this._flatCoords,a.flatCoords),this._flatCoordsOffset=Xr(this._flatCoordsOffset,a.flatCoordsOffset)):(this._flatCoords=a.flatCoords,this._flatCoordsOffset=a.flatCoordsOffset),t.data=new Float32Array(a.count)),this.getRawData().appendData(t.data)},e.prototype._getCoordsFromItemModel=function(t){var a=this.getData().getItemModel(t),n=a.option instanceof Array?a.option:a.getShallow("coords");return n},e.prototype.getLineCoordsCount=function(t){return this._flatCoordsOffset?this._flatCoordsOffset[t*2+1]:this._getCoordsFromItemModel(t).length},e.prototype.getLineCoords=function(t,a){if(this._flatCoordsOffset){for(var n=this._flatCoordsOffset[t*2],i=this._flatCoordsOffset[t*2+1],o=0;o<i;o++)a[o]=a[o]||[],a[o][0]=this._flatCoords[n+o*2],a[o][1]=this._flatCoords[n+o*2+1];return i}else{for(var s=this._getCoordsFromItemModel(t),o=0;o<s.length;o++)a[o]=a[o]||[],a[o][0]=s[o][0],a[o][1]=s[o][1];return s.length}},e.prototype._processFlatCoordsArray=function(t){var a=0;if(this._flatCoords&&(a=this._flatCoords.length),Xt(t[0])){for(var n=t.length,i=new Sy(n),o=new xy(n),s=0,l=0,u=0,v=0;v<n;){u++;var c=t[v++];i[l++]=s+a,i[l++]=c;for(var f=0;f<c;f++){var h=t[v++],p=t[v++];o[s++]=h,o[s++]=p}}return{flatCoordsOffset:new Uint32Array(i.buffer,0,l),flatCoords:o,count:u}}return{flatCoordsOffset:null,flatCoords:null,count:t.length}},e.prototype.getInitialData=function(t,a){var n=new te(["value"],this);return n.hasItemOption=!1,n.initData(t.data,[],function(i,o,s,l){if(i instanceof Array)return NaN;n.hasItemOption=!0;var u=i.value;if(u!=null)return u instanceof Array?u[l]:u}),n},e.prototype.formatTooltip=function(t,a,n){var i=this.getData(),o=i.getItemModel(t),s=o.get("name");if(s)return s;var l=o.get("fromName"),u=o.get("toName"),v=[];return l!=null&&v.push(l),u!=null&&v.push(u),Ft("nameValue",{name:v.join(" > ")})},e.prototype.preventIncremental=function(){return!!this.get(["effect","show"])},e.prototype.getProgressive=function(){var t=this.option.progressive;return t??(this.option.large?1e4:this.get("progressive"))},e.prototype.getProgressiveThreshold=function(){var t=this.option.progressiveThreshold;return t??(this.option.large?2e4:this.get("progressiveThreshold"))},e.prototype.getZLevelKey=function(){var t=this.getModel("effect"),a=t.get("trailLength");return this.getData().count()>this.getProgressiveThreshold()?this.id:t.get("show")&&a>0?a+"":""},e.type="series.lines",e.dependencies=["grid","polar","geo","calendar"],e.defaultOption={coordinateSystem:"geo",z:2,legendHoverLink:!0,xAxisIndex:0,yAxisIndex:0,symbol:["none","none"],symbolSize:[10,10],geoIndex:0,effect:{show:!1,period:4,constantSpeed:0,symbol:"circle",symbolSize:3,loop:!0,trailLength:.2},large:!1,largeThreshold:2e3,polyline:!1,clip:!0,label:{show:!1,position:"end"},lineStyle:{opacity:.5}},e}(xt);function Fr(r){return r instanceof Array||(r=[r,r]),r}var wy={seriesType:"lines",reset:function(r){var e=Fr(r.get("symbol")),t=Fr(r.get("symbolSize")),a=r.getData();a.setVisual("fromSymbol",e&&e[0]),a.setVisual("toSymbol",e&&e[1]),a.setVisual("fromSymbolSize",t&&t[0]),a.setVisual("toSymbolSize",t&&t[1]);function n(i,o){var s=i.getItemModel(o),l=Fr(s.getShallow("symbol",!0)),u=Fr(s.getShallow("symbolSize",!0));l[0]&&i.setItemVisual(o,"fromSymbol",l[0]),l[1]&&i.setItemVisual(o,"toSymbol",l[1]),u[0]&&i.setItemVisual(o,"fromSymbolSize",u[0]),u[1]&&i.setItemVisual(o,"toSymbolSize",u[1])}return{dataEach:a.hasItemOption?n:null}}};function _y(r){r.registerChartView(my),r.registerSeriesModel(by),r.registerLayout(Zu),r.registerVisual(wy)}var Ay=256,Ty=function(){function r(){this.blurSize=30,this.pointSize=20,this.maxOpacity=1,this.minOpacity=0,this._gradientPixels={inRange:null,outOfRange:null};var e=Fi.createCanvas();this.canvas=e}return r.prototype.update=function(e,t,a,n,i,o){var s=this._getBrush(),l=this._getGradient(i,"inRange"),u=this._getGradient(i,"outOfRange"),v=this.pointSize+this.blurSize,c=this.canvas,f=c.getContext("2d"),h=e.length;c.width=t,c.height=a;for(var p=0;p<h;++p){var d=e[p],g=d[0],S=d[1],m=d[2],y=n(m);f.globalAlpha=y,f.drawImage(s,g-v,S-v)}if(!c.width||!c.height)return c;for(var w=f.getImageData(0,0,c.width,c.height),x=w.data,b=0,_=x.length,T=this.minOpacity,I=this.maxOpacity,A=I-T;b<_;){var y=x[b+3]/256,D=Math.floor(y*(Ay-1))*4;if(y>0){var E=o(y)?l:u;y>0&&(y=y*A+T),x[b++]=E[D],x[b++]=E[D+1],x[b++]=E[D+2],x[b++]=E[D+3]*y*256}else b+=4}return f.putImageData(w,0,0),c},r.prototype._getBrush=function(){var e=this._brushCanvas||(this._brushCanvas=Fi.createCanvas()),t=this.pointSize+this.blurSize,a=t*2;e.width=a,e.height=a;var n=e.getContext("2d");return n.clearRect(0,0,a,a),n.shadowOffsetX=a,n.shadowBlur=this.blurSize,n.shadowColor="#000",n.beginPath(),n.arc(-t,t,this.pointSize,0,Math.PI*2,!0),n.closePath(),n.fill(),e},r.prototype._getGradient=function(e,t){for(var a=this._gradientPixels,n=a[t]||(a[t]=new Uint8ClampedArray(256*4)),i=[0,0,0,0],o=0,s=0;s<256;s++)e[t](s/255,!0,i),n[o++]=i[0],n[o++]=i[1],n[o++]=i[2],n[o++]=i[3];return n},r}();function Iy(r,e,t){var a=r[1]-r[0];e=O(e,function(o){return{interval:[(o.interval[0]-r[0])/a,(o.interval[1]-r[0])/a]}});var n=e.length,i=0;return function(o){var s;for(s=i;s<n;s++){var l=e[s].interval;if(l[0]<=o&&o<=l[1]){i=s;break}}if(s===n)for(s=i-1;s>=0;s--){var l=e[s].interval;if(l[0]<=o&&o<=l[1]){i=s;break}}return s>=0&&s<n&&t[s]}}function Dy(r,e){var t=r[1]-r[0];return e=[(e[0]-r[0])/t,(e[1]-r[0])/t],function(a){return a>=e[0]&&a<=e[1]}}function Qo(r){var e=r.dimensions;return e[0]==="lng"&&e[1]==="lat"}var Cy=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,a,n){var i;a.eachComponent("visualMap",function(s){s.eachTargetSeries(function(l){l===t&&(i=s)})}),this._progressiveEls=null,this.group.removeAll();var o=t.coordinateSystem;o.type==="cartesian2d"||o.type==="calendar"?this._renderOnCartesianAndCalendar(t,n,0,t.getData().count()):Qo(o)&&this._renderOnGeo(o,t,i,n)},e.prototype.incrementalPrepareRender=function(t,a,n){this.group.removeAll()},e.prototype.incrementalRender=function(t,a,n,i){var o=a.coordinateSystem;o&&(Qo(o)?this.render(a,n,i):(this._progressiveEls=[],this._renderOnCartesianAndCalendar(a,i,t.start,t.end,!0)))},e.prototype.eachRendered=function(t){Nl(this._progressiveEls||this.group,t)},e.prototype._renderOnCartesianAndCalendar=function(t,a,n,i,o){var s=t.coordinateSystem,l=ti(s,"cartesian2d"),u,v,c,f;if(l){var h=s.getAxis("x"),p=s.getAxis("y");u=h.getBandWidth()+.5,v=p.getBandWidth()+.5,c=h.scale.getExtent(),f=p.scale.getExtent()}for(var d=this.group,g=t.getData(),S=t.getModel(["emphasis","itemStyle"]).getItemStyle(),m=t.getModel(["blur","itemStyle"]).getItemStyle(),y=t.getModel(["select","itemStyle"]).getItemStyle(),w=t.get(["itemStyle","borderRadius"]),x=Et(t),b=t.getModel("emphasis"),_=b.get("focus"),T=b.get("blurScope"),I=b.get("disabled"),A=l?[g.mapDimension("x"),g.mapDimension("y"),g.mapDimension("value")]:[g.mapDimension("time"),g.mapDimension("value")],D=n;D<i;D++){var E=void 0,M=g.getItemVisual(D,"style");if(l){var C=g.get(A[0],D),L=g.get(A[1],D);if(isNaN(g.get(A[2],D))||isNaN(C)||isNaN(L)||C<c[0]||C>c[1]||L<f[0]||L>f[1])continue;var R=s.dataToPoint([C,L]);E=new It({shape:{x:R[0]-u/2,y:R[1]-v/2,width:u,height:v},style:M})}else{if(isNaN(g.get(A[1],D)))continue;E=new It({z2:1,shape:s.dataToRect([g.get(A[0],D)]).contentShape,style:M})}if(g.hasItemOption){var V=g.getItemModel(D),N=V.getModel("emphasis");S=N.getModel("itemStyle").getItemStyle(),m=V.getModel(["blur","itemStyle"]).getItemStyle(),y=V.getModel(["select","itemStyle"]).getItemStyle(),w=V.get(["itemStyle","borderRadius"]),_=N.get("focus"),T=N.get("blurScope"),I=N.get("disabled"),x=Et(V)}E.shape.r=w;var k=t.getRawValue(D),B="-";k&&k[2]!=null&&(B=k[2]+""),Bt(E,x,{labelFetcher:t,labelDataIndex:D,defaultOpacity:M.opacity,defaultText:B}),E.ensureState("emphasis").style=S,E.ensureState("blur").style=m,E.ensureState("select").style=y,ft(E,_,T,I),E.incremental=o,o&&(E.states.emphasis.hoverLayer=!0),d.add(E),g.setItemGraphicEl(D,E),this._progressiveEls&&this._progressiveEls.push(E)}},e.prototype._renderOnGeo=function(t,a,n,i){var o=n.targetVisuals.inRange,s=n.targetVisuals.outOfRange,l=a.getData(),u=this._hmLayer||this._hmLayer||new Ty;u.blurSize=a.get("blurSize"),u.pointSize=a.get("pointSize"),u.minOpacity=a.get("minOpacity"),u.maxOpacity=a.get("maxOpacity");var v=t.getViewRect().clone(),c=t.getRoamTransform();v.applyTransform(c);var f=Math.max(v.x,0),h=Math.max(v.y,0),p=Math.min(v.width+v.x,i.getWidth()),d=Math.min(v.height+v.y,i.getHeight()),g=p-f,S=d-h,m=[l.mapDimension("lng"),l.mapDimension("lat"),l.mapDimension("value")],y=l.mapArray(m,function(_,T,I){var A=t.dataToPoint([_,T]);return A[0]-=f,A[1]-=h,A.push(I),A}),w=n.getExtent(),x=n.type==="visualMap.continuous"?Dy(w,n.option.range):Iy(w,n.getPieceList(),n.option.selected);u.update(y,g,S,o.color.getNormalizer(),{inRange:o.color.getColorMapper(),outOfRange:s.color.getColorMapper()},x);var b=new ge({style:{width:g,height:S,x:f,y:h,image:u.canvas},silent:!0});this.group.add(b)},e.type="heatmap",e}(gt),Ly=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.getInitialData=function(t,a){return Pr(null,this,{generateCoord:"value"})},e.prototype.preventIncremental=function(){var t=Dl.get(this.get("coordinateSystem"));if(t&&t.dimensions)return t.dimensions[0]==="lng"&&t.dimensions[1]==="lat"},e.type="series.heatmap",e.dependencies=["grid","geo","calendar"],e.defaultOption={coordinateSystem:"cartesian2d",z:2,geoIndex:0,blurSize:30,pointSize:20,maxOpacity:1,minOpacity:0,select:{itemStyle:{borderColor:"#212121"}}},e}(xt);function Py(r){r.registerChartView(Cy),r.registerSeriesModel(Ly)}var My=["itemStyle","borderWidth"],ts=[{xy:"x",wh:"width",index:0,posDesc:["left","right"]},{xy:"y",wh:"height",index:1,posDesc:["top","bottom"]}],Ka=new Tr,Ey=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,a,n){var i=this.group,o=t.getData(),s=this._data,l=t.coordinateSystem,u=l.getBaseAxis(),v=u.isHorizontal(),c=l.master.getRect(),f={ecSize:{width:n.getWidth(),height:n.getHeight()},seriesModel:t,coordSys:l,coordSysExtent:[[c.x,c.x+c.width],[c.y,c.y+c.height]],isHorizontal:v,valueDim:ts[+v],categoryDim:ts[1-+v]};o.diff(s).add(function(p){if(o.hasValue(p)){var d=rs(o,p),g=es(o,p,d,f),S=as(o,f,g);o.setItemGraphicEl(p,S),i.add(S),is(S,f,g)}}).update(function(p,d){var g=s.getItemGraphicEl(d);if(!o.hasValue(p)){i.remove(g);return}var S=rs(o,p),m=es(o,p,S,f),y=Qu(o,m);g&&y!==g.__pictorialShapeStr&&(i.remove(g),o.setItemGraphicEl(p,null),g=null),g?Oy(g,f,m):g=as(o,f,m,!0),o.setItemGraphicEl(p,g),g.__pictorialSymbolMeta=m,i.add(g),is(g,f,m)}).remove(function(p){var d=s.getItemGraphicEl(p);d&&ns(s,p,d.__pictorialSymbolMeta.animationModel,d)}).execute();var h=t.get("clip",!0)?Qn(t.coordinateSystem,!1,t):null;return h?i.setClipPath(h):i.removeClipPath(),this._data=o,this.group},e.prototype.remove=function(t,a){var n=this.group,i=this._data;t.get("animation")?i&&i.eachItemGraphicEl(function(o){ns(i,ht(o).dataIndex,t,o)}):n.removeAll()},e.type="pictorialBar",e}(gt);function es(r,e,t,a){var n=r.getItemLayout(e),i=t.get("symbolRepeat"),o=t.get("symbolClip"),s=t.get("symbolPosition")||"start",l=t.get("symbolRotate"),u=(l||0)*Math.PI/180||0,v=t.get("symbolPatternSize")||2,c=t.isAnimationEnabled(),f={dataIndex:e,layout:n,itemModel:t,symbolType:r.getItemVisual(e,"symbol")||"circle",style:r.getItemVisual(e,"style"),symbolClip:o,symbolRepeat:i,symbolRepeatDirection:t.get("symbolRepeatDirection"),symbolPatternSize:v,rotation:u,animationModel:c?t:null,hoverScale:c&&t.get(["emphasis","scale"]),z2:t.getShallow("z",!0)||0};Ry(t,i,n,a,f),Vy(r,e,n,i,o,f.boundingLength,f.pxSign,v,a,f),Ny(t,f.symbolScale,u,a,f);var h=f.symbolSize,p=Vl(t.get("symbolOffset"),h);return Gy(t,h,n,i,o,p,s,f.valueLineWidth,f.boundingLength,f.repeatCutLength,a,f),f}function Ry(r,e,t,a,n){var i=a.valueDim,o=r.get("symbolBoundingData"),s=a.coordSys.getOtherAxis(a.coordSys.getBaseAxis()),l=s.toGlobalCoord(s.dataToCoord(0)),u=1-+(t[i.wh]<=0),v;if(Y(o)){var c=[Ja(s,o[0])-l,Ja(s,o[1])-l];c[1]<c[0]&&c.reverse(),v=c[u]}else o!=null?v=Ja(s,o)-l:e?v=a.coordSysExtent[i.index][u]-l:v=t[i.wh];n.boundingLength=v,e&&(n.repeatCutLength=t[i.wh]);var f=i.xy==="x",h=s.inverse;n.pxSign=f&&!h||!f&&h?v>=0?1:-1:v>0?1:-1}function Ja(r,e){return r.toGlobalCoord(r.dataToCoord(r.scale.parse(e)))}function Vy(r,e,t,a,n,i,o,s,l,u){var v=l.valueDim,c=l.categoryDim,f=Math.abs(t[c.wh]),h=r.getItemVisual(e,"symbolSize"),p;Y(h)?p=h.slice():h==null?p=["100%","100%"]:p=[h,h],p[c.index]=z(p[c.index],f),p[v.index]=z(p[v.index],a?f:Math.abs(i)),u.symbolSize=p;var d=u.symbolScale=[p[0]/s,p[1]/s];d[v.index]*=(l.isHorizontal?-1:1)*o}function Ny(r,e,t,a,n){var i=r.get(My)||0;i&&(Ka.attr({scaleX:e[0],scaleY:e[1],rotation:t}),Ka.updateTransform(),i/=Ka.getLineScale(),i*=e[a.valueDim.index]),n.valueLineWidth=i||0}function Gy(r,e,t,a,n,i,o,s,l,u,v,c){var f=v.categoryDim,h=v.valueDim,p=c.pxSign,d=Math.max(e[h.index]+s,0),g=d;if(a){var S=Math.abs(l),m=zt(r.get("symbolMargin"),"15%")+"",y=!1;m.lastIndexOf("!")===m.length-1&&(y=!0,m=m.slice(0,m.length-1));var w=z(m,e[h.index]),x=Math.max(d+w*2,0),b=y?0:w*2,_=Ac(a),T=_?a:os((S+b)/x),I=S-T*d;w=I/2/(y?T:Math.max(T-1,1)),x=d+w*2,b=y?0:w*2,!_&&a!=="fixed"&&(T=u?os((Math.abs(u)+b)/x):0),g=T*x-b,c.repeatTimes=T,c.symbolMargin=w}var A=p*(g/2),D=c.pathPosition=[];D[f.index]=t[f.wh]/2,D[h.index]=o==="start"?A:o==="end"?l-A:l/2,i&&(D[0]+=i[0],D[1]+=i[1]);var E=c.bundlePosition=[];E[f.index]=t[f.xy],E[h.index]=t[h.xy];var M=c.barRectShape=H({},t);M[h.wh]=p*Math.max(Math.abs(t[h.wh]),Math.abs(D[h.index]+A)),M[f.wh]=t[f.wh];var C=c.clipShape={};C[f.xy]=-t[f.xy],C[f.wh]=v.ecSize[f.wh],C[h.xy]=0,C[h.wh]=t[h.wh]}function Xu(r){var e=r.symbolPatternSize,t=Ve(r.symbolType,-e/2,-e/2,e,e);return t.attr({culling:!0}),t.type!=="image"&&t.setStyle({strokeNoScale:!0}),t}function qu(r,e,t,a){var n=r.__pictorialBundle,i=t.symbolSize,o=t.valueLineWidth,s=t.pathPosition,l=e.valueDim,u=t.repeatTimes||0,v=0,c=i[e.valueDim.index]+o+t.symbolMargin*2;for(hi(r,function(d){d.__pictorialAnimationIndex=v,d.__pictorialRepeatTimes=u,v<u?Xe(d,null,p(v),t,a):Xe(d,null,{scaleX:0,scaleY:0},t,a,function(){n.remove(d)}),v++});v<u;v++){var f=Xu(t);f.__pictorialAnimationIndex=v,f.__pictorialRepeatTimes=u,n.add(f);var h=p(v);Xe(f,{x:h.x,y:h.y,scaleX:0,scaleY:0},{scaleX:h.scaleX,scaleY:h.scaleY,rotation:h.rotation},t,a)}function p(d){var g=s.slice(),S=t.pxSign,m=d;return(t.symbolRepeatDirection==="start"?S>0:S<0)&&(m=u-1-d),g[l.index]=c*(m-u/2+.5)+s[l.index],{x:g[0],y:g[1],scaleX:t.symbolScale[0],scaleY:t.symbolScale[1],rotation:t.rotation}}}function ju(r,e,t,a){var n=r.__pictorialBundle,i=r.__pictorialMainPath;i?Xe(i,null,{x:t.pathPosition[0],y:t.pathPosition[1],scaleX:t.symbolScale[0],scaleY:t.symbolScale[1],rotation:t.rotation},t,a):(i=r.__pictorialMainPath=Xu(t),n.add(i),Xe(i,{x:t.pathPosition[0],y:t.pathPosition[1],scaleX:0,scaleY:0,rotation:t.rotation},{scaleX:t.symbolScale[0],scaleY:t.symbolScale[1]},t,a))}function Ku(r,e,t){var a=H({},e.barRectShape),n=r.__pictorialBarRect;n?Xe(n,null,{shape:a},e,t):(n=r.__pictorialBarRect=new It({z2:2,shape:a,silent:!0,style:{stroke:"transparent",fill:"transparent",lineWidth:0}}),n.disableMorphing=!0,r.add(n))}function Ju(r,e,t,a){if(t.symbolClip){var n=r.__pictorialClipPath,i=H({},t.clipShape),o=e.valueDim,s=t.animationModel,l=t.dataIndex;if(n)ct(n,{shape:i},s,l);else{i[o.wh]=0,n=new It({shape:i}),r.__pictorialBundle.setClipPath(n),r.__pictorialClipPath=n;var u={};u[o.wh]=t.clipShape[o.wh],Je[a?"updateProps":"initProps"](n,{shape:u},s,l)}}}function rs(r,e){var t=r.getItemModel(e);return t.getAnimationDelayParams=ky,t.isAnimationEnabled=zy,t}function ky(r){return{index:r.__pictorialAnimationIndex,count:r.__pictorialRepeatTimes}}function zy(){return this.parentModel.isAnimationEnabled()&&!!this.getShallow("animation")}function as(r,e,t,a){var n=new Z,i=new Z;return n.add(i),n.__pictorialBundle=i,i.x=t.bundlePosition[0],i.y=t.bundlePosition[1],t.symbolRepeat?qu(n,e,t):ju(n,e,t),Ku(n,t,a),Ju(n,e,t,a),n.__pictorialShapeStr=Qu(r,t),n.__pictorialSymbolMeta=t,n}function Oy(r,e,t){var a=t.animationModel,n=t.dataIndex,i=r.__pictorialBundle;ct(i,{x:t.bundlePosition[0],y:t.bundlePosition[1]},a,n),t.symbolRepeat?qu(r,e,t,!0):ju(r,e,t,!0),Ku(r,t,!0),Ju(r,e,t,!0)}function ns(r,e,t,a){var n=a.__pictorialBarRect;n&&n.removeTextContent();var i=[];hi(a,function(o){i.push(o)}),a.__pictorialMainPath&&i.push(a.__pictorialMainPath),a.__pictorialClipPath&&(t=null),P(i,function(o){qr(o,{scaleX:0,scaleY:0},t,e,function(){a.parent&&a.parent.remove(a)})}),r.setItemGraphicEl(e,null)}function Qu(r,e){return[r.getItemVisual(e.dataIndex,"symbol")||"none",!!e.symbolRepeat,!!e.symbolClip].join(":")}function hi(r,e,t){P(r.__pictorialBundle.children(),function(a){a!==r.__pictorialBarRect&&e.call(t,a)})}function Xe(r,e,t,a,n,i){e&&r.attr(e),a.symbolClip&&!n?t&&r.attr(t):t&&Je[n?"updateProps":"initProps"](r,t,a.animationModel,a.dataIndex,i)}function is(r,e,t){var a=t.dataIndex,n=t.itemModel,i=n.getModel("emphasis"),o=i.getModel("itemStyle").getItemStyle(),s=n.getModel(["blur","itemStyle"]).getItemStyle(),l=n.getModel(["select","itemStyle"]).getItemStyle(),u=n.getShallow("cursor"),v=i.get("focus"),c=i.get("blurScope"),f=i.get("scale");hi(r,function(d){if(d instanceof ge){var g=d.style;d.useStyle(H({image:g.image,x:g.x,y:g.y,width:g.width,height:g.height},t.style))}else d.useStyle(t.style);var S=d.ensureState("emphasis");S.style=o,f&&(S.scaleX=d.scaleX*1.1,S.scaleY=d.scaleY*1.1),d.ensureState("blur").style=s,d.ensureState("select").style=l,u&&(d.cursor=u),d.z2=t.z2});var h=e.valueDim.posDesc[+(t.boundingLength>0)],p=r.__pictorialBarRect;p.ignoreClip=!0,Bt(p,Et(n),{labelFetcher:e.seriesModel,labelDataIndex:a,defaultText:mn(e.seriesModel.getData(),a),inheritColor:t.style.fill,defaultOpacity:t.style.opacity,defaultOutsidePosition:h}),ft(r,v,c,i.get("disabled"))}function os(r){var e=Math.round(r);return Math.abs(r-e)<1e-4?e:Math.ceil(r)}var By=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t.hasSymbolVisual=!0,t.defaultSymbol="roundRect",t}return e.prototype.getInitialData=function(t){return t.stack=null,r.prototype.getInitialData.apply(this,arguments)},e.type="series.pictorialBar",e.dependencies=["grid"],e.defaultOption=Tc(ji.defaultOption,{symbol:"circle",symbolSize:null,symbolRotate:null,symbolPosition:null,symbolOffset:null,symbolMargin:null,symbolRepeat:!1,symbolRepeatDirection:"end",symbolClip:!1,symbolBoundingData:null,symbolPatternSize:400,barGap:"-100%",clip:!1,progressive:0,emphasis:{scale:!1},select:{itemStyle:{borderColor:"#212121"}}}),e}(ji);function Fy(r){r.registerChartView(Ey),r.registerSeriesModel(By),r.registerLayout(r.PRIORITY.VISUAL.LAYOUT,St(Ic,"pictorialBar")),r.registerLayout(r.PRIORITY.VISUAL.PROGRESSIVE_LAYOUT,Dc("pictorialBar"))}var Hy=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t._layers=[],t}return e.prototype.render=function(t,a,n){var i=t.getData(),o=this,s=this.group,l=t.getLayerSeries(),u=i.getLayout("layoutInfo"),v=u.rect,c=u.boundaryGap;s.x=0,s.y=v.y+c[0];function f(g){return g.name}var h=new Ke(this._layersSeries||[],l,f,f),p=[];h.add(lt(d,this,"add")).update(lt(d,this,"update")).remove(lt(d,this,"remove")).execute();function d(g,S,m){var y=o._layers;if(g==="remove"){s.remove(y[S]);return}for(var w=[],x=[],b,_=l[S].indices,T=0;T<_.length;T++){var I=i.getItemLayout(_[T]),A=I.x,D=I.y0,E=I.y;w.push(A,D),x.push(A,D+E),b=i.getItemVisual(_[T],"style")}var M,C=i.getItemLayout(_[0]),L=t.getModel("label"),R=L.get("margin"),V=t.getModel("emphasis");if(g==="add"){var N=p[S]=new Z;M=new Lf({shape:{points:w,stackedOnPoints:x,smooth:.4,stackedOnSmooth:.4,smoothConstraint:!1},z2:0}),N.add(M),s.add(N),t.isAnimationEnabled()&&M.setClipPath(Wy(M.getBoundingRect(),t,function(){M.removeClipPath()}))}else{var N=y[m];M=N.childAt(0),s.add(N),p[S]=N,ct(M,{shape:{points:w,stackedOnPoints:x}},t),Re(M)}Bt(M,Et(t),{labelDataIndex:_[T-1],defaultText:i.getName(_[T-1]),inheritColor:b.fill},{normal:{verticalAlign:"middle"}}),M.setTextConfig({position:null,local:!0});var k=M.getTextContent();k&&(k.x=C.x-R,k.y=C.y0+C.y/2),M.useStyle(b),i.setItemGraphicEl(S,M),Mt(M,t),ft(M,V.get("focus"),V.get("blurScope"),V.get("disabled"))}this._layersSeries=l,this._layers=p},e.type="themeRiver",e}(gt);function Wy(r,e,t){var a=new It({shape:{x:r.x-10,y:r.y-10,width:0,height:r.height+20}});return Yt(a,{shape:{x:r.x-50,width:r.width+100,height:r.height+20}},e,t),a}var Qa=2,Uy=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t){r.prototype.init.apply(this,arguments),this.legendVisualProvider=new ba(lt(this.getData,this),lt(this.getRawData,this))},e.prototype.fixData=function(t){var a=t.length,n={},i=yn(t,function(f){return n.hasOwnProperty(f[0]+"")||(n[f[0]+""]=-1),f[2]}),o=[];i.buckets.each(function(f,h){o.push({name:h,dataList:f})});for(var s=o.length,l=0;l<s;++l){for(var u=o[l].name,v=0;v<o[l].dataList.length;++v){var c=o[l].dataList[v][0]+"";n[c]=l}for(var c in n)n.hasOwnProperty(c)&&n[c]!==l&&(n[c]=l,t[a]=[c,0,u],a++)}return t},e.prototype.getInitialData=function(t,a){for(var n=this.getReferringComponents("singleAxis",Qe).models[0],i=n.get("type"),o=Pt(t.data,function(p){return p[2]!==void 0}),s=this.fixData(o||[]),l=[],u=this.nameMap=K(),v=0,c=0;c<s.length;++c)l.push(s[c][Qa]),u.get(s[c][Qa])||(u.set(s[c][Qa],v),v++);var f=Wn(s,{coordDimensions:["single"],dimensionsDefine:[{name:"time",type:Cc(i)},{name:"value",type:"float"},{name:"name",type:"ordinal"}],encodeDefine:{single:0,value:1,itemName:2}}).dimensions,h=new te(f,this);return h.initData(s),h},e.prototype.getLayerSeries=function(){for(var t=this.getData(),a=t.count(),n=[],i=0;i<a;++i)n[i]=i;var o=t.mapDimension("single"),s=yn(n,function(u){return t.get("name",u)}),l=[];return s.buckets.each(function(u,v){u.sort(function(c,f){return t.get(o,c)-t.get(o,f)}),l.push({name:v,indices:u})}),l},e.prototype.getAxisTooltipData=function(t,a,n){Y(t)||(t=t?[t]:[]);for(var i=this.getData(),o=this.getLayerSeries(),s=[],l=o.length,u,v=0;v<l;++v){for(var c=Number.MAX_VALUE,f=-1,h=o[v].indices.length,p=0;p<h;++p){var d=i.get(t[0],o[v].indices[p]),g=Math.abs(d-a);g<=c&&(u=d,c=g,f=o[v].indices[p])}s.push(f)}return{dataIndices:s,nestestValue:u}},e.prototype.formatTooltip=function(t,a,n){var i=this.getData(),o=i.getName(t),s=i.get(i.mapDimension("value"),t);return Ft("nameValue",{name:o,value:s})},e.type="series.themeRiver",e.dependencies=["singleAxis"],e.defaultOption={z:2,colorBy:"data",coordinateSystem:"singleAxis",boundaryGap:["10%","10%"],singleAxisIndex:0,animationEasing:"linear",label:{margin:4,show:!0,position:"left",fontSize:11},emphasis:{label:{show:!0}}},e}(xt);function $y(r,e){r.eachSeriesByType("themeRiver",function(t){var a=t.getData(),n=t.coordinateSystem,i={},o=n.getRect();i.rect=o;var s=t.get("boundaryGap"),l=n.getAxis();if(i.boundaryGap=s,l.orient==="horizontal"){s[0]=z(s[0],o.height),s[1]=z(s[1],o.height);var u=o.height-s[0]-s[1];ss(a,t,u)}else{s[0]=z(s[0],o.width),s[1]=z(s[1],o.width);var v=o.width-s[0]-s[1];ss(a,t,v)}a.setLayout("layoutInfo",i)})}function ss(r,e,t){if(r.count())for(var a=e.coordinateSystem,n=e.getLayerSeries(),i=r.mapDimension("single"),o=r.mapDimension("value"),s=O(n,function(g){return O(g.indices,function(S){var m=a.dataToPoint(r.get(i,S));return m[1]=r.get(o,S),m})}),l=Yy(s),u=l.y0,v=t/l.max,c=n.length,f=n[0].indices.length,h,p=0;p<f;++p){h=u[p]*v,r.setItemLayout(n[0].indices[p],{layerIndex:0,x:s[0][p][0],y0:h,y:s[0][p][1]*v});for(var d=1;d<c;++d)h+=s[d-1][p][1]*v,r.setItemLayout(n[d].indices[p],{layerIndex:d,x:s[d][p][0],y0:h,y:s[d][p][1]*v})}}function Yy(r){for(var e=r.length,t=r[0].length,a=[],n=[],i=0,o=0;o<t;++o){for(var s=0,l=0;l<e;++l)s+=r[l][o][1];s>i&&(i=s),a.push(s)}for(var u=0;u<t;++u)n[u]=(i-a[u])/2;i=0;for(var v=0;v<t;++v){var c=a[v]+n[v];c>i&&(i=c)}return{y0:n,max:i}}function Zy(r){r.registerChartView(Hy),r.registerSeriesModel(Uy),r.registerLayout($y),r.registerProcessor(wa("themeRiver"))}var Xy=2,qy=4,ls=function(r){G(e,r);function e(t,a,n,i){var o=r.call(this)||this;o.z2=Xy,o.textConfig={inside:!0},ht(o).seriesIndex=a.seriesIndex;var s=new qt({z2:qy,silent:t.getModel().get(["label","silent"])});return o.setTextContent(s),o.updateData(!0,t,a,n,i),o}return e.prototype.updateData=function(t,a,n,i,o){this.node=a,a.piece=this,n=n||this._seriesModel,i=i||this._ecModel;var s=this;ht(s).dataIndex=a.dataIndex;var l=a.getModel(),u=l.getModel("emphasis"),v=a.getLayout(),c=H({},v);c.label=null;var f=a.getVisual("style");f.lineJoin="bevel";var h=a.getVisual("decal");h&&(f.decal=Bn(h,o));var p=Hi(l.getModel("itemStyle"),c,!0);H(c,p),P(Lc,function(m){var y=s.ensureState(m),w=l.getModel([m,"itemStyle"]);y.style=w.getItemStyle();var x=Hi(w,c);x&&(y.shape=x)}),t?(s.setShape(c),s.shape.r=v.r0,Yt(s,{shape:{r:v.r}},n,a.dataIndex)):(ct(s,{shape:c},n),Re(s)),s.useStyle(f),this._updateLabel(n);var d=l.getShallow("cursor");d&&s.attr("cursor",d),this._seriesModel=n||this._seriesModel,this._ecModel=i||this._ecModel;var g=u.get("focus"),S=g==="relative"?Xr(a.getAncestorsIndices(),a.getDescendantIndices()):g==="ancestor"?a.getAncestorsIndices():g==="descendant"?a.getDescendantIndices():g;ft(this,S,u.get("blurScope"),u.get("disabled"))},e.prototype._updateLabel=function(t){var a=this,n=this.node.getModel(),i=n.getModel("label"),o=this.node.getLayout(),s=o.endAngle-o.startAngle,l=(o.startAngle+o.endAngle)/2,u=Math.cos(l),v=Math.sin(l),c=this,f=c.getTextContent(),h=this.node.dataIndex,p=i.get("minAngle")/180*Math.PI,d=i.get("show")&&!(p!=null&&Math.abs(s)<p);f.ignore=!d,P(Mc,function(S){var m=S==="normal"?n.getModel("label"):n.getModel([S,"label"]),y=S==="normal",w=y?f:f.ensureState(S),x=t.getFormattedLabel(h,S);y&&(x=x||a.node.name),w.style=$t(m,{},null,S!=="normal",!0),x&&(w.style.text=x);var b=m.get("show");b!=null&&!y&&(w.ignore=!b);var _=g(m,"position"),T=y?c:c.states[S],I=T.style.fill;T.textConfig={outsideFill:m.get("color")==="inherit"?I:null,inside:_!=="outside"};var A,D=g(m,"distance")||0,E=g(m,"align"),M=g(m,"rotate"),C=Math.PI*.5,L=Math.PI*1.5,R=Vr(M==="tangential"?Math.PI/2-l:l),V=R>C&&!Pc(R-C)&&R<L;_==="outside"?(A=o.r+D,E=V?"right":"left"):!E||E==="center"?(s===2*Math.PI&&o.r0===0?A=0:A=(o.r+o.r0)/2,E="center"):E==="left"?(A=o.r0+D,E=V?"right":"left"):E==="right"&&(A=o.r-D,E=V?"left":"right"),w.style.align=E,w.style.verticalAlign=g(m,"verticalAlign")||"middle",w.x=A*u+o.cx,w.y=A*v+o.cy;var N=0;M==="radial"?N=Vr(-l)+(V?Math.PI:0):M==="tangential"?N=Vr(Math.PI/2-l)+(V?Math.PI:0):Xt(M)&&(N=M*Math.PI/180),w.rotation=Vr(N)});function g(S,m){var y=S.get(m);return y??i.get(m)}f.dirtyStyle()},e}(Ge),Cn="sunburstRootToNode",us="sunburstHighlight",jy="sunburstUnhighlight";function Ky(r){r.registerAction({type:Cn,update:"updateView"},function(e,t){t.eachComponent({mainType:"series",subType:"sunburst",query:e},a);function a(n,i){var o=xr(e,[Cn],n);if(o){var s=n.getViewRoot();s&&(e.direction=li(s,o.node)?"rollUp":"drillDown"),n.resetViewRoot(o.node)}}}),r.registerAction({type:us,update:"none"},function(e,t,a){e=H({},e),t.eachComponent({mainType:"series",subType:"sunburst",query:e},n);function n(i){var o=xr(e,[us],i);o&&(e.dataIndex=o.node.dataIndex)}a.dispatchAction(H(e,{type:"highlight"}))}),r.registerAction({type:jy,update:"updateView"},function(e,t,a){e=H({},e),a.dispatchAction(H(e,{type:"downplay"}))})}var Jy=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,a,n,i){var o=this;this.seriesModel=t,this.api=n,this.ecModel=a;var s=t.getData(),l=s.tree.root,u=t.getViewRoot(),v=this.group,c=t.get("renderLabelForZeroData"),f=[];u.eachNode(function(m){f.push(m)});var h=this._oldChildren||[];p(f,h),S(l,u),this._initEvents(),this._oldChildren=f;function p(m,y){if(m.length===0&&y.length===0)return;new Ke(y,m,w,w).add(x).update(x).remove(St(x,null)).execute();function w(b){return b.getId()}function x(b,_){var T=b==null?null:m[b],I=_==null?null:y[_];d(T,I)}}function d(m,y){if(!c&&m&&!m.getValue()&&(m=null),m!==l&&y!==l){if(y&&y.piece)m?(y.piece.updateData(!1,m,t,a,n),s.setItemGraphicEl(m.dataIndex,y.piece)):g(y);else if(m){var w=new ls(m,t,a,n);v.add(w),s.setItemGraphicEl(m.dataIndex,w)}}}function g(m){m&&m.piece&&(v.remove(m.piece),m.piece=null)}function S(m,y){y.depth>0?(o.virtualPiece?o.virtualPiece.updateData(!1,m,t,a,n):(o.virtualPiece=new ls(m,t,a,n),v.add(o.virtualPiece)),y.piece.off("click"),o.virtualPiece.on("click",function(w){o._rootToNode(y.parentNode)})):o.virtualPiece&&(v.remove(o.virtualPiece),o.virtualPiece=null)}},e.prototype._initEvents=function(){var t=this;this.group.off("click"),this.group.on("click",function(a){var n=!1,i=t.seriesModel.getViewRoot();i.eachNode(function(o){if(!n&&o.piece&&o.piece===a.target){var s=o.getModel().get("nodeClick");if(s==="rootToNode")t._rootToNode(o);else if(s==="link"){var l=o.getModel(),u=l.get("link");if(u){var v=l.get("target",!0)||"_blank";_l(u,v)}}n=!0}})})},e.prototype._rootToNode=function(t){t!==this.seriesModel.getViewRoot()&&this.api.dispatchAction({type:Cn,from:this.uid,seriesId:this.seriesModel.id,targetNode:t})},e.prototype.containPoint=function(t,a){var n=a.getData(),i=n.getItemLayout(0);if(i){var o=t[0]-i.cx,s=t[1]-i.cy,l=Math.sqrt(o*o+s*s);return l<=i.r&&l>=i.r0}},e.type="sunburst",e}(gt),Qy=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t.ignoreStyleOnData=!0,t}return e.prototype.getInitialData=function(t,a){var n={name:t.name,children:t.data};tv(n);var i=this._levelModels=O(t.levels||[],function(l){return new Rt(l,this,a)},this),o=si.createTree(n,this,s);function s(l){l.wrapMethod("getItemModel",function(u,v){var c=o.getNodeByDataIndex(v),f=i[c.depth];return f&&(u.parentModel=f),u})}return o.data},e.prototype.optionUpdated=function(){this.resetViewRoot()},e.prototype.getDataParams=function(t){var a=r.prototype.getDataParams.apply(this,arguments),n=this.getData().tree.getNodeByDataIndex(t);return a.treePathInfo=Aa(n,this),a},e.prototype.getLevelModel=function(t){return this._levelModels&&this._levelModels[t.depth]},e.prototype.getViewRoot=function(){return this._viewRoot},e.prototype.resetViewRoot=function(t){t?this._viewRoot=t:t=this._viewRoot;var a=this.getRawData().tree.root;(!t||t!==a&&!a.contains(t))&&(this._viewRoot=a)},e.prototype.enableAriaDecal=function(){_u(this)},e.type="series.sunburst",e.defaultOption={z:2,center:["50%","50%"],radius:[0,"75%"],clockwise:!0,startAngle:90,minAngle:0,stillShowZeroSum:!0,nodeClick:"rootToNode",renderLabelForZeroData:!1,label:{rotate:"radial",show:!0,opacity:1,align:"center",position:"inside",distance:5,silent:!0},itemStyle:{borderWidth:1,borderColor:"white",borderType:"solid",shadowBlur:0,shadowColor:"rgba(0, 0, 0, 0.2)",shadowOffsetX:0,shadowOffsetY:0,opacity:1},emphasis:{focus:"descendant"},blur:{itemStyle:{opacity:.2},label:{opacity:.1}},animationType:"expansion",animationDuration:1e3,animationDurationUpdate:500,data:[],sort:"desc"},e}(xt);function tv(r){var e=0;P(r.children,function(a){tv(a);var n=a.value;Y(n)&&(n=n[0]),e+=n});var t=r.value;Y(t)&&(t=t[0]),(t==null||isNaN(t))&&(t=e),t<0&&(t=0),Y(r.value)?r.value[0]=t:r.value=t}var vs=Math.PI/180;function tm(r,e,t){e.eachSeriesByType(r,function(a){var n=a.get("center"),i=a.get("radius");Y(i)||(i=[0,i]),Y(n)||(n=[n,n]);var o=t.getWidth(),s=t.getHeight(),l=Math.min(o,s),u=z(n[0],o),v=z(n[1],s),c=z(i[0],l/2),f=z(i[1],l/2),h=-a.get("startAngle")*vs,p=a.get("minAngle")*vs,d=a.getData().tree.root,g=a.getViewRoot(),S=g.depth,m=a.get("sort");m!=null&&ev(g,m);var y=0;P(g.children,function(R){!isNaN(R.getValue())&&y++});var w=g.getValue(),x=Math.PI/(w||y)*2,b=g.depth>0,_=g.height-(b?-1:1),T=(f-c)/(_||1),I=a.get("clockwise"),A=a.get("stillShowZeroSum"),D=I?1:-1,E=function(R,V){if(R){var N=V;if(R!==d){var k=R.getValue(),B=w===0&&A?x:k*x;B<p&&(B=p),N=V+D*B;var W=R.depth-S-(b?-1:1),X=c+T*W,tt=c+T*(W+1),J=a.getLevelModel(R);if(J){var rt=J.get("r0",!0),pt=J.get("r",!0),Ht=J.get("radius",!0);Ht!=null&&(rt=Ht[0],pt=Ht[1]),rt!=null&&(X=z(rt,l/2)),pt!=null&&(tt=z(pt,l/2))}R.setLayout({angle:B,startAngle:V,endAngle:N,clockwise:I,cx:u,cy:v,r0:X,r:tt})}if(R.children&&R.children.length){var U=0;P(R.children,function(F){U+=E(F,V+U)})}return N-V}};if(b){var M=c,C=c+T,L=Math.PI*2;d.setLayout({angle:L,startAngle:h,endAngle:h+L,clockwise:I,cx:u,cy:v,r0:M,r:C})}E(g,h)})}function ev(r,e){var t=r.children||[];r.children=em(t,e),t.length&&P(r.children,function(a){ev(a,e)})}function em(r,e){if(st(e)){var t=O(r,function(n,i){var o=n.getValue();return{params:{depth:n.depth,height:n.height,dataIndex:n.dataIndex,getValue:function(){return o}},index:i}});return t.sort(function(n,i){return e(n.params,i.params)}),O(t,function(n){return r[n.index]})}else{var a=e==="asc";return r.sort(function(n,i){var o=(n.getValue()-i.getValue())*(a?1:-1);return o===0?(n.dataIndex-i.dataIndex)*(a?-1:1):o})}}function rm(r){var e={};function t(a,n,i){for(var o=a;o&&o.depth>1;)o=o.parentNode;var s=n.getColorFromPalette(o.name||o.dataIndex+"",e);return a.depth>1&&et(s)&&(s=Ec(s,(a.depth-1)/(i-1)*.5)),s}r.eachSeriesByType("sunburst",function(a){var n=a.getData(),i=n.tree;i.eachNode(function(o){var s=o.getModel(),l=s.getModel("itemStyle").getItemStyle();l.fill||(l.fill=t(o,a,i.root.height));var u=n.ensureUniqueItemVisual(o.dataIndex,"style");H(u,l)})})}function am(r){r.registerChartView(Jy),r.registerSeriesModel(Qy),r.registerLayout(St(tm,"sunburst")),r.registerProcessor(St(wa,"sunburst")),r.registerVisual(rm),Ky(r)}var cs={color:"fill",borderColor:"stroke"},nm={symbol:1,symbolSize:1,symbolKeepAspect:1,legendIcon:1,visualMeta:1,liftZ:1,decal:1},jt=re(),im=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.optionUpdated=function(){this.currentZLevel=this.get("zlevel",!0),this.currentZ=this.get("z",!0)},e.prototype.getInitialData=function(t,a){return Pr(null,this)},e.prototype.getDataParams=function(t,a,n){var i=r.prototype.getDataParams.call(this,t,a);return n&&(i.info=jt(n).info),i},e.type="series.custom",e.dependencies=["grid","polar","geo","singleAxis","calendar"],e.defaultOption={coordinateSystem:"cartesian2d",z:2,legendHoverLink:!0,clip:!1},e}(xt);function om(r,e){return e=e||[0,0],O(["x","y"],function(t,a){var n=this.getAxis(t),i=e[a],o=r[a]/2;return n.type==="category"?n.getBandWidth():Math.abs(n.dataToCoord(i-o)-n.dataToCoord(i+o))},this)}function sm(r){var e=r.master.getRect();return{coordSys:{type:"cartesian2d",x:e.x,y:e.y,width:e.width,height:e.height},api:{coord:function(t){return r.dataToPoint(t)},size:lt(om,r)}}}function lm(r,e){return e=e||[0,0],O([0,1],function(t){var a=e[t],n=r[t]/2,i=[],o=[];return i[t]=a-n,o[t]=a+n,i[1-t]=o[1-t]=e[1-t],Math.abs(this.dataToPoint(i)[t]-this.dataToPoint(o)[t])},this)}function um(r){var e=r.getBoundingRect();return{coordSys:{type:"geo",x:e.x,y:e.y,width:e.width,height:e.height,zoom:r.getZoom()},api:{coord:function(t){return r.dataToPoint(t)},size:lt(lm,r)}}}function vm(r,e){var t=this.getAxis(),a=e instanceof Array?e[0]:e,n=(r instanceof Array?r[0]:r)/2;return t.type==="category"?t.getBandWidth():Math.abs(t.dataToCoord(a-n)-t.dataToCoord(a+n))}function cm(r){var e=r.getRect();return{coordSys:{type:"singleAxis",x:e.x,y:e.y,width:e.width,height:e.height},api:{coord:function(t){return r.dataToPoint(t)},size:lt(vm,r)}}}function fm(r,e){return e=e||[0,0],O(["Radius","Angle"],function(t,a){var n="get"+t+"Axis",i=this[n](),o=e[a],s=r[a]/2,l=i.type==="category"?i.getBandWidth():Math.abs(i.dataToCoord(o-s)-i.dataToCoord(o+s));return t==="Angle"&&(l=l*Math.PI/180),l},this)}function hm(r){var e=r.getRadiusAxis(),t=r.getAngleAxis(),a=e.getExtent();return a[0]>a[1]&&a.reverse(),{coordSys:{type:"polar",cx:r.cx,cy:r.cy,r:a[1],r0:a[0]},api:{coord:function(n){var i=e.dataToRadius(n[0]),o=t.dataToAngle(n[1]),s=r.coordToPoint([i,o]);return s.push(i,o*Math.PI/180),s},size:lt(fm,r)}}}function pm(r){var e=r.getRect(),t=r.getRangeInfo();return{coordSys:{type:"calendar",x:e.x,y:e.y,width:e.width,height:e.height,cellWidth:r.getCellWidth(),cellHeight:r.getCellHeight(),rangeInfo:{start:t.start,end:t.end,weeks:t.weeks,dayCount:t.allDay}},api:{coord:function(a,n){return r.dataToPoint(a,n)}}}}var Kt="emphasis",ve="normal",pi="blur",di="select",de=[ve,Kt,pi,di],tn={normal:["itemStyle"],emphasis:[Kt,"itemStyle"],blur:[pi,"itemStyle"],select:[di,"itemStyle"]},en={normal:["label"],emphasis:[Kt,"label"],blur:[pi,"label"],select:[di,"label"]},dm=["x","y"],gm="e\0\0",Ct={normal:{},emphasis:{},blur:{},select:{}},ym={cartesian2d:sm,geo:um,single:cm,polar:hm,calendar:pm};function Ln(r){return r instanceof Vt}function Pn(r){return r instanceof mr}function mm(r,e){e.copyTransform(r),Pn(e)&&Pn(r)&&(e.setStyle(r.style),e.z=r.z,e.z2=r.z2,e.zlevel=r.zlevel,e.invisible=r.invisible,e.ignore=r.ignore,Ln(e)&&Ln(r)&&e.setShape(r.shape))}var Sm=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,a,n,i){this._progressiveEls=null;var o=this._data,s=t.getData(),l=this.group,u=fs(t,s,a,n);o||l.removeAll(),s.diff(o).add(function(c){rn(n,null,c,u(c,i),t,l,s)}).remove(function(c){var f=o.getItemGraphicEl(c);f&&ei(f,jt(f).option,t)}).update(function(c,f){var h=o.getItemGraphicEl(f);rn(n,h,c,u(c,i),t,l,s)}).execute();var v=t.get("clip",!0)?Qn(t.coordinateSystem,!1,t):null;v?l.setClipPath(v):l.removeClipPath(),this._data=s},e.prototype.incrementalPrepareRender=function(t,a,n){this.group.removeAll(),this._data=null},e.prototype.incrementalRender=function(t,a,n,i,o){var s=a.getData(),l=fs(a,s,n,i),u=this._progressiveEls=[];function v(h){h.isGroup||(h.incremental=!0,h.ensureState("emphasis").hoverLayer=!0)}for(var c=t.start;c<t.end;c++){var f=rn(null,null,c,l(c,o),a,this.group,s);f&&(f.traverse(v),u.push(f))}},e.prototype.eachRendered=function(t){Nl(this._progressiveEls||this.group,t)},e.prototype.filterForExposedEvent=function(t,a,n,i){var o=a.element;if(o==null||n.name===o)return!0;for(;(n=n.__hostTarget||n.parent)&&n!==this.group;)if(n.name===o)return!0;return!1},e.type="custom",e}(gt);function gi(r){var e=r.type,t;if(e==="path"){var a=r.shape,n=a.width!=null&&a.height!=null?{x:a.x||0,y:a.y||0,width:a.width,height:a.height}:null,i=nv(a);t=Nc(i,null,n,a.layout||"center"),jt(t).customPathData=i}else if(e==="image")t=new ge({}),jt(t).customImagePath=r.style.image;else if(e==="text")t=new qt({});else if(e==="group")t=new Z;else{if(e==="compoundPath")throw new Error('"compoundPath" is not supported yet.');var o=Gc(e);if(!o){var s="";ot(s)}t=new o}return jt(t).customGraphicType=e,t.name=r.name,t.z2EmphasisLift=1,t.z2SelectLift=1,t}function yi(r,e,t,a,n,i,o){Pf(e);var s=n&&n.normal.cfg;s&&e.setTextConfig(s),a&&a.transition==null&&(a.transition=dm);var l=a&&a.style;if(l){if(e.type==="text"){var u=l;mt(u,"textFill")&&(u.fill=u.textFill),mt(u,"textStroke")&&(u.stroke=u.textStroke)}var v=void 0,c=Ln(e)?l.decal:null;r&&c&&(c.dirty=!0,v=Bn(c,r)),l.__decalPattern=v}if(Pn(e)&&l){var v=l.__decalPattern;v&&(l.decal=v)}Mf(e,a,i,{dataIndex:t,isInit:o,clearStyle:!0}),Ef(e,a.keyframeAnimation,i)}function rv(r,e,t,a,n){var i=e.isGroup?null:e,o=n&&n[r].cfg;if(i){var s=i.ensureState(r);if(a===!1){var l=i.getState(r);l&&(l.style=null)}else s.style=a||null;o&&(s.textConfig=o),qe(i)}}function xm(r,e,t){if(!r.isGroup){var a=r,n=t.currentZ,i=t.currentZLevel;a.z=n,a.zlevel=i;var o=e.z2;o!=null&&(a.z2=o||0);for(var s=0;s<de.length;s++)bm(a,e,de[s])}}function bm(r,e,t){var a=t===ve,n=a?e:ia(e,t),i=n?n.z2:null,o;i!=null&&(o=a?r:r.ensureState(t),o.z2=i||0)}function fs(r,e,t,a){var n=r.get("renderItem"),i=r.coordinateSystem,o={};i&&(o=i.prepareCustoms?i.prepareCustoms(i):ym[i.type](i));for(var s=j({getWidth:a.getWidth,getHeight:a.getHeight,getZr:a.getZr,getDevicePixelRatio:a.getDevicePixelRatio,value:w,style:b,ordinalRawValue:x,styleEmphasis:_,visual:A,barLayout:D,currentSeriesIndices:E,font:M},o.api||{}),l={context:{},seriesId:r.id,seriesName:r.name,seriesIndex:r.seriesIndex,coordSys:o.coordSys,dataInsideLength:e.count(),encode:wm(r.getData())},u,v,c={},f={},h={},p={},d=0;d<de.length;d++){var g=de[d];h[g]=r.getModel(tn[g]),p[g]=r.getModel(en[g])}function S(C){return C===u?v||(v=e.getItemModel(C)):e.getItemModel(C)}function m(C,L){return e.hasItemOption?C===u?c[L]||(c[L]=S(C).getModel(tn[L])):S(C).getModel(tn[L]):h[L]}function y(C,L){return e.hasItemOption?C===u?f[L]||(f[L]=S(C).getModel(en[L])):S(C).getModel(en[L]):p[L]}return function(C,L){return u=C,v=null,c={},f={},n&&n(j({dataIndexInside:C,dataIndex:e.getRawIndex(C),actionType:L?L.type:null},l),s)};function w(C,L){return L==null&&(L=u),e.getStore().get(e.getDimensionIndex(C||0),L)}function x(C,L){L==null&&(L=u),C=C||0;var R=e.getDimensionInfo(C);if(!R){var V=e.getDimensionIndex(C);return V>=0?e.getStore().get(V,L):void 0}var N=e.get(R.name,L),k=R&&R.ordinalMeta;return k?k.categories[N]:N}function b(C,L){L==null&&(L=u);var R=e.getItemVisual(L,"style"),V=R&&R.fill,N=R&&R.opacity,k=m(L,ve).getItemStyle();V!=null&&(k.fill=V),N!=null&&(k.opacity=N);var B={inheritColor:et(V)?V:"#000"},W=y(L,ve),X=$t(W,null,B,!1,!0);X.text=W.getShallow("show")?ce(r.getFormattedLabel(L,ve),mn(e,L)):null;var tt=Wi(W,B,!1);return I(C,k),k=Ki(k,X,tt),C&&T(k,C),k.legacy=!0,k}function _(C,L){L==null&&(L=u);var R=m(L,Kt).getItemStyle(),V=y(L,Kt),N=$t(V,null,null,!0,!0);N.text=V.getShallow("show")?Cr(r.getFormattedLabel(L,Kt),r.getFormattedLabel(L,ve),mn(e,L)):null;var k=Wi(V,null,!0);return I(C,R),R=Ki(R,N,k),C&&T(R,C),R.legacy=!0,R}function T(C,L){for(var R in L)mt(L,R)&&(C[R]=L[R])}function I(C,L){C&&(C.textFill&&(L.textFill=C.textFill),C.textPosition&&(L.textPosition=C.textPosition))}function A(C,L){if(L==null&&(L=u),mt(cs,C)){var R=e.getItemVisual(L,"style");return R?R[cs[C]]:null}if(mt(nm,C))return e.getItemVisual(L,C)}function D(C){if(i.type==="cartesian2d"){var L=i.getBaseAxis();return Rc(j({axis:L},C))}}function E(){return t.getCurrentSeriesIndices()}function M(C){return Vc(C,t)}}function wm(r){var e={};return P(r.dimensions,function(t){var a=r.getDimensionInfo(t);if(!a.isExtraCoord){var n=a.coordDim,i=e[n]=e[n]||[];i[a.coordDimIndex]=r.getDimensionIndex(t)}}),e}function rn(r,e,t,a,n,i,o){if(!a){i.remove(e);return}var s=mi(r,e,t,a,n,i);return s&&o.setItemGraphicEl(t,s),s&&ft(s,a.focus,a.blurScope,a.emphasisDisabled),s}function mi(r,e,t,a,n,i){var o=-1,s=e;e&&av(e,a,n)&&(o=xe(i.childrenRef(),e),e=null);var l=!e,u=e;u?u.clearStates():(u=gi(a),s&&mm(s,u)),a.morph===!1?u.disableMorphing=!0:u.disableMorphing&&(u.disableMorphing=!1),Ct.normal.cfg=Ct.normal.conOpt=Ct.emphasis.cfg=Ct.emphasis.conOpt=Ct.blur.cfg=Ct.blur.conOpt=Ct.select.cfg=Ct.select.conOpt=null,Ct.isLegacy=!1,Am(u,t,a,n,l,Ct),_m(u,t,a,n,l),yi(r,u,t,a,Ct,n,l),mt(a,"info")&&(jt(u).info=a.info);for(var v=0;v<de.length;v++){var c=de[v];if(c!==ve){var f=ia(a,c),h=Si(a,f,c);rv(c,u,f,h,Ct)}}return xm(u,a,n),a.type==="group"&&Tm(r,u,t,a,n),o>=0?i.replaceAt(u,o):i.add(u),u}function av(r,e,t){var a=jt(r),n=e.type,i=e.shape,o=e.style;return t.isUniversalTransitionEnabled()||n!=null&&n!==a.customGraphicType||n==="path"&&Lm(i)&&nv(i)!==a.customPathData||n==="image"&&mt(o,"image")&&o.image!==a.customImagePath}function _m(r,e,t,a,n){var i=t.clipPath;if(i===!1)r&&r.getClipPath()&&r.removeClipPath();else if(i){var o=r.getClipPath();o&&av(o,i,a)&&(o=null),o||(o=gi(i),r.setClipPath(o)),yi(null,o,e,i,null,a,n)}}function Am(r,e,t,a,n,i){if(!r.isGroup){hs(t,null,i),hs(t,Kt,i);var o=i.normal.conOpt,s=i.emphasis.conOpt,l=i.blur.conOpt,u=i.select.conOpt;if(o!=null||s!=null||u!=null||l!=null){var v=r.getTextContent();if(o===!1)v&&r.removeTextContent();else{o=i.normal.conOpt=o||{type:"text"},v?v.clearStates():(v=gi(o),r.setTextContent(v)),yi(null,v,e,o,null,a,n);for(var c=o&&o.style,f=0;f<de.length;f++){var h=de[f];if(h!==ve){var p=i[h].conOpt;rv(h,v,p,Si(o,p,h),null)}}c?v.dirty():v.markRedraw()}}}}function hs(r,e,t){var a=e?ia(r,e):r,n=e?Si(r,a,Kt):r.style,i=r.type,o=a?a.textConfig:null,s=r.textContent,l=s?e?ia(s,e):s:null;if(n&&(t.isLegacy||Rf(n,i,!!o,!!l))){t.isLegacy=!0;var u=Vf(n,i,!e);!o&&u.textConfig&&(o=u.textConfig),!l&&u.textContent&&(l=u.textContent)}if(!e&&l){var v=l;!v.type&&(v.type="text")}var c=e?t[e]:t.normal;c.cfg=o,c.conOpt=l}function ia(r,e){return e?r?r[e]:null:r}function Si(r,e,t){var a=e&&e.style;return a==null&&t===Kt&&r&&(a=r.styleEmphasis),a}function Tm(r,e,t,a,n){var i=a.children,o=i?i.length:0,s=a.$mergeChildren,l=s==="byName"||a.diffChildrenByName,u=s===!1;if(!(!o&&!l&&!u)){if(l){Dm({api:r,oldChildren:e.children()||[],newChildren:i||[],dataIndex:t,seriesModel:n,group:e});return}u&&e.removeAll();for(var v=0;v<o;v++){var c=i[v],f=e.childAt(v);c?(c.ignore==null&&(c.ignore=!1),mi(r,f,t,c,n,e)):f.ignore=!0}for(var h=e.childCount()-1;h>=v;h--){var p=e.childAt(h);Im(e,p,n)}}}function Im(r,e,t){e&&ei(e,jt(r).option,t)}function Dm(r){new Ke(r.oldChildren,r.newChildren,ps,ps,r).add(ds).update(ds).remove(Cm).execute()}function ps(r,e){var t=r&&r.name;return t??gm+e}function ds(r,e){var t=this.context,a=r!=null?t.newChildren[r]:null,n=e!=null?t.oldChildren[e]:null;mi(t.api,n,t.dataIndex,a,t.seriesModel,t.group)}function Cm(r){var e=this.context,t=e.oldChildren[r];t&&ei(t,jt(t).option,e.seriesModel)}function nv(r){return r&&(r.pathData||r.d)}function Lm(r){return r&&(mt(r,"pathData")||mt(r,"d"))}function Pm(r){r.registerChartView(Sm),r.registerSeriesModel(im)}var Mm=function(r){G(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.prototype.makeElOption=function(t,a,n,i,o){var s=n.axis;s.dim==="angle"&&(this.animationThreshold=Math.PI/18);var l=s.polar,u=l.getOtherAxis(s),v=u.getExtent(),c=s.dataToCoord(a),f=i.get("type");if(f&&f!=="none"){var h=Gl(i),p=Rm[f](s,l,c,v);p.style=h,t.graphicKey=p.type,t.pointer=p}var d=i.get(["label","margin"]),g=Em(a,n,i,l,d);kc(t,n,i,o,g)},e}(kl);function Em(r,e,t,a,n){var i=e.axis,o=i.dataToCoord(r),s=a.getAngleAxis().getExtent()[0];s=s/180*Math.PI;var l=a.getRadiusAxis().getExtent(),u,v,c;if(i.dim==="radius"){var f=Ir();On(f,f,s),yr(f,f,[a.cx,a.cy]),u=Pl([o,-n],f);var h=e.getModel("axisLabel").get("rotate")||0,p=Ne.innerTextLayout(s,h*Math.PI/180,-1);v=p.textAlign,c=p.textVerticalAlign}else{var d=l[1];u=a.coordToPoint([d+n,o]);var g=a.cx,S=a.cy;v=Math.abs(u[0]-g)/d<.3?"center":u[0]>g?"left":"right",c=Math.abs(u[1]-S)/d<.3?"middle":u[1]>S?"top":"bottom"}return{position:u,align:v,verticalAlign:c}}var Rm={line:function(r,e,t,a){return r.dim==="angle"?{type:"Line",shape:zl(e.coordToPoint([a[0],t]),e.coordToPoint([a[1],t]))}:{type:"Circle",shape:{cx:e.cx,cy:e.cy,r:t}}},shadow:function(r,e,t,a){var n=Math.max(1,r.getBandWidth()),i=Math.PI/180;return r.dim==="angle"?{type:"Sector",shape:Ui(e.cx,e.cy,a[0],a[1],(-t-n/2)*i,(-t+n/2)*i)}:{type:"Sector",shape:Ui(e.cx,e.cy,t-n/2,t+n/2,0,Math.PI*2)}}},Vm=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.findAxisModel=function(t){var a,n=this.ecModel;return n.eachComponent(t,function(i){i.getCoordSysModel()===this&&(a=i)},this),a},e.type="polar",e.dependencies=["radiusAxis","angleAxis"],e.defaultOption={z:0,center:["50%","50%"],radius:"80%"},e}(me),xi=function(r){G(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.prototype.getCoordSysModel=function(){return this.getReferringComponents("polar",Qe).models[0]},e.type="polarAxis",e}(me);ye(xi,ma);var Nm=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.type="angleAxis",e}(xi),Gm=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.type="radiusAxis",e}(xi),bi=function(r){G(e,r);function e(t,a){return r.call(this,"radius",t,a)||this}return e.prototype.pointToData=function(t,a){return this.polar.pointToData(t,a)[this.dim==="radius"?0:1]},e}(ae);bi.prototype.dataToRadius=ae.prototype.dataToCoord;bi.prototype.radiusToData=ae.prototype.coordToData;var km=re(),wi=function(r){G(e,r);function e(t,a){return r.call(this,"angle",t,a||[0,360])||this}return e.prototype.pointToData=function(t,a){return this.polar.pointToData(t,a)[this.dim==="radius"?0:1]},e.prototype.calculateCategoryInterval=function(){var t=this,a=t.getLabelModel(),n=t.scale,i=n.getExtent(),o=n.count();if(i[1]-i[0]<1)return 0;var s=i[0],l=t.dataToCoord(s+1)-t.dataToCoord(s),u=Math.abs(l),v=zc(s==null?"":s+"",a.getFont(),"center","top"),c=Math.max(v.height,7),f=c/u;isNaN(f)&&(f=1/0);var h=Math.max(0,Math.floor(f)),p=km(t.model),d=p.lastAutoInterval,g=p.lastTickCount;return d!=null&&g!=null&&Math.abs(d-h)<=1&&Math.abs(g-o)<=1&&d>h?h=d:(p.lastTickCount=o,p.lastAutoInterval=h),h},e}(ae);wi.prototype.dataToAngle=ae.prototype.dataToCoord;wi.prototype.angleToData=ae.prototype.coordToData;var iv=["radius","angle"],zm=function(){function r(e){this.dimensions=iv,this.type="polar",this.cx=0,this.cy=0,this._radiusAxis=new bi,this._angleAxis=new wi,this.axisPointerEnabled=!0,this.name=e||"",this._radiusAxis.polar=this._angleAxis.polar=this}return r.prototype.containPoint=function(e){var t=this.pointToCoord(e);return this._radiusAxis.contain(t[0])&&this._angleAxis.contain(t[1])},r.prototype.containData=function(e){return this._radiusAxis.containData(e[0])&&this._angleAxis.containData(e[1])},r.prototype.getAxis=function(e){var t="_"+e+"Axis";return this[t]},r.prototype.getAxes=function(){return[this._radiusAxis,this._angleAxis]},r.prototype.getAxesByScale=function(e){var t=[],a=this._angleAxis,n=this._radiusAxis;return a.scale.type===e&&t.push(a),n.scale.type===e&&t.push(n),t},r.prototype.getAngleAxis=function(){return this._angleAxis},r.prototype.getRadiusAxis=function(){return this._radiusAxis},r.prototype.getOtherAxis=function(e){var t=this._angleAxis;return e===t?this._radiusAxis:t},r.prototype.getBaseAxis=function(){return this.getAxesByScale("ordinal")[0]||this.getAxesByScale("time")[0]||this.getAngleAxis()},r.prototype.getTooltipAxes=function(e){var t=e!=null&&e!=="auto"?this.getAxis(e):this.getBaseAxis();return{baseAxes:[t],otherAxes:[this.getOtherAxis(t)]}},r.prototype.dataToPoint=function(e,t){return this.coordToPoint([this._radiusAxis.dataToRadius(e[0],t),this._angleAxis.dataToAngle(e[1],t)])},r.prototype.pointToData=function(e,t){var a=this.pointToCoord(e);return[this._radiusAxis.radiusToData(a[0],t),this._angleAxis.angleToData(a[1],t)]},r.prototype.pointToCoord=function(e){var t=e[0]-this.cx,a=e[1]-this.cy,n=this.getAngleAxis(),i=n.getExtent(),o=Math.min(i[0],i[1]),s=Math.max(i[0],i[1]);n.inverse?o=s-360:s=o+360;var l=Math.sqrt(t*t+a*a);t/=l,a/=l;for(var u=Math.atan2(-a,t)/Math.PI*180,v=u<o?1:-1;u<o||u>s;)u+=v*360;return[l,u]},r.prototype.coordToPoint=function(e){var t=e[0],a=e[1]/180*Math.PI,n=Math.cos(a)*t+this.cx,i=-Math.sin(a)*t+this.cy;return[n,i]},r.prototype.getArea=function(){var e=this.getAngleAxis(),t=this.getRadiusAxis(),a=t.getExtent().slice();a[0]>a[1]&&a.reverse();var n=e.getExtent(),i=Math.PI/180,o=1e-4;return{cx:this.cx,cy:this.cy,r0:a[0],r:a[1],startAngle:-n[0]*i,endAngle:-n[1]*i,clockwise:e.inverse,contain:function(s,l){var u=s-this.cx,v=l-this.cy,c=u*u+v*v,f=this.r,h=this.r0;return f!==h&&c-o<=f*f&&c+o>=h*h}}},r.prototype.convertToPixel=function(e,t,a){var n=gs(t);return n===this?this.dataToPoint(a):null},r.prototype.convertFromPixel=function(e,t,a){var n=gs(t);return n===this?this.pointToData(a):null},r}();function gs(r){var e=r.seriesModel,t=r.polarModel;return t&&t.coordinateSystem||e&&e.coordinateSystem}function Om(r,e,t){var a=e.get("center"),n=t.getWidth(),i=t.getHeight();r.cx=z(a[0],n),r.cy=z(a[1],i);var o=r.getRadiusAxis(),s=Math.min(n,i)/2,l=e.get("radius");l==null?l=[0,"100%"]:Y(l)||(l=[0,l]);var u=[z(l[0],s),z(l[1],s)];o.inverse?o.setExtent(u[1],u[0]):o.setExtent(u[0],u[1])}function Bm(r,e){var t=this,a=t.getAngleAxis(),n=t.getRadiusAxis();if(a.scale.setExtent(1/0,-1/0),n.scale.setExtent(1/0,-1/0),r.eachSeries(function(s){if(s.coordinateSystem===t){var l=s.getData();P($i(l,"radius"),function(u){n.scale.unionExtentFromData(l,u)}),P($i(l,"angle"),function(u){a.scale.unionExtentFromData(l,u)})}}),jr(a.scale,a.model),jr(n.scale,n.model),a.type==="category"&&!a.onBand){var i=a.getExtent(),o=360/a.scale.count();a.inverse?i[1]+=o:i[1]-=o,a.setExtent(i[0],i[1])}}function Fm(r){return r.mainType==="angleAxis"}function ys(r,e){var t;if(r.type=e.get("type"),r.scale=$n(e),r.onBand=e.get("boundaryGap")&&r.type==="category",r.inverse=e.get("inverse"),Fm(e)){r.inverse=r.inverse!==e.get("clockwise");var a=e.get("startAngle"),n=(t=e.get("endAngle"))!==null&&t!==void 0?t:a+(r.inverse?-360:360);r.setExtent(a,n)}e.axis=r,r.model=e}var Hm={dimensions:iv,create:function(r,e){var t=[];return r.eachComponent("polar",function(a,n){var i=new zm(n+"");i.update=Bm;var o=i.getRadiusAxis(),s=i.getAngleAxis(),l=a.findAxisModel("radiusAxis"),u=a.findAxisModel("angleAxis");ys(o,l),ys(s,u),Om(i,a,e),t.push(i),a.coordinateSystem=i,i.model=a}),r.eachSeries(function(a){if(a.get("coordinateSystem")==="polar"){var n=a.getReferringComponents("polar",Qe).models[0];a.coordinateSystem=n.coordinateSystem}}),t}},Wm=["axisLine","axisLabel","axisTick","minorTick","splitLine","minorSplitLine","splitArea"];function Hr(r,e,t){e[1]>e[0]&&(e=e.slice().reverse());var a=r.coordToPoint([e[0],t]),n=r.coordToPoint([e[1],t]);return{x1:a[0],y1:a[1],x2:n[0],y2:n[1]}}function Wr(r){var e=r.getRadiusAxis();return e.inverse?0:1}function ms(r){var e=r[0],t=r[r.length-1];e&&t&&Math.abs(Math.abs(e.coord-t.coord)-360)<1e-4&&r.pop()}var Um=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t.axisPointerClass="PolarAxisPointer",t}return e.prototype.render=function(t,a){if(this.group.removeAll(),!!t.get("show")){var n=t.axis,i=n.polar,o=i.getRadiusAxis().getExtent(),s=n.getTicksCoords(),l=n.getMinorTicksCoords(),u=O(n.getViewLabels(),function(v){v=Zt(v);var c=n.scale,f=c.type==="ordinal"?c.getRawOrdinalNumber(v.tickValue):v.tickValue;return v.coord=n.dataToCoord(f),v});ms(u),ms(s),P(Wm,function(v){t.get([v,"show"])&&(!n.scale.isBlank()||v==="axisLine")&&$m[v](this.group,t,i,s,l,o,u)},this)}},e.type="angleAxis",e}(Lr),$m={axisLine:function(r,e,t,a,n,i){var o=e.getModel(["axisLine","lineStyle"]),s=t.getAngleAxis(),l=Math.PI/180,u=s.getExtent(),v=Wr(t),c=v?0:1,f,h=Math.abs(u[1]-u[0])===360?"Circle":"Arc";i[c]===0?f=new Je[h]({shape:{cx:t.cx,cy:t.cy,r:i[v],startAngle:-u[0]*l,endAngle:-u[1]*l,clockwise:s.inverse},style:o.getLineStyle(),z2:1,silent:!0}):f=new gl({shape:{cx:t.cx,cy:t.cy,r:i[v],r0:i[c]},style:o.getLineStyle(),z2:1,silent:!0}),f.style.fill=null,r.add(f)},axisTick:function(r,e,t,a,n,i){var o=e.getModel("axisTick"),s=(o.get("inside")?-1:1)*o.get("length"),l=i[Wr(t)],u=O(a,function(v){return new se({shape:Hr(t,[l,l+s],v.coord)})});r.add(kt(u,{style:j(o.getModel("lineStyle").getLineStyle(),{stroke:e.get(["axisLine","lineStyle","color"])})}))},minorTick:function(r,e,t,a,n,i){if(n.length){for(var o=e.getModel("axisTick"),s=e.getModel("minorTick"),l=(o.get("inside")?-1:1)*s.get("length"),u=i[Wr(t)],v=[],c=0;c<n.length;c++)for(var f=0;f<n[c].length;f++)v.push(new se({shape:Hr(t,[u,u+l],n[c][f].coord)}));r.add(kt(v,{style:j(s.getModel("lineStyle").getLineStyle(),j(o.getLineStyle(),{stroke:e.get(["axisLine","lineStyle","color"])}))}))}},axisLabel:function(r,e,t,a,n,i,o){var s=e.getCategories(!0),l=e.getModel("axisLabel"),u=l.get("margin"),v=e.get("triggerEvent");P(o,function(c,f){var h=l,p=c.tickValue,d=i[Wr(t)],g=t.coordToPoint([d+u,c.coord]),S=t.cx,m=t.cy,y=Math.abs(g[0]-S)/d<.3?"center":g[0]>S?"left":"right",w=Math.abs(g[1]-m)/d<.3?"middle":g[1]>m?"top":"bottom";if(s&&s[p]){var x=s[p];ga(x)&&x.textStyle&&(h=new Rt(x.textStyle,l,l.ecModel))}var b=new qt({silent:Ne.isLabelSilent(e),style:$t(h,{x:g[0],y:g[1],fill:h.getTextColor()||e.get(["axisLine","lineStyle","color"]),text:c.formattedLabel,align:y,verticalAlign:w})});if(r.add(b),v){var _=Ne.makeAxisEventDataBase(e);_.targetType="axisLabel",_.value=c.rawLabel,ht(b).eventData=_}},this)},splitLine:function(r,e,t,a,n,i){var o=e.getModel("splitLine"),s=o.getModel("lineStyle"),l=s.get("color"),u=0;l=l instanceof Array?l:[l];for(var v=[],c=0;c<a.length;c++){var f=u++%l.length;v[f]=v[f]||[],v[f].push(new se({shape:Hr(t,i,a[c].coord)}))}for(var c=0;c<v.length;c++)r.add(kt(v[c],{style:j({stroke:l[c%l.length]},s.getLineStyle()),silent:!0,z:e.get("z")}))},minorSplitLine:function(r,e,t,a,n,i){if(n.length){for(var o=e.getModel("minorSplitLine"),s=o.getModel("lineStyle"),l=[],u=0;u<n.length;u++)for(var v=0;v<n[u].length;v++)l.push(new se({shape:Hr(t,i,n[u][v].coord)}));r.add(kt(l,{style:s.getLineStyle(),silent:!0,z:e.get("z")}))}},splitArea:function(r,e,t,a,n,i){if(a.length){var o=e.getModel("splitArea"),s=o.getModel("areaStyle"),l=s.get("color"),u=0;l=l instanceof Array?l:[l];for(var v=[],c=Math.PI/180,f=-a[0].coord*c,h=Math.min(i[0],i[1]),p=Math.max(i[0],i[1]),d=e.get("clockwise"),g=1,S=a.length;g<=S;g++){var m=g===S?a[0].coord:a[g].coord,y=u++%l.length;v[y]=v[y]||[],v[y].push(new Ge({shape:{cx:t.cx,cy:t.cy,r0:h,r:p,startAngle:f,endAngle:-m*c,clockwise:d},silent:!0})),f=-m*c}for(var g=0;g<v.length;g++)r.add(kt(v[g],{style:j({fill:l[g%l.length]},s.getAreaStyle()),silent:!0}))}}},Ym=["axisLine","axisTickLabel","axisName"],Zm=["splitLine","splitArea","minorSplitLine"],Xm=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t.axisPointerClass="PolarAxisPointer",t}return e.prototype.render=function(t,a){if(this.group.removeAll(),!!t.get("show")){var n=this._axisGroup,i=this._axisGroup=new Z;this.group.add(i);var o=t.axis,s=o.polar,l=s.getAngleAxis(),u=o.getTicksCoords(),v=o.getMinorTicksCoords(),c=l.getExtent()[0],f=o.getExtent(),h=jm(s,t,c),p=new Ne(t,h);P(Ym,p.add,p),i.add(p.getGroup()),Yn(n,i,t),P(Zm,function(d){t.get([d,"show"])&&!o.scale.isBlank()&&qm[d](this.group,t,s,c,f,u,v)},this)}},e.type="radiusAxis",e}(Lr),qm={splitLine:function(r,e,t,a,n,i){var o=e.getModel("splitLine"),s=o.getModel("lineStyle"),l=s.get("color"),u=0,v=t.getAngleAxis(),c=Math.PI/180,f=v.getExtent(),h=Math.abs(f[1]-f[0])===360?"Circle":"Arc";l=l instanceof Array?l:[l];for(var p=[],d=0;d<i.length;d++){var g=u++%l.length;p[g]=p[g]||[],p[g].push(new Je[h]({shape:{cx:t.cx,cy:t.cy,r:Math.max(i[d].coord,0),startAngle:-f[0]*c,endAngle:-f[1]*c,clockwise:v.inverse}}))}for(var d=0;d<p.length;d++)r.add(kt(p[d],{style:j({stroke:l[d%l.length],fill:null},s.getLineStyle()),silent:!0}))},minorSplitLine:function(r,e,t,a,n,i,o){if(o.length){for(var s=e.getModel("minorSplitLine"),l=s.getModel("lineStyle"),u=[],v=0;v<o.length;v++)for(var c=0;c<o[v].length;c++)u.push(new Tr({shape:{cx:t.cx,cy:t.cy,r:o[v][c].coord}}));r.add(kt(u,{style:j({fill:null},l.getLineStyle()),silent:!0}))}},splitArea:function(r,e,t,a,n,i){if(i.length){var o=e.getModel("splitArea"),s=o.getModel("areaStyle"),l=s.get("color"),u=0;l=l instanceof Array?l:[l];for(var v=[],c=i[0].coord,f=1;f<i.length;f++){var h=u++%l.length;v[h]=v[h]||[],v[h].push(new Ge({shape:{cx:t.cx,cy:t.cy,r0:c,r:i[f].coord,startAngle:0,endAngle:Math.PI*2},silent:!0})),c=i[f].coord}for(var f=0;f<v.length;f++)r.add(kt(v[f],{style:j({fill:l[f%l.length]},s.getAreaStyle()),silent:!0}))}}};function jm(r,e,t){return{position:[r.cx,r.cy],rotation:t/180*Math.PI,labelDirection:-1,tickDirection:-1,nameDirection:1,labelRotate:e.getModel("axisLabel").get("rotate"),z2:1}}function ov(r){return r.get("stack")||"__ec_stack_"+r.seriesIndex}function sv(r,e){return e.dim+r.model.componentIndex}function Km(r,e,t){var a={},n=Jm(Pt(e.getSeriesByType(r),function(i){return!e.isSeriesFiltered(i)&&i.coordinateSystem&&i.coordinateSystem.type==="polar"}));e.eachSeriesByType(r,function(i){if(i.coordinateSystem.type==="polar"){var o=i.getData(),s=i.coordinateSystem,l=s.getBaseAxis(),u=sv(s,l),v=ov(i),c=n[u][v],f=c.offset,h=c.width,p=s.getOtherAxis(l),d=i.coordinateSystem.cx,g=i.coordinateSystem.cy,S=i.get("barMinHeight")||0,m=i.get("barMinAngle")||0;a[v]=a[v]||[];for(var y=o.mapDimension(p.dim),w=o.mapDimension(l.dim),x=Oc(o,y),b=l.dim!=="radius"||!i.get("roundCap",!0),_=p.model,T=_.get("startValue"),I=p.dataToCoord(T||0),A=0,D=o.count();A<D;A++){var E=o.get(y,A),M=o.get(w,A),C=E>=0?"p":"n",L=I;x&&(a[v][M]||(a[v][M]={p:I,n:I}),L=a[v][M][C]);var R=void 0,V=void 0,N=void 0,k=void 0;if(p.dim==="radius"){var B=p.dataToCoord(E)-I,W=l.dataToCoord(M);Math.abs(B)<S&&(B=(B<0?-1:1)*S),R=L,V=L+B,N=W-f,k=N-h,x&&(a[v][M][C]=V)}else{var X=p.dataToCoord(E,b)-I,tt=l.dataToCoord(M);Math.abs(X)<m&&(X=(X<0?-1:1)*m),R=tt+f,V=R+h,N=L,k=L+X,x&&(a[v][M][C]=k)}o.setItemLayout(A,{cx:d,cy:g,r0:R,r:V,startAngle:-N*Math.PI/180,endAngle:-k*Math.PI/180,clockwise:N>=k})}}})}function Jm(r){var e={};P(r,function(a,n){var i=a.getData(),o=a.coordinateSystem,s=o.getBaseAxis(),l=sv(o,s),u=s.getExtent(),v=s.type==="category"?s.getBandWidth():Math.abs(u[1]-u[0])/i.count(),c=e[l]||{bandWidth:v,remainedWidth:v,autoWidthCount:0,categoryGap:"20%",gap:"30%",stacks:{}},f=c.stacks;e[l]=c;var h=ov(a);f[h]||c.autoWidthCount++,f[h]=f[h]||{width:0,maxWidth:0};var p=z(a.get("barWidth"),v),d=z(a.get("barMaxWidth"),v),g=a.get("barGap"),S=a.get("barCategoryGap");p&&!f[h].width&&(p=Math.min(c.remainedWidth,p),f[h].width=p,c.remainedWidth-=p),d&&(f[h].maxWidth=d),g!=null&&(c.gap=g),S!=null&&(c.categoryGap=S)});var t={};return P(e,function(a,n){t[n]={};var i=a.stacks,o=a.bandWidth,s=z(a.categoryGap,o),l=z(a.gap,1),u=a.remainedWidth,v=a.autoWidthCount,c=(u-s)/(v+(v-1)*l);c=Math.max(c,0),P(i,function(d,g){var S=d.maxWidth;S&&S<c&&(S=Math.min(S,u),d.width&&(S=Math.min(S,d.width)),u-=S,d.width=S,v--)}),c=(u-s)/(v+(v-1)*l),c=Math.max(c,0);var f=0,h;P(i,function(d,g){d.width||(d.width=c),h=d,f+=d.width*(1+l)}),h&&(f-=h.width*l);var p=-f/2;P(i,function(d,g){t[n][g]=t[n][g]||{offset:p,width:d.width},p+=d.width*(1+l)})}),t}var Qm={startAngle:90,clockwise:!0,splitNumber:12,axisLabel:{rotate:0}},t0={splitNumber:5},e0=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.type="polar",e}(ze);function r0(r){$(Zn),Lr.registerAxisPointerClass("PolarAxisPointer",Mm),r.registerCoordinateSystem("polar",Hm),r.registerComponentModel(Vm),r.registerComponentView(e0),Qr(r,"angle",Nm,Qm),Qr(r,"radius",Gm,t0),r.registerComponentView(Um),r.registerComponentView(Xm),r.registerLayout(St(Km,"bar"))}function Mn(r,e){e=e||{};var t=r.coordinateSystem,a=r.axis,n={},i=a.position,o=a.orient,s=t.getRect(),l=[s.x,s.x+s.width,s.y,s.y+s.height],u={horizontal:{top:l[2],bottom:l[3]},vertical:{left:l[0],right:l[1]}};n.position=[o==="vertical"?u.vertical[i]:l[0],o==="horizontal"?u.horizontal[i]:l[3]];var v={horizontal:0,vertical:1};n.rotation=Math.PI/2*v[o];var c={top:-1,bottom:1,right:1,left:-1};n.labelDirection=n.tickDirection=n.nameDirection=c[i],r.get(["axisTick","inside"])&&(n.tickDirection=-n.tickDirection),zt(e.labelInside,r.get(["axisLabel","inside"]))&&(n.labelDirection=-n.labelDirection);var f=e.rotate;return f==null&&(f=r.get(["axisLabel","rotate"])),n.labelRotation=i==="top"?-f:f,n.z2=1,n}var a0=["axisLine","axisTickLabel","axisName"],n0=["splitArea","splitLine"],i0=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t.axisPointerClass="SingleAxisPointer",t}return e.prototype.render=function(t,a,n,i){var o=this.group;o.removeAll();var s=this._axisGroup;this._axisGroup=new Z;var l=Mn(t),u=new Ne(t,l);P(a0,u.add,u),o.add(this._axisGroup),o.add(u.getGroup()),P(n0,function(v){t.get([v,"show"])&&o0[v](this,this.group,this._axisGroup,t)},this),Yn(s,this._axisGroup,t),r.prototype.render.call(this,t,a,n,i)},e.prototype.remove=function(){Nf(this)},e.type="singleAxis",e}(Lr),o0={splitLine:function(r,e,t,a){var n=a.axis;if(!n.scale.isBlank()){var i=a.getModel("splitLine"),o=i.getModel("lineStyle"),s=o.get("color");s=s instanceof Array?s:[s];for(var l=o.get("width"),u=a.coordinateSystem.getRect(),v=n.isHorizontal(),c=[],f=0,h=n.getTicksCoords({tickModel:i}),p=[],d=[],g=0;g<h.length;++g){var S=n.toGlobalCoord(h[g].coord);v?(p[0]=S,p[1]=u.y,d[0]=S,d[1]=u.y+u.height):(p[0]=u.x,p[1]=S,d[0]=u.x+u.width,d[1]=S);var m=new se({shape:{x1:p[0],y1:p[1],x2:d[0],y2:d[1]},silent:!0});Bc(m.shape,l);var y=f++%s.length;c[y]=c[y]||[],c[y].push(m)}for(var w=o.getLineStyle(["color"]),g=0;g<c.length;++g)e.add(kt(c[g],{style:j({stroke:s[g%s.length]},w),silent:!0}))}},splitArea:function(r,e,t,a){Gf(r,t,a,a)}},Yr=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.getCoordSysModel=function(){return this},e.type="singleAxis",e.layoutMode="box",e.defaultOption={left:"5%",top:"5%",right:"5%",bottom:"5%",type:"value",position:"bottom",orient:"horizontal",axisLine:{show:!0,lineStyle:{width:1,type:"solid"}},tooltip:{show:!0},axisTick:{show:!0,length:6,lineStyle:{width:1}},axisLabel:{show:!0,interval:"auto"},splitLine:{show:!0,lineStyle:{type:"dashed",opacity:.2}}},e}(me);ye(Yr,ma.prototype);var s0=function(r){G(e,r);function e(t,a,n,i,o){var s=r.call(this,t,a,n)||this;return s.type=i||"value",s.position=o||"bottom",s}return e.prototype.isHorizontal=function(){var t=this.position;return t==="top"||t==="bottom"},e.prototype.pointToData=function(t,a){return this.coordinateSystem.pointToData(t)[0]},e}(ae),lv=["single"],l0=function(){function r(e,t,a){this.type="single",this.dimension="single",this.dimensions=lv,this.axisPointerEnabled=!0,this.model=e,this._init(e,t,a)}return r.prototype._init=function(e,t,a){var n=this.dimension,i=new s0(n,$n(e),[0,0],e.get("type"),e.get("position")),o=i.type==="category";i.onBand=o&&e.get("boundaryGap"),i.inverse=e.get("inverse"),i.orient=e.get("orient"),e.axis=i,i.model=e,i.coordinateSystem=this,this._axis=i},r.prototype.update=function(e,t){e.eachSeries(function(a){if(a.coordinateSystem===this){var n=a.getData();P(n.mapDimensionsAll(this.dimension),function(i){this._axis.scale.unionExtentFromData(n,i)},this),jr(this._axis.scale,this._axis.model)}},this)},r.prototype.resize=function(e,t){this._rect=Se({left:e.get("left"),top:e.get("top"),right:e.get("right"),bottom:e.get("bottom"),width:e.get("width"),height:e.get("height")},{width:t.getWidth(),height:t.getHeight()}),this._adjustAxis()},r.prototype.getRect=function(){return this._rect},r.prototype._adjustAxis=function(){var e=this._rect,t=this._axis,a=t.isHorizontal(),n=a?[0,e.width]:[0,e.height],i=t.inverse?1:0;t.setExtent(n[i],n[1-i]),this._updateAxisTransform(t,a?e.x:e.y)},r.prototype._updateAxisTransform=function(e,t){var a=e.getExtent(),n=a[0]+a[1],i=e.isHorizontal();e.toGlobalCoord=i?function(o){return o+t}:function(o){return n-o+t},e.toLocalCoord=i?function(o){return o-t}:function(o){return n-o+t}},r.prototype.getAxis=function(){return this._axis},r.prototype.getBaseAxis=function(){return this._axis},r.prototype.getAxes=function(){return[this._axis]},r.prototype.getTooltipAxes=function(){return{baseAxes:[this.getAxis()],otherAxes:[]}},r.prototype.containPoint=function(e){var t=this.getRect(),a=this.getAxis(),n=a.orient;return n==="horizontal"?a.contain(a.toLocalCoord(e[0]))&&e[1]>=t.y&&e[1]<=t.y+t.height:a.contain(a.toLocalCoord(e[1]))&&e[0]>=t.y&&e[0]<=t.y+t.height},r.prototype.pointToData=function(e){var t=this.getAxis();return[t.coordToData(t.toLocalCoord(e[t.orient==="horizontal"?0:1]))]},r.prototype.dataToPoint=function(e){var t=this.getAxis(),a=this.getRect(),n=[],i=t.orient==="horizontal"?0:1;return e instanceof Array&&(e=e[0]),n[i]=t.toGlobalCoord(t.dataToCoord(+e)),n[1-i]=i===0?a.y+a.height/2:a.x+a.width/2,n},r.prototype.convertToPixel=function(e,t,a){var n=Ss(t);return n===this?this.dataToPoint(a):null},r.prototype.convertFromPixel=function(e,t,a){var n=Ss(t);return n===this?this.pointToData(a):null},r}();function Ss(r){var e=r.seriesModel,t=r.singleAxisModel;return t&&t.coordinateSystem||e&&e.coordinateSystem}function u0(r,e){var t=[];return r.eachComponent("singleAxis",function(a,n){var i=new l0(a,r,e);i.name="single_"+n,i.resize(a,e),a.coordinateSystem=i,t.push(i)}),r.eachSeries(function(a){if(a.get("coordinateSystem")==="singleAxis"){var n=a.getReferringComponents("singleAxis",Qe).models[0];a.coordinateSystem=n&&n.coordinateSystem}}),t}var v0={create:u0,dimensions:lv},xs=["x","y"],c0=["width","height"],f0=function(r){G(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.prototype.makeElOption=function(t,a,n,i,o){var s=n.axis,l=s.coordinateSystem,u=an(l,1-oa(s)),v=l.dataToPoint(a)[0],c=i.get("type");if(c&&c!=="none"){var f=Gl(i),h=h0[c](s,v,u);h.style=f,t.graphicKey=h.type,t.pointer=h}var p=Mn(n);Fc(a,t,p,n,i,o)},e.prototype.getHandleTransform=function(t,a,n){var i=Mn(a,{labelInside:!1});i.labelMargin=n.get(["handle","margin"]);var o=Hc(a.axis,t,i);return{x:o[0],y:o[1],rotation:i.rotation+(i.labelDirection<0?Math.PI:0)}},e.prototype.updateHandleTransform=function(t,a,n,i){var o=n.axis,s=o.coordinateSystem,l=oa(o),u=an(s,l),v=[t.x,t.y];v[l]+=a[l],v[l]=Math.min(u[1],v[l]),v[l]=Math.max(u[0],v[l]);var c=an(s,1-l),f=(c[1]+c[0])/2,h=[f,f];return h[l]=v[l],{x:v[0],y:v[1],rotation:t.rotation,cursorPoint:h,tooltipOption:{verticalAlign:"middle"}}},e}(kl),h0={line:function(r,e,t){var a=zl([e,t[0]],[e,t[1]],oa(r));return{type:"Line",subPixelOptimize:!0,shape:a}},shadow:function(r,e,t){var a=r.getBandWidth(),n=t[1]-t[0];return{type:"Rect",shape:Wc([e-a/2,t[0]],[a,n],oa(r))}}};function oa(r){return r.isHorizontal()?0:1}function an(r,e){var t=r.getRect();return[t[xs[e]],t[xs[e]]+t[c0[e]]]}var p0=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.type="single",e}(ze);function d0(r){$(Zn),Lr.registerAxisPointerClass("SingleAxisPointer",f0),r.registerComponentView(p0),r.registerComponentView(i0),r.registerComponentModel(Yr),Qr(r,"single",Yr,Yr.defaultOption),r.registerCoordinateSystem("single",v0)}var g0=["rect","polygon","keep","clear"];function y0(r,e){var t=Ot(r?r.brush:[]);if(t.length){var a=[];P(t,function(l){var u=l.hasOwnProperty("toolbox")?l.toolbox:[];u instanceof Array&&(a=a.concat(u))});var n=r&&r.toolbox;Y(n)&&(n=n[0]),n||(n={feature:{}},r.toolbox=[n]);var i=n.feature||(n.feature={}),o=i.brush||(i.brush={}),s=o.type||(o.type=[]);s.push.apply(s,a),m0(s),e&&!s.length&&s.push.apply(s,g0)}}function m0(r){var e={};P(r,function(t){e[t]=1}),r.length=0,P(e,function(t,a){r.push(a)})}function S0(r){var e=r.brushType,t={point:function(a){return bs[e].point(a,t,r)},rect:function(a){return bs[e].rect(a,t,r)}};return t}var bs={lineX:ws(0),lineY:ws(1),rect:{point:function(r,e,t){return r&&t.boundingRect.contain(r[0],r[1])},rect:function(r,e,t){return r&&t.boundingRect.intersect(r)}},polygon:{point:function(r,e,t){return r&&t.boundingRect.contain(r[0],r[1])&&Me(t.range,r[0],r[1])},rect:function(r,e,t){var a=t.range;if(!r||a.length<=1)return!1;var n=r.x,i=r.y,o=r.width,s=r.height,l=a[0];if(Me(a,n,i)||Me(a,n+o,i)||Me(a,n,i+s)||Me(a,n+o,i+s)||dt.create(r).contain(l[0],l[1])||Nr(n,i,n+o,i,a)||Nr(n,i,n,i+s,a)||Nr(n+o,i,n+o,i+s,a)||Nr(n,i+s,n+o,i+s,a))return!0}}};function ws(r){var e=["x","y"],t=["width","height"];return{point:function(a,n,i){if(a){var o=i.range,s=a[r];return or(s,o)}},rect:function(a,n,i){if(a){var o=i.range,s=[a[e[r]],a[e[r]]+a[t[r]]];return s[1]<s[0]&&s.reverse(),or(s[0],o)||or(s[1],o)||or(o[0],s)||or(o[1],s)}}}}function or(r,e){return e[0]<=r&&r<=e[1]}var _s=["inBrush","outOfBrush"],nn="__ecBrushSelect",En="__ecInBrushSelectEvent";function uv(r){r.eachComponent({mainType:"brush"},function(e){var t=e.brushTargetManager=new Of(e.option,r);t.setInputRanges(e.areas,r)})}function x0(r,e,t){var a=[],n,i;r.eachComponent({mainType:"brush"},function(o){t&&t.type==="takeGlobalCursor"&&o.setBrushOption(t.key==="brush"?t.brushOption:{brushType:!1})}),uv(r),r.eachComponent({mainType:"brush"},function(o,s){var l={brushId:o.id,brushIndex:s,brushName:o.name,areas:Zt(o.areas),selected:[]};a.push(l);var u=o.option,v=u.brushLink,c=[],f=[],h=[],p=!1;s||(n=u.throttleType,i=u.throttleDelay);var d=O(o.areas,function(x){var b=A0[x.brushType],_=j({boundingRect:b?b(x):void 0},x);return _.selectors=S0(_),_}),g=kf(o.option,_s,function(x){x.mappingMethod="fixed"});Y(v)&&P(v,function(x){c[x]=1});function S(x){return v==="all"||!!c[x]}function m(x){return!!x.length}r.eachSeries(function(x,b){var _=h[b]=[];x.subType==="parallel"?y(x,b):w(x,b,_)});function y(x,b){var _=x.coordinateSystem;p=p||_.hasAxisBrushed(),S(b)&&_.eachActiveState(x.getData(),function(T,I){T==="active"&&(f[I]=1)})}function w(x,b,_){if(!(!x.brushSelector||_0(o,b))&&(P(d,function(I){o.brushTargetManager.controlSeries(I,x,r)&&_.push(I),p=p||m(_)}),S(b)&&m(_))){var T=x.getData();T.each(function(I){As(x,_,T,I)&&(f[I]=1)})}}r.eachSeries(function(x,b){var _={seriesId:x.id,seriesIndex:b,seriesName:x.name,dataIndex:[]};l.selected.push(_);var T=h[b],I=x.getData(),A=S(b)?function(D){return f[D]?(_.dataIndex.push(I.getRawIndex(D)),"inBrush"):"outOfBrush"}:function(D){return As(x,T,I,D)?(_.dataIndex.push(I.getRawIndex(D)),"inBrush"):"outOfBrush"};(S(b)?p:m(T))&&zf(_s,g,I,A)})}),b0(e,n,i,a,t)}function b0(r,e,t,a,n){if(n){var i=r.getZr();if(!i[En]){i[nn]||(i[nn]=w0);var o=Ll(i,nn,t,e);o(r,a)}}}function w0(r,e){if(!r.isDisposed()){var t=r.getZr();t[En]=!0,r.dispatchAction({type:"brushSelect",batch:e}),t[En]=!1}}function As(r,e,t,a){for(var n=0,i=e.length;n<i;n++){var o=e[n];if(r.brushSelector(a,t,o.selectors,o))return!0}}function _0(r,e){var t=r.option.seriesIndex;return t!=null&&t!=="all"&&(Y(t)?xe(t,e)<0:e!==t)}var A0={rect:function(r){return Ts(r.range)},polygon:function(r){for(var e,t=r.range,a=0,n=t.length;a<n;a++){e=e||[[1/0,-1/0],[1/0,-1/0]];var i=t[a];i[0]<e[0][0]&&(e[0][0]=i[0]),i[0]>e[0][1]&&(e[0][1]=i[0]),i[1]<e[1][0]&&(e[1][0]=i[1]),i[1]>e[1][1]&&(e[1][1]=i[1])}return e&&Ts(e)}};function Ts(r){return new dt(r[0][0],r[1][0],r[0][1]-r[0][0],r[1][1]-r[1][0])}var T0=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t,a){this.ecModel=t,this.api=a,this.model,(this._brushController=new Ql(a.getZr())).on("brush",lt(this._onBrush,this)).mount()},e.prototype.render=function(t,a,n,i){this.model=t,this._updateController(t,a,n,i)},e.prototype.updateTransform=function(t,a,n,i){uv(a),this._updateController(t,a,n,i)},e.prototype.updateVisual=function(t,a,n,i){this.updateTransform(t,a,n,i)},e.prototype.updateView=function(t,a,n,i){this._updateController(t,a,n,i)},e.prototype._updateController=function(t,a,n,i){(!i||i.$from!==t.id)&&this._brushController.setPanels(t.brushTargetManager.makePanelOpts(n)).enableBrush(t.brushOption).updateCovers(t.areas.slice())},e.prototype.dispose=function(){this._brushController.dispose()},e.prototype._onBrush=function(t){var a=this.model.id,n=this.model.brushTargetManager.setOutputRanges(t.areas,this.ecModel);(!t.isEnd||t.removeOnClick)&&this.api.dispatchAction({type:"brush",brushId:a,areas:Zt(n),$from:a}),t.isEnd&&this.api.dispatchAction({type:"brushEnd",brushId:a,areas:Zt(n),$from:a})},e.type="brush",e}(ze),I0="#ddd",D0=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t.areas=[],t.brushOption={},t}return e.prototype.optionUpdated=function(t,a){var n=this.option;!a&&Bf(n,t,["inBrush","outOfBrush"]);var i=n.inBrush=n.inBrush||{};n.outOfBrush=n.outOfBrush||{color:I0},i.hasOwnProperty("liftZ")||(i.liftZ=5)},e.prototype.setAreas=function(t){t&&(this.areas=O(t,function(a){return Is(this.option,a)},this))},e.prototype.setBrushOption=function(t){this.brushOption=Is(this.option,t),this.brushType=this.brushOption.brushType},e.type="brush",e.dependencies=["geo","grid","xAxis","yAxis","parallel","series"],e.defaultOption={seriesIndex:"all",brushType:"rect",brushMode:"single",transformable:!0,brushStyle:{borderWidth:1,color:"rgba(210,219,238,0.3)",borderColor:"#D2DBEE"},throttleType:"fixRate",throttleDelay:0,removeOnClick:!0,z:1e4},e}(me);function Is(r,e){return Qt({brushType:r.brushType,brushMode:r.brushMode,transformable:r.transformable,brushStyle:new Rt(r.brushStyle).getItemStyle(),removeOnClick:r.removeOnClick,z:r.z},e,!0)}var C0=["rect","polygon","lineX","lineY","keep","clear"],L0=function(r){G(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.prototype.render=function(t,a,n){var i,o,s;a.eachComponent({mainType:"brush"},function(l){i=l.brushType,o=l.brushOption.brushMode||"single",s=s||!!l.areas.length}),this._brushType=i,this._brushMode=o,P(t.get("type",!0),function(l){t.setIconStatus(l,(l==="keep"?o==="multiple":l==="clear"?s:l===i)?"emphasis":"normal")})},e.prototype.updateView=function(t,a,n){this.render(t,a,n)},e.prototype.getIcons=function(){var t=this.model,a=t.get("icon",!0),n={};return P(t.get("type",!0),function(i){a[i]&&(n[i]=a[i])}),n},e.prototype.onclick=function(t,a,n){var i=this._brushType,o=this._brushMode;n==="clear"?(a.dispatchAction({type:"axisAreaSelect",intervals:[]}),a.dispatchAction({type:"brush",command:"clear",areas:[]})):a.dispatchAction({type:"takeGlobalCursor",key:"brush",brushOption:{brushType:n==="keep"?i:i===n?!1:n,brushMode:n==="keep"?o==="multiple"?"single":"multiple":o}})},e.getDefaultOption=function(t){var a={show:!0,type:C0.slice(),icon:{rect:"M7.3,34.7 M0.4,10V-0.2h9.8 M89.6,10V-0.2h-9.8 M0.4,60v10.2h9.8 M89.6,60v10.2h-9.8 M12.3,22.4V10.5h13.1 M33.6,10.5h7.8 M49.1,10.5h7.8 M77.5,22.4V10.5h-13 M12.3,31.1v8.2 M77.7,31.1v8.2 M12.3,47.6v11.9h13.1 M33.6,59.5h7.6 M49.1,59.5 h7.7 M77.5,47.6v11.9h-13",polygon:"M55.2,34.9c1.7,0,3.1,1.4,3.1,3.1s-1.4,3.1-3.1,3.1 s-3.1-1.4-3.1-3.1S53.5,34.9,55.2,34.9z M50.4,51c1.7,0,3.1,1.4,3.1,3.1c0,1.7-1.4,3.1-3.1,3.1c-1.7,0-3.1-1.4-3.1-3.1 C47.3,52.4,48.7,51,50.4,51z M55.6,37.1l1.5-7.8 M60.1,13.5l1.6-8.7l-7.8,4 M59,19l-1,5.3 M24,16.1l6.4,4.9l6.4-3.3 M48.5,11.6 l-5.9,3.1 M19.1,12.8L9.7,5.1l1.1,7.7 M13.4,29.8l1,7.3l6.6,1.6 M11.6,18.4l1,6.1 M32.8,41.9 M26.6,40.4 M27.3,40.2l6.1,1.6 M49.9,52.1l-5.6-7.6l-4.9-1.2",lineX:"M15.2,30 M19.7,15.6V1.9H29 M34.8,1.9H40.4 M55.3,15.6V1.9H45.9 M19.7,44.4V58.1H29 M34.8,58.1H40.4 M55.3,44.4 V58.1H45.9 M12.5,20.3l-9.4,9.6l9.6,9.8 M3.1,29.9h16.5 M62.5,20.3l9.4,9.6L62.3,39.7 M71.9,29.9H55.4",lineY:"M38.8,7.7 M52.7,12h13.2v9 M65.9,26.6V32 M52.7,46.3h13.2v-9 M24.9,12H11.8v9 M11.8,26.6V32 M24.9,46.3H11.8v-9 M48.2,5.1l-9.3-9l-9.4,9.2 M38.9-3.9V12 M48.2,53.3l-9.3,9l-9.4-9.2 M38.9,62.3V46.4",keep:"M4,10.5V1h10.3 M20.7,1h6.1 M33,1h6.1 M55.4,10.5V1H45.2 M4,17.3v6.6 M55.6,17.3v6.6 M4,30.5V40h10.3 M20.7,40 h6.1 M33,40h6.1 M55.4,30.5V40H45.2 M21,18.9h62.9v48.6H21V18.9z",clear:"M22,14.7l30.9,31 M52.9,14.7L22,45.7 M4.7,16.8V4.2h13.1 M26,4.2h7.8 M41.6,4.2h7.8 M70.3,16.8V4.2H57.2 M4.7,25.9v8.6 M70.3,25.9v8.6 M4.7,43.2v12.6h13.1 M26,55.8h7.8 M41.6,55.8h7.8 M70.3,43.2v12.6H57.2"},title:t.getLocaleModel().get(["toolbox","brush","title"])};return a},e}(Ff);function P0(r){r.registerComponentView(T0),r.registerComponentModel(D0),r.registerPreprocessor(y0),r.registerVisual(r.PRIORITY.VISUAL.BRUSH,x0),r.registerAction({type:"brush",event:"brush",update:"updateVisual"},function(e,t){t.eachComponent({mainType:"brush",query:e},function(a){a.setAreas(e.areas)})}),r.registerAction({type:"brushSelect",event:"brushSelected",update:"none"},je),r.registerAction({type:"brushEnd",event:"brushEnd",update:"none"},je),Hf("brush",L0)}var M0=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.createMarkerModelFromSeries=function(t,a,n){return new e(t,a,n)},e.type="markPoint",e.defaultOption={z:5,symbol:"pin",symbolSize:50,tooltip:{trigger:"item"},label:{show:!0,position:"inside"},itemStyle:{borderWidth:2},emphasis:{label:{show:!0}}},e}(xa);function Ds(r,e,t){var a=e.coordinateSystem;r.each(function(n){var i=r.getItemModel(n),o,s=z(i.get("x"),t.getWidth()),l=z(i.get("y"),t.getHeight());if(!isNaN(s)&&!isNaN(l))o=[s,l];else if(e.getMarkerPosition)o=e.getMarkerPosition(r.getValues(r.dimensions,n));else if(a){var u=r.get(a.dimensions[0],n),v=r.get(a.dimensions[1],n);o=a.dataToPoint([u,v])}isNaN(s)||(o[0]=s),isNaN(l)||(o[1]=l),r.setItemLayout(n,o)})}var E0=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.updateTransform=function(t,a,n){a.eachSeries(function(i){var o=xa.getMarkerModelFromSeries(i,"markPoint");o&&(Ds(o.getData(),i,n),this.markerGroupMap.get(i.id).updateLayout())},this)},e.prototype.renderSeries=function(t,a,n,i){var o=t.coordinateSystem,s=t.id,l=t.getData(),u=this.markerGroupMap,v=u.get(s)||u.set(s,new Jn),c=R0(o,t,a);a.setData(c),Ds(a.getData(),t,i),c.each(function(f){var h=c.getItemModel(f),p=h.getShallow("symbol"),d=h.getShallow("symbolSize"),g=h.getShallow("symbolRotate"),S=h.getShallow("symbolOffset"),m=h.getShallow("symbolKeepAspect");if(st(p)||st(d)||st(g)||st(S)){var y=a.getRawValue(f),w=a.getDataParams(f);st(p)&&(p=p(y,w)),st(d)&&(d=d(y,w)),st(g)&&(g=g(y,w)),st(S)&&(S=S(y,w))}var x=h.getModel("itemStyle").getItemStyle(),b=Ol(l,"color");x.fill||(x.fill=b),c.setItemVisual(f,{symbol:p,symbolSize:d,symbolRotate:g,symbolOffset:S,symbolKeepAspect:m,style:x})}),v.updateData(c),this.group.add(v.group),c.eachItemGraphicEl(function(f){f.traverse(function(h){ht(h).dataModel=a})}),this.markKeep(v),v.group.silent=a.get("silent")||t.get("silent")},e.type="markPoint",e}(ru);function R0(r,e,t){var a;r?a=O(r&&r.dimensions,function(s){var l=e.getData().getDimensionInfo(e.getData().mapDimension(s))||{};return H(H({},l),{name:s,ordinalMeta:null})}):a=[{name:"value",type:"float"}];var n=new te(a,t),i=O(t.get("data"),St(Sn,e));r&&(i=Pt(i,St(xn,r)));var o=Wf(!!r,a);return n.initData(i,null,o),n}function V0(r){r.registerComponentModel(M0),r.registerComponentView(E0),r.registerPreprocessor(function(e){au(e.series,"markPoint")&&(e.markPoint=e.markPoint||{})})}var N0=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.createMarkerModelFromSeries=function(t,a,n){return new e(t,a,n)},e.type="markArea",e.defaultOption={z:1,tooltip:{trigger:"item"},animation:!1,label:{show:!0,position:"top"},itemStyle:{borderWidth:0},emphasis:{label:{show:!0,position:"top"}}},e}(xa),Ur=re(),G0=function(r,e,t,a){var n=a[0],i=a[1];if(!(!n||!i)){var o=Sn(r,n),s=Sn(r,i),l=o.coord,u=s.coord;l[0]=zt(l[0],-1/0),l[1]=zt(l[1],-1/0),u[0]=zt(u[0],1/0),u[1]=zt(u[1],1/0);var v=Fn([{},o,s]);return v.coord=[o.coord,s.coord],v.x0=o.x,v.y0=o.y,v.x1=s.x,v.y1=s.y,v}};function sa(r){return!isNaN(r)&&!isFinite(r)}function Cs(r,e,t,a){var n=1-r;return sa(e[n])&&sa(t[n])}function k0(r,e){var t=e.coord[0],a=e.coord[1],n={coord:t,x:e.x0,y:e.y0},i={coord:a,x:e.x1,y:e.y1};return ti(r,"cartesian2d")?t&&a&&(Cs(1,t,a)||Cs(0,t,a))?!0:Uf(r,n,i):xn(r,n)||xn(r,i)}function Ls(r,e,t,a,n){var i=a.coordinateSystem,o=r.getItemModel(e),s,l=z(o.get(t[0]),n.getWidth()),u=z(o.get(t[1]),n.getHeight());if(!isNaN(l)&&!isNaN(u))s=[l,u];else{if(a.getMarkerPosition){var v=r.getValues(["x0","y0"],e),c=r.getValues(["x1","y1"],e),f=i.clampData(v),h=i.clampData(c),p=[];t[0]==="x0"?p[0]=f[0]>h[0]?c[0]:v[0]:p[0]=f[0]>h[0]?v[0]:c[0],t[1]==="y0"?p[1]=f[1]>h[1]?c[1]:v[1]:p[1]=f[1]>h[1]?v[1]:c[1],s=a.getMarkerPosition(p,t,!0)}else{var d=r.get(t[0],e),g=r.get(t[1],e),S=[d,g];i.clampData&&i.clampData(S,S),s=i.dataToPoint(S,!0)}if(ti(i,"cartesian2d")){var m=i.getAxis("x"),y=i.getAxis("y"),d=r.get(t[0],e),g=r.get(t[1],e);sa(d)?s[0]=m.toGlobalCoord(m.getExtent()[t[0]==="x0"?0:1]):sa(g)&&(s[1]=y.toGlobalCoord(y.getExtent()[t[1]==="y0"?0:1]))}isNaN(l)||(s[0]=l),isNaN(u)||(s[1]=u)}return s}var Ps=[["x0","y0"],["x1","y0"],["x1","y1"],["x0","y1"]],z0=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.updateTransform=function(t,a,n){a.eachSeries(function(i){var o=xa.getMarkerModelFromSeries(i,"markArea");if(o){var s=o.getData();s.each(function(l){var u=O(Ps,function(c){return Ls(s,l,c,i,n)});s.setItemLayout(l,u);var v=s.getItemGraphicEl(l);v.setShape("points",u)})}},this)},e.prototype.renderSeries=function(t,a,n,i){var o=t.coordinateSystem,s=t.id,l=t.getData(),u=this.markerGroupMap,v=u.get(s)||u.set(s,{group:new Z});this.group.add(v.group),this.markKeep(v);var c=O0(o,t,a);a.setData(c),c.each(function(f){var h=O(Ps,function(T){return Ls(c,f,T,t,i)}),p=o.getAxis("x").scale,d=o.getAxis("y").scale,g=p.getExtent(),S=d.getExtent(),m=[p.parse(c.get("x0",f)),p.parse(c.get("x1",f))],y=[d.parse(c.get("y0",f)),d.parse(c.get("y1",f))];Kr(m),Kr(y);var w=!(g[0]>m[1]||g[1]<m[0]||S[0]>y[1]||S[1]<y[0]),x=!w;c.setItemLayout(f,{points:h,allClipped:x});var b=c.getItemModel(f).getModel("itemStyle").getItemStyle(),_=Ol(l,"color");b.fill||(b.fill=_,et(b.fill)&&(b.fill=dn(b.fill,.4))),b.stroke||(b.stroke=_),c.setItemVisual(f,"style",b)}),c.diff(Ur(v).data).add(function(f){var h=c.getItemLayout(f);if(!h.allClipped){var p=new Jt({shape:{points:h.points}});c.setItemGraphicEl(f,p),v.group.add(p)}}).update(function(f,h){var p=Ur(v).data.getItemGraphicEl(h),d=c.getItemLayout(f);d.allClipped?p&&v.group.remove(p):(p?ct(p,{shape:{points:d.points}},a,f):p=new Jt({shape:{points:d.points}}),c.setItemGraphicEl(f,p),v.group.add(p))}).remove(function(f){var h=Ur(v).data.getItemGraphicEl(f);v.group.remove(h)}).execute(),c.eachItemGraphicEl(function(f,h){var p=c.getItemModel(h),d=c.getItemVisual(h,"style");f.useStyle(c.getItemVisual(h,"style")),Bt(f,Et(p),{labelFetcher:a,labelDataIndex:h,defaultText:c.getName(h)||"",inheritColor:et(d.fill)?dn(d.fill,1):"#000"}),Mt(f,p),ft(f,null,null,p.get(["emphasis","disabled"])),ht(f).dataModel=a}),Ur(v).data=c,v.group.silent=a.get("silent")||t.get("silent")},e.type="markArea",e}(ru);function O0(r,e,t){var a,n,i=["x0","y0","x1","y1"];if(r){var o=O(r&&r.dimensions,function(u){var v=e.getData(),c=v.getDimensionInfo(v.mapDimension(u))||{};return H(H({},c),{name:u,ordinalMeta:null})});n=O(i,function(u,v){return{name:u,type:o[v%2].type}}),a=new te(n,t)}else n=[{name:"value",type:"float"}],a=new te(n,t);var s=O(t.get("data"),St(G0,e,r,t));r&&(s=Pt(s,St(k0,r)));var l=r?function(u,v,c,f){var h=u.coord[Math.floor(f/2)][f%2];return Yi(h,n[f])}:function(u,v,c,f){return Yi(u.value,n[f])};return a.initData(s,null,l),a.hasItemOption=!0,a}function B0(r){r.registerComponentModel(N0),r.registerComponentView(z0),r.registerPreprocessor(function(e){au(e.series,"markArea")&&(e.markArea=e.markArea||{})})}var F0={label:{enabled:!0},decal:{show:!1}},Ms=re(),H0={};function W0(r,e){var t=r.getModel("aria");if(!t.get("enabled"))return;var a=Zt(F0);Qt(a.label,r.getLocaleModel().get("aria"),!1),Qt(t.option,a,!1),n(),i();function n(){var u=t.getModel("decal"),v=u.get("show");if(v){var c=K();r.eachSeries(function(f){if(!f.isColorBySeries()){var h=c.get(f.type);h||(h={},c.set(f.type,h)),Ms(f).scope=h}}),r.eachRawSeries(function(f){if(r.isSeriesFiltered(f))return;if(st(f.enableAriaDecal)){f.enableAriaDecal();return}var h=f.getData();if(f.isColorBySeries()){var m=pn(f.ecModel,f.name,H0,r.getSeriesCount()),y=h.getVisual("decal");h.setVisual("decal",w(y,m))}else{var p=f.getRawData(),d={},g=Ms(f).scope;h.each(function(x){var b=h.getRawIndex(x);d[b]=x});var S=p.count();p.each(function(x){var b=d[x],_=p.getName(x)||x+"",T=pn(f.ecModel,_,g,S),I=h.getItemVisual(b,"decal");h.setItemVisual(b,"decal",w(I,T))})}function w(x,b){var _=x?H(H({},b),x):b;return _.dirty=!0,_}})}}function i(){var u=e.getZr().dom;if(u){var v=r.getLocaleModel().get("aria"),c=t.getModel("label");if(c.option=j(c.option,v),!!c.get("enabled")){if(u.setAttribute("role","img"),c.get("description")){u.setAttribute("aria-label",c.get("description"));return}var f=r.getSeriesCount(),h=c.get(["data","maxCount"])||10,p=c.get(["series","maxCount"])||10,d=Math.min(f,p),g;if(!(f<1)){var S=s();if(S){var m=c.get(["general","withTitle"]);g=o(m,{title:S})}else g=c.get(["general","withoutTitle"]);var y=[],w=f>1?c.get(["series","multiple","prefix"]):c.get(["series","single","prefix"]);g+=o(w,{seriesCount:f}),r.eachSeries(function(T,I){if(I<d){var A=void 0,D=T.get("name"),E=D?"withName":"withoutName";A=f>1?c.get(["series","multiple",E]):c.get(["series","single",E]),A=o(A,{seriesId:T.seriesIndex,seriesName:T.get("name"),seriesType:l(T.subType)});var M=T.getData();if(M.count()>h){var C=c.get(["data","partialData"]);A+=o(C,{displayCnt:h})}else A+=c.get(["data","allData"]);for(var L=c.get(["data","separator","middle"]),R=c.get(["data","separator","end"]),V=c.get(["data","excludeDimensionId"]),N=[],k=0;k<M.count();k++)if(k<h){var B=M.getName(k),W=V?Pt(M.getValues(k),function(tt,J){return xe(V,J)===-1}):M.getValues(k),X=c.get(["data",B?"withName":"withoutName"]);N.push(o(X,{name:B,value:W.join(L)}))}A+=N.join(L)+R,y.push(A)}});var x=c.getModel(["series","multiple","separator"]),b=x.get("middle"),_=x.get("end");g+=y.join(b)+_,u.setAttribute("aria-label",g)}}}}function o(u,v){if(!et(u))return u;var c=u;return P(v,function(f,h){c=c.replace(new RegExp("\\{\\s*"+h+"\\s*\\}","g"),f)}),c}function s(){var u=r.get("title");return u&&u.length&&(u=u[0]),u&&u.text}function l(u){var v=r.getLocaleModel().get(["series","typeNames"]);return v[u]||v.chart}}function U0(r){if(!(!r||!r.aria)){var e=r.aria;e.show!=null&&(e.enabled=e.show),e.label=e.label||{},P(["description","general","series","data"],function(t){e[t]!=null&&(e.label[t]=e[t])})}}function $0(r){r.registerPreprocessor(U0),r.registerVisual(r.PRIORITY.VISUAL.ARIA,W0)}var Es={value:"eq","<":"lt","<=":"lte",">":"gt",">=":"gte","=":"eq","!=":"ne","<>":"ne"},Y0=function(){function r(e){var t=this._condVal=et(e)?new RegExp(e):Yc(e)?e:null;if(t==null){var a="";ot(a)}}return r.prototype.evaluate=function(e){var t=typeof e;return et(t)?this._condVal.test(e):Xt(t)?this._condVal.test(e+""):!1},r}(),Z0=function(){function r(){}return r.prototype.evaluate=function(){return this.value},r}(),X0=function(){function r(){}return r.prototype.evaluate=function(){for(var e=this.children,t=0;t<e.length;t++)if(!e[t].evaluate())return!1;return!0},r}(),q0=function(){function r(){}return r.prototype.evaluate=function(){for(var e=this.children,t=0;t<e.length;t++)if(e[t].evaluate())return!0;return!1},r}(),j0=function(){function r(){}return r.prototype.evaluate=function(){return!this.child.evaluate()},r}(),K0=function(){function r(){}return r.prototype.evaluate=function(){for(var e=!!this.valueParser,t=this.getValue,a=t(this.valueGetterParam),n=e?this.valueParser(a):null,i=0;i<this.subCondList.length;i++)if(!this.subCondList[i].evaluate(e?n:a))return!1;return!0},r}();function _i(r,e){if(r===!0||r===!1){var t=new Z0;return t.value=r,t}var a="";return vv(r)||ot(a),r.and?Rs("and",r,e):r.or?Rs("or",r,e):r.not?J0(r,e):Q0(r,e)}function Rs(r,e,t){var a=e[r],n="";Y(a)||ot(n),a.length||ot(n);var i=r==="and"?new X0:new q0;return i.children=O(a,function(o){return _i(o,t)}),i.children.length||ot(n),i}function J0(r,e){var t=r.not,a="";vv(t)||ot(a);var n=new j0;return n.child=_i(t,e),n.child||ot(a),n}function Q0(r,e){for(var t="",a=e.prepareGetValue(r),n=[],i=Tt(r),o=r.parser,s=o?Bl(o):null,l=0;l<i.length;l++){var u=i[l];if(!(u==="parser"||e.valueGetterAttrMap.get(u))){var v=mt(Es,u)?Es[u]:u,c=r[u],f=s?s(c):c,h=$c(v,f)||v==="reg"&&new Y0(f);h||ot(t),n.push(h)}}n.length||ot(t);var p=new K0;return p.valueGetterParam=a,p.valueParser=s,p.getValue=e.getValue,p.subCondList=n,p}function vv(r){return ga(r)&&!Uc(r)}var tS=function(){function r(e,t){this._cond=_i(e,t)}return r.prototype.evaluate=function(){return this._cond.evaluate()},r}();function eS(r,e){return new tS(r,e)}var rS={type:"echarts:filter",transform:function(r){for(var e=r.upstream,t,a=eS(r.config,{valueGetterAttrMap:K({dimension:!0}),prepareGetValue:function(s){var l="",u=s.dimension;mt(s,"dimension")||ot(l);var v=e.getDimensionInfo(u);return v||ot(l),{dimIdx:v.index}},getValue:function(s){return e.retrieveValueFromItem(t,s.dimIdx)}}),n=[],i=0,o=e.count();i<o;i++)t=e.getRawDataItem(i),a.evaluate()&&n.push(t);return{data:n}}},aS={type:"echarts:sort",transform:function(r){var e=r.upstream,t=r.config,a="",n=Ot(t);n.length||ot(a);var i=[];P(n,function(v){var c=v.dimension,f=v.order,h=v.parser,p=v.incomparable;if(c==null&&ot(a),f!=="asc"&&f!=="desc"&&ot(a),p&&p!=="min"&&p!=="max"){var d="";ot(d)}if(f!=="asc"&&f!=="desc"){var g="";ot(g)}var S=e.getDimensionInfo(c);S||ot(a);var m=h?Bl(h):null;h&&!m&&ot(a),i.push({dimIdx:S.index,parser:m,comparator:new Zc(f,p)})});var o=e.sourceFormat;o!==Rl&&o!==Xc&&ot(a);for(var s=[],l=0,u=e.count();l<u;l++)s.push(e.getRawDataItem(l));return s.sort(function(v,c){for(var f=0;f<i.length;f++){var h=i[f],p=e.retrieveValueFromItem(v,h.dimIdx),d=e.retrieveValueFromItem(c,h.dimIdx);h.parser&&(p=h.parser(p),d=h.parser(d));var g=h.comparator.evaluate(p,d);if(g!==0)return g}return 0}),{data:s}}};function cv(r){r.registerTransform(rS),r.registerTransform(aS)}var on=Math.sin,sn=Math.cos,fv=Math.PI,Ae=Math.PI*2,nS=180/fv,hv=function(){function r(){}return r.prototype.reset=function(e){this._start=!0,this._d=[],this._str="",this._p=Math.pow(10,e||4)},r.prototype.moveTo=function(e,t){this._add("M",e,t)},r.prototype.lineTo=function(e,t){this._add("L",e,t)},r.prototype.bezierCurveTo=function(e,t,a,n,i,o){this._add("C",e,t,a,n,i,o)},r.prototype.quadraticCurveTo=function(e,t,a,n){this._add("Q",e,t,a,n)},r.prototype.arc=function(e,t,a,n,i,o){this.ellipse(e,t,a,a,0,n,i,o)},r.prototype.ellipse=function(e,t,a,n,i,o,s,l){var u=s-o,v=!l,c=Math.abs(u),f=ue(c-Ae)||(v?u>=Ae:-u>=Ae),h=u>0?u%Ae:u%Ae+Ae,p=!1;f?p=!0:ue(c)?p=!1:p=h>=fv==!!v;var d=e+a*sn(o),g=t+n*on(o);this._start&&this._add("M",d,g);var S=Math.round(i*nS);if(f){var m=1/this._p,y=(v?1:-1)*(Ae-m);this._add("A",a,n,S,1,+v,e+a*sn(o+y),t+n*on(o+y)),m>.01&&this._add("A",a,n,S,0,+v,d,g)}else{var w=e+a*sn(s),x=t+n*on(s);this._add("A",a,n,S,+p,+v,w,x)}},r.prototype.rect=function(e,t,a,n){this._add("M",e,t),this._add("l",a,0),this._add("l",0,n),this._add("l",-a,0),this._add("Z")},r.prototype.closePath=function(){this._d.length>0&&this._add("Z")},r.prototype._add=function(e,t,a,n,i,o,s,l,u){for(var v=[],c=this._p,f=1;f<arguments.length;f++){var h=arguments[f];if(isNaN(h)){this._invalid=!0;return}v.push(Math.round(h*c)/c)}this._d.push(e+v.join(" ")),this._start=e==="Z"},r.prototype.generateStr=function(){this._str=this._invalid?"":this._d.join(""),this._d=[]},r.prototype.getStr=function(){return this._str},r}(),Ai="none",iS=Math.round;function oS(r){var e=r.fill;return e!=null&&e!==Ai}function sS(r){var e=r.stroke;return e!=null&&e!==Ai}var Rn=["lineCap","miterLimit","lineJoin"],lS=O(Rn,function(r){return"stroke-"+r.toLowerCase()});function uS(r,e,t,a){var n=e.opacity==null?1:e.opacity;if(t instanceof ge){r("opacity",n);return}if(oS(e)){var i=Sr(e.fill);r("fill",i.color);var o=e.fillOpacity!=null?e.fillOpacity*i.opacity*n:i.opacity*n;o<1&&r("fill-opacity",o)}else r("fill",Ai);if(sS(e)){var s=Sr(e.stroke);r("stroke",s.color);var l=e.strokeNoScale?t.getLineScale():1,u=l?(e.lineWidth||0)/l:0,v=e.strokeOpacity!=null?e.strokeOpacity*s.opacity*n:s.opacity*n,c=e.strokeFirst;if(u!==1&&r("stroke-width",u),c&&r("paint-order",c?"stroke":"fill"),v<1&&r("stroke-opacity",v),e.lineDash){var f=qc(t),h=f[0],p=f[1];h&&(p=iS(p||0),r("stroke-dasharray",h.join(",")),(p||a)&&r("stroke-dashoffset",p))}for(var d=0;d<Rn.length;d++){var g=Rn[d];if(e[g]!==Zi[g]){var S=e[g]||Zi[g];S&&r(lS[d],S)}}}}var pv="http://www.w3.org/2000/svg",dv="http://www.w3.org/1999/xlink",vS="http://www.w3.org/2000/xmlns/",cS="http://www.w3.org/XML/1998/namespace",Vs="ecmeta_";function gv(r){return document.createElementNS(pv,r)}function ut(r,e,t,a,n){return{tag:r,attrs:t||{},children:a,text:n,key:e}}function fS(r,e){var t=[];if(e)for(var a in e){var n=e[a],i=a;n!==!1&&(n!==!0&&n!=null&&(i+='="'+n+'"'),t.push(i))}return"<"+r+" "+t.join(" ")+">"}function hS(r){return"</"+r+">"}function Ti(r,e){e=e||{};var t=e.newline?`
`:"";function a(n){var i=n.children,o=n.tag,s=n.attrs,l=n.text;return fS(o,s)+(o!=="style"?jc(l):l||"")+(i?""+t+O(i,function(u){return a(u)}).join(t)+t:"")+hS(o)}return a(r)}function pS(r,e,t){t=t||{};var a=t.newline?`
`:"",n=" {"+a,i=a+"}",o=O(Tt(r),function(l){return l+n+O(Tt(r[l]),function(u){return u+":"+r[l][u]+";"}).join(a)+i}).join(a),s=O(Tt(e),function(l){return"@keyframes "+l+n+O(Tt(e[l]),function(u){return u+n+O(Tt(e[l][u]),function(v){var c=e[l][u][v];return v==="d"&&(c='path("'+c+'")'),v+":"+c+";"}).join(a)+i}).join(a)+i}).join(a);return!o&&!s?"":["<![CDATA[",o,s,"]]>"].join(a)}function Vn(r){return{zrId:r,shadowCache:{},patternCache:{},gradientCache:{},clipPathCache:{},defs:{},cssNodes:{},cssAnims:{},cssStyleCache:{},cssAnimIdx:0,shadowIdx:0,gradientIdx:0,patternIdx:0,clipPathIdx:0}}function Ns(r,e,t,a){return ut("svg","root",{width:r,height:e,xmlns:pv,"xmlns:xlink":dv,version:"1.1",baseProfile:"full",viewBox:a?"0 0 "+r+" "+e:!1},t)}var dS=0;function yv(){return dS++}var Gs={cubicIn:"0.32,0,0.67,0",cubicOut:"0.33,1,0.68,1",cubicInOut:"0.65,0,0.35,1",quadraticIn:"0.11,0,0.5,0",quadraticOut:"0.5,1,0.89,1",quadraticInOut:"0.45,0,0.55,1",quarticIn:"0.5,0,0.75,0",quarticOut:"0.25,1,0.5,1",quarticInOut:"0.76,0,0.24,1",quinticIn:"0.64,0,0.78,0",quinticOut:"0.22,1,0.36,1",quinticInOut:"0.83,0,0.17,1",sinusoidalIn:"0.12,0,0.39,0",sinusoidalOut:"0.61,1,0.88,1",sinusoidalInOut:"0.37,0,0.63,1",exponentialIn:"0.7,0,0.84,0",exponentialOut:"0.16,1,0.3,1",exponentialInOut:"0.87,0,0.13,1",circularIn:"0.55,0,1,0.45",circularOut:"0,0.55,0.45,1",circularInOut:"0.85,0,0.15,1"},Le="transform-origin";function gS(r,e,t){var a=H({},r.shape);H(a,e),r.buildPath(t,a);var n=new hv;return n.reset(Hl(r)),t.rebuildPath(n,1),n.generateStr(),n.getStr()}function yS(r,e){var t=e.originX,a=e.originY;(t||a)&&(r[Le]=t+"px "+a+"px")}var mS={fill:"fill",opacity:"opacity",lineWidth:"stroke-width",lineDashOffset:"stroke-dashoffset"};function mv(r,e){var t=e.zrId+"-ani-"+e.cssAnimIdx++;return e.cssAnims[t]=r,t}function SS(r,e,t){var a=r.shape.paths,n={},i,o;if(P(a,function(l){var u=Vn(t.zrId);u.animation=!0,Ia(l,{},u,!0);var v=u.cssAnims,c=u.cssNodes,f=Tt(v),h=f.length;if(h){o=f[h-1];var p=v[o];for(var d in p){var g=p[d];n[d]=n[d]||{d:""},n[d].d+=g.d||""}for(var S in c){var m=c[S].animation;m.indexOf(o)>=0&&(i=m)}}}),!!i){e.d=!1;var s=mv(n,t);return i.replace(o,s)}}function ks(r){return et(r)?Gs[r]?"cubic-bezier("+Gs[r]+")":Kc(r)?r:"":""}function Ia(r,e,t,a){var n=r.animators,i=n.length,o=[];if(r instanceof xl){var s=SS(r,e,t);if(s)o.push(s);else if(!i)return}else if(!i)return;for(var l={},u=0;u<i;u++){var v=n[u],c=[v.getMaxTime()/1e3+"s"],f=ks(v.getClip().easing),h=v.getDelay();f?c.push(f):c.push("linear"),h&&c.push(h/1e3+"s"),v.getLoop()&&c.push("infinite");var p=c.join(" ");l[p]=l[p]||[p,[]],l[p][1].push(v)}function d(m){var y=m[1],w=y.length,x={},b={},_={},T="animation-timing-function";function I(U,F,Q){for(var q=U.getTracks(),at=U.getMaxTime(),vt=0;vt<q.length;vt++){var bt=q[vt];if(bt.needsAnimate()){var nt=bt.keyframes,yt=bt.propName;if(Q&&(yt=Q(yt)),yt)for(var _t=0;_t<nt.length;_t++){var Nt=nt[_t],be=Math.round(Nt.time/at*100)+"%",tr=ks(Nt.easing),Mi=Nt.rawValue;(et(Mi)||Xt(Mi))&&(F[be]=F[be]||{},F[be][yt]=Nt.rawValue,tr&&(F[be][T]=tr))}}}}for(var A=0;A<w;A++){var D=y[A],E=D.targetName;E?E==="shape"&&I(D,b):!a&&I(D,x)}for(var M in x){var C={};Jc(C,r),H(C,x[M]);var L=Fl(C),R=x[M][T];_[M]=L?{transform:L}:{},yS(_[M],C),R&&(_[M][T]=R)}var V,N=!0;for(var M in b){_[M]=_[M]||{};var k=!V,R=b[M][T];k&&(V=new Wl);var B=V.len();V.reset(),_[M].d=gS(r,b[M],V);var W=V.len();if(!k&&B!==W){N=!1;break}R&&(_[M][T]=R)}if(!N)for(var M in _)delete _[M].d;if(!a)for(var A=0;A<w;A++){var D=y[A],E=D.targetName;E==="style"&&I(D,_,function(q){return mS[q]})}for(var X=Tt(_),tt=!0,J,A=1;A<X.length;A++){var rt=X[A-1],pt=X[A];if(_[rt][Le]!==_[pt][Le]){tt=!1;break}J=_[rt][Le]}if(tt&&J){for(var M in _)_[M][Le]&&delete _[M][Le];e[Le]=J}if(Pt(X,function(U){return Tt(_[U]).length>0}).length){var Ht=mv(_,t);return Ht+" "+m[0]+" both"}}for(var g in l){var s=d(l[g]);s&&o.push(s)}if(o.length){var S=t.zrId+"-cls-"+yv();t.cssNodes["."+S]={animation:o.join(",")},e.class=S}}function xS(r,e,t){if(!r.ignore)if(r.isSilent()){var a={"pointer-events":"none"};zs(a,e,t)}else{var n=r.states.emphasis&&r.states.emphasis.style?r.states.emphasis.style:{},i=n.fill;if(!i){var o=r.style&&r.style.fill,s=r.states.select&&r.states.select.style&&r.states.select.style.fill,l=r.currentStates.indexOf("select")>=0&&s||o;l&&(i=Qc(l))}var u=n.lineWidth;if(u){var v=!n.strokeNoScale&&r.transform?r.transform[0]:1;u=u/v}var a={cursor:"pointer"};i&&(a.fill=i),n.stroke&&(a.stroke=n.stroke),u&&(a["stroke-width"]=u),zs(a,e,t)}}function zs(r,e,t,a){var n=JSON.stringify(r),i=t.cssStyleCache[n];i||(i=t.zrId+"-cls-"+yv(),t.cssStyleCache[n]=i,t.cssNodes["."+i+":hover"]=r),e.class=e.class?e.class+" "+i:i}var _r=Math.round;function Sv(r){return r&&et(r.src)}function xv(r){return r&&st(r.toDataURL)}function Ii(r,e,t,a){uS(function(n,i){var o=n==="fill"||n==="stroke";o&&Ul(i)?wv(e,r,n,a):o&&Xn(i)?_v(t,r,n,a):r[n]=i,o&&a.ssr&&i==="none"&&(r["pointer-events"]="visible")},e,t,!1),DS(t,r,a)}function Di(r,e){var t=pf(e);t&&(t.each(function(a,n){a!=null&&(r[(Vs+n).toLowerCase()]=a+"")}),e.isSilent()&&(r[Vs+"silent"]="true"))}function Os(r){return ue(r[0]-1)&&ue(r[1])&&ue(r[2])&&ue(r[3]-1)}function bS(r){return ue(r[4])&&ue(r[5])}function Ci(r,e,t){if(e&&!(bS(e)&&Os(e))){var a=1e4;r.transform=Os(e)?"translate("+_r(e[4]*a)/a+" "+_r(e[5]*a)/a+")":hf(e)}}function Bs(r,e,t){for(var a=r.points,n=[],i=0;i<a.length;i++)n.push(_r(a[i][0]*t)/t),n.push(_r(a[i][1]*t)/t);e.points=n.join(" ")}function Fs(r){return!r.smooth}function wS(r){var e=O(r,function(t){return typeof t=="string"?[t,t]:t});return function(t,a,n){for(var i=0;i<e.length;i++){var o=e[i],s=t[o[0]];s!=null&&(a[o[1]]=_r(s*n)/n)}}}var _S={circle:[wS(["cx","cy","r"])],polyline:[Bs,Fs],polygon:[Bs,Fs]};function AS(r){for(var e=r.animators,t=0;t<e.length;t++)if(e[t].targetName==="shape")return!0;return!1}function bv(r,e){var t=r.style,a=r.shape,n=_S[r.type],i={},o=e.animation,s="path",l=r.style.strokePercent,u=e.compress&&Hl(r)||4;if(n&&!e.willUpdate&&!(n[1]&&!n[1](a))&&!(o&&AS(r))&&!(l<1)){s=r.type;var v=Math.pow(10,u);n[0](a,i,v)}else{var c=!r.path||r.shapeChanged();r.path||r.createPathProxy();var f=r.path;c&&(f.beginPath(),r.buildPath(f,r.shape),r.pathUpdated());var h=f.getVersion(),p=r,d=p.__svgPathBuilder;(p.__svgPathVersion!==h||!d||l!==p.__svgPathStrokePercent)&&(d||(d=p.__svgPathBuilder=new hv),d.reset(u),f.rebuildPath(d,l),d.generateStr(),p.__svgPathVersion=h,p.__svgPathStrokePercent=l),i.d=d.getStr()}return Ci(i,r.transform),Ii(i,t,r,e),Di(i,r),e.animation&&Ia(r,i,e),e.emphasis&&xS(r,i,e),ut(s,r.id+"",i)}function TS(r,e){var t=r.style,a=t.image;if(a&&!et(a)&&(Sv(a)?a=a.src:xv(a)&&(a=a.toDataURL())),!!a){var n=t.x||0,i=t.y||0,o=t.width,s=t.height,l={href:a,width:o,height:s};return n&&(l.x=n),i&&(l.y=i),Ci(l,r.transform),Ii(l,t,r,e),Di(l,r),e.animation&&Ia(r,l,e),ut("image",r.id+"",l)}}function IS(r,e){var t=r.style,a=t.text;if(a!=null&&(a+=""),!(!a||isNaN(t.x)||isNaN(t.y))){var n=t.font||tf,i=t.x||0,o=ef(t.y||0,rf(n),t.textBaseline),s=af[t.textAlign]||t.textAlign,l={"dominant-baseline":"central","text-anchor":s};if(nf(t)){var u="",v=t.fontStyle,c=of(t.fontSize);if(!parseFloat(c))return;var f=t.fontFamily||sf,h=t.fontWeight;u+="font-size:"+c+";font-family:"+f+";",v&&v!=="normal"&&(u+="font-style:"+v+";"),h&&h!=="normal"&&(u+="font-weight:"+h+";"),l.style=u}else l.style="font: "+n;return a.match(/\s/)&&(l["xml:space"]="preserve"),i&&(l.x=i),o&&(l.y=o),Ci(l,r.transform),Ii(l,t,r,e),Di(l,r),e.animation&&Ia(r,l,e),ut("text",r.id+"",l,void 0,a)}}function Hs(r,e){if(r instanceof Vt)return bv(r,e);if(r instanceof ge)return TS(r,e);if(r instanceof yl)return IS(r,e)}function DS(r,e,t){var a=r.style;if(df(a)){var n=gf(r),i=t.shadowCache,o=i[n];if(!o){var s=r.getGlobalScale(),l=s[0],u=s[1];if(!l||!u)return;var v=a.shadowOffsetX||0,c=a.shadowOffsetY||0,f=a.shadowBlur,h=Sr(a.shadowColor),p=h.opacity,d=h.color,g=f/2/l,S=f/2/u,m=g+" "+S;o=t.zrId+"-s"+t.shadowIdx++,t.defs[o]=ut("filter",o,{id:o,x:"-100%",y:"-100%",width:"300%",height:"300%"},[ut("feDropShadow","",{dx:v/l,dy:c/u,stdDeviation:m,"flood-color":d,"flood-opacity":p})]),i[n]=o}e.filter=ya(o)}}function wv(r,e,t,a){var n=r[t],i,o={gradientUnits:n.global?"userSpaceOnUse":"objectBoundingBox"};if(lf(n))i="linearGradient",o.x1=n.x,o.y1=n.y,o.x2=n.x2,o.y2=n.y2;else if(uf(n))i="radialGradient",o.cx=ce(n.x,.5),o.cy=ce(n.y,.5),o.r=ce(n.r,.5);else return;for(var s=n.colorStops,l=[],u=0,v=s.length;u<v;++u){var c=vf(s[u].offset)*100+"%",f=s[u].color,h=Sr(f),p=h.color,d=h.opacity,g={offset:c};g["stop-color"]=p,d<1&&(g["stop-opacity"]=d),l.push(ut("stop",u+"",g))}var S=ut(i,"",o,l),m=Ti(S),y=a.gradientCache,w=y[m];w||(w=a.zrId+"-g"+a.gradientIdx++,y[m]=w,o.id=w,a.defs[w]=ut(i,w,o,l)),e[t]=ya(w)}function _v(r,e,t,a){var n=r.style[t],i=r.getBoundingRect(),o={},s=n.repeat,l=s==="no-repeat",u=s==="repeat-x",v=s==="repeat-y",c;if(cf(n)){var f=n.imageWidth,h=n.imageHeight,p=void 0,d=n.image;if(et(d)?p=d:Sv(d)?p=d.src:xv(d)&&(p=d.toDataURL()),typeof Image>"u"){var g="Image width/height must been given explictly in svg-ssr renderer.";Zr(f,g),Zr(h,g)}else if(f==null||h==null){var S=function(A,D){if(A){var E=A.elm,M=f||D.width,C=h||D.height;A.tag==="pattern"&&(u?(C=1,M/=i.width):v&&(M=1,C/=i.height)),A.attrs.width=M,A.attrs.height=C,E&&(E.setAttribute("width",M),E.setAttribute("height",C))}},m=ff(p,null,r,function(A){l||S(b,A),S(c,A)});m&&m.width&&m.height&&(f=f||m.width,h=h||m.height)}c=ut("image","img",{href:p,width:f,height:h}),o.width=f,o.height=h}else n.svgElement&&(c=Zt(n.svgElement),o.width=n.svgWidth,o.height=n.svgHeight);if(c){var y,w;l?y=w=1:u?(w=1,y=o.width/i.width):v?(y=1,w=o.height/i.height):o.patternUnits="userSpaceOnUse",y!=null&&!isNaN(y)&&(o.width=y),w!=null&&!isNaN(w)&&(o.height=w);var x=Fl(n);x&&(o.patternTransform=x);var b=ut("pattern","",o,[c]),_=Ti(b),T=a.patternCache,I=T[_];I||(I=a.zrId+"-p"+a.patternIdx++,T[_]=I,o.id=I,b=a.defs[I]=ut("pattern",I,o,[c])),e[t]=ya(I)}}function CS(r,e,t){var a=t.clipPathCache,n=t.defs,i=a[r.id];if(!i){i=t.zrId+"-c"+t.clipPathIdx++;var o={id:i};a[r.id]=i,n[i]=ut("clipPath",i,o,[bv(r,t)])}e["clip-path"]=ya(i)}function Ws(r){return document.createTextNode(r)}function Ee(r,e,t){r.insertBefore(e,t)}function Us(r,e){r.removeChild(e)}function $s(r,e){r.appendChild(e)}function Av(r){return r.parentNode}function Tv(r){return r.nextSibling}function ln(r,e){r.textContent=e}var Ys=58,LS=120,PS=ut("","");function Nn(r){return r===void 0}function Ut(r){return r!==void 0}function MS(r,e,t){for(var a={},n=e;n<=t;++n){var i=r[n].key;i!==void 0&&(a[i]=n)}return a}function hr(r,e){var t=r.key===e.key,a=r.tag===e.tag;return a&&t}function Ar(r){var e,t=r.children,a=r.tag;if(Ut(a)){var n=r.elm=gv(a);if(Li(PS,r),Y(t))for(e=0;e<t.length;++e){var i=t[e];i!=null&&$s(n,Ar(i))}else Ut(r.text)&&!ga(r.text)&&$s(n,Ws(r.text))}else r.elm=Ws(r.text);return r.elm}function Iv(r,e,t,a,n){for(;a<=n;++a){var i=t[a];i!=null&&Ee(r,Ar(i),e)}}function la(r,e,t,a){for(;t<=a;++t){var n=e[t];if(n!=null)if(Ut(n.tag)){var i=Av(n.elm);Us(i,n.elm)}else Us(r,n.elm)}}function Li(r,e){var t,a=e.elm,n=r&&r.attrs||{},i=e.attrs||{};if(n!==i){for(t in i){var o=i[t],s=n[t];s!==o&&(o===!0?a.setAttribute(t,""):o===!1?a.removeAttribute(t):t==="style"?a.style.cssText=o:t.charCodeAt(0)!==LS?a.setAttribute(t,o):t==="xmlns:xlink"||t==="xmlns"?a.setAttributeNS(vS,t,o):t.charCodeAt(3)===Ys?a.setAttributeNS(cS,t,o):t.charCodeAt(5)===Ys?a.setAttributeNS(dv,t,o):a.setAttribute(t,o))}for(t in n)t in i||a.removeAttribute(t)}}function ES(r,e,t){for(var a=0,n=0,i=e.length-1,o=e[0],s=e[i],l=t.length-1,u=t[0],v=t[l],c,f,h,p;a<=i&&n<=l;)o==null?o=e[++a]:s==null?s=e[--i]:u==null?u=t[++n]:v==null?v=t[--l]:hr(o,u)?(Ye(o,u),o=e[++a],u=t[++n]):hr(s,v)?(Ye(s,v),s=e[--i],v=t[--l]):hr(o,v)?(Ye(o,v),Ee(r,o.elm,Tv(s.elm)),o=e[++a],v=t[--l]):hr(s,u)?(Ye(s,u),Ee(r,s.elm,o.elm),s=e[--i],u=t[++n]):(Nn(c)&&(c=MS(e,a,i)),f=c[u.key],Nn(f)?Ee(r,Ar(u),o.elm):(h=e[f],h.tag!==u.tag?Ee(r,Ar(u),o.elm):(Ye(h,u),e[f]=void 0,Ee(r,h.elm,o.elm))),u=t[++n]);(a<=i||n<=l)&&(a>i?(p=t[l+1]==null?null:t[l+1].elm,Iv(r,p,t,n,l)):la(r,e,a,i))}function Ye(r,e){var t=e.elm=r.elm,a=r.children,n=e.children;r!==e&&(Li(r,e),Nn(e.text)?Ut(a)&&Ut(n)?a!==n&&ES(t,a,n):Ut(n)?(Ut(r.text)&&ln(t,""),Iv(t,null,n,0,n.length-1)):Ut(a)?la(t,a,0,a.length-1):Ut(r.text)&&ln(t,""):r.text!==e.text&&(Ut(a)&&la(t,a,0,a.length-1),ln(t,e.text)))}function RS(r,e){if(hr(r,e))Ye(r,e);else{var t=r.elm,a=Av(t);Ar(e),a!==null&&(Ee(a,e.elm,Tv(t)),la(a,[r],0,0))}return e}var VS=0,NS=function(){function r(e,t,a){if(this.type="svg",this.refreshHover=Zs(),this.configLayer=Zs(),this.storage=t,this._opts=a=H({},a),this.root=e,this._id="zr"+VS++,this._oldVNode=Ns(a.width,a.height),e&&!a.ssr){var n=this._viewport=document.createElement("div");n.style.cssText="position:relative;overflow:hidden";var i=this._svgDom=this._oldVNode.elm=gv("svg");Li(null,this._oldVNode),n.appendChild(i),e.appendChild(n)}this.resize(a.width,a.height)}return r.prototype.getType=function(){return this.type},r.prototype.getViewportRoot=function(){return this._viewport},r.prototype.getViewportRootOffset=function(){var e=this.getViewportRoot();if(e)return{offsetLeft:e.offsetLeft||0,offsetTop:e.offsetTop||0}},r.prototype.getSvgDom=function(){return this._svgDom},r.prototype.refresh=function(){if(this.root){var e=this.renderToVNode({willUpdate:!0});e.attrs.style="position:absolute;left:0;top:0;user-select:none",RS(this._oldVNode,e),this._oldVNode=e}},r.prototype.renderOneToVNode=function(e){return Hs(e,Vn(this._id))},r.prototype.renderToVNode=function(e){e=e||{};var t=this.storage.getDisplayList(!0),a=this._width,n=this._height,i=Vn(this._id);i.animation=e.animation,i.willUpdate=e.willUpdate,i.compress=e.compress,i.emphasis=e.emphasis,i.ssr=this._opts.ssr;var o=[],s=this._bgVNode=GS(a,n,this._backgroundColor,i);s&&o.push(s);var l=e.compress?null:this._mainVNode=ut("g","main",{},[]);this._paintList(t,i,l?l.children:o),l&&o.push(l);var u=O(Tt(i.defs),function(f){return i.defs[f]});if(u.length&&o.push(ut("defs","defs",{},u)),e.animation){var v=pS(i.cssNodes,i.cssAnims,{newline:!0});if(v){var c=ut("style","stl",{},[],v);o.push(c)}}return Ns(a,n,o,e.useViewBox)},r.prototype.renderToString=function(e){return e=e||{},Ti(this.renderToVNode({animation:ce(e.cssAnimation,!0),emphasis:ce(e.cssEmphasis,!0),willUpdate:!1,compress:!0,useViewBox:ce(e.useViewBox,!0)}),{newline:!0})},r.prototype.setBackgroundColor=function(e){this._backgroundColor=e},r.prototype.getSvgRoot=function(){return this._mainVNode&&this._mainVNode.elm},r.prototype._paintList=function(e,t,a){for(var n=e.length,i=[],o=0,s,l,u=0,v=0;v<n;v++){var c=e[v];if(!c.invisible){var f=c.__clipPaths,h=f&&f.length||0,p=l&&l.length||0,d=void 0;for(d=Math.max(h-1,p-1);d>=0&&!(f&&l&&f[d]===l[d]);d--);for(var g=p-1;g>d;g--)o--,s=i[o-1];for(var S=d+1;S<h;S++){var m={};CS(f[S],m,t);var y=ut("g","clip-g-"+u++,m,[]);(s?s.children:a).push(y),i[o++]=y,s=y}l=f;var w=Hs(c,t);w&&(s?s.children:a).push(w)}}},r.prototype.resize=function(e,t){var a=this._opts,n=this.root,i=this._viewport;if(e!=null&&(a.width=e),t!=null&&(a.height=t),n&&i&&(i.style.display="none",e=Xi(n,0,a),t=Xi(n,1,a),i.style.display=""),this._width!==e||this._height!==t){if(this._width=e,this._height=t,i){var o=i.style;o.width=e+"px",o.height=t+"px"}if(Xn(this._backgroundColor))this.refresh();else{var s=this._svgDom;s&&(s.setAttribute("width",e),s.setAttribute("height",t));var l=this._bgVNode&&this._bgVNode.elm;l&&(l.setAttribute("width",e),l.setAttribute("height",t))}}},r.prototype.getWidth=function(){return this._width},r.prototype.getHeight=function(){return this._height},r.prototype.dispose=function(){this.root&&(this.root.innerHTML=""),this._svgDom=this._viewport=this.storage=this._oldVNode=this._bgVNode=this._mainVNode=null},r.prototype.clear=function(){this._svgDom&&(this._svgDom.innerHTML=null),this._oldVNode=null},r.prototype.toDataURL=function(e){var t=this.renderToString(),a="data:image/svg+xml;";return e?(t=yf(t),t&&a+"base64,"+t):a+"charset=UTF-8,"+encodeURIComponent(t)},r}();function Zs(r){return function(){}}function GS(r,e,t,a){var n;if(t&&t!=="none")if(n=ut("rect","bg",{width:r,height:e,x:"0",y:"0"}),Ul(t))wv({fill:t},n.attrs,"fill",a);else if(Xn(t))_v({style:{fill:t},dirty:je,getBoundingRect:function(){return{width:r,height:e}}},n.attrs,"fill",a);else{var i=Sr(t),o=i.color,s=i.opacity;n.attrs.fill=o,s<1&&(n.attrs["fill-opacity"]=s)}return n}function kS(r){r.registerPainter("svg",NS)}var Wt=Wl.CMD;function Ze(r,e){return Math.abs(r-e)<1e-5}function Gn(r){var e=r.data,t=r.len(),a=[],n,i=0,o=0,s=0,l=0;function u(M,C){n&&n.length>2&&a.push(n),n=[M,C]}function v(M,C,L,R){Ze(M,L)&&Ze(C,R)||n.push(M,C,L,R,L,R)}function c(M,C,L,R,V,N){var k=Math.abs(C-M),B=Math.tan(k/4)*4/3,W=C<M?-1:1,X=Math.cos(M),tt=Math.sin(M),J=Math.cos(C),rt=Math.sin(C),pt=X*V+L,Ht=tt*N+R,U=J*V+L,F=rt*N+R,Q=V*B*W,q=N*B*W;n.push(pt-Q*tt,Ht+q*X,U+Q*rt,F-q*J,U,F)}for(var f,h,p,d,g=0;g<t;){var S=e[g++],m=g===1;switch(m&&(i=e[g],o=e[g+1],s=i,l=o,(S===Wt.L||S===Wt.C||S===Wt.Q)&&(n=[s,l])),S){case Wt.M:i=s=e[g++],o=l=e[g++],u(s,l);break;case Wt.L:f=e[g++],h=e[g++],v(i,o,f,h),i=f,o=h;break;case Wt.C:n.push(e[g++],e[g++],e[g++],e[g++],i=e[g++],o=e[g++]);break;case Wt.Q:f=e[g++],h=e[g++],p=e[g++],d=e[g++],n.push(i+2/3*(f-i),o+2/3*(h-o),p+2/3*(f-p),d+2/3*(h-d),p,d),i=p,o=d;break;case Wt.A:var y=e[g++],w=e[g++],x=e[g++],b=e[g++],_=e[g++],T=e[g++]+_;g+=1;var I=!e[g++];f=Math.cos(_)*x+y,h=Math.sin(_)*b+w,m?(s=f,l=h,u(s,l)):v(i,o,f,h),i=Math.cos(T)*x+y,o=Math.sin(T)*b+w;for(var A=(I?-1:1)*Math.PI/2,D=_;I?D>T:D<T;D+=A){var E=I?Math.max(D+A,T):Math.min(D+A,T);c(D,E,y,w,x,b)}break;case Wt.R:s=i=e[g++],l=o=e[g++],f=s+e[g++],h=l+e[g++],u(f,l),v(f,l,f,h),v(f,h,s,h),v(s,h,s,l),v(s,l,f,l);break;case Wt.Z:n&&v(i,o,s,l),i=s,o=l;break}}return n&&n.length>2&&a.push(n),a}function kn(r,e,t,a,n,i,o,s,l,u){if(Ze(r,t)&&Ze(e,a)&&Ze(n,o)&&Ze(i,s)){l.push(o,s);return}var v=2/u,c=v*v,f=o-r,h=s-e,p=Math.sqrt(f*f+h*h);f/=p,h/=p;var d=t-r,g=a-e,S=n-o,m=i-s,y=d*d+g*g,w=S*S+m*m;if(y<c&&w<c){l.push(o,s);return}var x=f*d+h*g,b=-f*S-h*m,_=y-x*x,T=w-b*b;if(_<c&&x>=0&&T<c&&b>=0){l.push(o,s);return}var I=[],A=[];Jr(r,t,n,o,.5,I),Jr(e,a,i,s,.5,A),kn(I[0],A[0],I[1],A[1],I[2],A[2],I[3],A[3],l,u),kn(I[4],A[4],I[5],A[5],I[6],A[6],I[7],A[7],l,u)}function zS(r,e){var t=Gn(r),a=[];e=e||1;for(var n=0;n<t.length;n++){var i=t[n],o=[],s=i[0],l=i[1];o.push(s,l);for(var u=2;u<i.length;){var v=i[u++],c=i[u++],f=i[u++],h=i[u++],p=i[u++],d=i[u++];kn(s,l,v,c,f,h,p,d,o,e),s=p,l=d}a.push(o)}return a}function Dv(r,e,t){var a=r[e],n=r[1-e],i=Math.abs(a/n),o=Math.ceil(Math.sqrt(i*t)),s=Math.floor(t/o);s===0&&(s=1,o=t);for(var l=[],u=0;u<o;u++)l.push(s);var v=o*s,c=t-v;if(c>0)for(var u=0;u<c;u++)l[u%o]+=1;return l}function Xs(r,e,t){for(var a=r.r0,n=r.r,i=r.startAngle,o=r.endAngle,s=Math.abs(o-i),l=s*n,u=n-a,v=l>Math.abs(u),c=Dv([l,u],v?0:1,e),f=(v?s:u)/c.length,h=0;h<c.length;h++)for(var p=(v?u:s)/c[h],d=0;d<c[h];d++){var g={};v?(g.startAngle=i+f*h,g.endAngle=i+f*(h+1),g.r0=a+p*d,g.r=a+p*(d+1)):(g.startAngle=i+p*d,g.endAngle=i+p*(d+1),g.r0=a+f*h,g.r=a+f*(h+1)),g.clockwise=r.clockwise,g.cx=r.cx,g.cy=r.cy,t.push(g)}}function OS(r,e,t){for(var a=r.width,n=r.height,i=a>n,o=Dv([a,n],i?0:1,e),s=i?"width":"height",l=i?"height":"width",u=i?"x":"y",v=i?"y":"x",c=r[s]/o.length,f=0;f<o.length;f++)for(var h=r[l]/o[f],p=0;p<o[f];p++){var d={};d[u]=f*c,d[v]=p*h,d[s]=c,d[l]=h,d.x+=r.x,d.y+=r.y,t.push(d)}}function qs(r,e,t,a){return r*a-t*e}function BS(r,e,t,a,n,i,o,s){var l=t-r,u=a-e,v=o-n,c=s-i,f=qs(v,c,l,u);if(Math.abs(f)<1e-6)return null;var h=r-n,p=e-i,d=qs(h,p,v,c)/f;return d<0||d>1?null:new le(d*l+r,d*u+e)}function FS(r,e,t){var a=new le;le.sub(a,t,e),a.normalize();var n=new le;le.sub(n,r,e);var i=n.dot(a);return i}function We(r,e){var t=r[r.length-1];t&&t[0]===e[0]&&t[1]===e[1]||r.push(e)}function HS(r,e,t){for(var a=r.length,n=[],i=0;i<a;i++){var o=r[i],s=r[(i+1)%a],l=BS(o[0],o[1],s[0],s[1],e.x,e.y,t.x,t.y);l&&n.push({projPt:FS(l,e,t),pt:l,idx:i})}if(n.length<2)return[{points:r},{points:r}];n.sort(function(g,S){return g.projPt-S.projPt});var u=n[0],v=n[n.length-1];if(v.idx<u.idx){var c=u;u=v,v=c}for(var f=[u.pt.x,u.pt.y],h=[v.pt.x,v.pt.y],p=[f],d=[h],i=u.idx+1;i<=v.idx;i++)We(p,r[i].slice());We(p,h),We(p,f);for(var i=v.idx+1;i<=u.idx+a;i++)We(d,r[i%a].slice());return We(d,f),We(d,h),[{points:p},{points:d}]}function js(r){var e=r.points,t=[],a=[];pa(e,t,a);var n=new dt(t[0],t[1],a[0]-t[0],a[1]-t[1]),i=n.width,o=n.height,s=n.x,l=n.y,u=new le,v=new le;return i>o?(u.x=v.x=s+i/2,u.y=l,v.y=l+o):(u.y=v.y=l+o/2,u.x=s,v.x=s+i),HS(e,u,v)}function ua(r,e,t,a){if(t===1)a.push(e);else{var n=Math.floor(t/2),i=r(e);ua(r,i[0],n,a),ua(r,i[1],t-n,a)}return a}function WS(r,e){for(var t=[],a=0;a<e;a++)t.push(qn(r));return t}function US(r,e){e.setStyle(r.style),e.z=r.z,e.z2=r.z2,e.zlevel=r.zlevel}function $S(r){for(var e=[],t=0;t<r.length;)e.push([r[t++],r[t++]]);return e}function YS(r,e){var t=[],a=r.shape,n;switch(r.type){case"rect":OS(a,e,t),n=It;break;case"sector":Xs(a,e,t),n=Ge;break;case"circle":Xs({r0:0,r:a.r,startAngle:0,endAngle:Math.PI*2,cx:a.cx,cy:a.cy},e,t),n=Ge;break;default:var i=r.getComputedTransform(),o=i?Math.sqrt(Math.max(i[0]*i[0]+i[1]*i[1],i[2]*i[2]+i[3]*i[3])):1,s=O(zS(r.getUpdatedPathProxy(),o),function(S){return $S(S)}),l=s.length;if(l===0)ua(js,{points:s[0]},e,t);else if(l===e)for(var u=0;u<l;u++)t.push({points:s[u]});else{var v=0,c=O(s,function(S){var m=[],y=[];pa(S,m,y);var w=(y[1]-m[1])*(y[0]-m[0]);return v+=w,{poly:S,area:w}});c.sort(function(S,m){return m.area-S.area});for(var f=e,u=0;u<l;u++){var h=c[u];if(f<=0)break;var p=u===l-1?f:Math.ceil(h.area/v*e);p<0||(ua(js,{points:h.poly},p,t),f-=p)}}n=Jt;break}if(!n)return WS(r,e);for(var d=[],u=0;u<t.length;u++){var g=new n;g.setShape(t[u]),US(r,g),d.push(g)}return d}function ZS(r,e){var t=r.length,a=e.length;if(t===a)return[r,e];for(var n=[],i=[],o=t<a?r:e,s=Math.min(t,a),l=Math.abs(a-t)/6,u=(s-2)/6,v=Math.ceil(l/u)+1,c=[o[0],o[1]],f=l,h=2;h<s;){var p=o[h-2],d=o[h-1],g=o[h++],S=o[h++],m=o[h++],y=o[h++],w=o[h++],x=o[h++];if(f<=0){c.push(g,S,m,y,w,x);continue}for(var b=Math.min(f,v-1)+1,_=1;_<=b;_++){var T=_/b;Jr(p,g,m,w,T,n),Jr(d,S,y,x,T,i),p=n[3],d=i[3],c.push(n[1],i[1],n[2],i[2],p,d),g=n[5],S=i[5],m=n[6],y=i[6]}f-=b-1}return o===r?[c,e]:[r,c]}function Ks(r,e){for(var t=r.length,a=r[t-2],n=r[t-1],i=[],o=0;o<e.length;)i[o++]=a,i[o++]=n;return i}function XS(r,e){for(var t,a,n,i=[],o=[],s=0;s<Math.max(r.length,e.length);s++){var l=r[s],u=e[s],v=void 0,c=void 0;l?u?(t=ZS(l,u),v=t[0],c=t[1],a=v,n=c):(c=Ks(n||l,l),v=l):(v=Ks(a||u,u),c=u),i.push(v),o.push(c)}return[i,o]}function Js(r){for(var e=0,t=0,a=0,n=r.length,i=0,o=n-2;i<n;o=i,i+=2){var s=r[o],l=r[o+1],u=r[i],v=r[i+1],c=s*v-u*l;e+=c,t+=(s+u)*c,a+=(l+v)*c}return e===0?[r[0]||0,r[1]||0]:[t/e/3,a/e/3,e]}function qS(r,e,t,a){for(var n=(r.length-2)/6,i=1/0,o=0,s=r.length,l=s-2,u=0;u<n;u++){for(var v=u*6,c=0,f=0;f<s;f+=2){var h=f===0?v:(v+f-2)%l+2,p=r[h]-t[0],d=r[h+1]-t[1],g=e[f]-a[0],S=e[f+1]-a[1],m=g-p,y=S-d;c+=m*m+y*y}c<i&&(i=c,o=u)}return o}function jS(r){for(var e=[],t=r.length,a=0;a<t;a+=2)e[a]=r[t-a-2],e[a+1]=r[t-a-1];return e}function KS(r,e,t,a){for(var n=[],i,o=0;o<r.length;o++){var s=r[o],l=e[o],u=Js(s),v=Js(l);i==null&&(i=u[2]<0!=v[2]<0);var c=[],f=[],h=0,p=1/0,d=[],g=s.length;i&&(s=jS(s));for(var S=qS(s,l,u,v)*6,m=g-2,y=0;y<m;y+=2){var w=(S+y)%m+2;c[y+2]=s[w]-u[0],c[y+3]=s[w+1]-u[1]}c[0]=s[S]-u[0],c[1]=s[S+1]-u[1];for(var x=a/t,b=-a/2;b<=a/2;b+=x){for(var _=Math.sin(b),T=Math.cos(b),I=0,y=0;y<s.length;y+=2){var A=c[y],D=c[y+1],E=l[y]-v[0],M=l[y+1]-v[1],C=E*T-M*_,L=E*_+M*T;d[y]=C,d[y+1]=L;var R=C-A,V=L-D;I+=R*R+V*V}if(I<p){p=I,h=b;for(var N=0;N<d.length;N++)f[N]=d[N]}}n.push({from:c,to:f,fromCp:u,toCp:v,rotation:-h})}return n}function va(r){return r.__isCombineMorphing}var Cv="__mOriginal_";function ca(r,e,t){var a=Cv+e,n=r[a]||r[e];r[a]||(r[a]=r[e]);var i=t.replace,o=t.after,s=t.before;r[e]=function(){var l=arguments,u;return s&&s.apply(this,l),i?u=i.apply(this,l):u=n.apply(this,l),o&&o.apply(this,l),u}}function dr(r,e){var t=Cv+e;r[t]&&(r[e]=r[t],r[t]=null)}function Qs(r,e){for(var t=0;t<r.length;t++)for(var a=r[t],n=0;n<a.length;){var i=a[n],o=a[n+1];a[n++]=e[0]*i+e[2]*o+e[4],a[n++]=e[1]*i+e[3]*o+e[5]}}function Lv(r,e){var t=r.getUpdatedPathProxy(),a=e.getUpdatedPathProxy(),n=XS(Gn(t),Gn(a)),i=n[0],o=n[1],s=r.getComputedTransform(),l=e.getComputedTransform();function u(){this.transform=null}s&&Qs(i,s),l&&Qs(o,l),ca(e,"updateTransform",{replace:u}),e.transform=null;var v=KS(i,o,10,Math.PI),c=[];ca(e,"buildPath",{replace:function(f){for(var h=e.__morphT,p=1-h,d=[],g=0;g<v.length;g++){var S=v[g],m=S.from,y=S.to,w=S.rotation*h,x=S.fromCp,b=S.toCp,_=Math.sin(w),T=Math.cos(w);mf(d,x,b,h);for(var I=0;I<m.length;I+=2){var A=m[I],D=m[I+1],E=y[I],M=y[I+1],C=A*p+E*h,L=D*p+M*h;c[I]=C*T-L*_+d[0],c[I+1]=C*_+L*T+d[1]}var R=c[0],V=c[1];f.moveTo(R,V);for(var I=2;I<m.length;){var E=c[I++],M=c[I++],N=c[I++],k=c[I++],B=c[I++],W=c[I++];R===E&&V===M&&N===B&&k===W?f.lineTo(B,W):f.bezierCurveTo(E,M,N,k,B,W),R=B,V=W}}}})}function Pi(r,e,t){if(!r||!e)return e;var a=t.done,n=t.during;Lv(r,e),e.__morphT=0;function i(){dr(e,"buildPath"),dr(e,"updateTransform"),e.__morphT=-1,e.createPathProxy(),e.dirtyShape()}return e.animateTo({__morphT:1},j({during:function(o){e.dirtyShape(),n&&n(o)},done:function(){i(),a&&a()}},t)),e}function JS(r,e,t,a,n,i){var o=16;r=n===t?0:Math.round(32767*(r-t)/(n-t)),e=i===a?0:Math.round(32767*(e-a)/(i-a));for(var s=0,l,u=(1<<o)/2;u>0;u/=2){var v=0,c=0;(r&u)>0&&(v=1),(e&u)>0&&(c=1),s+=u*u*(3*v^c),c===0&&(v===1&&(r=u-1-r,e=u-1-e),l=r,r=e,e=l)}return s}function fa(r){var e=1/0,t=1/0,a=-1/0,n=-1/0,i=O(r,function(s){var l=s.getBoundingRect(),u=s.getComputedTransform(),v=l.x+l.width/2+(u?u[4]:0),c=l.y+l.height/2+(u?u[5]:0);return e=Math.min(v,e),t=Math.min(c,t),a=Math.max(v,a),n=Math.max(c,n),[v,c]}),o=O(i,function(s,l){return{cp:s,z:JS(s[0],s[1],e,t,a,n),path:r[l]}});return o.sort(function(s,l){return s.z-l.z}).map(function(s){return s.path})}function Pv(r){return YS(r.path,r.count)}function zn(){return{fromIndividuals:[],toIndividuals:[],count:0}}function QS(r,e,t){var a=[];function n(x){for(var b=0;b<x.length;b++){var _=x[b];va(_)?n(_.childrenRef()):_ instanceof Vt&&a.push(_)}}n(r);var i=a.length;if(!i)return zn();var o=t.dividePath||Pv,s=o({path:e,count:i});if(s.length!==i)return console.error("Invalid morphing: unmatched splitted path"),zn();a=fa(a),s=fa(s);for(var l=t.done,u=t.during,v=t.individualDelay,c=new sr,f=0;f<i;f++){var h=a[f],p=s[f];p.parent=e,p.copyTransform(c),v||Lv(h,p)}e.__isCombineMorphing=!0,e.childrenRef=function(){return s};function d(x){for(var b=0;b<s.length;b++)s[b].addSelfToZr(x)}ca(e,"addSelfToZr",{after:function(x){d(x)}}),ca(e,"removeSelfFromZr",{after:function(x){for(var b=0;b<s.length;b++)s[b].removeSelfFromZr(x)}});function g(){e.__isCombineMorphing=!1,e.__morphT=-1,e.childrenRef=null,dr(e,"addSelfToZr"),dr(e,"removeSelfFromZr")}var S=s.length;if(v)for(var m=S,y=function(){m--,m===0&&(g(),l&&l())},f=0;f<S;f++){var w=v?j({delay:(t.delay||0)+v(f,S,a[f],s[f]),done:y},t):t;Pi(a[f],s[f],w)}else e.__morphT=0,e.animateTo({__morphT:1},j({during:function(x){for(var b=0;b<S;b++){var _=s[b];_.__morphT=e.__morphT,_.dirtyShape()}u&&u(x)},done:function(){g();for(var x=0;x<r.length;x++)dr(r[x],"updateTransform");l&&l()}},t));return e.__zr&&d(e.__zr),{fromIndividuals:a,toIndividuals:s,count:S}}function tx(r,e,t){var a=e.length,n=[],i=t.dividePath||Pv;function o(h){for(var p=0;p<h.length;p++){var d=h[p];va(d)?o(d.childrenRef()):d instanceof Vt&&n.push(d)}}if(va(r)){o(r.childrenRef());var s=n.length;if(s<a)for(var l=0,u=s;u<a;u++)n.push(qn(n[l++%s]));n.length=a}else{n=i({path:r,count:a});for(var v=r.getComputedTransform(),u=0;u<n.length;u++)n[u].setLocalTransform(v);if(n.length!==a)return console.error("Invalid morphing: unmatched splitted path"),zn()}n=fa(n),e=fa(e);for(var c=t.individualDelay,u=0;u<a;u++){var f=c?j({delay:(t.delay||0)+c(u,a,n[u],e[u])},t):t;Pi(n[u],e[u],f)}return{fromIndividuals:n,toIndividuals:e,count:e.length}}function tl(r){return Y(r[0])}function el(r,e){for(var t=[],a=r.length,n=0;n<a;n++)t.push({one:r[n],many:[]});for(var n=0;n<e.length;n++){var i=e[n].length,o=void 0;for(o=0;o<i;o++)t[o%a].many.push(e[n][o])}for(var s=0,n=a-1;n>=0;n--)if(!t[n].many.length){var l=t[s].many;if(l.length<=1)if(s)s=0;else return t;var i=l.length,u=Math.ceil(i/2);t[n].many=l.slice(u,i),t[s].many=l.slice(0,u),s++}return t}var ex={clone:function(r){for(var e=[],t=1-Math.pow(1-r.path.style.opacity,1/r.count),a=0;a<r.count;a++){var n=qn(r.path);n.setStyle("opacity",t),e.push(n)}return e},split:null};function un(r,e,t,a,n,i){if(!r.length||!e.length)return;var o=jn("update",a,n);if(!(o&&o.duration>0))return;var s=a.getModel("universalTransition").get("delay"),l=Object.assign({setToFinal:!0},o),u,v;tl(r)&&(u=r,v=e),tl(e)&&(u=e,v=r);function c(S,m,y,w,x){var b=S.many,_=S.one;if(b.length===1&&!x){var T=m?b[0]:_,I=m?_:b[0];if(va(T))c({many:[T],one:I},!0,y,w,!0);else{var A=s?j({delay:s(y,w)},l):l;Pi(T,I,A),i(T,I,T,I,A)}}else for(var D=j({dividePath:ex[t],individualDelay:s&&function(V,N,k,B){return s(V+y,w)}},l),E=m?QS(b,_,D):tx(_,b,D),M=E.fromIndividuals,C=E.toIndividuals,L=M.length,R=0;R<L;R++){var A=s?j({delay:s(R,L)},l):l;i(M[R],C[R],m?b[R]:S.one,m?S.one:b[R],A)}}for(var f=u?u===r:r.length>e.length,h=u?el(v,u):el(f?e:r,[f?r:e]),p=0,d=0;d<h.length;d++)p+=h[d].many.length;for(var g=0,d=0;d<h.length;d++)c(h[d],f,g,p),g+=h[d].many.length}function Pe(r){if(!r)return[];if(Y(r)){for(var e=[],t=0;t<r.length;t++)e.push(Pe(r[t]));return e}var a=[];return r.traverse(function(n){n instanceof Vt&&!n.disableMorphing&&!n.invisible&&!n.ignore&&a.push(n)}),a}var Mv=1e4,rx=0,rl=1,al=2,ax=re();function nx(r,e){for(var t=r.dimensions,a=0;a<t.length;a++){var n=r.getDimensionInfo(t[a]);if(n&&n.otherDims[e]===0)return t[a]}}function ix(r,e,t){var a=r.getDimensionInfo(t),n=a&&a.ordinalMeta;if(a){var i=r.get(a.name,e);return n&&n.categories[i]||i+""}}function nl(r,e,t,a){var n=a?"itemChildGroupId":"itemGroupId",i=nx(r,n);if(i){var o=ix(r,e,i);return o}var s=r.getRawDataItem(e),l=a?"childGroupId":"groupId";if(s&&s[l])return s[l]+"";if(!a)return t||r.getId(e)}function il(r){var e=[];return P(r,function(t){var a=t.data,n=t.dataGroupId;if(!(a.count()>Mv))for(var i=a.getIndices(),o=0;o<i.length;o++)e.push({data:a,groupId:nl(a,o,n,!1),childGroupId:nl(a,o,n,!0),divide:t.divide,dataIndex:o})}),e}function vn(r,e,t){r.traverse(function(a){a instanceof Vt&&Yt(a,{style:{opacity:0}},e,{dataIndex:t,isFrom:!0})})}function cn(r){if(r.parent){var e=r.getComputedTransform();r.setLocalTransform(e),r.parent.remove(r)}}function Ue(r){r.stopAnimation(),r.isGroup&&r.traverse(function(e){e.stopAnimation()})}function ox(r,e,t){var a=jn("update",t,e);a&&r.traverse(function(n){if(n instanceof mr){var i=Sf(n);i&&n.animateFrom({style:i},a)}})}function sx(r,e){var t=r.length;if(t!==e.length)return!1;for(var a=0;a<t;a++){var n=r[a],i=e[a];if(n.data.getId(n.dataIndex)!==i.data.getId(i.dataIndex))return!1}return!0}function Ev(r,e,t){var a=il(r),n=il(e);function i(y,w,x,b,_){(x||y)&&w.animateFrom({style:x&&x!==y?H(H({},x.style),y.style):y.style},_)}var o=!1,s=rx,l=K(),u=K();a.forEach(function(y){y.groupId&&l.set(y.groupId,!0),y.childGroupId&&u.set(y.childGroupId,!0)});for(var v=0;v<n.length;v++){var c=n[v].groupId;if(u.get(c)){s=rl;break}var f=n[v].childGroupId;if(f&&l.get(f)){s=al;break}}function h(y,w){return function(x){var b=x.data,_=x.dataIndex;return w?b.getId(_):y?s===rl?x.childGroupId:x.groupId:s===al?x.childGroupId:x.groupId}}var p=sx(a,n),d={};if(!p)for(var v=0;v<n.length;v++){var g=n[v],S=g.data.getItemGraphicEl(g.dataIndex);S&&(d[S.id]=!0)}function m(y,w){var x=a[w],b=n[y],_=b.data.hostModel,T=x.data.getItemGraphicEl(x.dataIndex),I=b.data.getItemGraphicEl(b.dataIndex);if(T===I){I&&ox(I,b.dataIndex,_);return}T&&d[T.id]||I&&(Ue(I),T?(Ue(T),cn(T),o=!0,un(Pe(T),Pe(I),b.divide,_,y,i)):vn(I,_,y))}new Ke(a,n,h(!0,p),h(!1,p),null,"multiple").update(m).updateManyToOne(function(y,w){var x=n[y],b=x.data,_=b.hostModel,T=b.getItemGraphicEl(x.dataIndex),I=Pt(O(w,function(A){return a[A].data.getItemGraphicEl(a[A].dataIndex)}),function(A){return A&&A!==T&&!d[A.id]});T&&(Ue(T),I.length?(P(I,function(A){Ue(A),cn(A)}),o=!0,un(Pe(I),Pe(T),x.divide,_,y,i)):vn(T,_,x.dataIndex))}).updateOneToMany(function(y,w){var x=a[w],b=x.data.getItemGraphicEl(x.dataIndex);if(!(b&&d[b.id])){var _=Pt(O(y,function(I){return n[I].data.getItemGraphicEl(n[I].dataIndex)}),function(I){return I&&I!==b}),T=n[y[0]].data.hostModel;_.length&&(P(_,function(I){return Ue(I)}),b?(Ue(b),cn(b),o=!0,un(Pe(b),Pe(_),x.divide,T,y[0],i)):P(_,function(I){return vn(I,T,y[0])}))}}).updateManyToMany(function(y,w){new Ke(w,y,function(x){return a[x].data.getId(a[x].dataIndex)},function(x){return n[x].data.getId(n[x].dataIndex)}).update(function(x,b){m(y[x],w[b])}).execute()}).execute(),o&&P(e,function(y){var w=y.data,x=w.hostModel,b=x&&t.getViewOfSeriesModel(x),_=jn("update",x,0);b&&x.isAnimationEnabled()&&_&&_.duration>0&&b.group.traverse(function(T){T instanceof Vt&&!T.animators.length&&T.animateFrom({style:{opacity:0}},_)})})}function ol(r){var e=r.getModel("universalTransition").get("seriesKey");return e||r.id}function sl(r){return Y(r)?r.sort().join(","):r}function ie(r){if(r.hostModel)return r.hostModel.getModel("universalTransition").get("divideShape")}function lx(r,e){var t=K(),a=K(),n=K();return P(r.oldSeries,function(i,o){var s=r.oldDataGroupIds[o],l=r.oldData[o],u=ol(i),v=sl(u);a.set(v,{dataGroupId:s,data:l}),Y(u)&&P(u,function(c){n.set(c,{key:v,dataGroupId:s,data:l})})}),P(e.updatedSeries,function(i){if(i.isUniversalTransitionEnabled()&&i.isAnimationEnabled()){var o=i.get("dataGroupId"),s=i.getData(),l=ol(i),u=sl(l),v=a.get(u);if(v)t.set(u,{oldSeries:[{dataGroupId:v.dataGroupId,divide:ie(v.data),data:v.data}],newSeries:[{dataGroupId:o,divide:ie(s),data:s}]});else if(Y(l)){var c=[];P(l,function(p){var d=a.get(p);d.data&&c.push({dataGroupId:d.dataGroupId,divide:ie(d.data),data:d.data})}),c.length&&t.set(u,{oldSeries:c,newSeries:[{dataGroupId:o,data:s,divide:ie(s)}]})}else{var f=n.get(l);if(f){var h=t.get(f.key);h||(h={oldSeries:[{dataGroupId:f.dataGroupId,data:f.data,divide:ie(f.data)}],newSeries:[]},t.set(f.key,h)),h.newSeries.push({dataGroupId:o,data:s,divide:ie(s)})}}}}),t}function ll(r,e){for(var t=0;t<r.length;t++){var a=e.seriesIndex!=null&&e.seriesIndex===r[t].seriesIndex||e.seriesId!=null&&e.seriesId===r[t].id;if(a)return t}}function ux(r,e,t,a){var n=[],i=[];P(Ot(r.from),function(o){var s=ll(e.oldSeries,o);s>=0&&n.push({dataGroupId:e.oldDataGroupIds[s],data:e.oldData[s],divide:ie(e.oldData[s]),groupIdDim:o.dimension})}),P(Ot(r.to),function(o){var s=ll(t.updatedSeries,o);if(s>=0){var l=t.updatedSeries[s].getData();i.push({dataGroupId:e.oldDataGroupIds[s],data:l,divide:ie(l),groupIdDim:o.dimension})}}),n.length>0&&i.length>0&&Ev(n,i,a)}function vx(r){r.registerUpdateLifecycle("series:beforeupdate",function(e,t,a){P(Ot(a.seriesTransition),function(n){P(Ot(n.to),function(i){for(var o=a.updatedSeries,s=0;s<o.length;s++)(i.seriesIndex!=null&&i.seriesIndex===o[s].seriesIndex||i.seriesId!=null&&i.seriesId===o[s].id)&&(o[s][Ma]=!0)})})}),r.registerUpdateLifecycle("series:transition",function(e,t,a){var n=ax(t);if(n.oldSeries&&a.updatedSeries&&a.optionChanged){var i=a.seriesTransition;if(i)P(Ot(i),function(h){ux(h,n,a,t)});else{var o=lx(n,a);P(o.keys(),function(h){var p=o.get(h);Ev(p.oldSeries,p.newSeries,t)})}P(a.updatedSeries,function(h){h[Ma]&&(h[Ma]=!1)})}for(var s=e.getSeries(),l=n.oldSeries=[],u=n.oldDataGroupIds=[],v=n.oldData=[],c=0;c<s.length;c++){var f=s[c].getData();f.count()<Mv&&(l.push(s[c]),u.push(s[c].get("dataGroupId")),v.push(f))}})}$([$l]);$([kS]);$([nu,iu,ih,$f,Ah,sp,Np,pd,Rd,Od,Xd,Dg,Jg,uy,Yf,hy,_y,Py,Fy,Zy,am,Pm]);$(ou);$(r0);$(mu);$(d0);$(Fu);$(Zf);$(Xf);$(qf);$(Yl);$(Zn);$(P0);$(Zl);$(jf);$(V0);$(Kf);$(B0);$(Xl);$(Jf);$(Qf);$(th);$(su);$(eh);$(rh);$($0);$(cv);$(ql);$(vx);$(oh);const fn="Trade Count",cx=ul({__name:"TimePeriodChart",props:{dailyStats:{},showTitle:{type:Boolean,default:!0},profitCol:{}},setup(r){$([iu,nu,$l,ou,ql,Xl,Zl,Yl,su,cv]);const e=r,t=oe(()=>e.profitCol==="abs_profit"?"Absolute profit":"Relative profit"),a=vl(),n=Rv(),i=cl(null),o=oe(()=>{var c;return e.dailyStats.data.reduce((f,h)=>h[e.profitCol]<f?h[e.profitCol]:f,(c=e.dailyStats.data[0])==null?void 0:c[e.profitCol])*(e.profitCol==="rel_profit"?100:1)}),s=oe(()=>{var c;return e.dailyStats.data.reduce((f,h)=>h[e.profitCol]>f?h[e.profitCol]:f,(c=e.dailyStats.data[0])==null?void 0:c[e.profitCol])*(e.profitCol==="rel_profit"?100:1)});xf({multiple:{type:"units:multiple",transform:function(c){const f=c.upstream.cloneRawData(),{dimension:h,factor:p}=c.config,d=f.map(g=>({...g,[h]:(g[h]*p).toFixed(2)}));return[{dimensions:c.upstream.cloneAllDimensionInfo(),data:d}]}}}.multiple);const u={type:"linear",x:0,y:0,x2:1,y2:0,colorStops:[{offset:0,color:n.colorProfit},{offset:.5,color:n.colorProfit},{offset:.5,color:n.colorLoss},{offset:1,color:n.colorLoss}]},v=oe(()=>({title:{text:"Daily profit",show:e.showTitle},backgroundColor:"rgba(0, 0, 0, 0)",dataset:[{dimensions:["date",e.profitCol,"trade_count"],source:e.dailyStats.data},{transform:{type:"units:multiple",config:{dimension:e.profitCol,factor:e.profitCol=="rel_profit"?100:1}}}],tooltip:{trigger:"axis",axisPointer:{type:"line",label:{backgroundColor:"#6a7985"}}},legend:{data:[{name:t.value,lineStyle:{color:u},itemStyle:{color:u}},{name:fn}],right:"5%"},xAxis:[{type:"category"}],visualMap:[{dimension:1,seriesIndex:0,show:!1,pieces:[{max:0,min:o.value,color:n.colorLoss},{min:0,max:s.value,color:n.colorProfit}]}],yAxis:[{type:"value",name:t.value,splitLine:{show:!1},nameRotate:90,nameLocation:"middle",nameGap:35,axisLabel:{formatter:c=>e.profitCol==="rel_profit"?`${c}%`:`${c}`}},{type:"value",name:fn,nameRotate:90,nameLocation:"middle",nameGap:30}],grid:{left:"50",right:"45",bottom:"15%"},series:[{type:"line",name:t.value,datasetIndex:1},{type:"bar",name:fn,itemStyle:{color:"rgba(150,150,150,0.3)"},yAxisIndex:1,datasetIndex:1}]}));return(c,f)=>c.dailyStats.data?(Te(),$r(it(bf),{key:0,ref_key:"dailyChart",ref:i,option:it(v),theme:it(a).chartTheme,style:{height:"100%"},autoresize:""},null,8,["option","theme"])):$e("",!0)}}),fx=Vv(cx,[["__scopeId","data-v-867a1e5d"]]),hx={class:"flex flex-col h-full"},px={key:0,class:"mb-2"},dx={class:"me-auto inline text-xl"},gx={class:"flex align-center justify-between"},yx={class:"ps-1"},mx={key:1},Tx=ul({__name:"PeriodBreakdown",props:{multiBotView:{type:Boolean}},setup(r){const e=Nv(),t=vl(),a=r,n=oe(()=>e.activeBot.botApiVersion>=2.33||a.multiBotView),i=oe(()=>{const v=[{value:we.daily,text:"Days"}];return n.value&&(v.push({value:we.weekly,text:"Weeks"}),v.push({value:we.monthly,text:"Months"})),v}),o=cl([{value:"abs_profit",text:"Abs $"},{value:"rel_profit",text:"Rel %"}]),s=oe(()=>{if(a.multiBotView)switch(t.timeProfitPeriod){case we.weekly:return e.allWeeklyStatsSelectedBots;case we.monthly:return e.allMonthlyStatsSelectedBots;default:return e.allDailyStatsSelectedBots}switch(t.timeProfitPeriod){case we.weekly:return e.activeBot.weeklyStats;case we.monthly:return e.activeBot.monthlyStats;default:return e.activeBot.dailyStats}}),l=oe(()=>({...s.value,data:s.value.data?Object.values(s.value.data).sort((v,c)=>v.date>c.date?1:-1):[]}));function u(){a.multiBotView?e.allGetTimeSummary(t.timeProfitPeriod):e.activeBot.getTimeSummary(t.timeProfitPeriod)}return Gv(()=>{u()}),(v,c)=>{const f=zv,h=kv,p=sh,d=fx,g=Fv,S=Bv;return Te(),Da("div",hx,[a.multiBotView?$e("",!0):(Te(),Da("div",px,[Ca("h3",dx,Er(it(n)?"Period":"Daily")+" Breakdown",1),ne(h,{class:"float-end",severity:"secondary",onClick:u},{icon:er(()=>[ne(f)]),_:1})])),Ca("div",gx,[it(n)?(Te(),$r(p,{key:0,id:"order-direction",modelValue:it(t).timeProfitPeriod,"onUpdate:modelValue":c[0]||(c[0]=m=>it(t).timeProfitPeriod=m),options:it(i),name:"radios-btn-default",size:"small","allow-empty":!1,"option-label":"text","option-value":"value",onChange:u},null,8,["modelValue","options"])):$e("",!0),ne(p,{modelValue:it(t).timeProfitPreference,"onUpdate:modelValue":c[1]||(c[1]=m=>it(t).timeProfitPreference=m),name:"radios-btn-select",size:"small","allow-empty":!1,"option-label":"text","option-value":"value",options:it(o),buttons:"","button-variant":"outline-primary"},null,8,["modelValue","options"])]),Ca("div",yx,[it(s)?(Te(),$r(d,{key:0,"daily-stats":it(l),"show-title":!1,"profit-col":it(t).timeProfitPreference},null,8,["daily-stats","profit-col"])):$e("",!0)]),a.multiBotView?$e("",!0):(Te(),Da("div",mx,[ne(S,{size:"small",value:it(s).data},{default:er(()=>[ne(g,{field:"date",header:"Day"}),ne(g,{field:"abs_profit",header:"Profit"},{body:er(({data:m,field:y})=>[La(Er(("formatPrice"in v?v.formatPrice:it(Ei))(m[y],it(e).activeBot.stakeCurrencyDecimals)),1)]),_:1}),ne(g,{field:"fiat_value",header:`In ${it(e).activeBot.dailyStats.fiat_display_currency}`},{body:er(({data:m,field:y})=>[La(Er(("formatPrice"in v?v.formatPrice:it(Ei))(m[y],2)),1)]),_:1},8,["header"]),ne(g,{field:"trade_count",header:"Trades"}),it(e).activeBot.botApiVersion>=2.16?(Te(),$r(g,{key:0,field:"rel_profit",header:"Profit%"},{body:er(({data:m,field:y})=>[La(Er(("formatPercent"in v?v.formatPercent:it(Ov))(m[y],2)),1)]),_:1})):$e("",!0)]),_:1},8,["value"])]))])}}});export{Tx as _};
//# sourceMappingURL=PeriodBreakdown.vue_vue_type_script_setup_true_lang-BcNLHw4D.js.map
