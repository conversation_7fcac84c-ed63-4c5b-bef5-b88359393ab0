{"version": 3, "file": "SettingsView-41zYQ4Vi.js", "sources": ["../../node_modules/.pnpm/@primeuix+styles@1.0.0/node_modules/@primeuix/styles/card/index.mjs", "../../node_modules/.pnpm/primevue@4.3.3_vue@3.5.13_typescript@5.8.2_/node_modules/primevue/card/style/index.mjs", "../../node_modules/.pnpm/primevue@4.3.3_vue@3.5.13_typescript@5.8.2_/node_modules/primevue/card/index.mjs", "../../src/views/SettingsView.vue"], "sourcesContent": ["var style=({dt:n})=>`\\n.p-card {\\n    background: ${n(\"card.background\")};\\n    color: ${n(\"card.color\")};\\n    box-shadow: ${n(\"card.shadow\")};\\n    border-radius: ${n(\"card.border.radius\")};\\n    display: flex;\\n    flex-direction: column;\\n}\\n\\n.p-card-caption {\\n    display: flex;\\n    flex-direction: column;\\n    gap: ${n(\"card.caption.gap\")};\\n}\\n\\n.p-card-body {\\n    padding: ${n(\"card.body.padding\")};\\n    display: flex;\\n    flex-direction: column;\\n    gap: ${n(\"card.body.gap\")};\\n}\\n\\n.p-card-title {\\n    font-size: ${n(\"card.title.font.size\")};\\n    font-weight: ${n(\"card.title.font.weight\")};\\n}\\n\\n.p-card-subtitle {\\n    color: ${n(\"card.subtitle.color\")};\\n}\\n`;export{style};//# sourceMappingURL=index.mjs.map", "import { style } from '@primeuix/styles/card';\nimport BaseStyle from '@primevue/core/base/style';\n\nvar classes = {\n  root: 'p-card p-component',\n  header: 'p-card-header',\n  body: 'p-card-body',\n  caption: 'p-card-caption',\n  title: 'p-card-title',\n  subtitle: 'p-card-subtitle',\n  content: 'p-card-content',\n  footer: 'p-card-footer'\n};\nvar CardStyle = BaseStyle.extend({\n  name: 'card',\n  style: style,\n  classes: classes\n});\n\nexport { CardStyle as default };\n//# sourceMappingURL=index.mjs.map\n", "import BaseComponent from '@primevue/core/basecomponent';\nimport Card<PERSON>tyle from 'primevue/card/style';\nimport { createElementBlock, openBlock, mergeProps, createCommentVNode, createElementVNode, renderSlot } from 'vue';\n\nvar script$1 = {\n  name: 'BaseCard',\n  \"extends\": BaseComponent,\n  style: CardStyle,\n  provide: function provide() {\n    return {\n      $pcCard: this,\n      $parentInstance: this\n    };\n  }\n};\n\nvar script = {\n  name: 'Card',\n  \"extends\": script$1,\n  inheritAttrs: false\n};\n\nfunction render(_ctx, _cache, $props, $setup, $data, $options) {\n  return openBlock(), createElementBlock(\"div\", mergeProps({\n    \"class\": _ctx.cx('root')\n  }, _ctx.ptmi('root')), [_ctx.$slots.header ? (openBlock(), createElementBlock(\"div\", mergeProps({\n    key: 0,\n    \"class\": _ctx.cx('header')\n  }, _ctx.ptm('header')), [renderSlot(_ctx.$slots, \"header\")], 16)) : createCommentVNode(\"\", true), createElementVNode(\"div\", mergeProps({\n    \"class\": _ctx.cx('body')\n  }, _ctx.ptm('body')), [_ctx.$slots.title || _ctx.$slots.subtitle ? (openBlock(), createElementBlock(\"div\", mergeProps({\n    key: 0,\n    \"class\": _ctx.cx('caption')\n  }, _ctx.ptm('caption')), [_ctx.$slots.title ? (openBlock(), createElementBlock(\"div\", mergeProps({\n    key: 0,\n    \"class\": _ctx.cx('title')\n  }, _ctx.ptm('title')), [renderSlot(_ctx.$slots, \"title\")], 16)) : createCommentVNode(\"\", true), _ctx.$slots.subtitle ? (openBlock(), createElementBlock(\"div\", mergeProps({\n    key: 1,\n    \"class\": _ctx.cx('subtitle')\n  }, _ctx.ptm('subtitle')), [renderSlot(_ctx.$slots, \"subtitle\")], 16)) : createCommentVNode(\"\", true)], 16)) : createCommentVNode(\"\", true), createElementVNode(\"div\", mergeProps({\n    \"class\": _ctx.cx('content')\n  }, _ctx.ptm('content')), [renderSlot(_ctx.$slots, \"content\")], 16), _ctx.$slots.footer ? (openBlock(), createElementBlock(\"div\", mergeProps({\n    key: 1,\n    \"class\": _ctx.cx('footer')\n  }, _ctx.ptm('footer')), [renderSlot(_ctx.$slots, \"footer\")], 16)) : createCommentVNode(\"\", true)], 16)], 16);\n}\n\nscript.render = render;\n\nexport { script as default };\n//# sourceMappingURL=index.mjs.map\n", "<script setup lang=\"ts\">\nimport { OpenTradeVizOptions, useSettingsStore } from '@/stores/settings';\nimport { useLayoutStore } from '@/stores/layout';\nimport { FtWsMessageTypes } from '@/types/wsMessageTypes';\nimport { ColorPreferences, useColorStore } from '@/stores/colors';\n\nconst settingsStore = useSettingsStore();\nconst colorStore = useColorStore();\nconst layoutStore = useLayoutStore();\n\nconst timezoneOptions = ['UTC', Intl.DateTimeFormat().resolvedOptions().timeZone];\nconst openTradesOptions = [\n  { value: OpenTradeVizOptions.showPill, text: 'Show pill in icon' },\n  { value: OpenTradeVizOptions.asTitle, text: 'Show in title' },\n  { value: OpenTradeVizOptions.noOpenTrades, text: \"Don't show open trades in header\" },\n];\nconst colorPreferenceOptions = [\n  { value: ColorPreferences.GREEN_UP, text: 'Green Up/Red Down' },\n  { value: ColorPreferences.RED_UP, text: 'Green Down/Red Up' },\n];\n\nconst resetDynamicLayout = () => {\n  layoutStore.resetTradingLayout();\n  layoutStore.resetDashboardLayout();\n  showAlert('Layouts have been reset.');\n};\n</script>\n\n<template>\n  <Card class=\"mx-auto mt-3 p-4 max-w-4xl\">\n    <template #title>FreqUI Settings</template>\n    <template #content>\n      <div class=\"flex flex-col gap-4 text-start dark:text-surface-300\">\n        <p class=\"text-left\">UI Version: {{ settingsStore.uiVersion }}</p>\n\n        <div class=\"border border-surface-400 rounded-sm p-4 space-y-4\">\n          <h4 class=\"text-xl font-semibold\">UI settings</h4>\n\n          <BaseCheckbox v-model=\"layoutStore.layoutLocked\" class=\"space-y-1\">\n            Lock dynamic layouts\n            <template #hint>\n              Lock dynamic layouts, so they cannot move anymore. Can also be set from the navbar at\n              the top.\n            </template>\n          </BaseCheckbox>\n\n          <div class=\"flex flex-row gap-2 space-y-2\">\n            <Button severity=\"secondary\" size=\"small\" @click=\"resetDynamicLayout\"\n              >Reset layout</Button\n            >\n            <small class=\"block text-surface-600 dark:text-surface-400\"\n              >Reset dynamic layouts to how they were.</small\n            >\n          </div>\n\n          <Divider />\n\n          <div class=\"space-y-1\">\n            <label class=\"block text-sm\">Show open trades in header</label>\n            <Select\n              v-model=\"settingsStore.openTradesInTitle\"\n              :options=\"openTradesOptions\"\n              option-label=\"text\"\n              option-value=\"value\"\n              size=\"small\"\n              class=\"w-full\"\n            />\n            <small class=\"text-surface-600 dark:text-surface-400\"\n              >Decide if open trades should be visualized</small\n            >\n          </div>\n\n          <div class=\"space-y-1\">\n            <label class=\"block text-sm\">UTC Timezone</label>\n            <Select\n              v-model=\"settingsStore.timezone\"\n              :options=\"timezoneOptions\"\n              class=\"w-full\"\n              size=\"small\"\n            />\n            <small class=\"text-surface-600 dark:text-surface-400\"\n              >Select timezone (UTC is recommended as exchanges usually work in UTC)</small\n            >\n          </div>\n\n          <BaseCheckbox v-model=\"settingsStore.backgroundSync\" class=\"space-y-1\">\n            Background sync\n            <template #hint> Keep background sync running while other bots are selected. </template>\n          </BaseCheckbox>\n\n          <BaseCheckbox v-model=\"settingsStore.confirmDialog\" class=\"space-y-1\">\n            Show Confirm Dialog for Trade Exits\n            <template #hint>Use confirmation dialogs when force-exiting a trade.</template>\n          </BaseCheckbox>\n\n          <BaseCheckbox v-model=\"settingsStore.multiPaneButtonsShowText\" class=\"space-y-1\">\n            Show Text on Multi Pane Buttons\n            <template #hint\n              >Show text on multi pane buttons. If disabled, only shows images.</template\n            >\n          </BaseCheckbox>\n        </div>\n\n        <div class=\"border border-surface-400 rounded-sm p-4 space-y-4\">\n          <h4 class=\"text-lg font-semibold\">Chart settings</h4>\n\n          <div class=\"space-y-1\">\n            <label class=\"block text-sm\">Chart scale Side</label>\n            <div class=\"flex gap-4\">\n              <div class=\"flex items-center\">\n                <RadioButton v-model=\"settingsStore.chartLabelSide\" value=\"left\" size=\"small\" />\n                <label class=\"ml-2\">Left</label>\n              </div>\n              <div class=\"flex items-center\">\n                <RadioButton v-model=\"settingsStore.chartLabelSide\" value=\"right\" size=\"small\" />\n                <label class=\"ml-2\">Right</label>\n              </div>\n            </div>\n            <small class=\"text-surface-600 dark:text-surface-400\"\n              >Should the scale be displayed on the right or left?</small\n            >\n          </div>\n\n          <BaseCheckbox v-model=\"settingsStore.useHeikinAshiCandles\" class=\"space-y-1\">\n            Use Heikin Ashi candles\n            <template #hint>Use Heikin Ashi candles in your charts</template>\n          </BaseCheckbox>\n\n          <BaseCheckbox v-model=\"settingsStore.useReducedPairCalls\" class=\"space-y-1\">\n            Only request necessary columns\n            <template #hint\n              >Can reduce the transfer size for large dataframes. May require additional calls if\n              the plot config changes.</template\n            >\n          </BaseCheckbox>\n\n          <div class=\"space-y-1\">\n            <label class=\"block text-sm\">Candle Color Preference</label>\n            <div class=\"flex flex-row gap-5 items-center\">\n              <div\n                v-for=\"option in colorPreferenceOptions\"\n                :key=\"option.value\"\n                class=\"flex items-center\"\n              >\n                <RadioButton\n                  v-model=\"colorStore.colorPreference\"\n                  :value=\"option.value\"\n                  :input-id=\"`input-id${option.value}`\"\n                  size=\"small\"\n                  @change=\"colorStore.updateProfitLossColor\"\n                />\n                <label :for=\"`input-id${option.value}`\" class=\"ml-2 flex items-center\">\n                  <span class=\"mr-2\">{{ option.text }}</span>\n                  <i-mdi-arrow-up-thin\n                    :color=\"\n                      option.value === ColorPreferences.GREEN_UP\n                        ? colorStore.colorProfit\n                        : colorStore.colorLoss\n                    \"\n                    class=\"-ml-2\"\n                  />\n                  <i-mdi-arrow-down-thin\n                    :color=\"\n                      option.value === ColorPreferences.GREEN_UP\n                        ? colorStore.colorLoss\n                        : colorStore.colorProfit\n                    \"\n                    class=\"-ml-2\"\n                  />\n                </label>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"border rounded-sm p-4 space-y-4\">\n          <h4 class=\"text-lg font-semibold\">Notification Settings</h4>\n          <div class=\"space-y-2\">\n            <BaseCheckbox v-model=\"settingsStore.notifications[FtWsMessageTypes.entryFill]\">\n              Entry notifications\n            </BaseCheckbox>\n            <BaseCheckbox v-model=\"settingsStore.notifications[FtWsMessageTypes.exitFill]\">\n              Exit notifications\n            </BaseCheckbox>\n            <BaseCheckbox v-model=\"settingsStore.notifications[FtWsMessageTypes.entryCancel]\">\n              Entry Cancel notifications\n            </BaseCheckbox>\n            <BaseCheckbox v-model=\"settingsStore.notifications[FtWsMessageTypes.exitCancel]\">\n              Exit Cancel notifications\n            </BaseCheckbox>\n          </div>\n        </div>\n\n        <div class=\"border rounded-sm p-4 space-y-4\">\n          <h4 class=\"text-lg font-semibold\">Backtesting settings</h4>\n          <div>\n            <label for=\"backtestMetrics\" class=\"block text-sm\">Backtesting metrics</label>\n            <MultiSelect\n              id=\"backtestMetrics\"\n              v-model=\"settingsStore.backtestAdditionalMetrics\"\n              :options=\"availableBacktestMetrics\"\n              option-label=\"header\"\n              option-value=\"field\"\n              class=\"w-full\"\n              size=\"small\"\n              display=\"chip\"\n            />\n            <small class=\"text-surface-600 dark:text-surface-400\"\n              >Select which metrics should be shown on a per pair / tag basis.</small\n            >\n          </div>\n        </div>\n      </div>\n    </template>\n  </Card>\n</template>\n"], "names": ["style", "n", "classes", "CardStyle", "BaseStyle", "script$1", "BaseComponent", "script", "render", "_ctx", "_cache", "$props", "$setup", "$data", "$options", "openBlock", "createElementBlock", "mergeProps", "renderSlot", "createCommentVNode", "createElementVNode", "settingsStore", "useSettingsStore", "colorStore", "useColorStore", "layoutStore", "useLayoutStore", "timezoneOptions", "openTradesOptions", "OpenTradeVizOptions", "colorPreferenceOptions", "ColorPreferences", "resetDynamicLayout", "show<PERSON><PERSON><PERSON>"], "mappings": "0UAAA,IAAIA,GAAM,CAAC,CAAC,GAAGC,CAAC,IAAI;AAAA;AAAA,kBAAgCA,EAAE,iBAAiB,CAAC;AAAA,aAAiBA,EAAE,YAAY,CAAC;AAAA,kBAAsBA,EAAE,aAAa,CAAC;AAAA,qBAAyBA,EAAE,oBAAoB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,WAAyIA,EAAE,kBAAkB,CAAC;AAAA;AAAA;AAAA;AAAA,eAAwCA,EAAE,mBAAmB,CAAC;AAAA;AAAA;AAAA,WAAgEA,EAAE,eAAe,CAAC;AAAA;AAAA;AAAA;AAAA,iBAA2CA,EAAE,sBAAsB,CAAC;AAAA,mBAAuBA,EAAE,wBAAwB,CAAC;AAAA;AAAA;AAAA;AAAA,aAA0CA,EAAE,qBAAqB,CAAC;AAAA;AAAA,ECGhqBC,GAAU,CACZ,KAAM,qBACN,OAAQ,gBACR,KAAM,cACN,QAAS,iBACT,MAAO,eACP,SAAU,kBACV,QAAS,iBACT,OAAQ,eACV,EACIC,GAAYC,EAAU,OAAO,CAC/B,KAAM,OACN,MAAOJ,GACP,QAASE,EACX,CAAC,ECbGG,GAAW,CACb,KAAM,WACN,QAAWC,EACX,MAAOH,GACP,QAAS,UAAmB,CAC1B,MAAO,CACL,QAAS,KACT,gBAAiB,IAClB,CACL,CACA,EAEII,EAAS,CACX,KAAM,OACN,QAAWF,GACX,aAAc,EAChB,EAEA,SAASG,GAAOC,EAAMC,EAAQC,EAAQC,EAAQC,EAAOC,EAAU,CAC7D,OAAOC,EAAW,EAAEC,EAAmB,MAAOC,EAAW,CACvD,MAASR,EAAK,GAAG,MAAM,CACxB,EAAEA,EAAK,KAAK,MAAM,CAAC,EAAG,CAACA,EAAK,OAAO,QAAUM,EAAW,EAAEC,EAAmB,MAAOC,EAAW,CAC9F,IAAK,EACL,MAASR,EAAK,GAAG,QAAQ,CAC7B,EAAKA,EAAK,IAAI,QAAQ,CAAC,EAAG,CAACS,EAAWT,EAAK,OAAQ,QAAQ,CAAC,EAAG,EAAE,GAAKU,EAAmB,GAAI,EAAI,EAAGC,EAAmB,MAAOH,EAAW,CACrI,MAASR,EAAK,GAAG,MAAM,CAC3B,EAAKA,EAAK,IAAI,MAAM,CAAC,EAAG,CAACA,EAAK,OAAO,OAASA,EAAK,OAAO,UAAYM,EAAW,EAAEC,EAAmB,MAAOC,EAAW,CACpH,IAAK,EACL,MAASR,EAAK,GAAG,SAAS,CAC3B,EAAEA,EAAK,IAAI,SAAS,CAAC,EAAG,CAACA,EAAK,OAAO,OAASM,EAAW,EAAEC,EAAmB,MAAOC,EAAW,CAC/F,IAAK,EACL,MAASR,EAAK,GAAG,OAAO,CACzB,EAAEA,EAAK,IAAI,OAAO,CAAC,EAAG,CAACS,EAAWT,EAAK,OAAQ,OAAO,CAAC,EAAG,EAAE,GAAKU,EAAmB,GAAI,EAAI,EAAGV,EAAK,OAAO,UAAYM,IAAaC,EAAmB,MAAOC,EAAW,CACxK,IAAK,EACL,MAASR,EAAK,GAAG,UAAU,CAC5B,EAAEA,EAAK,IAAI,UAAU,CAAC,EAAG,CAACS,EAAWT,EAAK,OAAQ,UAAU,CAAC,EAAG,EAAE,GAAKU,EAAmB,GAAI,EAAI,CAAC,EAAG,EAAE,GAAKA,EAAmB,GAAI,EAAI,EAAGC,EAAmB,MAAOH,EAAW,CAC/K,MAASR,EAAK,GAAG,SAAS,CAC9B,EAAKA,EAAK,IAAI,SAAS,CAAC,EAAG,CAACS,EAAWT,EAAK,OAAQ,SAAS,CAAC,EAAG,EAAE,EAAGA,EAAK,OAAO,QAAUM,IAAaC,EAAmB,MAAOC,EAAW,CAC1I,IAAK,EACL,MAASR,EAAK,GAAG,QAAQ,CAC7B,EAAKA,EAAK,IAAI,QAAQ,CAAC,EAAG,CAACS,EAAWT,EAAK,OAAQ,QAAQ,CAAC,EAAG,EAAE,GAAKU,EAAmB,GAAI,EAAI,CAAC,EAAG,EAAE,CAAC,EAAG,EAAE,CAC7G,CAEAZ,EAAO,OAASC,yoCCzChB,MAAMa,EAAgBC,EAAiB,EACjCC,EAAaC,EAAc,EAC3BC,EAAcC,EAAe,EAE7BC,EAAkB,CAAC,MAAO,KAAK,iBAAiB,kBAAkB,QAAQ,EAC1EC,EAAoB,CACxB,CAAE,MAAOC,EAAoB,SAAU,KAAM,mBAAoB,EACjE,CAAE,MAAOA,EAAoB,QAAS,KAAM,eAAgB,EAC5D,CAAE,MAAOA,EAAoB,aAAc,KAAM,kCAAmC,CACtF,EACMC,EAAyB,CAC7B,CAAE,MAAOC,EAAiB,SAAU,KAAM,mBAAoB,EAC9D,CAAE,MAAOA,EAAiB,OAAQ,KAAM,mBAAoB,CAC9D,EAEMC,EAAqB,IAAM,CAC/BP,EAAY,mBAAmB,EAC/BA,EAAY,qBAAqB,EACjCQ,EAAU,0BAA0B,CACtC", "x_google_ignoreList": [0, 1, 2]}