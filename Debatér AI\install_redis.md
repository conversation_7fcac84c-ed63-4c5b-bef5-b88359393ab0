# Instalace Redis pro Windows

Pro zprovoznění Server-Sent Events (SSE) v aplikaci je potřeba nainstalovat Redis server. Následujte tyto kroky:

## 1. Stáhněte Redis pro Windows

Stáhněte Redis pro Windows z oficiálního GitHub repozitáře:
- Navštivte stránku: https://github.com/tporadowski/redis/releases/tag/v5.0.14.1
- Stáhněte soubor `Redis-x64-5.0.14.1.msi`

## 2. Nainstalujte Redis

- Spusťte stažený MSI soubor
- Postupujte podle instalačních pokynů
- Ponechte výchozí nastavení (včetně instalace jako služby Windows)

## 3. Ověřte instalaci

Po dokončení instalace ověřte, že Redis server běží:
- Otevřete příkazový řádek (cmd)
- Zadejte příkaz: `redis-cli ping`
- <PERSON><PERSON><PERSON> byste obdržet odpověď: `PONG`

Pokud obdržíte odpověď `PONG`, Redis server je úspěšně nainstalován a běží.

## 4. Spuštění aplikace s Redis serverem

Po úspěšné instalaci Redis serveru můžete spustit aplikaci, která bude nyní používat Redis pro Server-Sent Events.
