/*! tailwindcss v4.1.0 | MIT License | https://tailwindcss.com */@supports (((-webkit-hyphens:none)) and (not (margin-trim:inline))) or ((-moz-orient:inline) and (not (color:rgb(from red r g b)))){@layer base{[data-v-d8a70cd1],[data-v-d8a70cd1]:before,[data-v-d8a70cd1]:after,[data-v-d8a70cd1]::backdrop{--tw-border-style:solid}}}.check[data-v-d8a70cd1]{color:#41b883;opacity:0;z-index:5;width:1.3em;height:1.3em;transition:opacity .2s;position:absolute;top:-.3em;left:-.3em}.pair.active .check[data-v-d8a70cd1]{opacity:1}.list[data-v-d8a70cd1]{grid-gap:.5rem;grid-template-columns:repeat(auto-fill,minmax(110px,1fr));padding-bottom:1rem;display:grid}.wide[data-v-d8a70cd1]{grid-template-columns:repeat(auto-fill,minmax(120px,1fr))}.pair[data-v-d8a70cd1]{cursor:pointer;border-style:var(--tw-border-style);border-width:1px;border-color:color-mix(in srgb,var(--p-surface-500) 100%,transparent);padding:calc(var(--spacing,.25rem)*2);border-radius:.25rem;position:relative}@property --tw-border-style{syntax:"*";inherits:false;initial-value:solid}
