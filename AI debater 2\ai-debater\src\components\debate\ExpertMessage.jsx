import React from 'react';
import { FaRobot } from 'react-icons/fa';

const ExpertMessage = ({ message, config }) => {
  const { expertNumber, expertise, content, round } = message;

  const isExpert1 = expertNumber === 1;

  return (
    <div className={`expert-message expert-${expertNumber} pl-4 py-2`}>
      <div className="flex items-center space-x-2 mb-2">
        <FaRobot className={isExpert1 ? "text-blue-600" : "text-red-600"} />
        <div>
          <span className="font-medium">Expert {expertNumber}: {expertise}</span>
          <span className="text-xs text-gray-500 ml-2">Round {round}</span>
        </div>
      </div>
      <div className="text-gray-800 dark:text-gray-200">
        {content}
      </div>
    </div>
  );
};

export default ExpertMessage;
