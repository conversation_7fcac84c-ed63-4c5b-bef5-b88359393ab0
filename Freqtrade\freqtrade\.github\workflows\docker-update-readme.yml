name: Update Docker Hub Description
on:
  push:
    branches:
    - stable

# disable permissions for all of the available permissions
permissions: {}

jobs:
  dockerHubDescription:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
      with:
        persist-credentials: false

    - name: Docker Hub Description
      uses: peter-evans/dockerhub-description@432a30c9e07499fd01da9f8a49f0faf9e0ca5b77 # v4.0.2
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}
        repository: freqtradeorg/freqtrade
