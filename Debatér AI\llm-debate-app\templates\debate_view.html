<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debata: {{ debate.topic }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background-color: #f0f2f5; padding-top: 20px;}
        .container { max-width: 900px; }
        .debate-header { background-color: #fff; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }
        .conversation-container { background-color: #fff; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); min-height: 400px; max-height: 60vh; overflow-y: auto; display: flex; flex-direction: column;}
        .summaries-container { background-color: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }
        .message-card { margin-bottom: 15px; padding: 10px 15px; border-radius: 15px; max-width: 80%; word-wrap: break-word; }
        .message-card .speaker { font-weight: bold; font-size: 0.9em; margin-bottom: 3px; color: #555; }
        .message-card p { margin-bottom: 0; font-size: 0.95em; }
        .message-ModelA { background-color: #e7f3ff; border: 1px solid #d0e7ff; align-self: flex-start; border-bottom-left-radius: 0;}
        .message-ModelB { background-color: #e8f5e9; border: 1px solid #c8e6c9; align-self: flex-end; text-align: right; border-bottom-right-radius: 0;}
        .message-System { background-color: #f5f5f5; border: 1px solid #e0e0e0; align-self: center; text-align: center; font-style: italic; font-size: 0.85em; max-width: 90%; padding: 5px 10px; }
        .summary-card { background-color: #fffde7; border: 1px solid #fff9c4; margin-bottom: 15px; padding: 15px; border-radius: 8px; }
        .summary-card h5 { font-size: 1em; font-weight: bold; margin-bottom: 5px; }
        .status-indicator { font-style: italic; color: #666; text-align: center; padding: 10px; font-size: 0.9em;}
        .error-message { color: red; font-weight: bold; text-align: center; padding: 10px; font-size: 0.9em; }
        .spinner-grow { width: 1rem; height: 1rem; vertical-align: text-bottom; margin-left: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="debate-header">
            <h1 class="h3">Debata: {{ debate.topic }}</h1>
            <p class="text-muted mb-1">Model A: {{ debate.model_a }} | Model B: {{ debate.model_b }}</p>
             <p class="text-muted mb-0">Stav: <span id="debate-status-text">{{ debate.status }}</span>
                <span id="thinking-indicator" class="d-none">
                   probíhá tah <span id="thinking-speaker"></span> <span class="spinner-grow spinner-grow-sm" role="status" aria-hidden="true"></span>
                </span>
             </p>
        </div>

        <div id="conversation" class="conversation-container mb-3">
            <!-- Zde se budou dynamicky vkládat zprávy -->
             {% for msg in initial_messages %}
                {% include 'message_card.html' %}
            {% endfor %}
            <div id="status-line" class="status-indicator mt-auto d-none"></div>
            <div id="error-line" class="error-message mt-auto d-none"></div>
        </div>

        <div id="summaries" class="summaries-container">
            <h2 class="h5">Shrnutí</h2>
             {% if initial_summaries %}
                 {% for summary in initial_summaries %}
                    {% include 'summary_card.html' %}
                {% endfor %}
            {% else %}
                <p id="no-summary-yet" class="text-muted">Shrnutí se vygenerují po skončení debaty.</p>
             {% endif %}
        </div>

         <div class="mt-3 text-center">
            <a href="{{ url_for('index') }}" class="btn btn-secondary">Zpět na hlavní stránku</a>
        </div>

    </div>

    <!-- Šablony pro dynamické vkládání -->
    <template id="message-template">
        {% include 'message_card.html' %}
    </template>
     <template id="summary-template">
        {% include 'summary_card.html' %}
    </template>

    <script>
        const debateId = {{ debate.id }};
        const conversationDiv = document.getElementById('conversation');
        const summariesDiv = document.getElementById('summaries');
        const statusLine = document.getElementById('status-line');
        const errorLine = document.getElementById('error-line');
        const noSummaryYet = document.getElementById('no-summary-yet');
        const debateStatusText = document.getElementById('debate-status-text');
        const thinkingIndicator = document.getElementById('thinking-indicator');
        const thinkingSpeaker = document.getElementById('thinking-speaker');

        // Funkce pro kontrolu, zda je debata dokončena
        function isDebateCompleted() {
            // Kontrola stavu debaty
            const status = debateStatusText.textContent.trim().toLowerCase();
            return status === 'completed' || status === 'error' || status === 'cancelled';
        }

        // Funkce pro scrollování na konec diskuze
        function scrollToBottom() {
            if (conversationDiv) {
                conversationDiv.scrollTop = conversationDiv.scrollHeight;
            }
        }

        // Zavolat scrollování po načtení stránky
        window.addEventListener('load', scrollToBottom);

        // Připojení k SSE streamu pro tuto debatu
        const eventSource = new EventSource(`/stream?channel=debate-${debateId}`);

        console.log(`SSE: Pokus o připojení na kanál: debate-${debateId}`); // DEBUG

        eventSource.onopen = function() { // Přidáno pro potvrzení spojení
            console.log("SSE: Spojení úspěšně otevřeno!");
            if (errorLine) {
                errorLine.textContent = 'Debata se aktualizuje v reálném čase.';
                errorLine.classList.remove('d-none');
                errorLine.style.color = '#007bff'; // Modrá barva pro informaci
            }
        };

        eventSource.onerror = function(err) {
            console.error("SSE: Chyba spojení (onerror):", err); // Logujeme celou chybu
            if(errorLine) {
                 errorLine.textContent = 'Chyba připojení k aktualizacím debaty (onerror).';
                 errorLine.classList.remove('d-none');
                 errorLine.style.color = 'red';
            }
             // Zkusíme nezavírat spojení hned, třeba se obnoví
             // eventSource.close();
        };

        eventSource.onmessage = function(event) {
            console.log("SSE: Událost 'message' přijata! Hrubá data:", event.data); // DEBUG
            try {
                const eventData = JSON.parse(event.data);
                console.log("SSE: Zpracovaná data události:", eventData); // DEBUG
                const message = eventData.data;
                const type = message.event;

                console.log("SSE event received:", type, message.data); // Logování přijatých dat

                errorLine.classList.add('d-none'); // Skryjeme chybu, pokud přišla platná událost

                if (type === 'system_message' || type === 'message') {
                    const template = document.getElementById('message-template').content.cloneNode(true);
                    const card = template.querySelector('.message-card');
                    const speakerSpan = template.querySelector('.speaker');
                    const messageP = template.querySelector('.message-text'); // Aktualizováno na třídu

                    let speakerName = message.data.speaker; // 'ModelA', 'ModelB', 'System'
                    let modelId = message.data.model ? ` (${message.data.model})` : '';
                    let text = message.data.message;

                    if(type === 'system_message'){
                        speakerName = 'System';
                        modelId = ''; // Systém nemá model
                        card.classList.remove('message-ModelA', 'message-ModelB');
                        card.classList.add('message-System');
                    } else {
                         card.classList.add(`message-${speakerName}`); // message-ModelA nebo message-ModelB
                    }

                    speakerSpan.textContent = `${speakerName}${modelId}:`;
                    messageP.innerHTML = text.replace(/\\n/g, '<br>'); // Nahradí nové řádky

                    conversationDiv.insertBefore(template, statusLine); // Vložíme před stavový řádek
                    conversationDiv.scrollTop = conversationDiv.scrollHeight; // Scroll dolů
                    statusLine.classList.add('d-none'); // Skryjeme stavový řádek po zprávě
                    thinkingIndicator.classList.add('d-none'); // Skryjeme indikátor přemýšlení

                } else if (type === 'summary') {
                    if(noSummaryYet) noSummaryYet.classList.add('d-none'); // Skryjeme 'žádné shrnutí'

                    const template = document.getElementById('summary-template').content.cloneNode(true);
                    const title = template.querySelector('h5');
                    const textP = template.querySelector('.summary-text'); // Aktualizováno na třídu

                    title.textContent = `Shrnutí (${message.data.model}):`;
                    textP.innerHTML = message.data.summary.replace(/\n/g, '<br>');

                    summariesDiv.appendChild(template);
                    thinkingIndicator.classList.add('d-none');

                } else if (type === 'status') {
                    // Zobrazíme stavovou zprávu nebo indikátor přemýšlení
                    if(message.data.speaker && message.data.speaker !== 'System') {
                        // Model přemýšlí
                        thinkingSpeaker.textContent = message.data.speaker;
                        thinkingIndicator.classList.remove('d-none');
                        statusLine.classList.add('d-none'); // Skryjeme textový status
                    } else {
                        // Obecný status
                        statusLine.textContent = message.data.message || 'Aktualizace stavu...';
                        statusLine.classList.remove('d-none');
                        thinkingIndicator.classList.add('d-none'); // Skryjeme indikátor přemýšlení
                    }

                } else if (type === 'error') {
                    errorLine.textContent = `Chyba: ${message.data.message}`;
                    errorLine.classList.remove('d-none');
                    errorLine.style.color = 'red';
                    statusLine.classList.add('d-none');
                    thinkingIndicator.classList.add('d-none');

                } else if (type === 'end') {
                    statusLine.textContent = message.data.message || 'Debata skončila.';
                    statusLine.classList.remove('d-none');
                    thinkingIndicator.classList.add('d-none');
                    // Aktualizujeme hlavní stav debaty
                    if(errorLine.classList.contains('d-none')) { // Pokud nebyla chyba
                        debateStatusText.textContent = 'completed'; // Nebo jiný finální stav
                    }
                    eventSource.close(); // Zavřeme spojení po konci
                    console.log("SSE spojení uzavřeno.");
                }

            } catch (e) {
                console.error("Chyba při zpracování SSE události:", e, "Data:", event.data);
            }
        };

    </script>
</body>
</html>
