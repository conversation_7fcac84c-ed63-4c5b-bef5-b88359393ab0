:root {
  --primary-color: #3b82f6;
  --secondary-color: #ef4444;
  --background-color: #f9fafb;
  --card-background: #ffffff;
  --text-color: #1f2937;
  --border-color: #e5e7eb;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
    Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  background-color: var(--background-color);
  color: var(--text-color);
  line-height: 1.6;
}

#root {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
}

.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

header {
  text-align: center;
  margin-bottom: 2rem;
  padding: 1rem;
}

header h1 {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
  color: var(--primary-color);
}

header p {
  font-size: 1.1rem;
  color: #4b5563;
}

.setup-container {
  background-color: var(--card-background);
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  margin-bottom: 2rem;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.form-group {
  margin-bottom: 1.5rem;
  text-align: left;
}

.form-row {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.form-row .form-group {
  flex: 1;
  margin-bottom: 0;
}

label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  font-size: 1rem;
}

input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

.primary-button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 0.375rem;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  display: block;
  margin: 0 auto;
}

.primary-button:hover {
  background-color: #2563eb;
}

.secondary-button {
  background-color: #e5e7eb;
  color: #1f2937;
  border: none;
  border-radius: 0.375rem;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  display: block;
  margin: 2rem auto;
}

.secondary-button:hover {
  background-color: #d1d5db;
}

.debate-container {
  background-color: var(--card-background);
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  margin-bottom: 2rem;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.debate-header {
  margin-bottom: 2rem;
  text-align: center;
}

.debate-header h2 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.experts-info {
  display: flex;
  justify-content: space-around;
  margin-bottom: 1rem;
}

.expert {
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
}

.expert-1 {
  color: var(--primary-color);
  background-color: rgba(59, 130, 246, 0.1);
}

.expert-2 {
  color: var(--secondary-color);
  background-color: rgba(239, 68, 68, 0.1);
}

.expert-label {
  font-weight: 600;
}

.loading {
  text-align: center;
  padding: 2rem;
}

.typing-indicator {
  display: inline-block;
  margin-top: 1rem;
}

.typing-indicator span {
  display: inline-block;
  width: 8px;
  height: 8px;
  background-color: #6b7280;
  border-radius: 50%;
  margin: 0 2px;
  opacity: 0.6;
  animation: typing-bounce 1.4s infinite ease-in-out both;
}

.typing-indicator span:nth-child(1) {
  animation-delay: 0s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing-bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

.messages-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.message {
  padding: 1rem;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.message.expert-1 {
  border-left: 4px solid var(--primary-color);
  background-color: rgba(59, 130, 246, 0.05);
}

.message.expert-2 {
  border-left: 4px solid var(--secondary-color);
  background-color: rgba(239, 68, 68, 0.05);
}

.message-header {
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.message-content {
  line-height: 1.6;
}

footer {
  text-align: center;
  padding: 1rem;
  margin-top: auto;
  color: #6b7280;
  font-size: 0.9rem;
}
