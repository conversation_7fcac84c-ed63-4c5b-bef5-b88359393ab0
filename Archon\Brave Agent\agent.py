"""
Hlavní modul pro AI agenta, k<PERSON><PERSON> zpracovává vyhledávací dotazy.
"""

import asyncio
from typing import Dict, Any, Optional
from dataclasses import dataclass

from .agent_tools import setup_tools, BraveSearchTool
from .agent_prompts import SYSTEM_PROMPT, SEARCH_PROMPT, ERROR_PROMPT

@dataclass
class SearchResult:
    """Třída reprezentující výsledek vyhledávání."""
    query: str
    results: Dict[str, Any]
    error: Optional[str] = None

class SearchAgent:
    """
    Agent pro zpracování vyhledávacích dotazů pomocí Brave API.
    """
    
    def __init__(self):
        self.search_tool = setup_tools()
        
    async def process_query(self, query: str) -> SearchResult:
        """
        Zpracuje vyhledávací dotaz a vrátí výsledky.
        
        Args:
            query: Vyhledávací dotaz od uživatele
            
        Returns:
            SearchResult obsahují<PERSON><PERSON> výsledky nebo chybu
        """
        try:
            results = await self.search_tool.search(query)
            return SearchResult(query=query, results=results)
        except Exception as e:
            error_message = ERROR_PROMPT.format(error=str(e))
            return SearchResult(query=query, results={}, error=error_message)
    
    async def run(self, query: str) -> Dict[str, Any]:
        """
        Spustí agenta s daným dotazem.
        
        Args:
            query: Vyhledávací dotaz
            
        Returns:
            Zpracované výsledky vyhledávání
        """
        result = await self.process_query(query)
        
        if result.error:
            return {"status": "error", "message": result.error}
            
        return {
            "status": "success",
            "query": result.query,
            "results": result.results
        }

async def create_agent() -> SearchAgent:
    """
    Vytvoří a inicializuje novou instanci agenta.
    
    Returns:
        Inicializovaný SearchAgent
    """
    return SearchAgent()

# Pomocná funkce pro synchronní použití
def run_query(query: str) -> Dict[str, Any]:
    """
    Synchronní wrapper pro spuštění dotazu.
    
    Args:
        query: Vyhledávací dotaz
        
    Returns:
        Výsledky vyhledávání
    """
    agent = asyncio.run(create_agent())
    return asyncio.run(agent.run(query)) 