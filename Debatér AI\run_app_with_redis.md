# Spuštění aplikace s Redis serverem

Po instalaci Redis serveru podle návodu v souboru `install_redis.md` můžete spustit aplikaci následujícím způsobem:

## 1. <PERSON><PERSON><PERSON><PERSON><PERSON>, že Redis server bě<PERSON><PERSON>

Otevřete příkazový <PERSON> (cmd) a zadejte:
```
redis-cli ping
```

<PERSON><PERSON><PERSON> byste obdržet odpověď `PONG`. Pokud ne, Redis server neběží a je potřeba ho spustit.

## 2. Spuštění Redis serveru (pokud neběží)

Pokud Redis server neběží, můžete ho spustit pomocí:
```
redis-server
```

Nebo ho spustit jako službu Windows:
```
net start Redis
```

## 3. Spuštění aplikace

Nyní můžete spustit aplikaci:
```
cd llm-debate-app
python app.py
```

## 4. Otevření aplikace v prohlížeči

Otevřete prohlížeč a přejděte na adresu:
```
http://localhost:5000
```

## 5. Sledování debat v reálném čase

Nyní by měly debaty fungovat v reálném čase s využitím Server-Sent Events (SSE) a Redis serveru. Při otevření stránky s debatou by se měly zprávy automaticky objevovat, jak jsou generovány, bez nutnosti obnovovat stránku.

## Řešení problémů

Pokud se stále zobrazuje chyba "Tento web není dostupný", zkontrolujte:

1. Běží Redis server? Ověřte pomocí `redis-cli ping`
2. Běží aplikace? Zkontrolujte, zda v terminálu, kde jste spustili `python app.py`, nejsou chybové hlášky
3. Zkuste restartovat aplikaci
4. Zkuste restartovat Redis server: `net stop Redis` a poté `net start Redis`
