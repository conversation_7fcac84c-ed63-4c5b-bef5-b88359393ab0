import{d as q,aU as ie,a4 as re,u as J,r as p,b8 as xe,l as A,a as u,b1 as Q,h,e as f,w as oe,c as x,k as F,f as d,b as m,i as _,s as le,b9 as Z,z as P,g as W,x as I,ba as se,Z as Se,$ as Ve,af as $e,ap as Te,am as Ee,bb as Be,bc as De,N as R,a6 as Pe,j as Ce,E as Le,H as X,bd as de,B as Ae,K as Fe,U as ze,aR as _e,F as N,m as Ie,D as ae,be as Me}from"./index-jan7QZNA.js";import{s as ue}from"./index-baoyobTy.js";import{s as ce}from"./index-1hBUUM27.js";import{s as Oe,a as He}from"./index-BliD00yU.js";import{a as Ue,_ as je}from"./InfoBox.vue_vue_type_script_setup_true_lang-CKarcGN-.js";import{s as Ne}from"./index-vUyST4l6.js";const Re={key:0},Ke={for:"stake-input",class:"block font-medium mb-1"},qe={key:1},We={key:2},Xe={class:"flex justify-end gap-2"},Ye=q({__name:"ForceEntryForm",props:ie({pair:{type:String,default:""},positionIncrease:{type:Boolean,default:!1}},{modelValue:{type:Boolean},modelModifiers:{}}),emits:["update:modelValue"],setup(e){const t=e,n=re(e,"modelValue"),a=J(),o=p(),r=p(""),i=p(void 0),k=p(void 0),b=p(void 0),B=p(""),S=p(xe.long),v=p("force_entry"),E=[{value:"market",text:"Market"},{value:"limit",text:"Limit"}],M=[{value:"long",text:"Long"},{value:"short",text:"Short"}],V=()=>{var l;return(l=o.value)==null?void 0:l.checkValidity()},c=async()=>{if(!V())return;const w={pair:r.value};i.value&&(w.price=Number(i.value)),B.value&&(w.ordertype=B.value),k.value&&(w.stakeamount=k.value),a.activeBot.botApiVersion>=2.13&&a.activeBot.shortAllowed&&(w.side=S.value),a.activeBot.botApiVersion>=2.16&&v.value&&(w.entry_tag=v.value),b.value&&(w.leverage=b.value),a.activeBot.forceentry(w),await se(),n.value=!1},z=()=>{var w,l,O,D,H,U,j,y;console.log("resetForm"),r.value=t.pair,i.value=void 0,k.value=void 0,B.value=((l=(w=a.activeBot.botState)==null?void 0:w.order_types)==null?void 0:l.forcebuy)||((D=(O=a.activeBot.botState)==null?void 0:O.order_types)==null?void 0:D.force_entry)||((U=(H=a.activeBot.botState)==null?void 0:H.order_types)==null?void 0:U.buy)||((y=(j=a.activeBot.botState)==null?void 0:j.order_types)==null?void 0:y.entry)||"limit"},C=()=>{c()};return(w,l)=>{const O=ce,D=le,H=ue,U=W,j=Q;return u(),A(j,{visible:n.value,"onUpdate:visible":l[9]||(l[9]=y=>n.value=y),header:e.positionIncrease?`Increasing position for ${e.pair}`:"Force entering a trade",modal:"",onShow:z,onHide:z},{footer:h(()=>[f("div",Xe,[m(U,{severity:"secondary",size:"small",onClick:l[8]||(l[8]=y=>n.value=!1)},{default:h(()=>l[16]||(l[16]=[I(" Cancel ")])),_:1}),m(U,{severity:"primary",size:"small",onClick:C},{default:h(()=>l[17]||(l[17]=[I(" Enter Position ")])),_:1})])]),default:h(()=>[f("form",{ref_key:"form",ref:o,class:"space-y-4 md:min-w-[32rem]",onSubmit:oe(c,["prevent"])},[d(a).activeBot.botApiVersion>=2.13&&d(a).activeBot.shortAllowed?(u(),x("div",Re,[l[10]||(l[10]=f("label",{class:"block font-medium mb-1"},"Order direction (Long or Short)",-1)),m(O,{modelValue:d(S),"onUpdate:modelValue":l[0]||(l[0]=y=>_(S)?S.value=y:null),options:M,"allow-empty":!1,"option-label":"text","option-value":"value",size:"small",class:"w-full"},null,8,["modelValue"])])):F("",!0),f("div",null,[l[11]||(l[11]=f("label",{for:"pair-input",class:"block font-medium mb-1"},"Pair",-1)),m(D,{id:"pair-input",modelValue:d(r),"onUpdate:modelValue":l[1]||(l[1]=y=>_(r)?r.value=y:null),disabled:e.positionIncrease,required:"",class:"w-full",onKeydown:Z(C,["enter"]),onFocus:l[2]||(l[2]=y=>y.target.select())},null,8,["modelValue","disabled"])]),f("div",null,[l[12]||(l[12]=f("label",{for:"price-input",class:"block font-medium mb-1"},"Price [optional]",-1)),m(H,{id:"price-input",modelValue:d(i),"onUpdate:modelValue":l[3]||(l[3]=y=>_(i)?i.value=y:null),"show-buttons":"",min:0,"max-fraction-digits":8,step:.1,class:"w-full",onKeydown:Z(C,["enter"])},null,8,["modelValue"])]),f("div",null,[f("label",Ke,"* Stake-amount in "+P(d(a).activeBot.stakeCurrency)+" [optional]",1),m(H,{id:"stake-input",modelValue:d(k),"onUpdate:modelValue":l[4]||(l[4]=y=>_(k)?k.value=y:null),"show-buttons":"",min:0,step:d(a).activeBot.stakeCurrency==="USDT"?10:1,"max-fraction-digits":5,fluid:""},null,8,["modelValue","step"])]),d(a).activeBot.botApiVersion>2.16&&d(a).activeBot.shortAllowed?(u(),x("div",qe,[l[13]||(l[13]=f("label",{for:"leverage-input",class:"block font-medium mb-1"},"Leverage to apply [optional]",-1)),m(H,{id:"leverage-input",modelValue:d(b),"onUpdate:modelValue":l[5]||(l[5]=y=>_(b)?b.value=y:null),"show-buttons":"",min:0,step:1,"max-fraction-digits":1,class:"w-full",onKeydown:Z(C,["enter"])},null,8,["modelValue"])])):F("",!0),f("div",null,[l[14]||(l[14]=f("label",{class:"block text-sm font-medium mb-1"},"OrderType",-1)),m(O,{modelValue:d(B),"onUpdate:modelValue":l[6]||(l[6]=y=>_(B)?B.value=y:null),options:E,"option-label":"text","option-value":"value",size:"small",class:"w-full"},null,8,["modelValue"])]),d(a).activeBot.botApiVersion>1.16?(u(),x("div",We,[l[15]||(l[15]=f("label",{for:"enterTag-input",class:"block text-sm font-medium mb-1"},"* Custom entry tag [optional]",-1)),m(D,{id:"enterTag-input",modelValue:d(v),"onUpdate:modelValue":l[7]||(l[7]=y=>_(v)?v.value=y:null),class:"w-full"},null,8,["modelValue"])])):F("",!0)],544)]),_:1},8,["visible","header"])}}});var Ze=({dt:e})=>`
.p-slider {
    position: relative;
    background: ${e("slider.track.background")};
    border-radius: ${e("slider.track.border.radius")};
}

.p-slider-handle {
    cursor: grab;
    touch-action: none;
    user-select: none;
    display: flex;
    justify-content: center;
    align-items: center;
    height: ${e("slider.handle.height")};
    width: ${e("slider.handle.width")};
    background: ${e("slider.handle.background")};
    border-radius: ${e("slider.handle.border.radius")};
    transition: background ${e("slider.transition.duration")}, color ${e("slider.transition.duration")}, border-color ${e("slider.transition.duration")}, box-shadow ${e("slider.transition.duration")}, outline-color ${e("slider.transition.duration")};
    outline-color: transparent;
}

.p-slider-handle::before {
    content: "";
    width: ${e("slider.handle.content.width")};
    height: ${e("slider.handle.content.height")};
    display: block;
    background: ${e("slider.handle.content.background")};
    border-radius: ${e("slider.handle.content.border.radius")};
    box-shadow: ${e("slider.handle.content.shadow")};
    transition: background ${e("slider.transition.duration")};
}

.p-slider:not(.p-disabled) .p-slider-handle:hover {
    background: ${e("slider.handle.hover.background")};
}

.p-slider:not(.p-disabled) .p-slider-handle:hover::before {
    background: ${e("slider.handle.content.hover.background")};
}

.p-slider-handle:focus-visible {
    box-shadow: ${e("slider.handle.focus.ring.shadow")};
    outline: ${e("slider.handle.focus.ring.width")} ${e("slider.handle.focus.ring.style")} ${e("slider.handle.focus.ring.color")};
    outline-offset: ${e("slider.handle.focus.ring.offset")};
}

.p-slider-range {
    display: block;
    background: ${e("slider.range.background")};
    border-radius: ${e("slider.track.border.radius")};
}

.p-slider.p-slider-horizontal {
    height: ${e("slider.track.size")};
}

.p-slider-horizontal .p-slider-range {
    inset-block-start: 0;
    inset-inline-start: 0;
    height: 100%;
}

.p-slider-horizontal .p-slider-handle {
    inset-block-start: 50%;
    margin-block-start: calc(-1 * calc(${e("slider.handle.height")} / 2));
    margin-inline-start: calc(-1 * calc(${e("slider.handle.width")} / 2));
}

.p-slider-vertical {
    min-height: 100px;
    width: ${e("slider.track.size")};
}

.p-slider-vertical .p-slider-handle {
    inset-inline-start: 50%;
    margin-inline-start: calc(-1 * calc(${e("slider.handle.width")} / 2));
    margin-block-end: calc(-1 * calc(${e("slider.handle.height")} / 2));
}

.p-slider-vertical .p-slider-range {
    inset-block-end: 0;
    inset-inline-start: 0;
    width: 100%;
}
`,Ge={handle:{position:"absolute"},range:{position:"absolute"}},Je={root:function(t){var n=t.instance,a=t.props;return["p-slider p-component",{"p-disabled":a.disabled,"p-invalid":n.$invalid,"p-slider-horizontal":a.orientation==="horizontal","p-slider-vertical":a.orientation==="vertical"}]},range:"p-slider-range",handle:"p-slider-handle"},Qe=Se.extend({name:"slider",style:Ze,classes:Je,inlineStyles:Ge}),et={name:"BaseSlider",extends:Ve,props:{min:{type:Number,default:0},max:{type:Number,default:100},orientation:{type:String,default:"horizontal"},step:{type:Number,default:null},range:{type:Boolean,default:!1},tabindex:{type:Number,default:0},ariaLabelledby:{type:String,default:null},ariaLabel:{type:String,default:null}},style:Qe,provide:function(){return{$pcSlider:this,$parentInstance:this}}};function K(e){"@babel/helpers - typeof";return K=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},K(e)}function tt(e,t,n){return(t=nt(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function nt(e){var t=at(e,"string");return K(t)=="symbol"?t:t+""}function at(e,t){if(K(e)!="object"||!e)return e;var n=e[Symbol.toPrimitive];if(n!==void 0){var a=n.call(e,t);if(K(a)!="object")return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function it(e){return st(e)||lt(e)||ot(e)||rt()}function rt(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function ot(e,t){if(e){if(typeof e=="string")return G(e,t);var n={}.toString.call(e).slice(8,-1);return n==="Object"&&e.constructor&&(n=e.constructor.name),n==="Map"||n==="Set"?Array.from(e):n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?G(e,t):void 0}}function lt(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function st(e){if(Array.isArray(e))return G(e)}function G(e,t){(t==null||t>e.length)&&(t=e.length);for(var n=0,a=Array(t);n<t;n++)a[n]=e[n];return a}var me={name:"Slider",extends:et,inheritAttrs:!1,emits:["change","slideend"],dragging:!1,handleIndex:null,initX:null,initY:null,barWidth:null,barHeight:null,dragListener:null,dragEndListener:null,beforeUnmount:function(){this.unbindDragListeners()},methods:{updateDomData:function(){var t=this.$el.getBoundingClientRect();this.initX=t.left+Be(),this.initY=t.top+De(),this.barWidth=this.$el.offsetWidth,this.barHeight=this.$el.offsetHeight},setValue:function(t){var n,a=t.touches?t.touches[0].pageX:t.pageX,o=t.touches?t.touches[0].pageY:t.pageY;this.orientation==="horizontal"?Ee(this.$el)?n=(this.initX+this.barWidth-a)*100/this.barWidth:n=(a-this.initX)*100/this.barWidth:n=(this.initY+this.barHeight-o)*100/this.barHeight;var r=(this.max-this.min)*(n/100)+this.min;if(this.step){var i=this.range?this.value[this.handleIndex]:this.value,k=r-i;k<0?r=i+Math.ceil(r/this.step-i/this.step)*this.step:k>0&&(r=i+Math.floor(r/this.step-i/this.step)*this.step)}else r=Math.floor(r);this.updateModel(t,r)},updateModel:function(t,n){var a=Math.round(n*100)/100,o;this.range?(o=this.value?it(this.value):[],this.handleIndex==0?(a<this.min?a=this.min:a>=this.max&&(a=this.max),o[0]=a):(a>this.max?a=this.max:a<=this.min&&(a=this.min),o[1]=a)):(a<this.min?a=this.min:a>this.max&&(a=this.max),o=a),this.writeValue(o,t),this.$emit("change",o)},onDragStart:function(t,n){this.disabled||(this.$el.setAttribute("data-p-sliding",!0),this.dragging=!0,this.updateDomData(),this.range&&this.value[0]===this.max?this.handleIndex=0:this.handleIndex=n,t.currentTarget.focus())},onDrag:function(t){this.dragging&&this.setValue(t)},onDragEnd:function(t){this.dragging&&(this.dragging=!1,this.$el.setAttribute("data-p-sliding",!1),this.$emit("slideend",{originalEvent:t,value:this.value}))},onBarClick:function(t){this.disabled||Te(t.target,"data-pc-section")!=="handle"&&(this.updateDomData(),this.setValue(t))},onMouseDown:function(t,n){this.bindDragListeners(),this.onDragStart(t,n)},onKeyDown:function(t,n){switch(this.handleIndex=n,t.code){case"ArrowDown":case"ArrowLeft":this.decrementValue(t,n),t.preventDefault();break;case"ArrowUp":case"ArrowRight":this.incrementValue(t,n),t.preventDefault();break;case"PageDown":this.decrementValue(t,n,!0),t.preventDefault();break;case"PageUp":this.incrementValue(t,n,!0),t.preventDefault();break;case"Home":this.updateModel(t,this.min),t.preventDefault();break;case"End":this.updateModel(t,this.max),t.preventDefault();break}},onBlur:function(t,n){var a,o;(a=(o=this.formField).onBlur)===null||a===void 0||a.call(o,t)},decrementValue:function(t,n){var a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,o;this.range?this.step?o=this.value[n]-this.step:o=this.value[n]-1:this.step?o=this.value-this.step:!this.step&&a?o=this.value-10:o=this.value-1,this.updateModel(t,o),t.preventDefault()},incrementValue:function(t,n){var a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,o;this.range?this.step?o=this.value[n]+this.step:o=this.value[n]+1:this.step?o=this.value+this.step:!this.step&&a?o=this.value+10:o=this.value+1,this.updateModel(t,o),t.preventDefault()},bindDragListeners:function(){this.dragListener||(this.dragListener=this.onDrag.bind(this),document.addEventListener("mousemove",this.dragListener)),this.dragEndListener||(this.dragEndListener=this.onDragEnd.bind(this),document.addEventListener("mouseup",this.dragEndListener))},unbindDragListeners:function(){this.dragListener&&(document.removeEventListener("mousemove",this.dragListener),this.dragListener=null),this.dragEndListener&&(document.removeEventListener("mouseup",this.dragEndListener),this.dragEndListener=null)},rangeStyle:function(){if(this.range){var t=this.rangeEndPosition>this.rangeStartPosition?this.rangeEndPosition-this.rangeStartPosition:this.rangeStartPosition-this.rangeEndPosition,n=this.rangeEndPosition>this.rangeStartPosition?this.rangeStartPosition:this.rangeEndPosition;return this.horizontal?{"inset-inline-start":n+"%",width:t+"%"}:{bottom:n+"%",height:t+"%"}}else return this.horizontal?{width:this.handlePosition+"%"}:{height:this.handlePosition+"%"}},handleStyle:function(){return this.horizontal?{"inset-inline-start":this.handlePosition+"%"}:{bottom:this.handlePosition+"%"}},rangeStartHandleStyle:function(){return this.horizontal?{"inset-inline-start":this.rangeStartPosition+"%"}:{bottom:this.rangeStartPosition+"%"}},rangeEndHandleStyle:function(){return this.horizontal?{"inset-inline-start":this.rangeEndPosition+"%"}:{bottom:this.rangeEndPosition+"%"}}},computed:{value:function(){var t;if(this.range){var n,a,o,r;return[(n=(a=this.d_value)===null||a===void 0?void 0:a[0])!==null&&n!==void 0?n:this.min,(o=(r=this.d_value)===null||r===void 0?void 0:r[1])!==null&&o!==void 0?o:this.max]}return(t=this.d_value)!==null&&t!==void 0?t:this.min},horizontal:function(){return this.orientation==="horizontal"},vertical:function(){return this.orientation==="vertical"},handlePosition:function(){return this.value<this.min?0:this.value>this.max?100:(this.value-this.min)*100/(this.max-this.min)},rangeStartPosition:function(){return this.value&&this.value[0]!==void 0?this.value[0]<this.min?0:(this.value[0]-this.min)*100/(this.max-this.min):0},rangeEndPosition:function(){return this.value&&this.value.length===2&&this.value[1]!==void 0?this.value[1]>this.max?100:(this.value[1]-this.min)*100/(this.max-this.min):100},dataP:function(){return $e(tt({},this.orientation,this.orientation))}}},dt=["data-p"],ut=["data-p"],ct=["tabindex","aria-valuemin","aria-valuenow","aria-valuemax","aria-labelledby","aria-label","aria-orientation","data-p"],mt=["tabindex","aria-valuemin","aria-valuenow","aria-valuemax","aria-labelledby","aria-label","aria-orientation","data-p"],ft=["tabindex","aria-valuemin","aria-valuenow","aria-valuemax","aria-labelledby","aria-label","aria-orientation","data-p"];function vt(e,t,n,a,o,r){return u(),x("div",R({class:e.cx("root"),onClick:t[18]||(t[18]=function(){return r.onBarClick&&r.onBarClick.apply(r,arguments)})},e.ptmi("root"),{"data-p-sliding":!1,"data-p":r.dataP}),[f("span",R({class:e.cx("range"),style:[e.sx("range"),r.rangeStyle()]},e.ptm("range"),{"data-p":r.dataP}),null,16,ut),e.range?F("",!0):(u(),x("span",R({key:0,class:e.cx("handle"),style:[e.sx("handle"),r.handleStyle()],onTouchstartPassive:t[0]||(t[0]=function(i){return r.onDragStart(i)}),onTouchmovePassive:t[1]||(t[1]=function(i){return r.onDrag(i)}),onTouchend:t[2]||(t[2]=function(i){return r.onDragEnd(i)}),onMousedown:t[3]||(t[3]=function(i){return r.onMouseDown(i)}),onKeydown:t[4]||(t[4]=function(i){return r.onKeyDown(i)}),onBlur:t[5]||(t[5]=function(i){return r.onBlur(i)}),tabindex:e.tabindex,role:"slider","aria-valuemin":e.min,"aria-valuenow":e.d_value,"aria-valuemax":e.max,"aria-labelledby":e.ariaLabelledby,"aria-label":e.ariaLabel,"aria-orientation":e.orientation},e.ptm("handle"),{"data-p":r.dataP}),null,16,ct)),e.range?(u(),x("span",R({key:1,class:e.cx("handle"),style:[e.sx("handle"),r.rangeStartHandleStyle()],onTouchstartPassive:t[6]||(t[6]=function(i){return r.onDragStart(i,0)}),onTouchmovePassive:t[7]||(t[7]=function(i){return r.onDrag(i)}),onTouchend:t[8]||(t[8]=function(i){return r.onDragEnd(i)}),onMousedown:t[9]||(t[9]=function(i){return r.onMouseDown(i,0)}),onKeydown:t[10]||(t[10]=function(i){return r.onKeyDown(i,0)}),onBlur:t[11]||(t[11]=function(i){return r.onBlur(i,0)}),tabindex:e.tabindex,role:"slider","aria-valuemin":e.min,"aria-valuenow":e.d_value?e.d_value[0]:null,"aria-valuemax":e.max,"aria-labelledby":e.ariaLabelledby,"aria-label":e.ariaLabel,"aria-orientation":e.orientation},e.ptm("startHandler"),{"data-p":r.dataP}),null,16,mt)):F("",!0),e.range?(u(),x("span",R({key:2,class:e.cx("handle"),style:[e.sx("handle"),r.rangeEndHandleStyle()],onTouchstartPassive:t[12]||(t[12]=function(i){return r.onDragStart(i,1)}),onTouchmovePassive:t[13]||(t[13]=function(i){return r.onDrag(i)}),onTouchend:t[14]||(t[14]=function(i){return r.onDragEnd(i)}),onMousedown:t[15]||(t[15]=function(i){return r.onMouseDown(i,1)}),onKeydown:t[16]||(t[16]=function(i){return r.onKeyDown(i,1)}),onBlur:t[17]||(t[17]=function(i){return r.onBlur(i,1)}),tabindex:e.tabindex,role:"slider","aria-valuemin":e.min,"aria-valuenow":e.d_value?e.d_value[1]:null,"aria-valuemax":e.max,"aria-labelledby":e.ariaLabelledby,"aria-label":e.ariaLabel,"aria-orientation":e.orientation},e.ptm("endHandler"),{"data-p":r.dataP}),null,16,ft)):F("",!0)],16,dt)}me.render=vt;const pt={class:"mb-4"},ht={class:"mb-2"},bt={for:"stake-input",class:"block font-medium mb-1"},gt={class:"text-sm italic ml-1"},yt={class:"space-y-2"},kt={class:"flex justify-end gap-2"},wt=q({__name:"ForceExitForm",props:ie({trade:{type:Object,required:!0},stakeCurrencyDecimals:{type:Number,required:!0}},{modelValue:{type:Boolean},modelModifiers:{}}),emits:["update:modelValue"],setup(e){const t=e,n=re(e,"modelValue"),a=J(),o=p(),r=p(void 0),i=p("limit"),k=()=>{var c;return(c=o.value)==null?void 0:c.checkValidity()};async function b(){if(!k())return;const V={tradeid:String(t.trade.trade_id)};i.value&&(V.ordertype=i.value),r.value&&(V.amount=r.value),await se(),a.activeBot.forceexit(V),n.value=!1}function B(){var V,c,z,C;r.value=t.trade.amount,i.value=((c=(V=a.activeBot.botState)==null?void 0:V.order_types)==null?void 0:c.force_exit)||((C=(z=a.activeBot.botState)==null?void 0:z.order_types)==null?void 0:C.exit)||"limit"}function S(){b()}const v=Pe(r,250,{maxWait:500}),E=Ce(()=>v.value&&t.trade.current_rate?`~${Le(v.value*t.trade.current_rate,t.trade.quote_currency||"",t.stakeCurrencyDecimals)} (Estimated value) `:""),M=[{value:"market",text:"Market"},{value:"limit",text:"Limit"}];return(V,c)=>{const z=ue,C=me,w=ce,l=W,O=Q;return u(),A(O,{visible:n.value,"onUpdate:visible":c[4]||(c[4]=D=>n.value=D),header:"Force exiting a trade",modal:"",onShow:B,onHide:B},{footer:h(()=>[f("div",kt,[m(l,{severity:"secondary",size:"small",onClick:c[3]||(c[3]=D=>n.value=!1)},{default:h(()=>c[7]||(c[7]=[I("Cancel")])),_:1}),m(l,{severity:"primary",size:"small",onClick:S},{default:h(()=>c[8]||(c[8]=[I("Exit Position")])),_:1})])]),default:h(()=>[f("form",{ref_key:"form",ref:o,class:"space-y-4 md:min-w-[32rem]",onSubmit:oe(b,["prevent"])},[f("div",pt,[f("p",ht,[f("span",null,"Exiting Trade #"+P(e.trade.trade_id)+" "+P(e.trade.pair)+".",1),c[5]||(c[5]=f("br",null,null,-1)),f("span",null,"Currently owning "+P(e.trade.amount)+" "+P(e.trade.base_currency),1)])]),f("div",null,[f("label",bt,[I(" Amount in "+P(e.trade.base_currency)+" [optional] ",1),f("span",gt,P(E.value),1)]),f("div",yt,[m(z,{id:"stake-input",modelValue:r.value,"onUpdate:modelValue":c[0]||(c[0]=D=>r.value=D),min:0,max:e.trade.amount,"use-grouping":!1,step:1e-6,"max-fraction-digits":8,class:"w-full","show-buttons":""},null,8,["modelValue","max"]),m(C,{modelValue:r.value,"onUpdate:modelValue":c[1]||(c[1]=D=>r.value=D),min:0,max:e.trade.amount,step:1e-6,class:"w-full"},null,8,["modelValue","max"])])]),f("div",null,[c[6]||(c[6]=f("label",{class:"block font-medium mb-1"},"*OrderType",-1)),m(w,{modelValue:i.value,"onUpdate:modelValue":c[2]||(c[2]=D=>i.value=D),options:M,"allow-empty":!1,"option-label":"text","option-value":"value",size:"small",class:"w-full"},null,8,["modelValue"])])],544)]),_:1},8,["visible"])}}}),xt={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"};function St(e,t){return u(),x("svg",xt,t[0]||(t[0]=[f("path",{fill:"currentColor",d:"M2 12c0 5 4 9 9 9c2.4 0 4.7-.9 6.4-2.6l-1.5-1.5c-1.3 1.4-3 2.1-4.9 2.1c-6.2 0-9.4-7.5-4.9-11.9S18 5.8 18 12h-3l4 4h.1l3.9-4h-3c0-5-4-9-9-9s-9 4-9 9m8 3h2v2h-2zm0-8h2v6h-2z"},null,-1)]))}const Vt=X({name:"mdi-reload-alert",render:St}),$t={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"};function Tt(e,t){return u(),x("svg",$t,t[0]||(t[0]=[f("path",{fill:"currentColor",d:"M18 11h-3v3h-2v-3h-3V9h3V6h2v3h3m2-5v12H8V4zm0-2H8c-1.1 0-2 .9-2 2v12a2 2 0 0 0 2 2h12c1.11 0 2-.89 2-2V4a2 2 0 0 0-2-2M4 6H2v14a2 2 0 0 0 2 2h14v-2H4z"},null,-1)]))}const Et=X({name:"mdi-plus-box-multiple-outline",render:Tt}),Bt={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"};function Dt(e,t){return u(),x("svg",Bt,t[0]||(t[0]=[f("path",{fill:"currentColor",d:"M4 20h14v2H4a2 2 0 0 1-2-2V6h2zM20.22 2H7.78C6.8 2 6 2.8 6 3.78v12.44C6 17.2 6.8 18 7.78 18h12.44c.98 0 1.78-.8 1.78-1.78V3.78C22 2.8 21.2 2 20.22 2M19 13.6L17.6 15L14 11.4L10.4 15L9 13.6l3.6-3.6L9 6.4L10.4 5L14 8.6L17.6 5L19 6.4L15.4 10z"},null,-1)]))}const Pt=X({name:"mdi-close-box-multiple",render:Dt}),Ct={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"};function Lt(e,t){return u(),x("svg",Ct,t[0]||(t[0]=[f("path",{fill:"currentColor",d:"M19 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2m-3.4 14L12 13.4L8.4 17L7 15.6l3.6-3.6L7 8.4L8.4 7l3.6 3.6L15.6 7L17 8.4L13.4 12l3.6 3.6z"},null,-1)]))}const At=X({name:"mdi-close-box",render:Lt}),Ft={class:"flex flex-col gap-1"},zt=q({__name:"TradeActions",props:{botApiVersion:{type:Number,default:1},trade:{type:Object,required:!0},enableForceEntry:{type:Boolean,default:!1}},emits:["forceExit","forceExitPartial","cancelOpenOrder","reloadTrade","deleteTrade","forceEntry"],setup(e){return(t,n)=>{const a=At,o=W,r=Pt,i=de,k=Et,b=Vt,B=Ae;return u(),x("div",Ft,[e.botApiVersion<=1.1?(u(),A(o,{key:0,class:"justify-start!",size:"small",severity:"secondary",title:"Forceexit",label:"Forceexit",onClick:n[0]||(n[0]=S=>t.$emit("forceExit",e.trade))},{icon:h(()=>[m(a)]),_:1})):F("",!0),e.botApiVersion>1.1?(u(),A(o,{key:1,size:"small",class:"justify-start!",severity:"secondary",title:"Forceexit limit",label:"Forceexit limit",onClick:n[1]||(n[1]=S=>t.$emit("forceExit",e.trade,"limit"))},{icon:h(()=>[m(a)]),_:1})):F("",!0),e.botApiVersion>1.1?(u(),A(o,{key:2,class:"justify-start!",size:"small",severity:"secondary",title:"Forceexit market",label:"Forceexit market",onClick:n[2]||(n[2]=S=>t.$emit("forceExit",e.trade,"market"))},{icon:h(()=>[m(a)]),_:1})):F("",!0),e.botApiVersion>2.16?(u(),A(o,{key:3,class:"justify-start!",size:"small",severity:"secondary",title:"Forceexit partial",label:"Forceexit partial",onClick:n[3]||(n[3]=S=>t.$emit("forceExitPartial",e.trade))},{icon:h(()=>[m(r)]),_:1})):F("",!0),e.botApiVersion>=2.24&&(e.trade.open_order_id||e.trade.has_open_orders)?(u(),A(o,{key:4,class:"justify-start!",size:"small",severity:"secondary",title:"Cancel open orders",label:"Cancel open orders",onClick:n[4]||(n[4]=S=>t.$emit("cancelOpenOrder",e.trade))},{icon:h(()=>[m(i)]),_:1})):F("",!0),e.enableForceEntry?(u(),A(o,{key:5,class:"justify-start!",size:"small",severity:"secondary",title:"Increase position",label:"Increase position",onClick:n[5]||(n[5]=S=>t.$emit("forceEntry",e.trade))},{icon:h(()=>[m(k)]),_:1})):F("",!0),e.botApiVersion>=2.28?(u(),A(o,{key:6,class:"justify-start!",size:"small",severity:"secondary",title:"Reload",label:"Reload",onClick:n[6]||(n[6]=S=>t.$emit("reloadTrade",e.trade))},{icon:h(()=>[m(b)]),_:1})):F("",!0),m(o,{class:"justify-start!",size:"small",severity:"secondary",title:"Delete trade",label:"Delete trade",onClick:n[7]||(n[7]=S=>t.$emit("deleteTrade",e.trade))},{icon:h(()=>[m(B)]),_:1})])}}}),_t={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"};function It(e,t){return u(),x("svg",_t,t[0]||(t[0]=[f("path",{fill:"currentColor",d:"M10 9a1 1 0 0 1 1-1a1 1 0 0 1 1 1v4.47l1.21.13l4.94 2.19c.53.24.85.77.85 1.35v4.36c-.03.82-.68 1.47-1.5 1.5H11c-.38 0-.74-.15-1-.43l-4.9-4.2l.74-.77c.19-.21.46-.32.74-.32h.22L10 19zm1-4a4 4 0 0 1 4 4c0 1.5-.8 2.77-2 3.46v-1.22c.61-.55 1-1.35 1-2.24a3 3 0 0 0-3-3a3 3 0 0 0-3 3c0 .89.39 1.69 1 2.24v1.22C7.8 11.77 7 10.5 7 9a4 4 0 0 1 4-4"},null,-1)]))}const Mt=X({name:"mdi-gesture-tap",render:It}),Ot=q({__name:"TradeActionsPopover",props:{trade:{type:Object,required:!0},id:{type:Number,required:!0},botApiVersion:{type:Number,required:!0},enableForceEntry:{type:Boolean,default:!1}},emits:["forceExit","forceExitPartial","cancelOpenOrder","reloadTrade","deleteTrade","forceEntry"],setup(e,{emit:t}){const n=t,a=p(!1);function o(v,E=void 0){a.value=!1,n("forceExit",v,E)}function r(v){a.value=!1,n("forceExitPartial",v)}function i(v){a.value=!1,n("cancelOpenOrder",v)}function k(v){a.value=!1,n("reloadTrade",v)}function b(v){a.value=!1,n("deleteTrade",v)}function B(v){a.value=!1,n("forceEntry",v)}const S=p(null);return(v,E)=>{var C;const M=Mt,V=W,c=zt,z=de;return u(),x("div",null,[m(V,{id:`btn-actions-${e.id}`,class:"btn-xs",size:"small",severity:"secondary",title:"Actions",onClick:(C=d(S))==null?void 0:C.toggle},{default:h(()=>[m(M)]),_:1},8,["id","onClick"]),m(d(Ne),{ref_key:"popover",ref:S,target:`btn-actions-${e.id}`,title:`Actions for ${e.trade.pair}`,triggers:"manual",placement:"left"},{default:h(()=>{var w;return[m(c,{trade:e.trade,"bot-api-version":e.botApiVersion,"enable-force-entry":e.enableForceEntry,onForceExit:o,onForceExitPartial:r,onDeleteTrade:E[0]||(E[0]=l=>b(e.trade)),onCancelOpenOrder:i,onReloadTrade:k,onForceEntry:B},null,8,["trade","bot-api-version","enable-force-entry"]),m(V,{class:"mt-1 w-full text-start",size:"small",severity:"secondary",label:"Close Actions menu",onClick:(w=d(S))==null?void 0:w.hide},{icon:h(()=>[m(z,{class:"me-1"})]),_:1},8,["onClick"])]}),_:1},8,["target","title"])])}}}),Ht={class:"h-full overflow-auto w-full"},Ut={class:"flex justify-end gap-2 p-2"},Yt=q({__name:"TradeList",props:{trades:{required:!0,type:Array},title:{default:"Trades",type:String},stakeCurrency:{required:!1,default:"",type:String},activeTrades:{default:!1,type:Boolean},showFilter:{default:!1,type:Boolean},multiBotView:{default:!1,type:Boolean},emptyText:{default:"No Trades to show.",type:String}},setup(e){const t=e,n=J(),a=Me(),o=Fe(),r=p(1),i=p(),k=p(""),b=p({}),B=t.activeTrades?200:15,S=p(),v=p(!1),E=p(!1),M=p(""),V=p(null),c=p({visible:!1,trade:{}});function z(s){return ae(s,n.activeBot.stakeCurrencyDecimals)}const C=p([{field:"trade_id",header:"ID"},{field:"pair",header:"Pair"},{field:"amount",header:"Amount"},t.activeTrades?{field:"stake_amount",header:"Stake amount"}:{field:"max_stake_amount",header:"Total stake amount"},{field:"open_rate",header:"Open rate"},{field:t.activeTrades?"current_rate":"close_rate",header:t.activeTrades?"Current rate":"Close rate"},{field:"profit",header:t.activeTrades?"Current profit %":"Profit %"},{field:"open_timestamp",header:"Open date"},...t.activeTrades?[{field:"actions",header:""}]:[{field:"close_timestamp",header:"Close date"},{field:"exit_reason",header:"Close Reason"}]]);t.multiBotView&&C.value.unshift({field:"botName",header:"Bot"});const w=p(void 0);function l(s,g=void 0){b.value=s,V.value=1,M.value=`Really exit trade ${s.trade_id} (Pair ${s.pair}) using ${g} Order?`,w.value=g,o.confirmDialog===!0?E.value=!0:O()}function O(){if(V.value===0){const s={tradeid:String(b.value.trade_id),botId:b.value.botId};n.deleteTradeMulti(s).catch(g=>console.log(g.response))}if(V.value===1){const s={tradeid:String(b.value.trade_id),botId:b.value.botId};w.value&&(s.ordertype=w.value),n.forceSellMulti(s).then(g=>console.log(g)).catch(g=>console.log(g.response))}if(V.value===3){const s={tradeid:String(b.value.trade_id),botId:b.value.botId};n.cancelOpenOrderMulti(s)}w.value=void 0,E.value=!1}function D(s){M.value=`Really delete trade ${s.trade_id} (Pair ${s.pair})?`,V.value=0,b.value=s,E.value=!0}function H(s){b.value=s,v.value=!0}function U(s){M.value=`Cancel open order for trade ${s.trade_id} (Pair ${s.pair})?`,b.value=s,V.value=3,E.value=!0}function j(s){n.reloadTradeMulti({tradeid:String(s.trade_id),botId:s.botId})}function y(s){c.value.trade=s,c.value.visible=!0}const fe=({data:s,index:g})=>{t.multiBotView&&n.selectedBot!==s.botId&&n.selectBot(s.botId),s&&s.trade_id!==n.activeBot.detailTradeId?(n.activeBot.setDetailTrade(s),i.value=g,t.multiBotView&&a.push({name:"Freqtrade Trading"})):(n.activeBot.setDetailTrade(null),i.value=void 0)};return ze(()=>n.activeBot.detailTradeId,s=>{t.trades.findIndex(Y=>Y.trade_id===s)<0&&(i.value=void 0)}),(s,g)=>{var ne;const Y=Ot,ve=Ue,ee=je,pe=He,he=le,be=Oe,ge=wt,ye=Ye,te=W,ke=Q;return u(),x("div",Ht,[m(be,{ref_key:"tradesTable",ref:S,selection:d(i),"onUpdate:selection":g[1]||(g[1]=T=>_(i)?i.value=T:null),value:e.trades.filter(T=>{var $,L;return T.pair.toLowerCase().includes(d(k).toLowerCase())||(($=T.exit_reason)==null?void 0:$.toLowerCase().includes(d(k).toLowerCase()))||((L=T.enter_tag)==null?void 0:L.toLowerCase().includes(d(k).toLowerCase()))}),rows:d(B),paginator:!e.activeTrades,first:(d(r)-1)*d(B),"selection-mode":"single","data-key":"botTradeId",class:"text-center",size:"small",scrollable:!0,"scroll-height":"flex",onRowClick:fe},_e({empty:h(()=>[I(P(e.emptyText),1)]),default:h(()=>[(u(!0),x(N,null,Ie(d(C),T=>(u(),A(pe,{key:T.field,field:T.field,header:T.header},{body:h(({data:$,field:L,index:we})=>[L==="trade_id"?(u(),x(N,{key:0},[I(P($.trade_id)+" "+P(d(n).activeBot.botApiVersion>2&&$.trading_mode!=="spot"?($.trade_id?"| ":"")+($.is_short?"Short":"Long"):""),1)],64)):L==="pair"?(u(),x(N,{key:1},[I(P(`${$.pair}${$.open_order_id||$.has_open_orders?"*":""}`),1)],64)):L==="actions"?(u(),A(Y,{key:2,id:we,"enable-force-entry":d(n).activeBot.botState.force_entry_enable,trade:$,"bot-api-version":d(n).activeBot.botApiVersion,onDeleteTrade:jt=>D($),onForceExit:l,onForceExitPartial:H,onCancelOpenOrder:U,onReloadTrade:j,onForceEntry:y},null,8,["id","enable-force-entry","trade","bot-api-version","onDeleteTrade"])):L==="stake_amount"?(u(),x(N,{key:3},[I(P(z($.stake_amount))+" "+P($.trading_mode!=="spot"?`(${$.leverage}x)`:""),1)],64)):L==="open_rate"||L==="current_rate"||L==="close_rate"?(u(),x(N,{key:4},[I(P(("formatPrice"in s?s.formatPrice:d(ae))($[L])),1)],64)):L==="profit"?(u(),A(ve,{key:5,trade:$},null,8,["trade"])):L==="open_timestamp"?(u(),A(ee,{key:6,date:$.open_timestamp},null,8,["date"])):L==="close_timestamp"?(u(),A(ee,{key:7,date:$.close_timestamp??0},null,8,["date"])):(u(),x(N,{key:8},[I(P($[L]),1)],64))]),_:2},1032,["field","header"]))),128))]),_:2},[e.showFilter?{name:"header",fn:h(()=>[f("div",Ut,[m(he,{modelValue:d(k),"onUpdate:modelValue":g[0]||(g[0]=T=>_(k)?k.value=T:null),placeholder:"Filter",class:"w-64",size:"small"},null,8,["modelValue"])])]),key:"0"}:void 0]),1032,["selection","value","rows","paginator","first"]),e.activeTrades?(u(),A(ge,{key:0,modelValue:d(v),"onUpdate:modelValue":g[2]||(g[2]=T=>_(v)?v.value=T:null),trade:d(b),"stake-currency-decimals":d(n).activeBot.botState.stake_currency_decimals??3},null,8,["modelValue","trade","stake-currency-decimals"])):F("",!0),m(ye,{modelValue:d(c).visible,"onUpdate:modelValue":g[3]||(g[3]=T=>d(c).visible=T),pair:(ne=d(c).trade)==null?void 0:ne.pair,"position-increase":""},null,8,["modelValue","pair"]),m(ke,{visible:d(E),"onUpdate:visible":g[5]||(g[5]=T=>_(E)?E.value=T:null),modal:!0,header:"Exit trade"},{footer:h(()=>[m(te,{label:"Cancel",onClick:g[4]||(g[4]=T=>E.value=!1)}),m(te,{label:"Confirm",severity:"danger",onClick:O})]),default:h(()=>[f("p",null,P(d(M)),1)]),_:1},8,["visible"])])}}});export{Pt as _,Et as a,Ye as b,Yt as c};
//# sourceMappingURL=TradeList.vue_vue_type_script_setup_true_lang-BoGONby_.js.map
