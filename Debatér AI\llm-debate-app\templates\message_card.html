<!-- templates/message_card.html -->
{% if msg is defined %}
    {# Renderování z počáteč<PERSON><PERSON><PERSON> (cyklus initial_messages) #}
    {% set speaker = msg.speaker %}
    {% set model = msg.model_used %}
    {% set text = msg.message_text %}
    {% set card_class = 'message-' + speaker %}
{% elif message is defined and message.data is defined %}
    {# Renderování z SSE události (message.data obsahuje data události) #}
    {% set speaker = message.data.speaker %}
    {% set model = message.data.model %}
    {% set text = message.data.message %}
    {% set card_class = 'message-' + speaker if speaker != 'System' else 'message-System' %}
{% else %}
    {# Fallback - nemělo by nastat, ale pro jistotu #}
    {% set speaker = 'Neznámý' %}
    {% set model = '' %}
    {% set text = 'Chybná data zprávy.' %}
    {% set card_class = 'message-System bg-warning' %} {# Z<PERSON>ýrazn<PERSON><PERSON> jako chybu #}
{% endif %}

<div class="message-card {{ card_class }}">
    <span class="speaker">{{ speaker }}{% if model %} ({{ model }}){% endif %}:</span>
    <p class="message-text">{{ text | replace('\n', '<br>') | safe }}</p>
</div>