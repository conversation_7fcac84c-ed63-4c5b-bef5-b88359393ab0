Manuál: <PERSON>ak Vytvořit AI Agenty s n8n a MCP (Model Context Protocol)
Cíl: <PERSON>u<PERSON><PERSON> se, jak kombinovat n8n (vizuální automatizační nástroj) a MCP (protokol pro komunikaci s AI modely a nástroji) k vytváření vý<PERSON>ý<PERSON>, hostovaných AI agentů, k<PERSON><PERSON><PERSON> mohou interagovat s různými nástroji a službami.
Klíčové Technologie:
n8n: Open-source platforma pro automatizaci pracovních postupů s vizuálním rozhraním.
MCP (Model Context Protocol): Protokol umožňující AI modelům (jako Claude) interagovat s externími nástro<PERSON> (tools).
Hostinger (nebo jiný VPS): Služba pro hostování vaší n8n instance, aby agenti mohli běžet 24/7.
<PERSON> (nebo jiný MCP klient): <PERSON><PERSON><PERSON>ace (jako <PERSON> Desktop nebo Cursor) sloužící jako r<PERSON> pro interakci s AI agentem, kter<PERSON> může využívat MCP nástroje.
Docker: Nástroj pro kontejnerizaci, použ<PERSON><PERSON><PERSON> k běhu a aktualizaci n8n.
Google Cloud Platform (GCP): Pro nastavení OAuth přihlašovacích údajů pro Google Sheets.
OpenAI API: Pro připojení chatovacího modelu (např. GPT-4.1) k AI agentovi v n8n.
Krok 1: Příprava Prostředí (Hosting a Aktualizace n8n)
Největší chybou je stavět agenty lokálně a nenasadit je. Potřebujete, aby běželi 24/7.
Získejte VPS (Virtuální Privátní Server):
Video doporučuje Hostinger (je sponzorem).
Použijte odkaz z popisku videa a kód DAVID pro slevu.
Vyberte plán (video zmiňuje KVM 2 jako dostatečný).
Dokončete nákup.
Nastavte Hostinger VPS pro n8n:
Po nákupu budete přesměrováni na průvodce nastavením.
Klikněte na "Start Now".
Vyberte lokaci serveru nejblíže vám.
Vyhledejte a vyberte n8n šablonu (template) pro instalaci OS.
Nastavte silné root heslo pro váš VPS.
Dokončete nastavení ("Finish setup"). Počkejte pár minut, než se VPS připraví.
Aktualizujte n8n na Nejnovější Verzi (pro podporu MCP):
V Hostinger panelu přejděte do správy vašeho VPS.
Otevřete Browser Terminal.
Instalujte/Aktualizujte Docker: (I když šablona n8n může Docker obsahovat, je dobré mít nejnovější verzi. Pokud už je nainstalován, tento krok může varovat, ale měl by proběhnout.)
curl -fsSL https://get.docker.com | sh
Use code with caution.
Bash
(Počkejte ~20 sekund, než skript začne instalaci.)
Povolte a spusťte službu Docker:
systemctl enable --now docker
Use code with caution.
Bash
Stáhněte nejnovější n8n image:
docker compose pull n8n
Use code with caution.
Bash
Zastavte a odeberte starý n8n kontejner:
docker compose down
Use code with caution.
Bash
Spusťte nový n8n kontejner s nejnovější verzí: ( -d spustí na pozadí)
docker compose up -d
Use code with caution.
Bash
Ověřte, že n8n běží:
docker compose ps
Use code with caution.
Bash
(Měli byste vidět běžící kontejnery pro n8n a pravděpodobně traefik.)
Vyčistěte terminál (volitelné):
clear
Use code with caution.
Bash
Přístup k n8n:
V Hostinger panelu u vašeho VPS klikněte na "Manage App".
Otevře se přihlašovací stránka vaší n8n instance. Pokud ji používáte poprvé, vytvořte si účet majitele (owner account). Jinak se přihlaste.
Krok 2: Vytvoření Hlavního MCP Server Workflow v n8n
Tento workflow zpřístupní nástroje z n8n pro externí MCP klienty (jako Claude Desktop).
V n8n klikněte na + -> "Workflow" pro vytvoření nového workflow.
Pojmenujte ho, např. "main n8n MCP".
Klikněte na + pro přidání prvního kroku (triggeru).
Vyhledejte a přidejte node MCP Server Trigger.
V nastavení triggeru přepněte na záložku Production URL. Zkopírujte tuto URL – budete ji potřebovat později pro Claude Desktop.
Klikněte na + pod výstupem Tools z MCP Server Triggeru.
Přidejte jednoduchý nástroj pro testování, např. node Calculator. Nepotřebuje žádné nastavení.
Znovu klikněte na + pod výstupem Tools a přidejte node Call n8n Workflow Tool.
Přejmenujte tento node na něco smysluplného, co reprezentuje, co bude dělat, např. "google_sheets".
Do pole Name (v nastavení node) napište také google_sheets (toto jméno uvidí Claude).
Do pole Description napište jasný popis pro AI, co tento nástroj dělá, např.: Useful for updating the Google Sheets with client data. The input to this tool should be a single clear sentence telling the google sheets ai agent exactly what to add. (Vyhněte se speciálním znakům jako uvozovky).
Pole Workflow zatím nechte prázdné (vybereme ho později).
V sekci Workflow Inputs klikněte na + Add workflow input to send.
Pojmenujte vstupní pole instructions (nebo input, jak je ukázáno později).
Klikněte na ikonku "ozubeného kola/blesku" vedle pole a vyberte Define automatically by the model. Tím umožníte AI (Claude) dynamicky vyplnit tento vstup. Zkopírujte popis z hlavního pole Description i sem.
Uložte workflow! (Klikněte na Save vpravo nahoře).
Aktivujte workflow! (Přepněte přepínač nahoře z Inactive na Active).
Krok 3: Vytvoření Workflow Google Sheets Agenta v n8n
Tento workflow bude specializovaným agentem pro práci s Google Sheets, volaným z hlavního MCP workflow.
Vytvořte nový workflow ( + -> "Workflow").
Pojmenujte ho, např. "google sheets agent".
Přidejte trigger When Executed by Another Workflow.
V nastavení triggeru:
Input Data Mode: Změňte na Define using fields below.
Klikněte na Add Field.
Name: Pojmenujte pole instructions (nebo input, musí odpovídat názvu z Kroku 2.8).
Type: Nastavte na String.
Přidejte node AI Agent.
Připojte Chat Model:
Klikněte na + pod vstupem Chat Model* AI Agenta.
Vyhledejte a přidejte OpenAI Chat Model (nebo jiný model).
Credential to connect with: Klikněte na tužku -> "Create New Credential".
Vložte váš OpenAI API Key. (Pokud ho nemáte, video ukazuje, jak se zeptat ChatGPT nebo Google, jak ho získat: jděte na platform.openai.com, přihlaste se, klikněte na ikonu profilu -> View API keys -> Create new secret key). Upozornění: S API klíčem zacházejte jako s heslem, nesdílejte ho!
Uložte přihlašovací údaje (credential).
Vyberte model (např. gpt-4.1 nebo gpt-4o-mini).
Připojte Tool:
Klikněte na + pod vstupem Tool AI Agenta.
Vyhledejte a přidejte node Google Sheets.
Přejmenujte node na "add a new row".
Credential to connect with: Klikněte na tužku -> "Create New Credential".
Zde začíná složitější část nastavení Google OAuth2. Postupujte přesně:
Zkopírujte OAuth Redirect URL z n8n.
Otevřete Google Cloud Console (console.cloud.google.com).
Vytvořte/Vyberte projekt: Pokud nemáte projekt, vytvořte nový (vlevo nahoře). Pojmenujte ho např. "n8n-testing". Ujistěte se, že je vybraný správný projekt.
V levém menu jděte na APIs & Services -> OAuth consent screen.
User Type: Vyberte External (pokud nejste Google Workspace admin). Klikněte na Create.
App Information: Zadejte název aplikace (např. "n8n-google-sheets-agent"), email podpory (váš email), kontaktní email vývojáře (váš email). Klikněte na Save and Continue.
Scopes: Klikněte na Add or Remove Scopes. Vyhledejte a přidejte scope pro Google Drive API (.../auth/drive) a Google Sheets API (.../auth/spreadsheets). Klikněte na Update. Pak Save and Continue.
Test Users: Přidejte svůj vlastní Google email jako testovacího uživatele. Klikněte na Add, pak Save and Continue.
Zkontrolujte souhrn a klikněte na Back to Dashboard.
Nyní jděte v levém menu na APIs & Services -> Credentials.
Klikněte na + Create Credentials -> OAuth client ID.
Application type: Vyberte Web application.
Name: Pojmenujte ho (např. "n8n Web client 1").
V sekci Authorized redirect URIs klikněte na + Add URI a vložte URL zkopírovanou z n8n na začátku tohoto kroku.
Klikněte na Create.
Zkopírujte Your Client ID a Your Client Secret.
Vraťte se do n8n a vložte Client ID a Client Secret do příslušných polí v nastavení Google Sheets přihlašovacích údajů.
Klikněte na Save.
Mělo by se objevit tlačítko Sign in with Google. Klikněte na něj.
Vyberte Google účet, který jste přidali jako testovacího uživatele.
Potvrďte přístup ("Allow"). Měli byste vidět "Connection successful". Zavřete okno.
Povolte potřebné API v GCP: V Google Cloud Console jděte do APIs & Services -> Library. Vyhledejte "Google Drive API" a klikněte na Enable. Poté vyhledejte "Google Sheets API" a klikněte na Enable.
Vraťte se k nastavení Google Sheets node v n8n:
Operation: Vyberte Append Row.
Document: Klikněte na Choose -> From List. Po chvíli by se měl načíst seznam vašich Google Sheets. Vyberte ten, který jste vytvořili (např. "Potential business leads"). Pokud se nenačte, zkuste obnovit stránku n8n workflow nebo chvíli počkat.
Sheet: Vyberte Sheet1 (nebo název vašeho listu).
Mapping Column Mode: Nastavte na Map Each Column Manually.
Values to Send: Klikněte na ikonku "ozubeného kola/blesku" vedle každého sloupce (Name, Company, Website, Email) a vyberte Defined automatically by the model.
Vraťte se k AI Agent node:
Options -> Add Option -> System Message.
Napište prompt pro AI Agenta, který mu řekne, jaké nástroje má a co má dělat, např.: You are a helpful assistant, you can call the "google sheets" tool to interact with the user's Google Sheet files.
Uložte workflow!
Aktivujte workflow!
Krok 4: Propojení Workflowů
Vraťte se do prvního workflow ("main n8n MCP").
Otevřete nastavení node Call n8n Workflow Tool (přejmenovaného na "google_sheets").
V poli Workflow nyní vyberte workflow "google sheets agent".
Zkontrolujte, že Workflow Inputs (např. instructions nebo input) jsou stále nastaveny na Defined automatically by the model.
Uložte workflow! Ujistěte se, že je stále Active.
Krok 5: Konfigurace Claude Desktop (nebo jiného MCP klienta)
Stáhněte a nainstalujte Claude Desktop (pokud jste tak ještě neučinili) z claude.ai/download. Vyberte verzi pro váš OS.
Nainstalujte Node.js: Pokud ho nemáte, stáhněte a nainstalujte LTS verzi z nodejs.org. Claude Desktop ho potřebuje pro MCP.
Otevřete Claude Desktop a přihlaste se stejným účtem jako na webu claude.ai.
Otevřete nastavení Claude Desktop (na macOS: v horním menu klikněte na Claude -> Settings).
Přejděte do sekce Developer. Pokud ji nevidíte, možná ji musíte povolit v sekci General (hledání této volby nebylo ve videu ukázáno, ale může tam být).
Klikněte na Edit Config. Otevře se soubor claude_desktop_config.json ve vašem textovém editoru.
Najděte (nebo vytvořte) sekci mcpServers. Přidejte nový záznam pro n8n. Struktura by měla vypadat takto (nahraďte URL tou vaší z Kroku 2.5):
{
  "mcpServers": {
    "n8n": {
      "command": "npx",
      "args": [
        "-y",
        "supergateway",
        "--sse",
        "https://VASE_N8N_PRODUKCNI_URL_Z_KROKU_2.5" // Vložte sem URL
      ]
    }
  },
  "globalShortcut": "" // Nebo jiná nastavení, která tam už máte
}
Use code with caution.
Json
Důležité: Ujistěte se, že JSON formát je validní (správné závorky, čárky atd.). VASE_N8N_PRODUKCNI_URL_Z_KROKU_2.5 nahraďte skutečnou URL.
Uložte soubor claude_desktop_config.json.
Zcela ukončete a znovu spusťte Claude Desktop, aby se načetla nová konfigurace.
Krok 6: Testování
Otevřete Claude Desktop. Měli byste vidět ikonu signalizující dostupné MCP nástroje (např. +2 vedle tlačítka pro připojení souborů). Kliknutím na ni uvidíte seznam nástrojů ("calculator", "google_sheets") s popisem "From server: n8n".
Test Kalkulačky: Napište do Claude: how much is 89 * 91 use the n8n tool
Claude by měl rozpoznat, že má použít nástroj, zeptat se na povolení ("Allow tool from n8n (local)?") a zavolat n8n kalkulačku. Měli byste vidět výsledek.
Test Google Sheets Agenta: Napište do Claude: use the "google sheets" MCP tool to add mock data about a new Australian client (nebo podobný příkaz).
Claude by měl opět požádat o povolení.
Měl by zavolat váš hlavní MCP workflow, který zavolá druhý workflow (Google Sheets Agenta).
AI Agent v druhém workflow by měl zpracovat požadavek a použít Google Sheets nástroj k přidání řádku do vaší tabulky "Potential business leads".
Zkontrolujte vaši Google Sheet tabulku, zda se tam data objevila. (Video ukazuje, že se data úspěšně přidala).
Řešení Problémů a Klíčové Body:
Chyby v n8n: Pokud node zčervená, klikněte na něj a podívejte se na chybovou hlášku. Zkuste ji pochopit nebo ji zkopírujte a vložte do ChatGPT/Claude/Google.
Chyby v Claude Desktop: Pokud se nástroje nezobrazí nebo nefungují, zkontrolujte:
Správnost URL v claude_desktop_config.json.
Validitu JSON formátu.
Zda je nainstalován Node.js.
Zda jste Claude Desktop po úpravě configu restartovali.
Zda jsou oba n8n workflow uloženy a aktivní.
Google OAuth Chyby: Toto je nejčastější zdroj problémů. Ujistěte se, že:
Jste v Google Cloud Console ve správném projektu.
Máte povolené API pro Google Drive a Google Sheets.
Máte správně nastavený OAuth consent screen.
Máte vytvořený OAuth 2.0 Client ID typu "Web application".
Máte správně vloženou Redirect URI z n8n do nastavení klienta v GCP.
Máte správně zkopírované Client ID a Client Secret do n8n.
Přihlašujete se správným Google účtem (tím, který je test user, pokud je app external).
Pokud máte účet Google Workspace, mohou platit další omezení.
Používejte AI jako Pomocníka: Pokud narazíte na problém, popište ho AI (ChatGPT, Claude, Vectal), přiložte screenshoty chyb nebo nastavení a požádejte o pomoc. Naučte se "lovit ryby" (řešit problémy) místo čekání na "rybu" (přesné řešení pro váš konkrétní případ).
Ukládejte Často: V n8n vždy ukládejte změny.
Specializace Agentů: Místo jednoho obřího agenta pro všechno je lepší mít hlavního agenta (např. v Claude Desktop), který deleguje úkoly specializovaným agentům (postaveným jako n8n workflow).
Gratulujeme! Nyní máte funkční systém, kde váš AI agent (Claude) může volat nástroje definované ve vašem hostovaném n8n, včetně interakce s Google Sheets. Můžete tento princip rozšířit na téměř jakoukoli automatizaci.