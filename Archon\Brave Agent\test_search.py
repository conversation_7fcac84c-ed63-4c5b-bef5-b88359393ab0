"""
Test vyhledávání pomocí Brave Search agenta.
"""
import asyncio
import os
from dotenv import load_dotenv
from agent_tools import BraveSearchTool

async def main():
    load_dotenv()
    brave_api_key = os.getenv("BRAVE_API_KEY")
    
    if not brave_api_key:
        print("Error: BRAVE_API_KEY není nastaven v .env souboru")
        return
        
    search_tool = BraveSearchTool(brave_api_key)
    results = await search_tool.search("Matěj Faksa Ostrava")
    
    print("Výsledky vyhledávání pro 'Matěj Faksa Ostrava':")
    print("-" * 50)
    
    for result in results.get("web", {}).get("results", []):
        print(f"\nNázev: {result.get('title', 'N/A')}")
        print(f"URL: {result.get('url', 'N/A')}")
        print(f"Popis: {result.get('description', 'N/A')}")
        print("-" * 50)

if __name__ == "__main__":
    asyncio.run(main()) 