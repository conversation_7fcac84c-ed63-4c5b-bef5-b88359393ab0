import sys
import cv2
import numpy as np
import pytesseract
import mss
from PyQt5 import QtWidgets, QtGui, QtCore

# Pokud je potřeba, nastavte cestu k Tesseract-OCR (Windows)
# pytesseract.pytesseract.tesseract_cmd = r"C:\Program Files\Tesseract-OCR\tesseract.exe"

class MainWindow(QtWidgets.QMainWindow):
    def __init__(self):
        super().__init__()

        self.setWindowTitle("BlackJack Master AI - Desktop Assistant")
        self.setGeometry(100, 100, 1200, 800)

        # Hlavní widget a layout
        self.main_widget = QtWidgets.QWidget(self)
        self.setCentralWidget(self.main_widget)
        self.main_layout = QtWidgets.QHBoxLayout(self.main_widget)

        # Levý panel: Zobrazení snímku obrazovky
        self.feed_label = QtWidgets.QLabel("Screen Feed")
        self.feed_label.setFixedSize(800, 800)
        self.feed_label.setStyleSheet("background-color: black;")
        self.main_layout.addWidget(self.feed_label)

        # Pravý panel: Chatbot s doporučeními
        self.chat_layout = QtWidgets.QVBoxLayout()
        self.chat_title = QtWidgets.QLabel("AI Chatbot")
        self.chat_title.setStyleSheet("font-weight: bold; font-size: 16px;")
        self.chat_log = QtWidgets.QTextEdit()
        self.chat_log.setReadOnly(True)
        self.chat_input = QtWidgets.QLineEdit()
        self.send_button = QtWidgets.QPushButton("Odeslat")
        self.send_button.clicked.connect(self.send_chat)

        # Přidání prvků do pravého panelu
        self.chat_layout.addWidget(self.chat_title)
        self.chat_layout.addWidget(self.chat_log)
        self.chat_layout.addWidget(self.chat_input)
        self.chat_layout.addWidget(self.send_button)
        self.main_layout.addLayout(self.chat_layout)

        # Timer pro periodické zachytávání obrazovky
        self.timer = QtCore.QTimer()
        self.timer.timeout.connect(self.capture_and_process)
        self.timer.start(1000)  # interval 1000 ms = 1 sekunda

        # Inicializace mss pro zachytávání obrazovky
        self.sct = mss.mss()
        # Zachytáváme primární monitor (u online her můžeš později upravit oblast)
        self.monitor = self.sct.monitors[1]

        # Úložiště pro historii karet a běžící počet pro Hi-Lo systém
        self.card_history = []
        self.running_count = 0

    def send_chat(self):
        """Obsluha tlačítka Odeslat u chatbotu."""
        user_text = self.chat_input.text().strip()
        if user_text:
            self.chat_log.append("Uživatel: " + user_text)
            # Zde lze rozšířit interakci, např. o analýzu textu či dotazy na AI
            self.chat_input.clear()

    def capture_and_process(self):
        """Zachytí aktuální snímek obrazovky, zpracuje ho a aktualizuje GUI."""
        # Zachycení snímku z definované oblasti
        screenshot = np.array(self.sct.grab(self.monitor))
        # Převod z BGRA do BGR
        img = cv2.cvtColor(screenshot, cv2.COLOR_BGRA2BGR)
        # Pro zobrazení změníme velikost na 800x800
        display_img = cv2.resize(img, (800, 800))
        # Konverze do QImage pro zobrazení v QLabel
        height, width, channel = display_img.shape
        bytes_per_line = 3 * width
        q_img = QtGui.QImage(display_img.data, width, height, bytes_per_line, QtGui.QImage.Format_RGB888).rgbSwapped()
        self.feed_label.setPixmap(QtGui.QPixmap.fromImage(q_img))

        # Zpracování obrazu pro detekci karet pomocí OCR
        advice, detected_cards = self.process_image(img)

        # Pokud máme doporučení, zobrazíme ho v chatbotu
        if advice:
            self.chat_log.append("AI Asistent: " + advice)

    def process_image(self, img):
        """
        Základní předzpracování a OCR zpracování snímku.
        Vrací doporučení a seznam rozpoznaných karet.
        """
        # Konverze na odstíny šedi
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        # Jednoduché prahování pro zvýraznění textových prvků
        ret, thresh = cv2.threshold(gray, 150, 255, cv2.THRESH_BINARY)

        # Použití Tesseract OCR pro získání textu
        custom_config = r'--oem 3 --psm 6'
        ocr_result = pytesseract.image_to_string(thresh, config=custom_config)
        print("OCR výsledek:", ocr_result)  # Pro ladění v konzoli

        # Simulovaná detekce karet – hledáme znaky odpovídající hodnotám karet
        detected_cards = []
        possible_cards = ["A", "2", "3", "4", "5", "6", "7", "8", "9", "10", "J", "Q", "K"]
        for card in possible_cards:
            if card in ocr_result:
                detected_cards.append(card)

        # Aktualizace historie karet a výpočet běžícího počtu podle Hi-Lo metody
        for card in detected_cards:
            self.card_history.append(card)
            self.running_count += self.get_card_value(card)

        # Jednoduchá logika pro doporučení tahu:
        advice = self.get_advice(detected_cards, self.running_count)
        return advice, detected_cards

    def get_card_value(self, card):
        """
        Vrací hodnotu karty podle Hi-Lo systému:
          - Karty 2-6: +1
          - Karty 7-9: 0
          - 10, J, Q, K, A: -1
        """
        if card in ["2", "3", "4", "5", "6"]:
            return 1
        elif card in ["7", "8", "9"]:
            return 0
        elif card in ["10", "J", "Q", "K", "A"]:
            return -1
        return 0

    def get_advice(self, detected_cards, running_count):
        """
        Vrací doporučení na základě detekovaných karet a běžícího počtu.
        Toto je zjednodušená logika – lze ji dále rozšiřovat.
        """
        if running_count > 5:
            return "Doporučuji: Stát. Počítadlo je vysoké."
        elif running_count < -5:
            return "Doporučuji: Dobít (Hit). Počítadlo je nízké."
        else:
            return "Doporučuji: Hrát opatrně."

def main():
    app = QtWidgets.QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
