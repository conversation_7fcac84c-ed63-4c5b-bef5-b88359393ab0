---
name: Bug Report
about: Create a report to help improve Archon
title: '[BUG] '
labels: bug
assignees: ''
---

## Description
A clear and concise description of the issue.

## Steps to Reproduce
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

## Expected Behavior
A clear and concise description of what you expected to happen.

## Actual Behavior
A clear and concise description of what actually happened.

## Screenshots
If applicable, add screenshots to help explain your problem.

## Environment
 - OS: [e.g. Windows 10, macOS Monterey, Ubuntu 22.04]
 - Python Version: [e.g. Python 3.13, Python 3.12]
 - Using MCP or Streamlit (or something else)

## Additional Context
Add any other context about the problem here, such as:
- Does this happen consistently or intermittently?
- Were there any recent changes that might be related?
- Any workarounds you've discovered?

## Possible Solution
If you have suggestions on how to fix the issue or what might be causing it.