import { useState } from 'react'
import './App.css'

// Import components
import Header from './components/layout/Header'
import Footer from './components/layout/Footer'
import DebateForm from './components/form/DebateForm'
import Debate<PERSON>rena from './components/debate/DebateArena'

function App() {
  const [debateConfig, setDebateConfig] = useState(null);
  const [debateHistory, setDebateHistory] = useState([]);
  const [isDebateActive, setIsDebateActive] = useState(false);

  const startDebate = (config) => {
    setDebateConfig(config);
    setDebateHistory([]);
    setIsDebateActive(true);
  };

  const resetDebate = () => {
    setDebateConfig(null);
    setDebateHistory([]);
    setIsDebateActive(false);
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      <main className="flex-grow container mx-auto px-4 py-8">
        {!isDebateActive ? (
          <DebateForm onStartDebate={startDebate} />
        ) : (
          <DebateArena
            config={debateConfig}
            history={debateHistory}
            setHistory={setDebateHistory}
            onReset={resetDebate}
          />
        )}
      </main>

      <Footer />
    </div>
  )
}

export default App
